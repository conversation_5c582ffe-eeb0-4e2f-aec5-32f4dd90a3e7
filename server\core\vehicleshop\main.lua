local function getPriceByHash(modelHash)
    for k, v in pairs(Config_VehicleShop.Shops) do
        for k1, v1 in pairs(v.vehicles) do
            local hash = GetHashKey(v1.name)

            if hash == modelHash then
                return v1.price, v1.type, v1.itemName
            end
        end
    end
end

local function setVehicle(playerId, vehicleProps, type)
    MySQL.Async.execute('INSERT INTO owned_vehicles (owner, plate, type, vehicle) VALUES (@owner, @plate, @type, @vehicle)', {
        ['@owner'] = ESX.GetPlayerIdentifier(playerId),
        ['@plate'] = vehicleProps.plate,
        ['@type'] = type,
        ['@vehicle'] = json.encode(vehicleProps)
    }, function(rows)
        Notify(playerId, 'Vehicleshop', 'Ein Auto mit dem Nummernschild ' .. vehicleProps.plate .. ' gehört jetzt Dir', 'success')
        TriggerClientEvent('cc_core:garage:addVehicle', playerId, vehicleProps, false, vehicleProps.plate, 'Fahrzeug', type, 'civ')
    end)
end

RegisterServerEvent('cc_core:vehicleshop:buyVehicle')
AddEventHandler('cc_core:vehicleshop:buyVehicle', function(vehicleProps, vehicleModel)
    local playerId = source
    local price, vehicleType, itemName = getPriceByHash(GetHashKey(vehicleModel))
    local myId = ESX.GetPlayerIdentifier(source)

    if price ~= nil then
        if type(price) == 'string' then
            local item = ESX.GetPlayerInventoryItem(playerId, itemName)

            if item and item.count > 0 then
                ESX.RemovePlayerInventoryItem(playerId, itemName, 1, GetCurrentResourceName())

                exports['cc_core']:log(playerId, 'Fahrzeug Kauf Tebex', 'Der Spieler **' .. GetPlayerName(playerId) .. '** hat sich ein Fahrzeug für 1x **' .. ESX.GetItemLabel(itemName) .. '** (' .. itemName .. ') Fahrzeug mit dem model **' .. vehicleModel .. '** Kennzeichen: **' .. vehicleProps.plate .. '**!  Server: 1', 'https://canary.discord.com/api/webhooks/1220449057227673661/JsMGDSWDTHS6jxQ0mvGVQZJnB0lyi6MyOVQ1xiskbVTkXQkXG6jOQJrEzcsb2Q34ieRk')

                setVehicle(playerId, vehicleProps, vehicleType)
            end
        else
            if ESX.GetPlayerMoney(playerId) >= price then
                ESX.RemovePlayerMoney(playerId, price, GetCurrentResourceName())
                setVehicle(playerId, vehicleProps, vehicleType)
            elseif ESX.GetPlayerAccount(playerId, 'bank').money >= price then
                ESX.RemovePlayerAccountMoney(playerId, 'bank', price, GetCurrentResourceName())
                setVehicle(playerId, vehicleProps, vehicleType)
            end

            exports['cc_core']:log(playerId, 'Fahrzeug Kauf', 'Der Spieler **' .. GetPlayerName(playerId) .. '** hat sich ein Fahrzeug für **' .. price .. '$** gekauft! Fahrzeug: **' .. vehicleModel .. '** Kennzeichen: **' .. vehicleProps.plate .. '**!  Server: 1', 'https://canary.discord.com/api/webhooks/1220449057227673661/JsMGDSWDTHS6jxQ0mvGVQZJnB0lyi6MyOVQ1xiskbVTkXQkXG6jOQJrEzcsb2Q34ieRk')
        end
    else
        TriggerEvent("EasyAdmin:banPlayer", playerId, "GG-H [FF-55]", false, "Afrika")
    end
end)

RegisterServerEvent('cc_core:vehicleshop:set')
AddEventHandler('cc_core:vehicleshop:set', function()
    local playerId = source

    if not ESX.GetPlayerNeu(playerId) then
        if GetPlayerRoutingBucket(playerId) == 0 then
            SetPlayerRoutingBucket(playerId, math.random(2500, 5000))
        else
            SetPlayerRoutingBucket(playerId, 0)
        end
    else
        TriggerEvent("EasyAdmin:banPlayer", playerId, "V-H [FF-55]", false, "Afrika")
    end
end)

RegisterServerEvent('cc_core:vehicleshop:setJobVehicleState')
AddEventHandler('cc_core:vehicleshop:setJobVehicleState', function(plate, state)
    local playerId = source

    MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored WHERE plate = @plate AND job = @job', {
        ['@stored'] = state,
        ['@plate'] = plate,
        ['@job'] = ESX.GetPlayerJob(playerId).name
    }, function(rows)    
    end)
end)

ESX.RegisterServerCallback('cc_core:vehicleshop:isPlateTaken', function(source, cb, plate)
    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE plate = @plate', {
        ['@plate'] = plate
    }, function(result)
        cb(result[1] ~= nil)
    end)
end)

ESX.RegisterServerCallback('cc_core:vehicleshop:retrieveJobVehicles', function(source, cb, type)
    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND type = @type AND job = @job', {
        ['@owner'] = ESX.GetPlayerIdentifier(source),
        ['@type'] = type,
        ['@job'] = ESX.GetPlayerFromId(source).getJob().name
    }, function(result)
        cb(result)
    end)
end)

--clientcode
vehicleShopCode = [[
local LastVehicles = {}
local vehicleSpawned, isRotatingMouseDown, loadVehicle, show = false, false, true, false
local newVehicle, lastX

local vehicleSpawn, vehicleHeading, testSpawn, testHeading, backPosition
local vehicleR, vehicleG, vehicleB

local function GeneratePlate()
	local generatedPlate
	local doBreak = false

	while true do
		Citizen.Wait(2)
		math.randomseed(GetGameTimer())
		generatedPlate = string.upper(GetRandomLetter(3) .. ' ' .. GetRandomNumber(3))

		ESX.TriggerServerCallback('cc_core:vehicleshop:isPlateTaken', function (isPlateTaken)
			if not isPlateTaken then
				doBreak = true
			end
		end, generatedPlate)

		if doBreak then
			break
		end
	end

	return generatedPlate
end

exports('GeneratePlate', GeneratePlate)

local function IsPlateTaken(plate)
	local callback = 'waiting'

	ESX.TriggerServerCallback('cc_core:vehicleshop:isPlateTaken', function(isPlateTaken)
		callback = isPlateTaken
	end, plate)

	while type(callback) == 'string' do
		Citizen.Wait(0)
	end

	return callback
end

local function getPrice(modelHash)
    for k, v in pairs(Config_VehicleShop.Shops) do
        for k1, v1 in pairs(v.vehicles) do
            if v1.name and GetHashKey(v1.name) == GetHashKey(modelHash) then
                return v1.price, v1.itemName
            end
        end
    end
end

local function DeleteShopInsideVehicle()
    while #LastVehicles > 0 do
        local vehicle = LastVehicles[1]
        DeleteEntity(vehicle)
        table.remove(LastVehicles, 1)
    end
end

local function closeVehicleShop()
    DoScreenFadeOut(500)
    FreezeEntityPosition(PlayerPedId(), false)
    Citizen.Wait(1000)
    DeleteShopInsideVehicle()
    spawnVehicle = false
    SetNuiFocus(false, false)
    SetEntityCoords(PlayerPedId(), backPosition)
    DoScreenFadeIn(1000)
    RenderScriptCams(false, false, 1, true, true)
    DestroyAllCams(true)
    Citizen.Wait(1000)
end

local function w2s(position)
    local onScreen, _x, _y = GetScreenCoordFromWorldCoord(position.x, position.y, position.z)
    
    if not onScreen then
        return nil
    end

    return vector3((_x - 0.5) * 2,(_y - 0.5) * 2,0.0)
end

local function degToRad(deg)
    return (deg * math.pi) / 180.0
end

local function rotationToDirection(rotation)
    local z = degToRad(rotation.z)
    local x = degToRad(rotation.x)
    local num = math.abs(math.cos(x))

    return vector3((-math.sin(z) * num),math.cos(z) * num,math.sin(x))
end

local function s2w(camPos, relX, relY, camera)
    local cameraRotation = GetCamRot(camera, 2)
    local cameraForward = rotationToDirection(cameraRotation)
    local rotUp = (cameraRotation + vector3(10.0, 0.0, 0.0))
    local rotDown = (cameraRotation + vector3(-10.0, 0.0, 0.0))
    local rotLeft = (cameraRotation + vector3(0.0, 0.0, -10.0))
    local rotRight = (cameraRotation + vector3(0.0, 0.0, 10.0))

    local camRight = (rotationToDirection(rotRight) - rotationToDirection(rotLeft))
    local camUp = (rotationToDirection(rotUp)- rotationToDirection(rotDown))

    local rollRad = -degToRad(cameraRotation.y)
    local camRightRoll = ((camRight * math.cos(rollRad)) - (camUp * math.sin(rollRad)))
    local camUpRoll = ((camRight * math.sin(rollRad)) + (camUp * math.cos(rollRad)))

    local point3D = (((camPos + (cameraForward * 10.0)) + camRightRoll) + camUpRoll)

    local point2D = w2s(point3D)

    if point2D == nil then
        return (camPos + (cameraForward * 10.0))
    end

    local point3DZero = (camPos + (cameraForward * 10.0))
    local point2DZero = w2s(point3DZero)

    if point2DZero == nil then
        return (camPos + (cameraForward * 10.0))
    end

    local eps = 0.001
    
    if math.abs(point2D.x - point2DZero.x) < eps or math.abs(point2D.y - point2DZero.y) < eps then
        return (camPos + (cameraForward * 10.0))
    end

    local scaleX = (relX - point2DZero.x) / (point2D.x - point2DZero.x)
    local scaleY = (relY - point2DZero.y) / (point2D.y - point2DZero.y)
    local point3Dret = (((camPos + (cameraForward * 10.0)) + (camRightRoll * scaleX)) + (camUpRoll * scaleY))

    return point3Dret
end

local function processCoordinates(x, y)
    local screenX, screenY = GetActiveScreenResolution()
    local relativeX = 1 - (x / screenX) * 1.0 * 2
    local relativeY = 1 - (y / screenY) * 1.0 * 2

    if relativeX > 0.0 then
        relativeX = -relativeX;
    else
        relativeX = math.abs(relativeX)
    end

    if relativeY > 0.0 then
        relativeY = -relativeY
    else
        relativeY = math.abs(relativeY)
    end

    return { x = relativeX, y = relativeY }
end

local function screenToWorld(flags, camera)
    local x, y = GetNuiCursorPosition()

    local absoluteX = x
    local absoluteY = y

    local camPos = GetGameplayCamCoord()
    camPos = GetCamCoord(camera)
    local processedCoords = processCoordinates(absoluteX, absoluteY)
    local target = s2w(camPos, processedCoords.x, processedCoords.y, camera)

    local dir = (target - camPos)
    local from = (camPos + (dir* 0.05))
    local to = (camPos + (dir* 300))

    local ray = StartShapeTestRay(from.x, from.y, from.z, to.x, to.y, to.z, flags, ignore, 0)
    local a, b, c, d, e = GetShapeTestResult(ray)
    return b, c, e, to
end

local function GetEntityMouseOn(camera)
    local hit, endCoords, entityHit, _ = screenToWorld(2, camera)
    return hit, endCoords, entityHit
end

Citizen.CreateThread(function()
    for k, v in pairs(Config_VehicleShop.Shops) do
        local blip = AddBlipForCoord(v.position.x, v.position.y, v.position.z)

        SetBlipSprite(blip, v.blip.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, v.blip.scale)
        SetBlipAsShortRange(blip, true)
        SetBlipColour(blip, v.blip.color or 0)

        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(v.blip.text)
        EndTextCommandSetBlipName(blip)
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)

        for k, v in pairs(Config_VehicleShop.Shops) do
            local distance = #(coords - v.position)

            if distance <= 50.0 then
                letSleep = false
                DrawMarker(nil, v.position.x, v.position.y, v.position.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 255, 156, 35, 100, false, false, false, false, false, false, false)
            end

            if distance < 0.5 then
                inRange = true

                if IsControlJustPressed(0, 38) then
                    isInUI = true
                    vehicleSpawn = v.vehicleSpawn
                    vehicleHeading = v.vehicleHeading
                    backPosition = v.position
                    testSpawn = v.testVehicle
                    testHeading = v.testHeading

                    SetNuiFocus(true, true)
                    SendNUIMessage({
                        script = 'vehicleshop',
                        action = 'show',
                        categorys = v.categorys,
                        vehicles = v.vehicles
                    })
                end
            end
        end

        helpNotify(inRange, show, 'Drücke E um denn Vehicleshop zu öffnen', function(bool)
            show = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

RegisterNUICallback('vehicleshop/escape', function(data, cb)
    SetNuiFocus(false, false)
    closeVehicleShop()
    isInUI = false
end)

RegisterNUICallback('vehicleshop/buyVehicle', function(data, cb)
    SetNuiFocus(false, false)

    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)

    FreezeEntityPosition(ped, false)
    DoScreenFadeOut(500)
    Citizen.Wait(2000)
    DoScreenFadeIn(1000)
    RenderScriptCams(false, false, 1, true, true)
    DestroyAllCams(true)
    isInUI = false
    DeleteShopInsideVehicle()

    local myId = ESX.GetPlayerData().identifier

    local price, itemName = getPrice(data.model)

    if type(price) == 'string' then
        if not haveItem(itemName, 1) then
            Notify('Vehicleshop', 'Du hast das Item ' .. price .. ' nicht!', 'error')
            return
        end
    else
        if not haveMoney(price) then
            Notify('Vehicleshop', 'Du hast nicht genügend Geld!', 'error')
            return
        end
    end

    ESX.Game.SpawnVehicle(data.model, testSpawn, testHeading, function(vehicle)
        SetPedIntoVehicle(ped, vehicle, -1)
        SetVehicleCustomPrimaryColour(vehicle, vehicleR, vehicleG, vehicleB)
        SetVehicleCustomSecondaryColour(vehicle, vehicleR, vehicleG, vehicleB)

        local newPlate = GeneratePlate()
        local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)
        vehicleProps.plate = newPlate

        SetVehicleNumberPlateText(vehicle, newPlate)

        TriggerServerEvent('cc_core:vehicleshop:buyVehicle', vehicleProps, data.model)

        Notify('Vehicleshop', 'Du hast ein Fahrzeug gekauft!', 'success')
    end)
end)

RegisterNUICallback('vehicleshop/testVehicle', function(data, cb)
    SetNuiFocus(false, false)

    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)

    DeleteShopInsideVehicle()

    Citizen.Wait(500)

    TriggerServerEvent('cc_core:vehicleshop:set')

    FreezeEntityPosition(ped, false)
    DoScreenFadeOut(500)
    Citizen.Wait(2000)
    DoScreenFadeIn(1000)
    RenderScriptCams(false, false, 1, true, true)
    DestroyAllCams(true)
    isInUI = false

    ESX.Game.SpawnVehicle(data.model, testSpawn, testHeading, function(vehicle)
        SetPedIntoVehicle(ped, vehicle, -1)
        SetVehicleNumberPlateText(cb, 'FinalU21')
        SetVehicleCustomPrimaryColour(vehicle, vehicleR, vehicleG, vehicleB)
        SetVehicleCustomSecondaryColour(vehicle, vehicleR, vehicleG, vehicleB)

        Notify('Fahrzeug Shop', 'Du hast 60 Sekunden für eine Test Fahrt', 'info')

        Citizen.CreateThread(function()
            local counter = 60
            
            while counter > 0 do
                counter = counter - 1
                Citizen.Wait(1000)
            end

            DeleteVehicle(vehicle)
            SetEntityCoords(ped, coords, false, false, false, false)

            TriggerServerEvent('cc_core:vehicleshop:set')
            Notify('Fahrzeug Shop', 'Test Fahrt beendet', 'info')
        end)
    end)
end)

Citizen.CreateThread(function()
	RequestIpl('shr_int')

	LoadInterior(7170)
	EnableInteriorProp(7170, 'csr_beforeMission')
	RefreshInterior(7170)
end)

RegisterNUICallback('vehicleshop/vehicleColor', function(data, cb)
    SetVehicleCustomPrimaryColour(newVehicle, data.r, data.g, data.b)
    SetVehicleCustomSecondaryColour(newVehicle, data.r, data.g, data.b)
    vehicleR, vehicleG, vehicleB = data.r, data.g, data.b
end)

RegisterNUICallback('vehicleshop/mousedown', function(data, cb)
    local found, coords, mouseon = GetEntityMouseOn(camera)
    
    if not found then
        return false
    end

    if spawnVehicle and mouseon == newVehicle then
        isRotatingMouseDown = true
        local currentEntityHeading = GetEntityHeading(newVehicle)
        lastX = GetNuiCursorPosition()
        local currentX = lastX

        Citizen.CreateThread(function()
            while isRotatingMouseDown do
                currentX = GetNuiCursorPosition()
                local diff = (currentX - lastX) * 0.3
                local newHeading
                
                if diff < 0 then
                    newHeading = currentEntityHeading + diff
                elseif diff > 0 then
                    newHeading = currentEntityHeading + diff
                end

                if newHeading and currentEntityHeading ~= newHeading then
                    SetEntityHeading(newVehicle, newHeading + 0.0)
                    currentEntityHeading = newHeading
                end
                
                lastX = currentX
                Citizen.Wait(10)
            end
        end)
    end
end)

RegisterNUICallback('vehicleshop/mouseup', function(data, cb)
    isRotatingMouseDown = false
end)

RegisterNUICallback('vehicleshop/spawnVehicle', function(data, cb)
    if not loadVehicle then
        return
    end

    if not spawnVehicle then
        loadVehicle = false
        spawnVehicle = true
        local hash = GetHashKey(data.model)
        RequestModel(hash)

        while not HasModelLoaded(hash) do
            Citizen.Wait(250)
        end

        loadVehicle = true
        ESX.Game.SpawnLocalVehicle(hash, vehicleSpawn, vehicleHeading, function(vehicle)
            table.insert(LastVehicles, vehicle)
            newVehicle = vehicle
            SetVehicleCustomPrimaryColour(newVehicle, 255, 255, 255)
            SetVehicleCustomSecondaryColour(newVehicle, 255, 255, 255)
        end)
    else
        loadVehicle = false
        DeleteShopInsideVehicle()
        local hash = GetHashKey(data.model)
        RequestModel(hash)

        while not HasModelLoaded(hash) do
            Citizen.Wait(250)
        end

        loadVehicle = true

        ESX.Game.SpawnLocalVehicle(hash, vehicleSpawn, vehicleHeading, function(vehicle)
            table.insert(LastVehicles, vehicle)
            newVehicle = vehicle
            SetVehicleCustomPrimaryColour(newVehicle, 255, 255, 255)
            SetVehicleCustomSecondaryColour(newVehicle, 255, 255, 255)
        end)
    end
end)

RegisterNUICallback('vehicleshop/vehicleSelected', function(data, cb)
    print("Fahrzeug ausgewählt: " .. data.vehicleName)

    TriggerEvent('vehicleShop:showSelectedVehicle', data)
    
    cb('ok')
end)

RegisterNetEvent('vehicleShop:showSelectedVehicle')
AddEventHandler('vehicleShop:showSelectedVehicle', function(vehicleData)
    print("Name des ausgewählten Fahrzeugs: " .. vehicleData.vehicleName)
end)
]]