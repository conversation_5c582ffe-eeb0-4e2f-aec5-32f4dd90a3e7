<?xml version="1.0" encoding="UTF-8"?>

<CWeaponInfoBlob>
  <SlotNavigateOrder>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="84" />
          <Entry>SLOT_KNUCKLE</Entry>
        </Item>
      </WeaponSlots>
    </Item>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="84" />
          <Entry>SLOT_KNUCKLE</Entry>
        </Item>
      </WeaponSlots>
    </Item>
  </SlotNavigateOrder>
  <SlotBestOrder>
    <WeaponSlots>
      <Item>
        <OrderNumber value="375" />
        <Entry>SLOT_KNUCKLE</Entry>
      </Item>
    </WeaponSlots>
  </SlotBestOrder>
  <TintSpecValues />
  <FiringPatternAliases />
  <UpperBodyFixupExpressionData />
  <AimingInfos />
  <Infos>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos>
        <Item type="CWeaponInfo">
          <Name>WEAPON_KNUCKLE</Name>
          <Model>W_ME_Knuckle</Model>
          <Audio />
          <Slot>SLOT_KNUCKLE</Slot>
          <DamageType>MELEE</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>MELEE</FireType>
          <WheelSlot>WHEEL_UNARMED_MELEE</WheelSlot>
          <Group>GROUP_UNARMED</Group>
          <AmmoInfo ref="NULL" />
          <AimingInfo ref="UNARMED_TARGETING_RESTRAINTS" />
          <ClipSize value="0" />
          <AccuracySpread value="0.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMinOverride value="-1.000000" />
          <RecoilAccuracyMax value="0.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="230000.000000" />
          <HeadShotDamageModifierPlayer value="1288860.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="0.000000" />
          <ForceHitPed value="120.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="70.000000" />
              <ForceBack value="60.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="80.000000" />
              <ForceBack value="60.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_SPINE2</BoneTag>
              <ForceFront value="150.000000" />
              <ForceBack value="100.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="2250.000000" />
          <Penetration value="0.000000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_PUNCH_KICK</EffectGroup>
            <FlashFx />
            <FlashFxAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <ShellFx />
            <TracerFx />
            <PedDamageHash />
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <FlashFxChanceSP value="0.000000" />
            <FlashFxChanceMP value="0.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.000000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="0" />
          <RumbleIntensity value="0.000000" />
          <RumbleIntensityTrigger value="0.000000" />
          <RumbleDamageIntensity value="1.000000" />
          <NetworkPlayerDamageModifier value="1.000000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.000000" />
          <LockOnRange value="11.500000" />
          <WeaponRange value="500.000000"  />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="1.600000" />
          <DamageFallOffRangeMax value="1.600000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>MELEE_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash />
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="50.000000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash />
          <RecoilShakeHashFirstPerson />
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="0.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMin value="0.100000" />
          <AimOffsetMax x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeLengthMax value="0.150000" />
          <AimOffsetMinFPSIdle x="0.089000" y="0.375000" z="0.302000" />
          <AimOffsetMedFPSIdle x="0.063000" y="0.280000" z="0.278000" />
          <AimOffsetMaxFPSIdle x="0.098000" y="0.110000" z="0.341000" />
          <AimOffsetMinFPSLT x="0.000000" y="0.100000" z="0.300000" />
          <AimOffsetMaxFPSLT x="0.100000" y="0.100000" z="0.400000" />
          <AimOffsetMinFPSRNG x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetMaxFPSRNG x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetMinFPSScope x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetMaxFPSScope x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMinFPSIdle x="0.100000" y="0.500000" z="0.001000" />
          <AimOffsetEndPosMedFPSIdle x="0.000000" y="0.600000" z="0.301000" />
          <AimOffsetEndPosMaxFPSIdle x="0.000000" y="0.300000" z="0.810000" />
          <AimProbeRadiusOverrideFPSIdle value="-1.000000" />
          <AimProbeRadiusOverrideFPSIdleStealth value="0.300000" />
          <AimProbeRadiusOverrideFPSLT value="0.200000" />
          <AimProbeRadiusOverrideFPSRNG value="0.000000" />
          <AimProbeRadiusOverrideFPSScope value="0.000000" />
          <TorsoAimOffset x="0.000000" y="0.000000" />
          <TorsoCrouchedAimOffset x="0.000000" y="0.000000" />
          <LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="1.000000" />
          <ReticuleStyleHash>WEAPON_UNARMED</ReticuleStyleHash>
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_KNUCKLE</PickupHash>
          <MPPickupHash />
          <HumanNameHash>WT_KNUCKLE</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>KNUCKLE</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>Normal</NmShotTuningSet>
          <AttachPoints>
			<Item>
              <AttachBone>gun_root</AttachBone>
              <Components>
				<Item>
                  <Name>COMPONENT_KNUCKLE_VARMOD_BASE</Name>
                  <Default value="false" />
                </Item>
				<Item>
                  <Name>COMPONENT_KNUCKLE_VARMOD_PIMP</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_KNUCKLE_VARMOD_BALLAS</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_KNUCKLE_VARMOD_DOLLAR</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_KNUCKLE_VARMOD_DIAMOND</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_KNUCKLE_VARMOD_HATE</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_KNUCKLE_VARMOD_LOVE</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_KNUCKLE_VARMOD_PLAYER</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_KNUCKLE_VARMOD_KING</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_KNUCKLE_VARMOD_VAGOS</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
		  </AttachPoints>
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>AllowMeleeBlock CarriedInHand ArmourPenetrating CanLockonOnFoot CanLockonInVehicle CanFreeAim UsableOnFoot UsableClimbing UsableInCover DisableLeftHandIkInCover DoesRevivableDamage AllowCloseQuarterKills AllowMeleeIntroAnim HasLowCoverSwaps NoWheelStats QuitTransitionToIdleIntroOnWeaponChange DisableFPSScope AttachFPSLeftHandIKToRight UseFPSAimIK UseFPSSecondaryMotion MeleeFist UsableUnderwater</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="default" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="1.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="5" />
          <HudSpeed value="20" />
          <HudCapacity value="0" />
          <HudAccuracy value="0" />
          <HudRange value="0" />
		  <MeleeRightFistTargetHealthDamageScaler value="0.660000" />
        </Item>
      </Infos>
    </Item>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos />
    </Item>
  </Infos>
  <VehicleWeaponInfos />
  <Name>DLC - Knuckle Dusters</Name>
</CWeaponInfoBlob>