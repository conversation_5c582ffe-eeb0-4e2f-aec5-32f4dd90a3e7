Config_Clsguide = {}

Config_Clsguide.BlackPosition = {
    {
        coords = vector3(-2050.69, 3236.63, 31.5)
    },
}

Config_Clsguide.DienstShit = {
    {
        job = 'ambulance',
        coords = vector3(-681.0396, 328.9331, 83.0831)
    },
    {
        job = 'police',
        coords = vector3(436.2858, -980.0966, 31.0261)
    },
	{
        job = 'fib',
        coords = vector3(115.84, -748.54, 45.75)
    },

    {
        job = 'mechanic',
        coords = vector3(883.0842, -2100.8855, 30.4594)
    }
}

Config_Clsguide.Objects = {
	{ -- PLZ 9148
        entity = nil,
		name = 'prop_atm_01',
		coords = vector3(402.267548, -1624.8114, 28.289402),
		heading = 50.0,
		freeze = true
	},

	{ -- PLZ 3063
        entity = nil,
		name = 'prop_atm_01',
		coords = vector3(2766.18, 1349.02, 23.52),
		heading = 271.32,
		freeze = true
	},

	{ -- Life Invader
        entity = nil,
		name = 'prop_atm_01',
		coords = vector3(4055.26, 2345.02, 23.9),
		heading = 0.0,
		freeze = true
	},

	{ -- PLZ 8000
        entity = nil,
		name = 'prop_atm_01',
		coords = vector3(-1802.31885, -1196.53333, 12.0132189),
		heading = -130.0,
		freeze = true
	},

    { -- Casino
        entity = nil,
        name = 'prop_atm_01',
        coords = vector3(1117.4758, 216.6205, -50.4352),
        heading = 319.0776,
        freeze = true
    },

    { -- Autohaus
        entity = nil,
        name = 'prop_atm_01',
        coords = vector3(-943.5706, -2071.1028, 8.2260),
        heading = 43.8208,
        freeze = true
    },

    { -- Autohaus
        entity = nil,
        name = 'prop_helipad_01',
        coords = vector3(-919.9295, -2161.0664, 7.6552),
        heading = 86.0149,
        freeze = true
    },

    { -- Autohaus
        entity = nil,
        name = 'prop_vend_soda_01',
        coords = vector3(-939.6119, -2067.4954, 8.5269),
        heading = 44.9060,
        freeze = true
    },

    { -- Autohaus
        entity = nil,
        name = 'prop_vend_snak_01',
        coords = vector3(-938.5271, -2066.3259, 8.5269),
        heading = 44.9060,
        freeze = true
    },

    { -- PLZ 5009
        entity = nil,
        name = 'prop_atm_01',
        coords = vector3(-1900.8491, 2065.4541, 139.8904),
        heading = 320.5939,
        freeze = true
    },

    { -- Casino
        entity = nil,
        name = 'prop_atm_01',
        coords = vector3(919.8786, 39.1003, 80.0960),
        heading = 237.4021,
        freeze = true
    },

    { -- WP
        entity = nil,
        name = 'prop_atm_01',
        coords = vector3(72.3497, -804.8713, 30.5029),
        heading = 160.0789,
        freeze = true
    },

    { -- Army
        entity = nil,
        name = 'prop_atm_01',
        coords = vector3(-2323.86, 3257.79, 32.04),
        heading = 330.16,
        freeze = true
    },
    { -- PD
    entity = nil,
    name = 'prop_atm_01',
    coords = vector3(-598.93, -933.56, 22.86),
    heading = 268.15,
    freeze = true
    },
    { -- Baumarkt
    entity = nil,
    name = 'prop_atm_01',
    coords = vector3(-376.41, -100.48, 38.68),
    heading = 343.79,
    freeze = true
    },
}

Config_Clsguide.Shops = {
    {
        job = 'elpresidente',
        job3 = false,
        grade = 1,
        coords = vector3(131.76, -1285.13, 29.27),
        items = {
            {
                black = false,
                name = 'gin_tonic',
                label = 'Gintonic',
                price = 1
            },
            {
                black = false,
                name = 'jacky',
                label = 'Jacky',
                price = 1
            },
            {
                black = false,
                name = 'jackycola',
                label = 'Jackycola',
                price = 1
            },
            {
                black = false,
                name = 'jimbeam',
                label = 'Jimbeam',
                price = 1
            },
            {
                black = false,
                name = 'mojito',
                label = 'Mojito',
                price = 1
            },
            {
                black = false,
                name = 'finalbull',
                label = 'Finalbull',
                price = 1
            },
            {
                black = false,
                name = 'sex_on_the_beach',
                label = 'SexOnTheBeach',
                price = 1
            },
            {
                black = false,
                name = 'swimmingpool',
                label = 'Swimmingpool',
                price = 1
            },
            {
                black = false,
                name = 'apfel2',
                label = 'Apfelsaft',
                price = 1
            },
            {
                black = false,
                name = 'ham_menu',
                label = 'Hamburger Menü',
                price = 1
            },
        }
    },
    {
        job = 'burgershot',
        job3 = true,
        grade = 1,
        coords = vector3(-553.34, 280.22, 82.18),
        items = {
            {
                black = false,
                name = 'hamburger',
                label = 'Hamburger',
                price = 1
            },
            {
                black = false,
                name = 'cheeseburger',
                label = 'Cheeseburger',
                price = 1
            },
            {
                black = false,
                name = 'nuggets',
                label = 'Chicken Nuggets',
                price = 1
            },
            {
                black = false,
                name = 'pommes',
                label = 'Pommes',
                price = 1
            },
            {
                black = false,
                name = 'cola',
                label = 'Cola',
                price = 1
            },
            {
                black = false,
                name = 'icetea',
                label = 'Eistee',
                price = 1
            },
            {
                black = false,
                name = 'milkshake',
                label = 'Milchshake',
                price = 1
            },
            {
                black = false,
                name = 'icecream',
                label = 'Eis',
                price = 1
            },
            {
                black = false,
                name = 'cheesy_menu',
                label = 'Cheeseburger Menü',
                price = 1
            },
            {
                black = false,
                name = 'ham_menu',
                label = 'Hamburger Menü',
                price = 1
            },
            {
                black = false,
                name = 'mini_menu',
                label = 'Mini Menü',
                price = 1
            },
        }
    },
    {
        job = 'cafeistanbul',
        job3 = true,
        grade = 1,
        coords = vector3(410.43, -1501.84, 30.16),
        items = {
            {
                black = false,
                name = 'adana_kebap',
                label = 'Adana Kebap',
                price = 1
            },
            {
                black = false,
                name = 'linsensuppe',
                label = 'Linsensuppe',
                price = 1
            },
            {
                black = false,
                name = 'simit',
                label = 'Simit',
                price = 1
            },
            {
                black = false,
                name = 'kuenefe',
                label = 'Künefe',
                price = 1
            },
            {
                black = false,
                name = 'baklava',
                label = 'Baklava',
                price = 1
            },
            {
                black = false,
                name = 'ayran',
                label = 'Ayran',
                price = 1
            },
            {
                black = false,
                name = 'turkish_coffee',
                label = 'Türkischer Kaffee',
                price = 1
            },
            {
                black = false,
                name = 'cay',
                label = 'Cay',
                price = 1
            }
        }
    },
    {
        job = "none",
        job3 = false,
        grade = nil,
        coords = vector3(-552.41, 5328.1, 73.64),
        items = {
            {
                black = true,
                name = 'clip_extended',
                label = 'Erweitertes Magazin',
                price = 1500
            },
            {
                black = true,
                name = 'compensator',
                label = 'Kompensator',
                price = 1500
            },
            {
                black = true,
                name = 'suppressor',
                label = 'Schalldämpfer',
                price = 1200
            },
            {
                black = true,
                name = 'mounted_scope',
                label = 'Holo Visier',
                price = 1500
            },
            {
                black = true,
                name = 'flashlight',
                label = 'Taschenlampe',
                price = 1000
            },
            {
                black = true,
                name = 'gold_tint',
                label = 'Waffenskin Gold',
                price = 1000
            },
            {
                black = true,
                name = 'platinum_tint',
                label = 'Waffenskin Platinum',
                price = 1000
            },
            {
                black = true,
                name = 'papiere',
                label = 'Akten Faker',
                price = 10000
            },
            {
                black = true,
                name = 'clip_drum',
                label = 'Trommelmagazin',
                price = 10000
            },
        }
    }

}

Config_Clsguide.ViskyStuff = {
    --[[{
        coords = vector3(-811.15, -1215.53, 7.34),
        message = 'Drücke E zum Bauen',
        job3 = true,
        items = {
            {
                name = 'schokodope',
                label = 'Schokodope',
                type = 'item',

                need = {
                    {
                        itemName = 'opium',
                        itemCount = 2
                    },
                    {
                        itemName = 'mdma',
                        itemCount = 1
                    },
                }
            },

            {
                name = 'hashbrownie',
                label = 'Hash Brownie',
                type = 'item',

                need = {
                    {
                        itemName = 'ecstasy',
                        itemCount = 1
                    }
                }
            },

            {
                name = 'bong',
                label = 'Bong',
                type = 'item',

                need = {
                    {
                        itemName = 'mdma',
                        itemCount = 2
                    }
                }
            },
        },

        jobs = {
            'Coffee'
        }
    }]]
}

Config_Clsguide.ClothRoom = {
}

Config_Clsguide.Teleporters = {

    ['tp1'] = {
        Job = {
            'none'
        },

        Enter = {
            Coords = vector3(392.24, -76.03, 68.18),
            Text = 'Drücke E'
        },

        Exit = {
            Coords = vector3(372.81, -57.25, 103.36),
			Text = 'Drücke E'
        }
    },

    ['tp2'] = {
        Job = {
            'none'
        },

        Enter = {
            Coords = vector3(371.83, -60.31, 103.36),
            Text = 'Drücke E'
        },

        Exit = {
            Coords = vector3(382.76, -51.36, 122.54),
			Text = 'Drücke E'
        }
    },

    ['fib1'] = {
        Job = {
            'fib'
        },

        Enter = {
            Coords = vector3(136.17, -761.46, 44.80),
            Text = 'Drücke E'
        },

        Exit = {
            Coords = vector3(136.02, -761.7, 241.20),
			Text = 'Drücke E'
        }
    },

    ['fib2'] = {
        Job = {
            'fib'
        },

        Enter = {
            Coords = vector3(141.26, -765.55, 44.80),
            Text = 'Drücke E'
        },

        Exit = {
            Coords = vector3(114.83, -742.15, 257.20),
			Text = 'Drücke E'
        }
    },

    ['fib3'] = {
        Job = {
            'fib'
        },

        Enter = {
            Coords = vector3(138.95, -762.75, 43.75),
            Text = 'Drücke E'
        },

        Exit = {
            Coords = vector3(176.0, -684.97, 32.12),
			Text = 'Drücke E'
        }
    },
}

Config_Clsguide.Drugs = {
    'jimbeam',
    'schokodope',
    'hashbrownie',
    'bong',
    'greygoose',
    'chantre',
    'bombay',
    'belvedere',
    'jacky',
    'tequilla',
    'swimmingpool',
    'pinacolada',
    'mojito',
    'sex_on_the_beach',
    'gin_tonic',
    'champagner_hotel',
    'wein_suess_hotel',
    'martini_hotel',
    'wein_hotel',
}

Config_Clsguide.AnchorStops = {
    [1] = { coords = vector3(1891.9, 4238.5, 30.0) },
    [2] = { coords = vector3(173, 4055, 30) },
    [3] = { coords = vector3(1107.0974, 3964.3523, 30.5007) },

    [4] = { coords = vector3(-533.1880, 6644.2573, 0.4061) },
    [5] = { coords = vector3(-307.7582, 6829.9243, -0.0922) },

    [6] = { coords = vector3(-1396.6758, -1924.5250, -1.3390) },
    [7] = { coords = vector3(-1584.5125, -1771.0519, -0.1996) },
    [8] = { coords = vector3(-1859.3271, -1242.8535, 8.6158) },
    [9] = { coords = vector3(180.1190, -969.3015, 30.8662) }
}

Config_Clsguide.attachments = {
	["clip_drum"] = {
		label = "Trommelmagazin"
	},

	["flashlight"] = {
		label = "Taschenlampe"
	},

	["clip_extended"] = {
		label = "Erweitertes Magazin"
	},

	["clip_box"] = {
		label = "Box Magazin"
	},

	["scope"] = {
		label = "Zielfernrohr"
	},

	["suppressor"] = {
		label = "Schalldämpfer"
	},

	["grip"] = {
		label = "Griff"
	},

    ["mounted_scope"] = {
        label = "Holo Visir"
    },

    ["compensator"] = {
        label = 'Kompensator'
    },

    ["heavy_barrel"] = {
        label = 'Heavy Barrel'
    },

    ["heavy_barrel"] = {
        label = 'Heavy Barrel'
    },

    ["heavy_barrel"] = {
        label = 'Heavy Barrel'
    },

    -- cop shit

    ["cop_suppressor"] = {
        label = 'Schalldämpfer',
        jobs = {
            'police',
            'sheriff',
            'fib'
        }
    },

    ["cop_clip_extended"] = {
        label = 'Erweitertes Magazin',
        jobs = {
            'police',
            'sheriff',
            'fib'
        }
    },

    ["cop_flashlight"] = {
        label = 'Taschenlampe',
        jobs = {
            'police',
            'sheriff',
            'fib'
        }
    },

    ["cop_compensator"] = {
        label = 'Kompensator',
        jobs = {
            'police',
            'sheriff',
            'fib'
        }
    },

    ["cop_grip"] = {
        label = 'Griff',
        jobs = {
            'police',
            'sheriff',
            'fib'
        }
    },

    ["cop_scope"] = {
        label = 'Zielfernrohr',
        jobs = {
            'police',
            'sheriff',
            'fib'
        }
    },

    ["cop_mounted_scope"] = {
        label = 'Holo Visier',
        jobs = {
            'police',
            'sheriff',
            'fib'
        }
    }
}

Config_Clsguide.weaponHashes = {
    [`weapon_machete`] = "weapon_machete",
    [`weapon_poolcue`] = "weapon_poolcue",
    [`weapon_pistol_mk2`] = 'weapon_pistol_mk2',
    [`weapon_sniperrifle`] = "weapon_sniperrifle",
    [`weapon_minigun`] = "weapon_minigun",
    [`weapon_heavysniper`] = "weapon_heavysniper",
    [`weapon_vintagepistol`] = "weapon_vintagepistol",
    [`weapon_ball`] = "weapon_ball",
    [`weapon_railgun`] = "weapon_railgun",
    [`weapon_pipebomb`] = "weapon_pipebomb",
    [`weapon_gusenberg`] = "weapon_gusenberg",
    [`weapon_hammer`] = "weapon_hammer",
    [`weapon_advancedrifle`] = "weapon_advancedrifle",
    [`weapon_golfclub`] = "weapon_golfclub",
    [`weapon_marksmanrifle`] = "weapon_marksmanrifle",
    [`weapon_snspistol`] = "weapon_snspistol",
    [`weapon_autoshotgun`] = "weapon_autoshotgun",
    [`weapon_petrolcan`] = "weapon_petrolcan",
    [`weapon_knuckle`] = "weapon_knuckle",
    [`weapon_pistol50`] = "weapon_pistol50",
    [`weapon_switchblade`] = "weapon_switchblade",
    [`weapon_microsmg`] = "weapon_microsmg",
    [`weapon_flashlight`] = "weapon_flashlight",
    [`weapon_specialcarbine`] = "weapon_specialcarbine",
    [`weapon_bat`] = "weapon_bat",
    [`weapon_battleaxe`] = "weapon_battleaxe",
    [`weapon_flare`] = "weapon_flare",
    [`weapon_sawnoffshotgun`] = "weapon_sawnoffshotgun",
    [`weapon_smg`] = "weapon_smg",
    [`weapon_wrench`] = "weapon_wrench",
    [`weapon_dbshotgun`] = "weapon_dbshotgun",
    [`weapon_flaregun`] = "weapon_flaregun",
    [`weapon_carbinerifle`] = "weapon_carbinerifle",
    [`weapon_assaultshotgun`] = "weapon_assaultshotgun",
    [`weapon_assaultsmg`] = "weapon_assaultsmg",
    [`weapon_knife`] = "weapon_knife",
    [`weapon_snowball`] = "weapon_snowball",
    [`weapon_crowbar`] = "weapon_crowbar",
    [`weapon_heavypistol`] = "weapon_heavypistol",
    [`weapon_machinepistol`] = "weapon_machinepistol",
    [`weapon_bzgas`] = "weapon_bzgas",
    [`weapon_stungun`] = "weapon_stungun",
    [`weapon_combatmg`] = "weapon_combatmg",
    [`weapon_musket`] = "weapon_musket",
    [`weapon_appistol`] = "weapon_appistol",
    [`weapon_heavyshotgun`] = "weapon_heavyshotgun",
    [`weapon_nightstick`] = "weapon_nightstick",
    [`weapon_bullpuprifle`] = "weapon_bullpuprifle",
    [`weapon_doubleaction`] = "weapon_doubleaction",
    [`weapon_hatchet`] = "weapon_hatchet",
    [`weapon_dagger`] = "weapon_dagger",
    [`weapon_minismg`] = "weapon_minismg",
    [`weapon_rpg`] = "weapon_rpg",
    [`weapon_pumpshotgun`] = "weapon_pumpshotgun",
    [`weapon_bullpupshotgun`] = "weapon_bullpupshotgun",
    [`weapon_compactlauncher`] = "weapon_compactlauncher",
    [`weapon_assaultrifle`] = "weapon_assaultrifle",
    [`weapon_compactrifle`] = "weapon_compactrifle",
    [`weapon_revolver`] = "weapon_revolver",
    [`weapon_marksmanpistol`] = "weapon_marksmanpistol",
    [`weapon_combatpistol`] = "weapon_combatpistol",
    [`weapon_mg`] = "weapon_mg",
    [`weapon_firework`] = "weapon_firework",
    [`weapon_proxmine`] = "weapon_proxmine",
    [`weapon_hominglauncher`] = "weapon_hominglauncher",
    [`gadget_parachute`] = "gadget_parachute",
    [`weapon_bottle`] = "weapon_bottle",
    [`weapon_combatpdw`] = "weapon_combatpdw",
    [`weapon_molotov`] = "weapon_molotov",
    [`weapon_smokegrenade`] = "weapon_smokegrenade",
    [`weapon_stickybomb`] = "weapon_stickybomb",
    [`weapon_grenade`] = "weapon_grenade",
    [`weapon_grenadelauncher`] = "weapon_grenadelauncher",
    [`weapon_pistol`] = "weapon_pistol",
    [`weapon_fireextinguisher`] = "weapon_fireextinguisher",
    [`weapon_snspistol_mk2`] = "weapon_snspistol_mk2",
    [`weapon_revolver_mk2`] = "weapon_revolver_mk2",
    [`weapon_smg_mk2`] = "weapon_smg_mk2",
    [`weapon_combatmg_mk2`] = "weapon_combatmg_mk2",
    [`weapon_pumpshotgun_mk2`] = "weapon_pumpshotgun_mk2"
}

Config_Clsguide.Locations = {
    [1] = {
        coords = vector3(111.2044, -775.6351, 31.43665),
        cops = 5,
        time = 300,
        account = 'black_money',
        money = math.random(1700, 2500)
    },

    [2] = {
        coords = vector3(-866.5581, -187.7576, 37.8339),
        cops = 5,
        time = 300,
        account = 'black_money',
        money = math.random(1700, 2500)
    },

    [3] = {
        coords = vector3(158.5892, 234.1335, 106.6277),
        cops = 5,
        time = 300,
        account = 'black_money',
        money = math.random(1700, 2500)
    },

    [4] = {
        coords = vector3(-57.6866, -92.6429, 57.7798),
        cops = 5,
        time = 300,
        account = 'black_money',
        money = math.random(1700, 2500)
    },

    [5] = {
        coords = vector3(402.7280, -1625.2699, 29.2919),
        cops = 5,
        time = 300,
        account = 'black_money',
        money = math.random(1700, 2500)
    },

    [6] = {
        coords = vector3(174.0982, 6637.8042, 31.5730),
        cops = 5,
        time = 300,
        account = 'black_money',
        money = math.random(1700, 2500)
    }
}

Config_Clsguide.AdvancedItems = {
}

Config_Clsguide.RestaurantBlips = {
    ['elpresidente'] = {
        coords = vector3(126.7, -1277.86, 29.32),
        text = 'Der MooreClub hat nun geöffnet!'
    },
    ['burgershot'] = {
        coords = vector3(-560.28, 285.91, 82.18),
        text = 'BurgerShot hat nun geöffnet!'
    },
    ['puffpuff'] = {
        coords = vector3(-1551.96, -436.24, 40.52),
        text = 'PuffPuff hat nun geöffnet!'
    },
    ['mechanic'] = {
        coords = vector3(874.7047, -2097.0393, 30.4797),
        text = 'Bennys hat nun geöffnet!'
    },
    ['cafeistanbul'] = {
        coords = vector3(417.6293, -1498.0660, 30.1554),
        text = 'Das Cafe Istanbul hat nun geöffnet!'
    }
}