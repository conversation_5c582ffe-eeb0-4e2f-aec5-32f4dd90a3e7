local houses = {}
local motelSafes = {}

local houseInventory = {}
local houseIdToDim = {}

local function createHouse(dimension, identifier, inventory)
    local self = {}
    local timeoutCallbacks = {}

    self.dimension = dimension
    self.identifier = identifier
    self.inventory = json.decode(inventory) or {}

    function self.setPin(pin)
        self.pin = pin
        self.save()
    end
    
    function self.setItems(items)
        self.inventory.items = items
        self.save()
    end

    function self.setWeapons(weapons)
        self.inventory.weapons = weapons
        self.save()
    end

    function self.setAccounts(accounts)
        self.inventory.accounts = accounts
        self.save()
    end

    function self.save()
        for i = 1, #timeoutCallbacks, 1 do
            ESX.ClearTimeout(timeoutCallbacks[i])
            timeoutCallbacks[i] = nil
        end

        local timeoutCallback = ESX.SetTimeout(1000 * 60 * 3, function()
            MySQL.update('UPDATE hex_properties SET inventory = ? WHERE dimension = ? AND owner = ?', {
                json.encode(self.inventory),
                self.dimension,
                self.identifier
            })
        end)

        timeoutCallbacks[#timeoutCallbacks + 1] = timeoutCallback
    end

    return self
end

MySQL.ready(function()
    local result = MySQL.query.await('SELECT * FROM hex_properties')

    if #result ~= 0 then
        for k, v in pairs(result) do
            if houses[v.dimension] == nil then
                houses[v.dimension] = {}
                houses[v.dimension].x = v.x
                houses[v.dimension].y = v.y
                houses[v.dimension].z = v.z
                houses[v.dimension].h = v.h
                houses[v.dimension].dimension = v.dimension
                houses[v.dimension].house = v.house
                houses[v.dimension].price = v.price * Config_House.Price.buyMultiplier
                houses[v.dimension].open = v.open
                houses[v.dimension].owner = v.owner
                houses[v.dimension].ownername = v.ownername
                houses[v.dimension].garageninfo = json.decode(v.garageninfo)

                houseInventory[v.dimension] = createHouse(v.dimension, v.owner, v.inventory)
                houseIdToDim[v.owner] = v.dimension
            end
        end

        Citizen.Wait(1000)

        TriggerClientEvent('cc_core:house:reload', -1, houses)
    end

    MySQL.query('SELECT * FROM motel_safes', {}, function(result)
        if result ~= 0 then
            for _, data in pairs(result) do
                if data['identifier'] and data['data'] then
                    motelSafes[data['identifier']] = json.decode(data['data'])
                end
            end
        end
        print('^2Created^0:', #result, 'Motel ^3Safes^0!')
    end)
end)

local function GetHouseIndex(id)
    for index, data in pairs(houses) do
        if id == data.owner then
            return index
        end
    end
    return false
end

RegisterServerEvent('cc_core:house:joinHouse')
AddEventHandler('cc_core:house:joinHouse', function(house, dimension)
    local playerId = source
    local ownHouse = false

    print('cc_core:house:joinHouse', house, dimension, ESX.DumpTable(house))

    if GetPlayerRoutingBucket(playerId) ~= 0 then
        return
    end

    if ESX.GetPlayerNeu(playerId) then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [FF-72]", false, "Afrika")
        return
    end

    if houses[dimension] ~= nil then
        local owner, open = houses[dimension].owner, houses[dimension].open

        if owner == ESX.GetPlayerIdentifier(playerId) then
            ownHouse = true
        end

        if owner == ESX.GetPlayerMarried(playerId) then
            ownHouse = true
        end

        if open then
            SetPlayerRoutingBucket(playerId, dimension)
            TriggerClientEvent('cc_core:house:joinHouse', playerId, house, dimension, owner, ownHouse)
            ESX.SetPlayerHouse(playerId, house)
            ESX.SetPlayerDim(playerId, dimension)
        else
            if ownHouse then
                SetPlayerRoutingBucket(playerId, dimension)
                TriggerClientEvent('cc_core:house:joinHouse', playerId, house, dimension, owner, ownHouse)
                ESX.SetPlayerHouse(playerId, house)
                ESX.SetPlayerDim(playerId, dimension)
            else
                Notify(playerId, 'Haus', 'Dieses Haus ist geschlossen', 'info')
                TriggerClientEvent('cc_core:house:canJoin', playerId)
            end
        end
    end
end)

RegisterServerEvent('cc_core:house:leaveHouse')
AddEventHandler('cc_core:house:leaveHouse', function(house, dimension)
    local playerId = source

    if ESX.GetPlayerNeu(playerId) then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [FF-72]", false, "Afrika")
        return
    end

    if houses[dimension] ~= nil then
        local x, y, z, h = houses[dimension].x, houses[dimension].y, houses[dimension].z, houses[dimension].h
        SetPlayerRoutingBucket(playerId, 0)
        TriggerClientEvent('cc_core:house:leaveHouse', playerId, house, dimension, x, y, z, h)
        ESX.SetPlayerHouse(playerId, 0)
        ESX.SetPlayerDim(playerId, 0)
    end
end)

RegisterServerEvent('cc_core:house:buyHouse')
AddEventHandler('cc_core:house:buyHouse', function(house, dimension)
    local playerId = source
    local identifier = ESX.GetPlayerIdentifier(playerId)

    for k, v in pairs(houses) do
        if identifier == v.owner then
            Notify(playerId, 'Haus', 'Du kannst nur ein Haus besitzen', 'info')
            return
        end
    end

    if houses[dimension] ~= nil then
        local price, owner = houses[dimension].price, houses[dimension].owner

        if owner == '0' then
            if ESX.GetPlayerMoney(playerId) >= price then
                houses[dimension].owner = identifier
                houses[dimension].ownername = ESX.GetPlayerRPName(playerId)

                ESX.RemovePlayerMoney(playerId, price, GetCurrentResourceName())

                MySQL.update('UPDATE hex_properties SET owner = @owner, ownername = @ownername WHERE dimension = @dimension', {
                    ['@owner'] = houses[dimension].owner,
                    ['@ownername'] = houses[dimension].ownername,
                    ['@dimension'] = dimension
                }, function()
                    TriggerClientEvent('cc_core:house:refreshHouse', -1, 'owner', dimension, houses[dimension].open, houses[dimension].owner, houses[dimension].ownername, identifier)
                    Notify(playerId, 'Haus', 'Herzlichen Glückwunsch, du bist nun Hausbesitzer', 'success')

                    -- discord log
                end)
            else
                Notify(playerId, 'Haus', 'Du hast nicht genug Geld dabei', 'info')
            end
        else
            Notify(playerId, 'Haus', 'Dieses Haus gehört schon jemanden', 'error')
        end
    end    
end)

RegisterServerEvent('cc_core:house:sellHouse')
AddEventHandler('cc_core:house:sellHouse', function(dimension)
    local playerId = source
    local identifier = ESX.GetPlayerIdentifier(playerId)

    if houses[dimension] ~= nil then
        if houses[dimension].owner == identifier then
            local price = houses[dimension].price * Config_House.Price.sellPrice

            houses[dimension].owner = '0'
            houses[dimension].ownername = '0'
            houses[dimension].open = true

            MySQL.update('UPDATE hex_properties SET owner = @owner, ownername = @ownername, open = @open WHERE dimension = @dimension', {
                ['@owner'] = '0',
                ['@ownername'] = '0',
                ['@open'] = true,
                ['@dimension'] = dimension
            }, function()
                ESX.AddPlayerMoney(playerId, price, GetCurrentResourceName())

                TriggerClientEvent('cc_core:house:refreshHouse', -1, 'owner', dimension, houses[dimension].open, houses[dimension].owner, houses[dimension].ownername, identifier)
                Notify(playerId, 'Haus', 'Du hast dein Haus an den Staat verkauft', 'success')

                -- discord log
            end)
        else
            Notify(playerId, 'Haus', 'Du kannst das Haus nicht verkaufen, da es dir nicht gehört', 'info')
        end
    end
end)

RegisterServerEvent('cc_core:house:openClose')
AddEventHandler('cc_core:house:openClose', function(dimension)
    local playerId = source

    if houses[dimension] ~= nil then
        if houses[dimension].owner == ESX.GetPlayerIdentifier(playerId) then
            if houses[dimension].open then
                MySQL.update('UPDATE hex_properties SET open = @open WHERE dimension = @dimension', {
                    ['@open'] = false,
                    ['@dimension'] = dimension
                }, function()
                    houses[dimension].open = false
                    Notify(playerId, 'Haus', 'Haus abgeschlossen', 'info')
                    TriggerClientEvent('cc_core:house:refreshHouse', -1, 'door', dimension, false)
                end)
            else
                MySQL.update('UPDATE hex_properties SET open = @open WHERE dimension = @dimension', {
                    ['@open'] = true,
                    ['@dimension'] = dimension
                }, function()
                    houses[dimension].open = true
                    Notify(playerId, 'Haus', 'Haus aufgeschlossen', 'info')
                    TriggerClientEvent('cc_core:house:refreshHouse', -1, 'door', dimension, true)
                end)
            end
        end
    end
end)

local BlacklistedItems = {
    'ephidrinkonzentrat',
    'toilettenreiniger'
}

local ViskyItems = {
	--'heroin',
	--'ecstasy',
	--'speed'
    'test'
}

local max2000 = {
	'koks2',
	'weed2',
	'tilidin2'
}

function Is2000(i)
	for _, ii in pairs(max2000) do
		if i == ii then
			return true
		end
	end
	return false
end

function IsVisky(i)
	for _, ii in pairs(ViskyItems) do
		if i == ii then
			return true
		end
	end
	return false
end

-- stock shit

local CustomWeight = {

}

local function getItemWeight(item)
    local itemWeight = 0

    if item ~= nil then
        itemWeight = 1

        if CustomWeight[item] ~= nil then
            itemWeight = CustomWeight[item]
        end
    end

    return itemWeight
end

local function getInventoryWeight(inventory)
    local weight = 0
    local itemWeight = 0

    if inventory ~= nil then
        for k, v in pairs(inventory) do
            itemWeight = 1

            if CustomWeight[k] ~= nil then
                itemWeight = CustomWeight[k]
            end

            weight = weight + (itemWeight * (v or 1))
        end
    end

    return weight
end

local function getWeaponWeight(weapons)
    local weight = 0

    for k, v in pairs(weapons) do
        for k1, v1 in pairs(v) do
            weight = weight + 1
        end
    end

    return weight
end

local function getTotalWarehouseWeight(id)
    local total = 0

    if houseInventory[id] ~= nil then
        local weight_items = getInventoryWeight(houseInventory[id].inventory.items or {})
        local weight_weapons = getWeaponWeight(houseInventory[id].inventory.weapons or {})
        
        total = weight_weapons + weight_items
    end

    return total
end

RegisterServerEvent('cc_core:house:addArmoryItems')
AddEventHandler('cc_core:house:addArmoryItems', function(itemName, count)
    local playerId = source
    local sourceItem = ESX.GetPlayerInventoryItem(playerId, itemName)

    if ESX.IsBlacklistedItem(itemName) then
        return
    end

	if exports['cc_core']:isRestart() then
        Notify(playerId, 'Information', 'Du kannst 5 Minuten vor der Sonnenwende nix einlagern!', 'info')
		return
    end

    if not IsVisky(itemName) then
        local dim = houseIdToDim[ESX.GetPlayerIdentifier(playerId)]

        if dim then            
            if sourceItem.count >= count and count > 0 then
                local found = false
                local items = houseInventory[dim].inventory.items

                if items == nil then
                    items = {}
                end

                if items[itemName] ~= nil then
                    local oldCount = items[itemName]

                    items[itemName] = oldCount + count
                else
                    items[itemName] = count
                end

                ESX.RemovePlayerInventoryItem(playerId, itemName, count)
                houseInventory[dim].setItems(items)
                Notify(playerId, 'Haus', 'Eingelagert: ' .. count .. 'x ' .. itemName, 'info')
                exports['cc_core']:log(playerId, 'Item rein - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat ' .. count .. 'x ' .. itemName .. ' in sein Haus eingelagert', 'https://canary.discord.com/api/webhooks/1378340839415353344/zV68Ajbz3ffnGrsfhTZlCx7j_vFAD0KxLTVB5RweL5NxfwXCfUXAzJuQDK3Z9sHN72rz')
            end
        end
    else
        Notify(playerId, 'Information', 'Dieses Item geht hier nicht rein!', 'error')
    end
end)

RegisterServerEvent('cc_core:house:removeArmoryItems')
AddEventHandler('cc_core:house:removeArmoryItems', function(itemName, count)
    local playerId = source
    local sourceItem = ESX.GetPlayerInventoryItem(playerId, itemName)

	if exports['cc_core']:isRestart() then
        Notify(playerId, 'Information', 'Du kannst 5 Minuten vor der Sonnenwende nix auslagern!', 'info')
		return
    end

    local dim = houseIdToDim[ESX.GetPlayerIdentifier(playerId)]

    if dim then
        if ESX.PlayerCanCarryItem(playerId, itemName, count) then
            local items = houseInventory[dim].inventory.items
            local itemCount = items[itemName]

            print(itemCount)

            if itemCount ~= nil then
                if itemCount >= count and count > 0 then
                    ESX.AddPlayerInventoryItem(playerId, itemName, count)

                    if itemCount - count == 0 then
                        items[itemName] = nil
                    else
                        items[itemName] = itemCount - count
                    end

                    houseInventory[dim].setItems(items)
                    Notify(playerId, 'Haus', 'Ausgelagert: ' .. count .. 'x ' .. itemName, 'info')
                    exports['cc_core']:log(playerId, 'Item rein - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat ' .. count .. 'x ' .. itemName .. ' aus sein Haus ausgelagert', 'https://canary.discord.com/api/webhooks/1378340839415353344/zV68Ajbz3ffnGrsfhTZlCx7j_vFAD0KxLTVB5RweL5NxfwXCfUXAzJuQDK3Z9sHN72rz')
                end
            end
        end
    end
end)

RegisterServerEvent('cc_core:house:addArmoryWeapon')
AddEventHandler('cc_core:house:addArmoryWeapon', function(weaponName)
    local playerId = source

	if exports['cc_core']:isRestart() then
        Notify(playerId, 'Information', 'Du kannst 5 Minuten vor der Sonnenwende nix einlagern!', 'info')
		return
    end

	if ESX.HasPlayerWeapon(playerId, weaponName) then
        local dim = houseIdToDim[ESX.GetPlayerIdentifier(playerId)]

        if dim then
            local weaponNum, weapon = ESX.GetPlayerWeapon(playerId, weaponName)
            ESX.RemovePlayerWeapon(playerId, weaponName)
            local weapons = houseInventory[dim].inventory.weapons

            if weapons == nil then
                weapons = {}
            end

            if weapons[weaponName] then
                weapons[weaponName][#weapons[weaponName] + 1] = weapon.ammo
            else
                weapons[weaponName] = {
                    weapon.ammo
                }
            end

            ESX.RemovePlayerWeapon(playerId, weaponName, weapon.ammo)
            houseInventory[dim].setWeapons(weapons)
            exports['cc_core']:log(playerId, 'Waffen rein - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat 1x ' .. weaponName .. ' in sein Haus eingelagert', 'https://canary.discord.com/api/webhooks/1378340839415353344/zV68Ajbz3ffnGrsfhTZlCx7j_vFAD0KxLTVB5RweL5NxfwXCfUXAzJuQDK3Z9sHN72rz')
        end
    end
end)

RegisterServerEvent('cc_core:house:removeArmoryWeapon')
AddEventHandler('cc_core:house:removeArmoryWeapon', function(weaponName, wepaonIndex)
    local playerId = source

	if exports['cc_core']:isRestart() then
        Notify(playerId, 'Information', 'Du kannst 5 Minuten vor der Sonnenwende nix einlagern!', 'info')
		return
    end

    if not ESX.HasPlayerWeapon(playerId, weaponName) then
        local dim = houseIdToDim[ESX.GetPlayerIdentifier(playerId)]

        if dim then
            local weapons = houseInventory[dim].inventory.weapons

            if weapons[weaponName] then
                if weapons[weaponName][wepaonIndex] then
                    ESX.AddPlayerWeapon(playerId, weaponName, weapons[weaponName][wepaonIndex])

                    table.remove(weapons[weaponName], wepaonIndex)

                    houseInventory[dim].setWeapons(weapons)
                    exports['cc_core']:log(playerId, 'Waffen rein - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat 1x ' .. weaponName .. ' aus sein Haus ausgelagert', 'https://canary.discord.com/api/webhooks/1378340839415353344/zV68Ajbz3ffnGrsfhTZlCx7j_vFAD0KxLTVB5RweL5NxfwXCfUXAzJuQDK3Z9sHN72rz')
                end
            end
        end
    end
end)

ESX.RegisterServerCallback('cc_core:house:getArmoryItems', function(source, cb)
    local dim = houseIdToDim[ESX.GetPlayerIdentifier(source)]

    if dim then
        cb(houseInventory[dim].inventory.items or {})
    else
        cb({})
    end
end)

ESX.RegisterServerCallback('cc_core:house:getArmoryWeapons', function(source, cb)
    local dim = houseIdToDim[ESX.GetPlayerIdentifier(source)]

    if dim then
        cb(houseInventory[dim].inventory.weapons or {})
    else
        cb({})
    end
end)

ESX.RegisterServerCallback('cc_core:house:getHouses', function(source, cb)
    cb(houses)
end)

RegisterCommand('createHouse', function(source, args, rawCommand)
    if ESX.GetPlayerGroup(source) == 'projektleitung' then
        TriggerClientEvent('cc_core:house:createHouse', source)
    end
end)

RegisterCommand('createGarage', function(source, args, rawCommand)
    if ESX.GetPlayerGroup(source) == 'projektleitung' then
        TriggerClientEvent('cc_core:house:createGarage', source)
    end
end)

RegisterNetEvent('cc_core:house:createHouse')
AddEventHandler('cc_core:house:createHouse', function(x, y, z, h, houseType, housePrice)
    if ESX.GetPlayerGroup(source) == 'projektleitung' then
        MySQL.update('INSERT INTO hex_properties (x, y, z, h, house, price) VALUES (@x, @y, @z, @h, @house, @price)', {
            ['@x'] = x,
            ['@y'] = y,
            ['@z'] = z,
            ['@h'] = h,
            ['@house'] = houseType,
            ['@price'] = housePrice
        })
    end
end)

RegisterNetEvent('cc_core:house:createGarage')
AddEventHandler('cc_core:house:createGarage', function(x, y, z, h, x1, y1, z1, h1, dimension)
    if ESX.GetPlayerGroup(source) == 'projektleitung' then
        local garageninfo = '{"npc": {"x": ' .. x .. ', "y": ' .. y .. ', "z": ' .. z .. ', "^h": ' .. h .. '}, "spawnPoint": {"x": ' .. x1 .. ', "y": ' .. y1 .. ', "z": ' .. z1 .. ', "h": ' .. h1 .. '}}'
        
        MySQL.update('UPDATE hex_properties SET garageninfo = @garageninfo WHERE dimension = @dimension', {
            ['@garageninfo'] = garageninfo,
            ['@dimension'] = dimension
        })
    end
end)

--clientcode
houseCode = [[
print('house module loaded')
local dimension, house, owner, ownHouse, price, streetName, ownerName, notOpen, notOpen2, cantLeave = nil, nil, nil, false, 0, nil, nil, false, false, false
local houses, blips = {}, {}

local show, show2 = false, false

local function format(coords)
    return tonumber(string.format('%.2f', coords))
end

local isInHouse = false

local interiorBedSpots = {
    [1] = vector4(349.7360, -996.1333, -99.5399, 2.0599),
    [2] = vector4(262.4727, -1004.3268, -99.2661, 180.3416),
    [3] = vector4(1277.3170, -1713.8000, 54.5111, 114.3825),
    [4] = vector4(-163.4006, 483.5598, 133.5500, 189.7497),
    [5] = vector4(987.5938, 72.7081, 115.7500, 239.6280),
    [6] = vector4(839.0050, -3245.6057, -98.5000, 16.1013),
}

local function Draw3DText(x, y, z, text, scale)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)

    if onScreen then
        SetTextScale(scale, scale)
        SetTextFont(0)
        SetTextProportional(1)
        SetTextEntry("STRING")
        SetTextCentre(true)
        SetTextColour(255, 255, 255, 255)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

local function CreateBlips()
    Citizen.Wait(1500)
    
    for k,v in pairs(houses) do
        v.x = v.x + 0.0
        v.y = v.y + 0.0
        v.z = v.z + 0.0

        if v.owner == "0" or v.owner == 0 then
            local blip = AddBlipForCoord(v.x, v.y, v.z)
            SetBlipSprite(blip, 374)
            SetBlipScale(blip, 0.4)
            SetBlipAsShortRange(blip, true)
            SetBlipColour(blip, 46)
            BeginTextCommandSetBlipName('STRING')
            AddTextComponentString('Haus')
            EndTextCommandSetBlipName(blip)
            table.insert(blips, {
                blip = blip,
                dimension = v.dimension
            })
        end

        if v.owner == ESX.PlayerData.identifier then
            local blip = AddBlipForCoord(v.x, v.y, v.z)
            SetBlipSprite(blip, 40)
            SetBlipScale(blip, 0.8)
            SetBlipAsShortRange(blip, true)
            SetBlipColour(blip, 2)
            BeginTextCommandSetBlipName('STRING')
            AddTextComponentString('Eigenes Haus')
            EndTextCommandSetBlipName(blip)
            table.insert(blips, {
                blip = blip,
                dimension = v.dimension
            })
        end
    end
end

local function openClothesMenu()
    local clothings = exports['cc_core']:getClothings()
    local elements = {}

    for k, v in pairs(clothings) do
        elements[#elements + 1] = {
            label = v.label,
            value = k
        }
    end

    if #elements ~= 0 then
        isInHouseUI = true

        SetNuiFocus(true, true)
        SendNUIMessage({
            script = 'house',
            action = 'showClothing',
            open = open,
            clothings = elements
        })
    else
        Notify('Haus - System', 'Du hast keine Kleidungen!', 'info')
    end
end

local function openGetWeaponMenu()
    print('Open House Menu()')
    ESX.TriggerServerCallback('cc_core:house:getArmoryWeapons', function(weapons)
		local status, error = pcall(function()
			local elements = {}

			for k, v in pairs(weapons) do
				local weapon = ESX.GetWeaponLabel(k)
				local ammos = {}

                if #v > 0 then                    
                    for k1, v1 in pairs(v) do
                        table.insert(ammos, {
                            index = k1,
                            label = v1,
                            ammo = v1
                        })
                    end

                    if weapon ~= nil then
                        table.insert(elements, {
                            name = k,
                            label = weapon .. ' - ' .. #v,
                            ammos = ammos
                        }) 
                    end
                end
			end
	
			ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'armory_get_weapon', {
				title = 'Lager',
				align = 'top-left',
				elements = elements
			}, function(data, menu)
				menu.close()
	
				local elements = data.current.ammos
	
				ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'armory_get_weapon_2', {
					title = 'Lager',
					align = 'top-left',
					elements = elements
				}, function(data2, menu)
					menu.close()
                    print(data.current.name, data2.current.index)
                    TriggerServerEvent('cc_core:house:removeArmoryWeapon', data.current.name, data2.current.index)
				end, function(data,menu)
					menu.close()
				end)
	
			end, function(data,menu)
				menu.close()
			end)
		end)

		if not status then
			print(error)
		end
    end)
end

local function openPutWeaponMenu()
    local elements = {}
    local ped = PlayerPedId()
    local weaponList = ESX.GetWeaponList()
    

    for k, v in pairs(weaponList) do
        local weaponHash = GetHashKey(v.name)

        if HasPedGotWeapon(ped, weaponHash, false) and v.name ~= "WEAPON_UNARMED" then
            table.insert(elements, {
                label = v.label,
                value = v.name
            })
        end
    end
    ESX.UI.Menu.Open("default", GetCurrentResourceName(), "armory_put_weapon", {
        title = "Inventar",
        align = "top-left",
        elements = elements
    }, function(data, menu)
        menu.close()

        TriggerServerEvent('cc_core:house:addArmoryWeapon', data.current.value)
    end, function(data, menu)
        menu.close()
    end)
end

local function openGetStocksMenu()
    ESX.TriggerServerCallback("cc_core:house:getArmoryItems", function(items)
        local elements = {}

        for k, v in pairs(items) do
            if v > 0 and ESX.GetItemLabel(k) ~= nil then
                table.insert(elements, {
                    label = "x" .. v .. " " .. ESX.GetItemLabel(k),
                    value = k
                })
            end
        end
        ESX.UI.Menu.Open("default", GetCurrentResourceName(), "stocks_menu", {
            title = "Lager",
            align = "top-left",
            elements = elements
        }, function(data, menu)
            local itemName = data.current.value

            ESX.UI.Menu.Open("dialog", GetCurrentResourceName(), "stocks_menu_get_item_count", {
                title = "Menge"
            }, function(data, menu2)
                local count = tonumber(data.value)
                
                menu.close()
                menu2.close()

                if not count then
                    Notify('Haus', 'Ungültige Menge', 'info')
                else
                    TriggerServerEvent("cc_core:house:removeArmoryItems", itemName, count)
                end
            end, function(data, menu)
                menu.close()
            end)
        end, function(data, menu)
            menu.close()
        end)
    end)
end

local function openPutStocksMenu()
    local elements = {}

    for k, v in pairs(ESX.GetPlayerData().inventory) do
        if v.count > 0 then
            table.insert(elements, {
                label = v.label .. " x" .. v.count,
                value = v.name
            })
        end
    end
    ESX.UI.Menu.Open("default", GetCurrentResourceName(), "stocks_menu", {
        title = "Inventar",
        align = "top-left",
        elements = elements
    }, function(data, menu)
        local itemName = data.current.value

        ESX.UI.Menu.Open("dialog", GetCurrentResourceName(), "stocks_menu_put_item_count", {
            title = "Menge"
        }, function(data, menu2)
            local count = tonumber(data.value)

            menu.close()
            menu2.close()

            if not count then
                Notify('Haus', 'Ungültige Menge', 'info')
            else
                TriggerServerEvent("cc_core:house:addArmoryItems", itemName, count)
            end
        end, function(data, menu2)
            menu2.close()
        end)

    end, function(data, menu)
        menu.close()
    end)
end

local function openArmoryMenu()
    local playerPed = PlayerPedId()

    local elements = {
        { label = "[Waffe] Lagern", value = "put_weapon" },
        { label = "[Gegenstand] Lagern", value = "put_stock" },
        { label = "[Waffe] Auslagern", value = "get_weapon" },
        { label = "[Gegenstand] Auslagern", value = "get_stock" }
    }

    ESX.UI.Menu.Open("default", GetCurrentResourceName(), "armory", {
        title = "Waffenkammer",
        align = "top-left",
        elements = elements
    }, function(data, menu)
        if data.current.value == "get_weapon" then
            openGetWeaponMenu()
        elseif data.current.value == "put_weapon" then
            openPutWeaponMenu()
        elseif data.current.value == "put_stock" then
            openPutStocksMenu()
        elseif data.current.value == "get_stock" then
            openGetStocksMenu()
        end
    end, function(data, menu)
        menu.close()
    end)
end

local function openHouseMenu()
    ESX.UI.Menu.Open("default", GetCurrentResourceName(), "open_house_actions", {
        title = "House",
        align = "top-left",
        elements = {
            {label = "Haus Menü", value = "house"},
            {label = "Armory Menü", value = "armory"}
        }
    }, function(data, menu)
        if data.current.value == "house" then
            ESX.UI.Menu.Open("default", GetCurrentResourceName(), "hex_house", {
                title = "House",
                align = "top-left",
                elements = {
                    { label = "Haus Öffnen / Schließen", value = "houseopen" },
                }
            }, function(data, menu)
                if data.current.value == "houseopen" then
                    TriggerServerEvent("cc_core:house:openClose", dimension)
                end
            end, function(data, menu)
                menu.close()
            end)
        elseif data.current.value == "armory" then
            openArmoryMenu()
        end
    end, function(data, menu)
        menu.close()
    end) 
end

RegisterNUICallback('house/loadOutfit', function(data, cb)
    TriggerEvent('skinchanger:getSkin', function(skin)
        local clothings = exports['cc_core']:getClothings()
        local clothes = clothings[tonumber(data.value)]

        TriggerEvent('skinchanger:loadClothes', skin, json.decode(clothes.skin))
        TriggerEvent('cc_core:skin:setLastSkin', skin)

        TriggerEvent('skinchanger:getSkin', function(skin)
            TriggerServerEvent('cc_core:skin:save', skin)
        end)
    end)
end)

Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(500)
    end
    
    ESX.TriggerServerCallback("cc_core:house:getHouses", function(house)
        houses = house
        CreateBlips()
    end)

    if ESX.PlayerData.house == 0 or ESX.PlayerData.dim == 0 then
        house = nil
        dimension = nil
    else
        house = ESX.PlayerData.house
        dimension = ESX.PlayerData.dim
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local message = ''

        for k, v in pairs(houses) do
            local houseCoords = vector3(v.x, v.y, v.z)
            local houseDistance = #(coords - houseCoords)

            if houseDistance <= 15.0 and house == nil and not cantLeave then
                letSleep = false

                if v.owner == '0' then
                    Draw3DText(v.x, v.y, v.z + 0.9, "Preis: " .. ESX.Math.GroupDigits(v.price) .. "$ \nOffen", 0.4)
                else
                    if v.open then
                        Draw3DText(v.x, v.y, v.z + 0.9, "Besitzer: " .. v.ownername .. '\nOffen', 0.4)
                    else
                        Draw3DText(v.x, v.y, v.z + 0.9, "Besitzer: " .. v.ownername .. '\nGeschlossen', 0.4)
                    end
                end

                DrawMarker(nil, v.x, v.y, v.z - 0.1, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, true, 2, true, false, false, false)
            end

            if houseDistance <= 1.0 then
                inRange = true
                message = 'Drücke E um das Haus zu betreten'

                if IsControlJustPressed(1, 38) then
                    price = ESX.Math.GroupDigits(v.price) .. '$'
                    streetName = GetStreetNameFromHashKey(GetStreetNameAtCoord(v.x, v.y, v.z))

                    if v.owner == "0" then
                        ownerName = "Keiner"
                    else
                        ownerName = v.ownername
                    end

                    if not cantLeave then
                        cantLeave = true
                        TriggerServerEvent('cc_core:house:joinHouse', v.house, v.dimension)
                    end
                end
            end

            if v.garageninfo then
                if v.owner == ESX.PlayerData.identifier then
                    local garagenNPCCoords = vector3(v.garageninfo.npc.x, v.garageninfo.npc.y, v.garageninfo.npc.z)
                    local garagenSpawnCoords = vector3(v.garageninfo.spawnPoint.x, v.garageninfo.spawnPoint.y, v.garageninfo.spawnPoint.z)

                    local garagenNPCDistance = #(coords - garagenNPCCoords)
                    local garagenSpawnDistance = #(coords - garagenSpawnCoords)

                    if garagenNPCDistance <= 15 then
                        letSleep = false
                        DrawMarker(nil, garagenNPCCoords.x, garagenNPCCoords.y, garagenNPCCoords.z - 0.1, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, true, 2, false, false, false, false)                            
                    end

                    if garagenNPCDistance <= 1 then
                        inRange = true
                        message = 'Drücke E um die Garage zu öffnen!'
                    
                        if IsControlJustPressed(0, 38) then
                            TriggerEvent('cc_core:garage:openGarage', v.garageninfo.spawnPoint, 'haus', false)
                        end
                    end
                end
            end
        end
        
        helpNotify(inRange, show2, message, function(bool)
            show2 = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local message = ''

        if house ~= nil then
            letSleep = false

            for k, v in pairs(Config_House.HouseTypes) do
                if k == house then
                    local object = GetClosestObjectOfType(1273.81600, -1720.69700, 54.92143, 1.0, GetHashKey('v_ilev_lester_doorfront'), false, false, false)
                    FreezeEntityPosition(object, true)

                    local leaveDistance = #(coords - vector3(v.joinleave.x, v.joinleave.y, v.joinleave.z))
                    local armoryDistance = #(coords - vector3(v.armorymenu.x, v.armorymenu.y, v.armorymenu.z))
                    local clothDistance = #(coords - vector3(v.clothesmenu.x, v.clothesmenu.y, v.clothesmenu.z))
                    local houseDistance = #(coords - vector3(v.housemenu.x, v.housemenu.y, v.housemenu.z))

                    if leaveDistance <= 100 then
                        if v.joinleave.marker_enabled then
                            DrawMarker(v.housemenu.marker_type, v.joinleave.x, v.joinleave.y, v.joinleave.z - 0.1, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 0.5, 0.5, 0.5, v.joinleave.marker_color.r, v.joinleave.marker_color.g, v.joinleave.marker_color.b, v.joinleave.marker_color.a, false, true, 2, false, false, false, false)                            
                        end
    
                        if v.housemenu.marker_enabled then
                            DrawMarker(v.housemenu.marker_type, v.housemenu.x, v.housemenu.y, v.housemenu.z - 0.1, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 0.5, 0.5, 0.5, v.housemenu.marker_color.r, v.housemenu.marker_color.g, v.housemenu.marker_color.b, v.housemenu.marker_color.a, false, true, 2, false, false, false, false)
                        end
                    end

                    if leaveDistance >= 300.0 then
                        house = nil
                        dimension = nil
                    end

                    if ownHouse then
                        if armoryDistance <= 100 then
                            if v.armorymenu.marker_enabled then
                                DrawMarker(nil, v.armorymenu.x, v.armorymenu.y, v.armorymenu.z - 0.1, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 0.5, 0.5, 0.5, v.armorymenu.marker_color.r, v.armorymenu.marker_color.g, v.armorymenu.marker_color.b, v.armorymenu.marker_color.a, false, true, 2, false, false, false, false)
                            end
                        
                            if v.clothesmenu then
                                DrawMarker(nil, v.clothesmenu.x, v.clothesmenu.y, v.clothesmenu.z - 0.1, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 0.5, 0.5, 0.5, v.clothesmenu.marker_color.r, v.clothesmenu.marker_color.g, v.clothesmenu.marker_color.b, v.clothesmenu.marker_color.a, false, true, 2, false, false, false, false)                                
                            end
                        end
    
                        if armoryDistance <= 1 then
                            inRange = true
                            message = 'Drücke E um das Hausmenü zu öffnen'
                            notOpen = true
    
                            if IsControlJustPressed(1, 38) then
                                openHouseMenu()
                            end
                        else
                            if not notOpen2 and notOpen then
                                notOpen = false
                                ESX.UI.Menu.CloseAll()
                            end
                        end
    
                        if clothDistance <= 1 then
                            inRange = true
                            message = 'Drücke E um das Klamottenmenü zu öffnen'
                            notOpen2 = true

                            if IsControlJustPressed(1, 38) then
                                openClothesMenu()
                            end
                        else
                            if not notOpen and notOpen2 then
                                notOpen2 = false
                                ESX.UI.Menu.CloseAll()
                            end
                        end
                    end

                    if houseDistance <= 1 then
                        inRange = true
                        message = 'Drücke E um das Hausmenü zu öffnen'

                        if IsControlJustPressed(1, 38) then
                            SetNuiFocus(true, true)
                            hideHud(true)

                            SendNUIMessage({
                                script = 'house',
                                action = 'show',
                                price = price,
                                streetName = streetName,
                                ownerName = ownerName,
                                ownHouse = ownHouse,
                                left_img = v.left_backgroundUrl,
                                right_img = v.right_backgroundUrl
                            })
                        end
                    end
    
                    if leaveDistance <= 1 then
                        inRange = true
                        message = 'Drücke E um das Haus zu verlassen'
    
                        if IsControlJustPressed(1, 38) then
                            if not cantLeave then
                                cantLeave = true
                                TriggerServerEvent("cc_core:house:leaveHouse", house, dimension)
                            end
                        end
                    end
                end
            end
        end

		if not show and inRange then
			exports['cc_core']:showHelpNotification(message)
			show = true
		elseif show and not inRange then
			exports['cc_core']:closeHelpNotification()
			show = false
		end

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

RegisterNetEvent('cc_core:house:refreshHouse')
AddEventHandler('cc_core:house:refreshHouse', function(type, dim, open, owner, ownername, oldOwner)
    for k, v in pairs(houses) do
        v.x = v.x + 0.0
        v.y = v.y + 0.0
        v.z = v.z + 0.0
        if v.dimension == dim then
            if type == 'owner' then
                v.owner = owner
                v.ownername = ownername
                v.open = open

                if owner ~= '0' then
                    if house ~= nil then
                        if owner == ESX.PlayerData.identifier then
                            ownHouse = true
                            ownerName = ownername

                            SendNUIMessage({
                                script = 'house',
                                action = 'setName',
                                ownerName = ownerName
                            })
                        end
                    end

                    for k1, v1 in pairs(blips) do
                        if v1.dimension == dim then
                            RemoveBlip(v1.blip)
                            table.remove(blips, k1)
                        end
                    end

                    if v.owner == ESX.PlayerData.identifier then
                        local blip = AddBlipForCoord(v.x, v.y, v.z)
                        SetBlipSprite(blip, 40)
                        SetBlipScale(blip, 0.8)
                        SetBlipAsShortRange(blip, true)
                        SetBlipColour(blip, 2)
                        BeginTextCommandSetBlipName('STRING')
                        AddTextComponentString('Eigenes Haus')
                        EndTextCommandSetBlipName(blip)
                        table.insert(blips, {
                            blip = blip,
                            dimension = v.dimension
                        })
                    end
                else
                    if house then
                        if oldOwner == ESX.PlayerData.identifier then
                            ownHouse = false
                            ownerName = 'Keiner'

                            SendNUIMessage({
                                script = 'house',
                                action = 'setName',
                                ownerName = ownerName
                            })
                        end
                    end

                    for k1, v1 in pairs(blips) do
                        if v1.dimension == dim then
                            RemoveBlip(v1.blip)
                            table.remove(blips, k1)
                        end
                    end

                    local blip = AddBlipForCoord(v.x, v.y, v.z)
                    SetBlipSprite(blip, 374)
                    SetBlipScale(blip, 0.4)
                    SetBlipAsShortRange(blip, true)
                    SetBlipColour(blip, 46)
                    BeginTextCommandSetBlipName('STRING')
                    AddTextComponentString('Haus')
                    EndTextCommandSetBlipName(blip)
                    table.insert(blips, {
                        blip = blip,
                        dimension = v.dimension
                    })
                end
            elseif type == 'door' then
                v.open = open
            end
        end
    end
end)

RegisterNetEvent('cc_core:house:reload')
AddEventHandler('cc_core:house:reload', function(all)
    Citizen.Wait(100)

    if #blips ~= 0 then
        for k, v in pairs(blips) do
            RemoveBlip(v.blip)
        end 
    end
    
    Citizen.Wait(100)

    houses = all

    CreateBlips()
end)

print('Registerd Event!')

RegisterNetEvent("cc_core:house:joinHouse")
AddEventHandler("cc_core:house:joinHouse", function(hous, dim, own, ownhous, sleepy)    
    hideHud(true)
    DoScreenFadeOut(1000)
    Citizen.Wait(1000)

    local ped = PlayerPedId()
    local coords = nil
    local heading = nil
    FreezeEntityPosition(ped, true)

    for k, v in pairs(Config_House.HouseTypes) do
        if k == hous then
            coords = vector3(v.joinleave.x, v.joinleave.y, v.joinleave.z - 1)
            heading = v.joinleave.h
        end
    end

    if sleepy then
        GetNakedHouse()
        Citizen.Wait(2500)
        SetEntityCoords(ped, interiorBedSpots[hous].x, interiorBedSpots[hous].y, interiorBedSpots[hous].z, false, false, false, false)
        SetEntityHeading(ped, interiorBedSpots[hous].w)
        ExecuteCommand('e sleep')
        Citizen.Wait(1000)
        DoScreenFadeIn(1000)
        Citizen.Wait(1000)
        cantLeave = false
        FreezeEntityPosition(ped, false)
        cantLeave = false
        hideHud(false)
    else
        SetEntityCoords(ped, coords, false, false, false, false)
        SetEntityHeading(ped, heading)
        DoScreenFadeIn(1000)
        Citizen.Wait(1000)
        cantLeave = false
        FreezeEntityPosition(ped, false)
        cantLeave = false
        hideHud(false)
    end

    isInHouse = true

    dimension = dim
    house = hous
    owner = own
    ownHouse = ownhous
end)

RegisterNetEvent("cc_core:house:leaveHouse")
AddEventHandler("cc_core:house:leaveHouse", function(hous, dim, x, y, z, h)
    hideHud(true)
    local ped = PlayerPedId()
    DoScreenFadeOut(1000)
    Citizen.Wait(1000)
    FreezeEntityPosition(ped, true)
    local coords = vector3(x, y, z)

    dimension = nil
    house = nil
    owner = nil
    ownHouse = nil

    SetEntityCoords(ped, x, y, z - 1, false, false, false, false)
    SetEntityHeading(ped, h)

    DoScreenFadeIn(1000)
    Citizen.Wait(1000)
    cantLeave = false
    FreezeEntityPosition(ped, false)
    cantLeave = false
    hideHud(false)

    isInHouse = false
end)

RegisterNetEvent('cc_core:house:canJoin')
AddEventHandler('cc_core:house:canJoin', function()
    cantLeave = false
end)

RegisterNetEvent('cc_core:house:createHouse')
AddEventHandler('cc_core:house:createHouse', function()
    ESX.UI.Menu.CloseAll()

    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'create_house_first_step', {
        title  = "Haus Position",
        align = 'top-left',
        elements = { 
            { label = 'Haus Position Setzen' }
        }
    }, function(data, menu)
        menu.close()
        Citizen.Wait(100)
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local x, y, z = format(playerCoords.x), format(playerCoords.y), format(playerCoords.z)
        local h = format(GetEntityHeading(playerPed))

        ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'create_house_two_step', {
            title = "Geben sie den Haus Typen ein",
        }, function(data, menu)
            local houseType = data.value
            menu.close()
            Citizen.Wait(500)

            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'create_house_three_step', {
                title = "Geben sie den Haus Preis an",
            }, function(data, menu)
                local housePrice = data.value
                menu.close()
                Citizen.Wait(500)

                TriggerServerEvent('cc_core:house:createHouse', x, y, z, h, houseType, housePrice)
    
            end, function(data, menu)
                menu.close()
            end)
        end, function(data, menu)
            menu.close()
        end)

    end, function(data, menu)
        menu.close()
    end)
end)

RegisterNetEvent('cc_core:house:createGarage')
AddEventHandler('cc_core:house:createGarage', function()
    ESX.UI.Menu.CloseAll()

    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'create_garage_first_step', {
        title  = "Garagen NPC Position",
        align = 'top-left',
        elements = { 
            { label = 'Garagen NPC Position Setzen' }
        }
    }, function(data, menu)
        menu.close()
        Citizen.Wait(100)
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local x, y, z = format(playerCoords.x), format(playerCoords.y), format(playerCoords.z)
        local h = format(GetEntityHeading(playerPed))

        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'create_garage_two_step', {
            title = "Garagen Spawn Position",
            align = 'top-left',
            elements = { 
                { label = 'Garagen Spawnpunkt setzten' }
            }
        }, function(data, menu)
            local playerPed1 = PlayerPedId()
            local playerCoords1 = GetEntityCoords(playerPed1)
            local x1, y1, z1 = format(playerCoords1.x), format(playerCoords1.y), format(playerCoords1.z)
            local h1 = format(GetEntityHeading(playerPed1))

            menu.close()
            Citizen.Wait(500)

            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'create_garage_three_step', {
                title = "Geben sie die Haus ID ein",
            }, function(data, menu)
                local dimension = data.value
                menu.close()
                Citizen.Wait(500)
                
                TriggerServerEvent('cc_core:house:createGarage', x, y, z, h, x1, y1, z1, h1, dimension)
            end, function(data, menu)
                menu.close()
            end)

        end, function(data, menu)
            menu.close()
        end)

    end, function(data, menu)
        menu.close()
    end)
end)

RegisterNUICallback('house/buyHouse', function(data, cb)
    TriggerServerEvent("cc_core:house:buyHouse", house, dimension)
end)

RegisterNUICallback('house/sellHouse', function(data, cb)
    TriggerServerEvent("cc_core:house:sellHouse", dimension, owner)
end)

RegisterCommand('houseid', function()
    if dimension ~= nil then
        Notify('Haus', 'Haus id: ' .. dimension, 'info')
    end
end)
]]