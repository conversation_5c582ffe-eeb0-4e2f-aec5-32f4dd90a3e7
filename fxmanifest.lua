fx_version 'cerulean'
game 'gta5'
version '1.0.0'
lua54 'yes'

client_scripts {
	-- 'client/utils/skinchanger/main.lua',
	-- 'client/playermanagement/main.lua',
	-- 'client/playermanagement/entityiter.lua',
	-- 'client/playermanagement/functions.lua',

	-- 'common/functions.lua',

    'client/utils/blip_info/main.lua',
    'client/utils/holdup/main.lua',
    'client/utils/klinik/main.lua',
    'client/utils/leftpeak/main.lua',
    'client/utils/lootdrop/main.lua',
    'client/utils/marry/main.lua',
    'client/utils/minijobs/main.lua',
    'client/utils/restaurants/main.lua',
    'client/utils/revivestation/main.lua',
    'client/utils/savezones/main.lua',
    'client/utils/skin/main.lua',

    'client/utils/rp_functions/anschnallen.lua',
    'client/utils/rp_functions/bag.lua',
    'client/utils/rp_functions/blips.lua',
    'client/utils/rp_functions/bullshit.lua',
    'client/utils/rp_functions/carry.lua',
    'client/utils/rp_functions/clsguide.lua',
    'client/utils/rp_functions/discord.lua',
    'client/utils/rp_functions/gps.lua',
    'client/utils/rp_functions/hunting.lua',
    'client/utils/rp_functions/michy.lua',
    'client/utils/rp_functions/nonpc.lua',
    'client/utils/rp_functions/panicbutton.lua',
    'client/utils/rp_functions/peds.lua',
    'client/utils/rp_functions/postcode.lua',
    'client/utils/rp_functions/rent.lua',
    'client/utils/rp_functions/sit.lua',
    'client/utils/rp_functions/tempo.lua',
    'client/utils/rp_functions/utils.lua',
    'client/utils/rp_functions/vehicle_functions.lua',

    'client/core/airport/main.lua',
    'client/core/anticheat/main.lua',
    'client/core/main.lua',
    'client/core/banking/main.lua',
    'client/core/barber/main.lua',
    'client/core/billing/main.lua',
    'client/core/carpanel/main.lua',
    'client/core/carwash/main.lua',
    'client/core/clothing/main.lua',
    'client/core/drivingschool/main.lua',
    'client/core/farmer/main.lua',
    'client/core/ffa/main.lua',
    'client/core/fraction/society.lua',
    'client/core/fuel/main.lua',
    'client/core/garage/main.lua',
    'client/core/housing/main.lua',
    'client/core/hud/main.lua',
    'client/core/identity/main.lua',
    'client/core/items/main.lua',
    'client/core/jail/main.lua',
    'client/core/lifeinvader/main.lua',
    'client/core/multichar/main.lua',
    'client/core/norecoil/main.lua',
    'client/core/npcdialog/main.lua',
    'client/core/pausemenu/main.lua',
    'client/core/rageui/RageUI.lua',
    'client/core/rageui/main.lua',
    'client/core/rageui/menus/admin.lua',
    'client/core/rageui/menus/animal.lua',
    'client/core/rageui/menus/machine.lua',
    'client/core/rageui/menus/personal.lua',
    'client/core/rageui/menus/tuner.lua',
    'client/core/realtime/main.lua',
    'client/core/referral/main.lua',
    'client/core/saltychat/main.lua',
    'client/core/schrottplatz/main.lua',
    'client/core/supermarket/main.lua',
    'client/core/vehicleshop/main.lua',
    'client/core/weaponcrafter/main.lua',
    'client/core/workstation/main.lua',
}

shared_scripts {
	'configs/config.lua',
	'configs/config.items.lua',

	'configs/housing/config.lua',

	'configs/**/**/*.lua'
}

server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/core/main.lua',
    'server/server_config.lua',

    'server/playermanagement/main.lua',

    'server/core/airport/main.lua',
    'server/core/anticheat/main.lua',
    'server/core/banking/main.lua',
    'server/core/barber/main.lua',
    'server/core/billing/main.lua',
    'server/core/carpanel/main.lua',
    'server/core/carwash/main.lua',
    'server/core/clothing/main.lua',
    'server/core/drivingschool/main.lua',
    'server/core/farmer/main.lua',
    'server/core/ffa/main.lua',
    'server/core/fraction/society.lua',
    'server/core/fuel/main.lua',
    'server/core/garage/main.lua',
    'server/core/housing/main.lua',
    'server/core/hud/main.lua',
    'server/core/identity/main.lua',
    'server/core/items/main.lua',
    'server/core/jail/main.lua',
    'server/core/lifeinvader/main.lua',
    'server/core/multichar/main.lua',
    'server/core/npcdialog/main.lua',
    'server/core/pausemenu/main.lua',
    'server/core/pass/main.lua',
    'server/core/rageui/main.lua',
    'server/core/rageui/menus/admin.lua',
    'server/core/rageui/menus/animal.lua',
    'server/core/rageui/menus/machine.lua',
    'server/core/rageui/menus/personal.lua',
    'server/core/rageui/menus/tuner.lua',
    'server/core/realtime/main.lua',
    'server/core/referral/main.lua',
    'server/core/schrottplatz/main.lua',
    'server/core/security/main.lua',
    'server/core/supermarket/main.lua',
    'server/core/vehicleshop/main.lua',
    'server/core/weaponcrafter/main.lua',
    'server/core/workstation/main.lua',

    'server/utils/rp_functions/anschnallen.lua',
    'server/utils/rp_functions/bag.lua',
    'server/utils/rp_functions/bullshit.lua',
    'server/utils/rp_functions/carry.lua',
    'server/utils/rp_functions/clsguide.lua',
    'server/utils/rp_functions/discord.lua',
    'server/utils/rp_functions/givecar.lua',
    'server/utils/rp_functions/gps.lua',
    'server/utils/rp_functions/hunting.lua',
    'server/utils/rp_functions/michy.lua',
    'server/utils/rp_functions/nonpc.lua',
    'server/utils/rp_functions/panicbutton.lua',
    'server/utils/rp_functions/postcode.lua',
    'server/utils/rp_functions/rent.lua',
    'server/utils/rp_functions/sit.lua',
    'server/utils/rp_functions/tempo.lua',
    'server/utils/rp_functions/utils.lua',
    'server/utils/rp_functions/vehicle_functions.lua',

    'server/utils/clsguide/main.js',
    'server/utils/holdup/main.lua',
    'server/utils/klinik/main.lua',
    'server/utils/leftpeak/main.lua',
    'server/utils/logs/main.lua',
    'server/utils/lootdrop/main.lua',
    'server/utils/marry/main.lua',
    'server/utils/minijobs/main.lua',
    'server/utils/restaurants/main.lua',
    'server/utils/revivestation/main.lua',
    'server/utils/skin/main.lua',
    'server/utils/syncrole/main.lua',
    'server/utils/syncrole/main.js',
    'server/utils/txadmin/main.lua',
}

files {
    'html/**/',
    'loadingscreen/**/**/',
    'postals.json',
    'metas/*',
    'metas/**/*',
    'metas/**/**/*',
    'metas/**/**/**/*'
}

escrow_ignore {
  "metas/*",
  "metas/**/*",
  "metas/**/**/*",
  "metas/**/**/**/*",
}

ui_page 'html/index.html'
loadscreen 'loadingscreen/index.html'
loadscreen_cursor 'yes'