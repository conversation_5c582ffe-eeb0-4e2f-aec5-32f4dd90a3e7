bullshitCode = [[
ESX = nil

Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(0)
    end
end)

local Player = {
	isDead = false,
	inAnim = false,
	crouched = false,
	handsup = false,
	pointing = false,
}

local function startPointing(plyPed)	
	ESX.Streaming.RequestAnimDict('anim@mp_point', function()
		SetPedConfigFlag(plyPed, 36, 1)
		TaskMoveNetworkByName(plyPed, 'task_mp_pointing', 0.5, 0, 'anim@mp_point', 24)
		RemoveAnimDict('anim@mp_point')
	end)
end

local function stopPointing(plyPed)
	RequestTaskMoveNetworkStateTransition(plyPed, 'Stop')

	if not IsPedInjured(plyPed) then
		ClearPedSecondaryTask(plyPed)
	end

	SetPedConfigFlag(plyPed, 36, 0)
	ClearPedSecondaryTask(plyPed)
end

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
        local sleep = true
		local id = PlayerId()

        local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

        DisableControlAction(1, 44, true)

        if vehicle then
            sleep = false
		    DisablePlayerVehicleRewards(id)
        else
            sleep = true
        end

        if sleep then
            Wait(1000)
        end
	end
end)

RegisterNetEvent('esx:setJobDienst')
AddEventHandler('esx:setJobDienst', function(job, state)
	ESX.PlayerData.job.dienst = state
end)

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
        local ped = PlayerPedId()
		if IsPedInAnyVehicle(ped, false) and disableShuffle then
			if GetPedInVehicleSeat(GetVehiclePedIsIn(ped, false), 0) == ped then
				if GetIsTaskActive(ped, 165) then
					SetPedIntoVehicle(ped, GetVehiclePedIsIn(ped, false), 0)
				end
			end
        else
            Citizen.Wait(500)
		end
	end
end)

RegisterCommand("shuff", function(source, args, raw) 
    if IsPedInAnyVehicle(PlayerPedId(), false) then
        disableShuffle = false
		Citizen.Wait(5000)
        disableShuffle = true
	end
end, false)

local handCamera, active = nil, false

local function disableCamera()
    RenderScriptCams(false, true, Config_Leftpeak.easeTime, false, false)

    SetTimeout(Config_Leftpeak.easeTime * 2, function()
        if handCamera ~= nil and not active then
            SetCamActive(handCamera, false)
            DestroyCam(handCamera)
            handCamera = nil
        end
    end)

    active = false
end

local function setCameraLook()
    local ped = PlayerPedId()
    local cameraCoords = GetGameplayCamCoord()
    local cameraRotation = GetGameplayCamRot(2)
    local gameplayCamFov = GetGameplayCamFov()

    local coordsRelativeToPlayer = GetOffsetFromEntityGivenWorldCoords(ped, cameraCoords.x, cameraCoords.y, cameraCoords.z)
    local leftShoulderCoords = GetOffsetFromEntityInWorldCoords(ped, coordsRelativeToPlayer.x, coordsRelativeToPlayer.y, coordsRelativeToPlayer.z)

    SetCamCoord(handCamera, leftShoulderCoords.x, leftShoulderCoords.y, leftShoulderCoords.z)
    SetCamRot(handCamera, cameraRotation.x, cameraRotation.y, cameraRotation.z, 2)
    AttachCamToEntity(handCamera, ped, coordsRelativeToPlayer.x - Config_Leftpeak.leftRange, coordsRelativeToPlayer.y, coordsRelativeToPlayer.z, true)
    SetCamFov(handCamera, gameplayCamFov)

    ShowHudComponentThisFrame(14)
end

local function toggleCamera()
    if not active then
        if GetFollowPedCamViewMode() == 4 or not IsPlayerFreeAiming(PlayerId()) then return end

        if handCamera ~= nil then
            SetCamActive(handCamera, false)
            DestroyCam(handCamera)
            handCamera = nil
        end

        handCamera = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
        SetCamActive(handCamera, true)
        RenderScriptCams(true, true, Config_Leftpeak.easeTime, false, false)
        
        if not DoesCamExist(handCamera) then
            return disableCamera()
        end

        active = true

        startThreads()
        setCameraLook()
    else
        SetCamAffectsAiming(handCamera, true)
        disableCamera()
    end
end


RegisterCommand('getDim', function(source, args)
    if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'frakverwaltung' then
       TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Information', 'Der Spieler befindet sich in der dim ' .. GetPlayerRoutingBucket(args[1])) 
    end
end)

RegisterCommand('setDim', function(source, args)    
    if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'frakverwaltung' then
        if tonumber(args[2]) < 6 then
            SetPlayerRoutingBucket(tonumber(args[1]), tonumber(args[2])) 
        end
    end
end)

RegisterCommand('setGW', function(source, args)
    if ESX.GetPlayerGroup(source) == 'projektleitung' then
        print('have permissions')
        MySQL.Async.execute('UPDATE jobs SET gwallowed = @gwallowed, gwcode = @gwcode WHERE name = @name', {
            ['@gwallowed'] = true,
            ['@gwcode'] = args[2],
            ['@name'] = args[1]
        }, function(result)
            TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Fraktion', 'Erfolgreich hinzugefügt')
        end)
    end
end)

RegisterCommand('setGangwar', function(source, args)    
    if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'frakverwaltung' then
        if tonumber(args[1]) then
            TriggerEvent('cc_gangwar:setGangwar', source, tonumber(args[1]), args[2])
        end
    end
end)

RegisterCommand('setjob', function(source, args, rawCommand)
    if source == 0 then
        local targetId = tonumber(args[1])
        
        if GetPlayerName(targetId) ~= nil then
            if ESX.DoesJobExist(args[2], tonumber(args[3])) then
                if not ESX.IsJob3(args[2]) then
                    ESX.SetPlayerJob(targetId, args[2], tonumber(args[3]))
                    print('Erfolgreich gesetzt')
                else
                    print('ist kein 1. job') 
                end
            else
                print('Ungültig')
            end
        end
    else
        local group = ESX.GetPlayerGroup(source)

        print('setjob', source, group, args[1], args[2])

        if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'frakverwaltung' then
            local targetId = tonumber(args[1])
            if GetPlayerName(targetId) ~= nil then
                if ESX.DoesJobExist(args[2], tonumber(args[3])) then
                    if not ESX.IsJob3(args[2]) then
                        ESX.SetPlayerJob(targetId, args[2], tonumber(args[3]))
                        Notify(source, 'Information', 'Erfolgreich gesetzt!', 'info')
                    else
                        Notify(source, 'Information', 'Dies ist kein 1. Job!', 'info')
                    end
                else
                    Notify(source, 'Information', 'Invalid job, grade or both are invalid!', 'info')
                end
            end
        end
    end
end)

function startThreads()
    if not active then 
        return 
    end

    Citizen.CreateThread(function()
        while active do
            Citizen.Wait(0)
    
            if GetFollowPedCamViewMode() == 4 or not IsPlayerFreeAiming(PlayerId()) then
                toggleCamera()
            else
                setCameraLook()
            end
        end
    end)
end

RegisterKeyMapping(Config_Leftpeak.keymapping.name, Config_Leftpeak.keymapping.description, 'keyboard', Config_Leftpeak.keymapping.key)

RegisterCommand(Config_Leftpeak.keymapping.name, function()
    toggleCamera()
end)
]]