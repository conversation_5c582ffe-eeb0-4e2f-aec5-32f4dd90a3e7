RegisterServerEvent('cc_core:barbershop:pay')
AddEventHandler('cc_core:barbershop:pay', function()
    local playerId = source

    if ESX.GetPlayerMoney(playerId) >= Config_Barbershop.Price then
        ESX.RemovePlayerMoney(playerId, Config_Barbershop.Price, GetCurrentResourceName())
    elseif ESX.GetPlayerAccount(playerId, 'bank').money >= Config_Barbershop.Price then
        ESX.RemovePlayerAccountMoney(playerId, 'bank', Config_Barbershop.Price, GetCurrentResourceName())
    end
end)

--clientcode
barberShopCode = [[
local show = false
local paid, leave = false, false

local function isMale()
    if GetEntityModel(PlayerPedId()) == GetHashKey('mp_m_freemode_01') then
        return true
    end

    return false
end

local function openShopMenu()
    paid = false
    local elements2 = {}

    if isMale() then
        elements2 = {
            'beard_1',
            'beard_2',
            'beard_3',
            'beard_4',
            'hair_1',
            'hair_2',
            'hair_color_1',
            'hair_color_2',
            'eyebrows_1',
            'eyebrows_2',
            'eyebrows_3',
            'eyebrows_4'
        }
    else
        elements2 = {
            'beard_1',
            'beard_2',
            'beard_3',
            'beard_4',
            'hair_1',
            'hair_2',
            'hair_color_1',
            'hair_color_2',
            'eyebrows_1',
            'eyebrows_2',
            'eyebrows_3',
            'eyebrows_4',
            'makeup_1',
            'makeup_2',
            'makeup_3',
            'makeup_4',
            'lipstick_1',
            'lipstick_2',
            'lipstick_3',
            'lipstick_4'
        }
    end

    TriggerEvent('cc_core:skin:openRestrictedMenu', function(data, menu)
		menu.close()

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'shop_confirm', {
			title = 'Einkauf bestätigen',
			align = 'top-left',
			elements = {
				{ label = 'Nein',  value = 'no' },
				{ label = 'Ja', value = 'yes' }
			}
		}, function(data, menu)
            menu.close()

            if data.current.value == 'yes' then
                if haveMoney(50) then
                    TriggerEvent('skinchanger:getSkin', function(skin)
                        TriggerServerEvent('cc_core:skin:save', skin)
                    end)

                    TriggerServerEvent('cc_core:barbershop:pay')

                    paid = true
                else
                    ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin)
                        TriggerEvent('skinchanger:loadSkin', skin) 
                    end)

                    Notify('Friseur', 'Du hast nicht genug Geld', 'info')
                end
			elseif data.current.value == 'no' then
				ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin)
					TriggerEvent('skinchanger:loadSkin', skin) 
				end)
			end
		end, function(data, menu)
			menu.close()
		end)
	end, function(data, menu)
        menu.close()
	end, elements2)
end

Citizen.CreateThread(function()
    for k, v in pairs(Config_Barbershop.Shops) do
        if v.enabled then
            local blip = AddBlipForCoord(v.coords)

            SetBlipSprite(blip, 71)
            SetBlipDisplay(blip, 2)
            SetBlipScale(blip, 0.7)
            SetBlipColour(blip, 0)
            SetBlipAsShortRange(blip, true)
    
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString('Friseur')
            EndTextCommandSetBlipName(blip) 
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)

        for k, v in pairs(Config_Barbershop.Shops) do
            local distance = #(coords - v.coords)

            if distance <= 20.0 then
                letSleep = false

                DrawMarker(1, v.coords.x, v.coords.y, v.coords.z - 1, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 1.5, 1.5, 1.5, 140, 73, 184, 100, false, true, 2, false, false, false, false)
            end

            if distance <= 1.5 then
                inRange = true

                if not leave then
                    leave = true
                end

                if IsControlJustReleased(0, 38) then
                    openShopMenu()
                end
            else
                if leave and not show then
                    leave = false

                    if not paid then
                        ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin)
                            TriggerEvent('skinchanger:loadSkin', skin)
                        end)
                    end
                end
            end
        end

        helpNotify(inRange, show, 'Drücke E um das Menü zu öffnen', function(bool)
            show = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)
]]