local blacklistedStrings = {
    "<",
    ">",
    "script",
    "img",
    "video",
    "iframe",
    "audio",
    "mp3",
    "mp4",
    "ogg",
    "webm",
    "AliOG"
}

local function isStringValid(str)
    for _, v in pairs(blacklistedStrings) do
        if string.find(str, v) then
            return false
        end
    end

    return true
end

function htmlEncode(str)
    local map = {
        ["&"] = "&amp;",
        ["<"] = "&lt;",
        [">"] = "&gt;",
        ['"'] = "&quot;",
        ["'"] = "&#39;"
    }

    return str:gsub("[&<>\'\"]", function(c)
        return map[c]
    end)
end

local function isJobLegal(jobName)
    if jobName == 'driver' or jobName == 'marshal' or jobName == 'shadowops' or jobName == 'Anwaelte' or jobName == 'unicorn' or jobName == 'Pearls' or jobName == 'osaka' or jobName == 'corleone' or jobName == 'police' or jobName == 'fib' or jobName == 'sheriff' or jobName == 'ambulance' or jobName == 'army' or jobName == 'sameday' or jobName == 'abschlepper' or jobName == 'mechanic' or jobName == 'lsc' or jobName == 'taxi' or jobName == 'Securo' or jobName == 'Doj' or jobName == 'doj' then
        return true
    end

    return false
end


function detectXSS(input)
    local patterns = {
        "<script",
        "<",
        ">",
        "http",
        "https",
        "://",
        "javascript:",
        "onerror=",
        "onload=",
        "<iframe",
        "<img",
        "<svg",
        "document%.cookie",
        "document%.write",
        "eval%(",
        "alert%(",
        "confirm%(",
        "<object",
        "<embed",
        "setTimeout%(",
        "setInterval%(",
        "nigga",
        "nigger",
        "n1gga",
    }


    for _, pattern in ipairs(patterns) do
        if string.find(input, pattern) then
            -- tiziano du ayri mach so nächste mal
            return true
        end
    end

    return false
end

Citizen.CreateThread(function()
    for k, v in pairs(Config_Hud.Items) do
        ESX.RegisterUsableItem(k, function(playerId)
            if k == 'lunchpackage' then
                if isJobLegal(ESX.GetPlayerJob(playerId).name) or GetPlayerRoutingBucket(playerId) == 5 then
                    ESX.RemovePlayerInventoryItem(playerId, k, 1)
                    TriggerClientEvent('cc_core:hud:useItem', playerId, v.type, v.value, true)
                else
                    Notify('Information', 'Du kannst dies nicht benutzen', 'info')
                end
            else
                ESX.RemovePlayerInventoryItem(playerId, k, 1)
                TriggerClientEvent('cc_core:hud:useItem', playerId, v.type, v.value, true)
            end
        end)
    end
end)

RegisterServerEvent('cc_core:hud:ooc')
AddEventHandler('cc_core:hud:ooc', function(closestPlayer, argString)
    local playerId = source

    if detectXSS(argString) then return end

    if isStringValid(argString) then
        TriggerClientEvent("cc_core:hud:notify", playerId, "ooc", "OOC (" .. GetPlayerName(playerId) .. ")", htmlEncode(argString))
        TriggerClientEvent("cc_core:hud:notify", closestPlayer, "ooc", "OOC (" .. GetPlayerName(playerId) .. ")", htmlEncode(argString))
    end
end)

-- RegisterCommand('announce', function(source, args)
--     local msg = table.concat(args, " ")
--     if detectXSS(msg) then return end
--     if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'superadmin' or ESX.GetPlayerGroup(source) == 'frakverwaltung' then
--         if detectXSS(table.concat(args, ' ')) then return end

--         TriggerClientEvent('cc_core:hud:announce', -1, 'Ankündigung', table.concat(args, ' '))
--         exports['cc_core']:log(source, 'Announce - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' schreibt \n\n**Inhalt:** ' ..msg, 'https://canary.discord.com/api/webhooks/1217830291742134363/8z5qU5q6vN7m3hA9deySSNuyPDGAVtWbGyiQ_UG-DZeL8Cw19brDC-T85sPqGxDJD8qz')
--     end
-- end)

RegisterCommand('announce', function(source, args, rawCommand)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    local msg = table.concat(args, " ")
    if detectXSS(msg) then return end
    local group = xPlayer.getGroup()

    if group == 'projektleitung' then
        TriggerClientEvent("cc_core:hud:announce", -1, "Ankündigung von - " .. GetPlayerName(source), msg)
        -- exports['cc_core']:log(source, 'Announce - Log',
        -- 'Der Spieler ' .. GetPlayerName(source) .. ' schreibt \n\n**Inhalt:** ' .. msg,
        --     'https://canary.discord.com/api/webhooks/1378340984500391987/glytV7F9qz49K_0mfDh8Cd57dEj8aGAcXKu58pXLmaCMEqS4gtvKR8RviSXrYxdt02TD')
    end
end)

RegisterCommand('heal', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(args[1])
    local group = xPlayer.getGroup()
    if group == 'projektleitung' or group == 'managment' or group == 'teamleitung' or group == 'superadmin' then
        if args[1] == 'me' then
            TriggerClientEvent('essenArmourTrinkenFull', source)
        elseif args[1] == nil then
            TriggerClientEvent('essenArmourTrinkenFull', source)
        else
            TriggerClientEvent('essenArmourTrinkenFull', args[1])
        end
    end
end)

RegisterCommand('ta', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    local group = xPlayer.getGroup()
	local msg = table.concat(args, " ")
    if detectXSS(msg) then return end

    if group == 'projektleitung' or group == 'managment' or group == 'teamleitung' then
        local xPlayers = exports["cc_core"]:GetPlayersFix()
        for k, v in pairs(xPlayers) do
            if v.group ~= 'user' then
                TriggerClientEvent("cc_core:hud:announce", v.playerId , "Teamannounce - " .. GetPlayerName(source), msg)
            end
        end
        exports['cc_core']:log(source, 'Team Announce - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' schreibt \n\n**Inhalt:** ' ..msg, 'https://canary.discord.com/api/webhooks/1378340984500391987/glytV7F9qz49K_0mfDh8Cd57dEj8aGAcXKu58pXLmaCMEqS4gtvKR8RviSXrYxdt02TD')
    end
end)

RegisterCommand('cop_announce', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    local job = xPlayer.job.name
    if job == 'police' then
        if detectXSS(table.concat(args, ' ')) then return end
        TriggerClientEvent('cc_core:hud:announce', -1, 'PD Ankündigung', table.concat(args, ' '))
    end
end)

RegisterCommand('tc', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    local group = xPlayer.getGroup()
	local msg = table.concat(args, " ")
    if detectXSS(msg) then return end

    if group ~= 'user' then
        local xPlayers = exports["cc_core"]:GetPlayersFix()
        for k, v in pairs(xPlayers) do
            if v.group ~= 'user' then
                TriggerClientEvent("cc_core:hud:teamchat", v.playerId, "TEAMCHAT - " .. GetPlayerName(source) .. ' ID: ' .. source, msg)
            end
        end
        exports['cc_core']:log(source, 'TeamChat - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' schreibt \n\n**Inhalt:** ' ..msg, 'https://canary.discord.com/api/webhooks/1378340984500391987/glytV7F9qz49K_0mfDh8Cd57dEj8aGAcXKu58pXLmaCMEqS4gtvKR8RviSXrYxdt02TD')
    end
end)

RegisterCommand('dm', function(source, args)
    local target = args[1]
    local message = table.concat(args, ' ')
    message = string.gsub(message, target, '')
    if source ~= 0 then
        message = message..'\n['..GetPlayerName(source)..']'
    end
    if detectXSS(message) then return end

    if source == 0 then
        if GetPlayerName(target) ~= nil then
            TriggerClientEvent("cc_core:hud:notify", target, "ooc", "ADMIN DM", message)
        end
    elseif ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'superadmin' or ESX.GetPlayerGroup(source) == 'administrator' or ESX.GetPlayerGroup(source) == 'moderator' or ESX.GetPlayerGroup(source) == 'support' or ESX.GetPlayerGroup(source) == 'testsupporter' or ESX.GetPlayerGroup(source) == 'frakverwaltung' then
        if GetPlayerName(target) ~= nil then
            TriggerClientEvent("cc_core:hud:notify", target, "ooc", "ADMIN DM", message)
        end
    end
end)

function isAdmin(xPlayer)
    local allowedGroups = { "projektleitung" }
    return xPlayer.getGroup and contains(allowedGroups, xPlayer.getGroup())
end

function contains(table, element)
    for _, value in ipairs(table) do
        if value == element then return true end
    end
    return false
end

RegisterServerEvent('hudsettings:requestTeamlerStatus')
AddEventHandler('hudsettings:requestTeamlerStatus', function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)

    if xPlayer and xPlayer.getGroup() ~= 'user' then
        TriggerClientEvent('hudsettings:checkTeamler', src, true)
    else
        TriggerClientEvent('hudsettings:checkTeamler', src, false)
    end
end)

--clientcode
hudCode = [[
local currentFood, currentThirst = 50, 50
local showShit = false
local change = false
local sendung = true
local currRange = 0
local drawing = false
local fadeIn = false
local fadeOut = false
local alpha = 0
local maxAlpha = 255
local targetRange = 0
local currRange = 0
local hideTime = 0
local hideTime2 = 0
local voiceMode = 2
local voiceModes = {}
local HudModules = {
    ["essenhud_info"] = true,
    ["speed_info"] = true,
    ["ammo_info"] = true,
    ["job_info"] = true,
    ["team_info"] = true,
    ["straße_info"] = true
}
local isTeamler = false

local HidenHudComponents = { 2, 6, 7, 8, 9 }

RegisterNetEvent('essenTrinkenFull', function()
    currentFood, currentThirst = 75, 75
end)

local function playAnimation(which)
    local animDict = which == 'food' and 'mp_player_inteat@burger' or 'mp_player_intdrink'
    local animName = which == 'food' and 'mp_player_int_eat_burger' or 'loop_bottle'

    ESX.Streaming.RequestAnimDict(animDict, function()
        TaskPlayAnim(PlayerPedId(), animDict, animName, 8.0, -8, -1, 49, 0, 0, 0, 0)
        startProgressbar(2, "Esssen & Trinken")
        Citizen.Wait(2500)
        ClearPedTasks(PlayerPedId())
    end)
end

local function startHudThreads()
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(100)

            local ped = PlayerPedId()
            local weapon = GetSelectedPedWeapon(ped)

            if IsPedArmed(ped, 4) then
                local weaponName = ESX.GetWeaponFromHash(weapon)
                local weaponLabel = ESX.GetWeaponLabel(weaponName.name)
                local _, ammo = GetAmmoInClip(ped, weapon)
                local ammo2 = GetAmmoInPedWeapon(ped, weapon)

                SendNUIMessage({
                    script = 'hud',
                    action = 'weapon',
                    currentWeapon = weaponName.name,
                    currentWeaponLabel = weaponLabel,
                    currentAmmo = ammo,
                    maxAmmo = ammo2 - ammo
                })

                if not showShit then
                    showShit = true
                end
            else
                if showShit then
                    SendNUIMessage({
                        script = 'hud',
                        action = 'weapon',
                        currentWeapon = 'WEAPON_UNARMED',
                        currentAmmo = 0,
                        maxAmmo = 0
                    })
                    showShit = false
                end

                Citizen.Wait(1000)
            end
        end
    end)

    Citizen.CreateThread(function()
        while ESX == nil do
            ESX = exports['es_extended']:getSharedObject()
            Citizen.Wait(0)
        end

        while not ESX.IsPlayerLoaded() do
            Citizen.Wait(500)
        end

        while ESX.GetPlayerData().job == nil do
            Citizen.Wait(10)
        end

        PlayerData = ESX.GetPlayerData()

        local settings = GetResourceKvpString("cc_core:hud:SettingsModule")

        for _, comp in ipairs(HidenHudComponents) do
            SetHudComponentPosition(comp, 0.5, 0.5)
        end

        Citizen.Wait(5000)

        if settings then
            HudModules = json.decode(settings)
        else
            SetResourceKvp("cc_core.hud:SettingsModule", json.encode(HudModules))
        end

        SendNUIMessage({
            action = "LoadHudModules",
            HudModules = HudModules,
        })
    end)

    Citizen.CreateThread(function()
        while true do
            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            local distance = #(coords - vector3(-1080.7798, -2824.1260, 27.7087))

            if distance >= 250 and not exports['cc_core']:isInCharCreator() then
                currentFood = currentFood - 1
                print('Essen: ' .. currentFood)

                if currentFood <= 0 then
                    currentFood = 0
                end

                SendNUIMessage({
                    script = 'hud',
                    action = 'food',
                    currentFood = currentFood
                })
            end

            Citizen.Wait(Config_Hud.Hunger)
        end
    end)

    Citizen.CreateThread(function()
        local done = false
        while not done do
            if not exports['cc_core']:isInCharCreator() then
                SendNUIMessage({
                    script = 'hud',
                    action = 'job',
                    grade = ESX.GetPlayerData().job.grade,
                    job = ESX.GetPlayerData().job.name
                })
                done = true
            end

            Citizen.Wait(30000)
        end
    end)

    Citizen.CreateThread(function()
        while true do
            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            local distance = #(coords - vector3(-1080.7798, -2824.1260, 27.7087))

            if distance >= 250 and not exports['cc_core']:isInCharCreator() then
                currentThirst = currentThirst - 1
                print('Trinken: ' .. currentThirst)

                if currentThirst <= 0 then
                    currentThirst = 0
                end

                SendNUIMessage({
                    script = 'hud',
                    action = 'thirst',
                    currentThirst = currentThirst
                })
            end

            Citizen.Wait(Config_Hud.Thirst)
        end
    end)

    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(5000)

            if currentFood < 1 or currentThirst < 1 and not exports['cc_core']:isInCharCreator() then
                local ped = PlayerPedId()
                SetEntityHealth(ped, GetEntityHealth(ped) - 1)
            else
                Citizen.Wait(30000)
            end
        end
    end)

    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(60 * 1000 * 5)
            SetResourceKvpInt('cc_core:eat:food2', currentFood)
            Citizen.Wait(1000)
            SetResourceKvpInt('cc_core:eat:thirst2', currentThirst)
        end
    end)

    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(0)
            local ped = PlayerPedId()

            if IsPedInAnyVehicle(ped, false) then
                local vehicle = GetVehiclePedIsIn(ped, false)

                if vehicle then
                    local speed, fuel, rpm, gear = math.ceil(GetEntitySpeed(vehicle) * 3.6), math.ceil(GetVehicleFuelLevel(vehicle)), GetVehicleCurrentRpm(vehicle), GetVehicleCurrentGear(vehicle)

                    SendNUIMessage({
                        script = 'hud',
                        action = 'carHud',
                        show = true,
                        speed = speed,
                        fuel = fuel,
                        rpm = rpm,
                        gear = gear
                    })

                    SendNUIMessage({
                        script = 'hud',
                        action = 'carEngine',
                        state = GetIsVehicleEngineRunning(vehicle)
                    })

                    if GetVehicleDoorLockStatus(vehicle) == 1 then
                        SendNUIMessage({
                            script = 'hud',
                            action = 'carLock',
                            state = true
                        })
                    else
                        SendNUIMessage({
                            script = 'hud',
                            action = 'carLock',
                            state = false
                        })
                    end

                    Citizen.Wait(100)
                else
                    SendNUIMessage({
                        script = 'hud',
                        action = 'carHud',
                        show = false
                    })
                    Citizen.Wait(1000)
                end
            else
                SendNUIMessage({
                    script = 'hud',
                    action = 'carHud',
                    show = false
                })
                Citizen.Wait(1000)
            end
        end
    end)

    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(1000)

            if not isInUI then
                SendNUIMessage({
                    script = 'hud',
                    action = 'pause',
                    pause = IsPauseMenuActive()
                })
            end
        end
    end)

    local show88 = false

    Citizen.CreateThread(function()
        Citizen.Wait(5000)
        while true do
            Citizen.Wait(1000)

            if GetResourceState('cc_menus') == 'started' then
                if not show88 and exports['cc_menus']:isInKino() then
                    show88 = true

                    SendNUIMessage({
                        script = 'hud',
                        action = 'kino',
                        show = false
                    })
                elseif show88 and not exports['cc_menus']:isInKino() then
                    show88 = false

                    SendNUIMessage({
                        script = 'hud',
                        action = 'kino',
                        show = true
                    })
                end
            end
        end
    end)
end

local function getBankMoney()
    for k, v in pairs(ESX.GetPlayerData().accounts) do
        if v.name == 'bank' then
            return v.money
        end
    end
end

RegisterNUICallback("UpdateHudSettings", function(data, cb)
    HudModules[data.module] = data.state

    SetResourceKvp("cc_core:hud:SettingsModule", json.encode(HudModules))

    SendNUIMessage({
        action = "LoadHudModules",
        HudModules = HudModules
    })

    TriggerEvent("updateHudElements")

    if cb then cb("ok") end
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function()
    -- Wait(1000)
    if not exports['cc_core']:isInCharCreator() then
        SendNUIMessage({
            script = 'hud',
            action = 'job',
            grade = ESX.GetPlayerData().job.grade,
            job = ESX.GetPlayerData().job.label
        })
    end
end)

local myId = ''

Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(0)
    end

    while ESX.GetPlayerData().job == nil do
        Citizen.Wait(0)
    end

    myId = ESX.GetPlayerData().identifier

    Citizen.Wait(1500)

    local currentKVPFood = GetResourceKvpInt('cc_core:eat:food2')
    local currentKVPThirst = GetResourceKvpInt('cc_core:eat:thirst2')
    local check_food_shit = GetResourceKvpString('cc_core:firstTime23')

    if check_food_shit ~= nil then
        currentFood = currentKVPFood
        currentThirst = currentKVPThirst
    else
        currentFood = 100
        currentThirst = 100
        SetResourceKvp('cc_core:firstTime23', 'yes')

        SetResourceKvpInt('cc_core:eat:food2', currentFood)
        Citizen.Wait(1000)
        SetResourceKvpInt('cc_core:eat:thirst2', currentThirst)
    end

    SendNUIMessage({
        script = 'hud',
        action = 'food',
        currentFood = currentFood
    })

    SendNUIMessage({
        script = 'hud',
        action = 'thirst',
        currentThirst = currentThirst
    })

    SendNUIMessage({
        script = 'hud',
        action = 'setup',
        playerId = GetPlayerServerId(PlayerId()),
        money = getMoney('money'),
        bank = getBankMoney(),
        blackMoney = getMoney('black_money')
    })

    SetTextChatEnabled(false)

    startHudThreads()
end)

local blacklistedStrings = {
    "<",
    ">",
    "script",
    "img",
    "video",
    "iframe",
    "audio",
    "mp3",
    "mp4",
    "ogg",
    "webm",
    "AliOG"
}

local function isStringValid(str)
    for _, v in pairs(blacklistedStrings) do
        if string.find(string.lower(str), string.lower(v), 1, true) then
            return false
        end
    end

    return true
end

function htmlEncode(str)
    local map = {
        ["&"] = "&amp;",
        ["<"] = "&lt;",
        [">"] = "&gt;",
        ['"'] = "&quot;",
        ["'"] = "&#39;"
    }

    return str:gsub("[&<>\'\"]", function(c)
        return map[c]
    end)
end

local function detectXSS(input)
    local patterns = {
        "<script",
        "<",
        ">",
        "http",
        "https",
        "://",
        "javascript:",
        "onerror=",
        "onload=",
        "<iframe",
        "<img",
        "<svg",
        "document%.cookie",
        "document%.write",
        "eval%(",
        "alert%(",
        "confirm%(",
        "<object",
        "<embed",
        "setTimeout%(",
        "setInterval%("
    }

    for _, pattern in ipairs(patterns) do
        if string.find(input, pattern) then
            -- tiziano du ayri mach so nächste mal
            return true
        end
    end

    return false
end

RegisterNetEvent('cc_core:hud:announce')
AddEventHandler('cc_core:hud:announce', function(title, message)
    if detectXSS(title) or detectXSS(message) then return end
    local mStatus, error = pcall(function()
        if isStringValid(title) and isStringValid(message) then
            SendNUIMessage({
                script = 'hud',
                action = 'announce',
                title = htmlEncode(title),
                message = htmlEncode(message)
            })

            PlaySoundFrontend(-1, "CHALLENGE_UNLOCKED", "HUD_AWARDS", 1)
        end
    end)

    if not mStatus then
        print(error)
    end
end)

RegisterNetEvent('cc_core:hud:notify')
AddEventHandler('cc_core:hud:notify', function(type, title, message)
    if detectXSS(title) or detectXSS(message) then return end
    local mStatus, error = pcall(function()
        if isStringValid(type) and isStringValid(title) and isStringValid(message) then
            SendNUIMessage({
                script = 'hud',
                action = 'notify',
                type = type,
                title = htmlEncode(title),
                message = htmlEncode(message)
            })

            PlaySoundFrontend(-1, "ATM_WINDOW", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
        end
    end)

    if not mStatus then
        print(error)
    end
end)

RegisterNetEvent('cc_core:hud:teamchat')
AddEventHandler('cc_core:hud:teamchat', function(title, message)
    if detectXSS(title) or detectXSS(message) then return end
    local mStatus, error = pcall(function()
        if group ~= 'user' then
            if isStringValid(title) and isStringValid(message) then
                SendNUIMessage({
                    script = 'hud',
                    action = 'teamchat',
                    title = htmlEncode(title),
                    message = htmlEncode(message)
                })
                PlaySoundFrontend(-1, "ATM_WINDOW", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
            end
        end
    end)

    if not mStatus then
        print(error)
    end
end)

RegisterNetEvent('cc_core:hud:lifeinvader')
AddEventHandler('cc_core:hud:lifeinvader', function(message, phone_number, rpName)
    if detectXSS(message) then return end
    local mStatus, error = pcall(function()
        if isStringValid(message) and isStringValid(rpName) then
            SendNUIMessage({
                script = 'hud',
                action = 'lifeinvader-notify',
                message = htmlEncode(message),
                phone_number = phone_number,
                rpName = rpName
            })
        end
    end)

    if not mStatus then
        print(error)
    end
end)

RegisterNetEvent('esx:setAccountMoney')
AddEventHandler('esx:setAccountMoney', function(account)
    if account.name == "bank" or account.name == "money" then
        SendNUIMessage({
            script = 'hud',
            action = 'setup',
            playerId = GetPlayerServerId(PlayerId()),
            money = account.name == "money" and account.money or getMoney('money'),
            bank = account.name == "bank" and account.money or getBankMoney()
        })
    end
end)

RegisterNetEvent('cc_core:hud:useItem')
AddEventHandler('cc_core:hud:useItem', function(type, value, animation)
    if type == 'food' then
        if animation then
            playAnimation('food')
        end

        currentFood = currentFood + value

        if currentFood >= 100 then
            currentFood = 100
        end

        SendNUIMessage({
            script = 'hud',
            action = 'food',
            currentFood = currentFood
        })
    elseif type == 'thirst' then
        if animation then
            playAnimation('thirst')
        end

        currentThirst = currentThirst + value

        if currentThirst >= 100 then
            currentThirst = 100
        end

        SendNUIMessage({
            script = 'hud',
            action = 'thirst',
            currentThirst = currentThirst
        })
    elseif type == 'both' then
        if animation then
            playAnimation('thirst')
        end

        currentThirst = currentThirst + value
        currentFood = currentFood + value

        if currentFood >= 100 then
            currentFood = 100
        end

        if currentThirst >= 100 then
            currentThirst = 100
        end

        SendNUIMessage({
            script = 'hud',
            action = 'thirst',
            currentThirst = currentThirst
        })

        SendNUIMessage({
            script = 'hud',
            action = 'food',
            currentFood = currentFood
        })
    end

    Citizen.Wait(1000)
    SetResourceKvpInt('cc_core:eat:food2', currentFood)
    Citizen.Wait(1000)
    SetResourceKvpInt('cc_core:eat:thirst2', currentThirst)
end)

RegisterCommand("id", function(source, args, rawCommand)
    local playerPed = PlayerPedId()
    TriggerEvent("cc_core:hud:notify", "info", "Deine ID:", tostring(GetPlayerServerId(PlayerId())))
end)

RegisterCommand("ids", function(source, args, rawCommand)
    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()

    if closestPlayer ~= -1 and closestDistance <= 3.0 then
        TriggerEvent("cc_core:hud:notify", "info", "Seine ID:", tostring(GetPlayerServerId(closestPlayer)))
    else
        TriggerEvent("cc_core:hud:notify", "error", "Information", "Keine Spieler in deiner Nähe")
    end
end)

RegisterCommand('ooc', function(source, args)
    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
    local argString = table.concat(args, " ")

    if not string.find(argString, "<") then
        if closestPlayer ~= -1 and closestDistance <= 10.0 then
            TriggerServerEvent('cc_core:hud:ooc', GetPlayerServerId(closestPlayer), argString)
        else
            TriggerEvent("cc_core:hud:notify", "error", "Information", "Keine Spieler in der Nähe")
        end
    end
end)

AddEventHandler('SaltyChat_VoiceRangeChanged', function(voiceRange, index, availableVoiceRanges)
    voiceMode = newTalkingRange
    hideTime = 150
    hideTime2 = 60

    local plyState = Player(LocalPlayer).state

    TriggerEvent('cc_core:hud:notify', 'info', "SaltyChat", 'Sprachreichweite: ' .. voiceRange .. 'M')
    changeRangeSmoothly(voiceRange)

    voice = voiceRange

    if index == 0 then
        SendNUIMessage({
            script = 'hud',
            action = 'saltychat',
            type = 'range',
            range = 1
        })
    elseif index == 1 then
        SendNUIMessage({
            script = 'hud',
            action = 'saltychat',
            type = 'range',
            range = 2
        })
    elseif index == 2 then
        SendNUIMessage({
            script = 'hud',
            action = 'saltychat',
            type = 'range',
            range = 3
        })
    elseif index == 3 then
        SendNUIMessage({
            script = 'hud',
            action = 'saltychat',
            type = 'range',
            range = 4
        })
    end

    change = true
end)

function drawSalty()
    if drawing then return end

    drawing = true
    fadeIn = true

    CreateThread(function()
        while hideTime > 0 do
            Wait(0)
            hideTime = hideTime - 1
        end

        fadeIn = false
        fadeOut = true

        while hideTime2 > 0 do
            Wait(0)
            hideTime2 = hideTime2 - 1
        end

        drawing = false
    end)

    CreateThread(function()
        while drawing do
            if fadeIn then
                alpha = math.min(alpha + 5, maxAlpha)
            elseif fadeOut then
                alpha = math.max(alpha - 5, 0)
            end

            local pos = GetEntityCoords(PlayerPedId())
            DrawMarker(1, pos.x, pos.y, pos.z - 0.7, 0, 0, 0, 0, 0, 0, currRange * 2, currRange * 2, 0.5, 255, 156, 35, alpha, false, false)

            if not fadeIn and alpha == 0 then
                fadeOut = false
            end

            Wait(0)
        end
    end)
end

function changeRangeSmoothly(range)
    local target = range
    targetRange = range

    local step = (target - currRange) / 10
    local duration = 100

    for i = 1, 10 do
        currRange = currRange + step
        drawSalty()
        Wait(duration / 10)
    end

    currRange = target
    drawSalty()
end

AddEventHandler('SaltyChat_MicStateChanged', function(isMicrophoneEnabled)
    SendNUIMessage({
        script = 'hud',
        action = 'saltychat',
        type = 'mic',
        state = isMicrophoneEnabled
    })
end)

AddEventHandler('SaltyChat_TalkStateChanged', function(isTalking)
    SendNUIMessage({
        script = 'hud',
        action = 'saltychat',
        type = 'talking',
        state = isTalking
    })
end)

AddEventHandler('SaltyChat_RadioChannelChanged', function(radioChannel, isPrimaryChannel)
    print(radioChannel)
    if radioChannel and radioChannel ~= '0' then
        SendNUIMessage({
            script = 'hud',
            action = 'saltychat',
            type = 'radio',
            state = true
        })
    else
        SendNUIMessage({
            script = 'hud',
            action = 'saltychat',
            type = 'radio',
            state = false
        })
    end
end)

RegisterNUICallback('hud/escape', function()
    SetNuiFocus(false, false)
end)

RegisterNUICallback('hud/enterCommand', function(data, cb)
    SetNuiFocus(false, false)

    if data.message:sub(1, 1) == '/' then
        ExecuteCommand(data.message:sub(2))
    end
end)

RegisterKey('openChat', 'Open the chat', 't')

RegisterCommand('openChat', function()
   if exports["lb-phone"]:IsOpen() then
       return
    end

    SetNuiFocus(true, true)

    SendNUIMessage({
        script = 'hud',
        action = 'openChat'
    })
end)

Citizen.CreateThread(function()
    ReplaceHudColourWithRgba(116, 255, 165, 0, 255)
end)

RegisterCommand("hud", function()
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = "SettingsMenu",
        state = true,
    })
end)

RegisterCommand("settings", function()
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = "SettingsMenu",
        state = true,
    })
end)

local function sendTimeToUI()
    local hour = GetClockHours()
    local isDay = hour >= 7 and hour < 21

    SendNUIMessage({
        type = "updateTheme",
        isDay = isDay
    })
end

Citizen.CreateThread(function()
    while true do
        sendTimeToUI()
        Citizen.Wait(5000)
    end
end)

local displayStars = false

RegisterNetEvent("updateStarsUI")
AddEventHandler("updateStarsUI", function(stars)
    if stars > 0 then
        displayStars = true
        SendNUIMessage({
            action = "updateStars",
            stars = stars
        })
    else
        displayStars = false
        SendNUIMessage({
            action = "updateStars",
            stars = 0
        })
    end
end)

RegisterNUICallback("closeUI", function(data, cb)
    displayStars = false
    cb("ok")
end)

RegisterNetEvent('hudsettings:checkTeamler')
AddEventHandler('hudsettings:checkTeamler', function(status)
    isTeamler = status
    SendNUIMessage({
        action = "SetTeamlerStatus",
        isTeamler = isTeamler
    })
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(5000)
        TriggerServerEvent('hudsettings:requestTeamlerStatus')
    end
end)

RegisterNUICallback("closeHudSettings", function()
    SetNuiFocus(false, false)
    SendNUIMessage({ action = "SettingsMenu", state = false })
end)


RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
    ESX.PlayerData.job = job
end)

local postals = nil

CreateThread(function()
    postals = LoadResourceFile(GetCurrentResourceName(), 'postals.json')
    postals = json.decode(postals)
    for i, postal in ipairs(postals) do
        postals[i] = { x = postal.x, y = postal.y, code = postal.code }
    end
end)

local nearestPostalText = ""
local nearestPostalCoords = vector3(0.0, 0.0, 0.0)

CreateThread(function()
    while postals == nil do Wait(1) end

    local delay = 500


    local postals = postals
    local _total = #postals

    while true do
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local _nearestIndex, _nearestD
        coords = vec(coords[1], coords[2])

        for i = 1, _total do
            local D = #(coords - vec(postals[i].x, postals[i].y))
            if not _nearestD or D < _nearestD then
                _nearestIndex = i
                _nearestD = D
            end
        end

        local _code = postals[_nearestIndex].code
        nearest = { code = _code, dist = _nearestD }
        nearestPostalText = _code
        nearestPostalCoords = vector3(postals[_nearestIndex].x, postals[_nearestIndex].y, 0.0)
        Wait(delay)
    end
end)

CreateThread(function()
    local sleep = 1500
    while true do
        local ped = PlayerPedId()
        local playerCoords = GetEntityCoords(ped)
        Wait(sleep)
        local streetNameHash, crossingRoadHash = GetStreetNameAtCoord(playerCoords.x, playerCoords.y, playerCoords.z, Citizen.ResultAsInteger(), Citizen.ResultAsInteger())
        local streetName, crossingRoad = GetStreetNameFromHashKey(streetNameHash), GetStreetNameFromHashKey(crossingRoadHash)
        SendNUIMessage({
            script = 'hud',
            action = 'jobhud',
            type = 'infos',
            job = ESX.PlayerData.job.label,
            rang = ESX.PlayerData.job.grade_label,
            street = streetName,
            plz = nearestPostalText
        })
    end
end)
]]