local function getVehiclePrice(vehicles, vehicleName)
    for k, v in pairs(vehicles) do
        if v.name == vehicleName then
            return v.price
        end
    end
    
    return 0
end

RegisterServerEvent('cc_core:vermietung:rentVehicle')
AddEventHandler('cc_core:vermietung:rentVehicle', function(vehicle, index, coords)
    local playerId = source
    local price = getVehiclePrice(Config_Vermietung.Zones[index].vehicles, vehicle)

    if price ~= 0 then
        if ESX.GetPlayerMoney(playerId) >= price then
            ESX.RemovePlayerMoney(playerId, price, GetCurrentResourceName())
            local rentVehicle = CreateVehicle(GetHashKey(vehicle), coords.x, coords.y, coords.z, coords.w, true, true)
            SetPedIntoVehicle(GetPlayerPed(playerId), rentVehicle, -1)
            SetVehicleNumberPlateText(rentVehicle, 'FinalU21')
            SetVehicleCustomPrimaryColour(rentVehicle, 255, 156, 35)
        end
    end
end)

--clientcode
vermietungCode = [[
local show = false

Citizen.CreateThread(function()
    for k, v in pairs(Config_Vermietung.Zones) do

        if v.blip then
            local blip = AddBlipForCoord(v.buy.x, v.buy.y, v.buy.z)
            SetBlipSprite(blip, v.blip.id)
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, 0.8)
            SetBlipColour(blip, v.blip.color)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName('STRING')
            AddTextComponentString(v.blip.name)
            EndTextCommandSetBlipName(blip)
        end

        while not HasModelLoaded(GetHashKey("s_m_y_armymech_01")) do
            RequestModel(GetHashKey("s_m_y_armymech_01"))
            Citizen.Wait(500)
        end

        local ped = CreatePed(0, GetHashKey("s_m_y_armymech_01"), v.buy.x, v.buy.y, v.buy.z, v.buy.w, false, true)
        print('CREATEPED', GetCurrentResourceName())
        SetEntityInvincible(ped, true)
        SetBlockingOfNonTemporaryEvents(ped, true)
        FreezeEntityPosition(ped, true)
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(4)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)

        for k, v in pairs(Config_Vermietung.Zones) do
            local distance = #(coords - vector3(v.buy.x, v.buy.y, v.buy.z))

            if distance <= 2.0 then
                local canUse = false

                if Config_Vermietung.Zones[k].job then
                    if ESX.PlayerData.job.name == Config_Vermietung.Zones[k].job or ESX.PlayerData.job3.name then
                        canUse = true
                    end
                else
                    canUse = true
                end

                letSleep = false
                inRange = true

                if IsControlJustReleased(0, 38) then
                    if canUse then
                        local elements = {}

                        for k1, v1 in pairs(v.vehicles) do
                            local label = GetDisplayNameFromVehicleModel(joaat(v1.name)) .. ' ' .. v1.price .. '$'

                            if v1.name == 'longboard' then
                                label = 'Longboard ' .. v1.price .. '$'
                            end
    
                            table.insert(elements, {
                                label = label,
                                vehicle = v1.name,
                                coords = v.spawn,
                                value = k
                            })
                        end
    
                        ESX.UI.Menu.CloseAll()
    
                        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'core_vermietung', {
                            title = 'Vermietung',
                            align = 'top-left',
                            elements = elements
                        }, function(data, menu)
                            if data.current.vehicle == 'longboard' then
                                TriggerEvent('cc_clsguide:spawnBoard')
                            else
                                TriggerServerEvent("cc_core:vermietung:rentVehicle", data.current.vehicle, data.current.value, data.current.coords)
                            end

                            menu.close()
                        end, function(data, menu)
                            menu.close()
                        end)
                    else
                        Notify('Vermietung', 'Du kannst hier nichts machen', 'info')
                    end
                end
            end
        end

        helpNotify(inRange, show, 'Drücke E um ein Fahrzeug zu mieten', function(bool)
            show = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)
]]