Config_VehicleShop = {}

Config_VehicleShop.Shops = {
    ['normal'] = {
        name = 'vehicle',
        label = 'Car Shop',
        position = vector3(-57.1572, -1096.1903, 26.4224),
        camPosition = vector3(-52.3667, -1095.0632, 27.4223),
        camRotation = vector3(1.0, 0.0, 250.0568),
        vehicleSpawn = vector3(-41.3816, -1098.7827, 26.4223),
        vehicleHeading = 87.4103,
        testVehicle = vector3(-45.1389, -1076.2151, 26.7114),
        testHeading = 70.1155,

        blip = {
            sprite = 326,
            scale = 0.8,
            text = 'Final Allstars Auto Shop'
        },

        categorys = {
            { name = 'compacts', label = 'Compacts' },
            { name = 'coupes', label = 'Coupés' },
            { name = 'muscle', label = 'Muscle' },
            { name = 'offroad', label = 'Off Road' },
            { name = 'sedans', label = 'Sedans' },
            { name = 'sports', label = 'Sports' },
            { name = 'sportsclassics', label = 'Sports Classics' },
            { name = 'super', label = 'Super' },
            { name = 'suvs', label = 'SUVs' },
            { name = 'vans', label = 'Vans' }
        },

        vehicles = {
            { name = 'brioso', label = 'Brioso R/A', category = 'compacts', price = 3000, type = 'car' },
            { name = 'issi2', label = 'Issi', category = 'compacts', price = 5000, type = 'car' },
            { name = 'felon', label = 'Felon', category = 'coupes', price = 9000, type = 'car' },
            { name = 'sentinel2', label = 'Sentinel XS', category = 'coupes', price = 12500, type = 'car' },
            { name = 'zion', label = 'Zion', category = 'coupes', price = 17000, type = 'car' },
            { name = 'exemplar', label = 'Exemplar', category = 'coupes', price = 180000, type = 'car' },
            { name = 'jackal', label = 'Jackal', category = 'coupes', price = 22000, type = 'car' },
            { name = 'gauntlet', label = 'Gauntlet', category = 'muscle', price = 13000, type = 'car' },
            { name = 'chino', label = 'Chino', category = 'muscle', price = 12000, type = 'car' },
            { name = 'dominator3', label = 'Dominator', category = 'muscle', price = 23000, type = 'car' },
            { name = 'faction2', label = 'Faction Rider', category = 'muscle', price = 9000, type = 'car' },
            { name = 'bodhi2', label = 'bodhi2', category = 'offroad', price = 3000, type = 'car' },
            { name = 'washington', label = 'Washington', category = 'sedans', price = 4000, type = 'car' },
            { name = 'buffalo2', label = 'Buffalo S', category = 'sports', price = 25000, type = 'car' },
            { name = 'omnis', label = 'Omnis', category = 'sports', price = 27000, type = 'car' },
            { name = 'jester', label = 'Jester', category = 'sports', price = 35000, type = 'car' },
            { name = 'sentinel3', label = 'Sentinel3', category = 'sports', price = 17000, type = 'car' },
            { name = 'banshee2', label = 'Banshee 900R', category = 'super', price = 19000, type = 'car' },
            { name = 'baller7', label = 'Baller', category = 'suvs', price = 32000, type = 'car' },
            { name = 'dubsta3', label = 'Dubsta', category = 'suvs', price = 45000, type = 'car' },
            { name = 'gburrito2', label = 'Burrito', category = 'vans', price = 15000, type = 'car' },
            { name = 'zentorno', label = 'Zentorno', category = 'super', price = 300000, type = 'car' },
            { name = 't20', label = 'T20', category = 'super', price = 300000, type = 'car' },
            { name = 'sc1', label = 'SC1', category = 'super', price = 250000, type = 'car' },
            { name = 'tempesta', label = 'Tempesta', category = 'super', price = 250000, type = 'car' },
            { name = 'vstr', label = 'VSTR', category = 'sedans', price = 150000, type = 'car' }
        }
    },

    ['luxus'] = {
        name = 'vehicle',
        label = 'Vehicle Shop',
        position = vector3(-803.5289, -224.0806, 37.2257),
        camPosition = vector3(-796.4543, -204.3861, 58.5377),
        camRotation = vector3(1.0, 0.0, 210.7031),
        vehicleSpawn = vector3(-790.9207, -213.7938, 57.5377),
        vehicleHeading = 49.1685,
        testVehicle = vector3(-804.7132, -154.0321, 37.6950),
        testHeading = 28.2863,

        blip = {
            sprite = 523,
            scale = 0.8,
            text = 'Premium Deluxe Luxus Autohaus'
        },

        categorys = {
            { name = 'vanilla', label = 'Vanilla' },
            { name = 'extra', label = 'Extras' },
        },

        vehicles = {
            { name = 'huntley', label = 'Huntley', category = 'vanilla', price = 1000000, type = 'car' },
            { name = 'streiter', label = 'Streiter', category = 'vanilla', price = 1200000, type = 'car' },
            { name = 'tigon', label = 'Tigon', category = 'vanilla', price = 1500000, type = 'car' },
            { name = 'toros', label = 'Toros', category = 'vanilla', price = 1500000, type = 'car' },
            { name = 'zentorno', label = 'Zentorno', category = 'vanilla', price = 1600000, type = 'car' },
            { name = 'thrax', label = 'thrax', category = 'vanilla', price = 1650000, type = 'car' },
            { name = 'emerus', label = 'emerus', category = 'vanilla', price = 1680000, type = 'car' },
            { name = 'rocoto', label = 'rocoto', category = 'vanilla', price = 1750000, type = 'car' },
            { name = 'ninef', label = 'Ninef', category = 'vanilla', price = 2000000, type = 'car' },
            { name = 'neon', label = 'Neon', category = 'vanilla', price = 2500000, type = 'car' },
            { name = 'ninef2', label = 'Ninef V2', category = 'vanilla', price = 3000000, type = 'car' },
            { name = 'cyclone', label = 'Cyclone', category = 'vanilla', price = 3500000, type = 'car' },
            { name = 'seven70', label = 'Seven 70', category = 'vanilla', price = 4000000, type = 'car' },
            { name = 'airglider', label = 'Airglider', category = 'extra', price = 120000, type = 'car' },
            { name = 'arcocopter', label = 'Arcocopter', category = 'extra', price = 150000, type = 'car' },
            { name = 'bansheeaas', label = 'Bansheeaas', category = 'extra', price = 160000, type = 'car' },
            { name = 'bestiagt', label = 'Bestiagt', category = 'extra', price = 180000, type = 'car' },
            { name = 'clubgtr', label = 'Club GTR', category = 'extra', price = 140000, type = 'car' },
            { name = 'cometcup', label = 'Comet Cup', category = 'extra', price = 220000, type = 'car' },
            { name = 'contenderc', label = 'Contender C', category = 'extra', price = 135000, type = 'car' },
            { name = 'coquette4c', label = 'Coquette 4C', category = 'extra', price = 170000, type = 'car' },
            { name = 'cyphart', label = 'Cyphart', category = 'extra', price = 160000, type = 'car' },
            { name = 'deluxogt', label = 'Deluxo GT', category = 'extra', price = 280000, type = 'car' },
            { name = 'doubled', label = 'Doubled', category = 'extra', price = 90000, type = 'car' },
            { name = 'dubsta22', label = 'Dubsta 22', category = 'extra', price = 140000, type = 'car' },
            { name = 'eleghyr7', label = 'Elegy R7', category = 'extra', price = 190000, type = 'car' },
            { name = 'elegyanshi', label = 'Elegy Anshi', category = 'extra', price = 220000, type = 'car' },
            { name = 'elegyw', label = 'Elegy W', category = 'extra', price = 145000, type = 'car' },
            { name = 'elegyx', label = 'Elegy X', category = 'extra', price = 170000, type = 'car' },
            { name = 'enduromk2', label = 'Enduro Mk2', category = 'extra', price = 125000, type = 'car' },
            { name = 'es550', label = 'ES550', category = 'extra', price = 165000, type = 'car' },
            { name = 'futope', label = 'Futope', category = 'extra', price = 80000, type = 'car' },
            { name = 'infernus2se', label = 'Infernus 2SE', category = 'extra', price = 300000, type = 'car' },
            { name = 'infernuswb', label = 'Infernus WB', category = 'extra', price = 260000, type = 'car' },
            { name = 'italigton', label = 'Italigton', category = 'extra', price = 180000, type = 'car' },
            { name = 'italiisxrod', label = 'Itali SX Rod', category = 'extra', price = 230000, type = 'car' },
            { name = 'jubilee8', label = 'Jubilee 8', category = 'extra', price = 200000, type = 'car' },
            { name = 'kanjor', label = 'Kanjor', category = 'extra', price = 100000, type = 'car' },
            { name = 'luxicopter', label = 'Luxicopter', category = 'extra', price = 300000, type = 'car' },
            { name = 'nerops', label = 'Nerops', category = 'extra', price = 110000, type = 'car' },
            { name = 'OraclePack', label = 'Oracle Pack', category = 'extra', price = 150000, type = 'car' },
            { name = 'oracsle', label = 'Oracsle', category = 'extra', price = 130000, type = 'car' },

        }
    },

    ['heli'] = {
        name = 'vehicle',
        label = 'Heli Shop',
        position = vector3(-940.23, -2964.28, 13.95),
        camPosition = vector3(-982.2763, -2973.7504, 14.9451),
        camRotation = vector3(1.0, 0.0, 240.6278),
        vehicleSpawn = vector3(-966.8717, -2982.7449, 13.9451),
        vehicleHeading = 77.3366,
        testVehicle = vector3(-966.8717, -2982.7449, 13.9451),
        testHeading = 55.9012,

        blip = {
            sprite = 43,
            scale = 0.8,
            text = 'Final Allstars Heli shop'
        },

        categorys = {
            { name = 'heli', label = 'Hubschrauber' }
        },

        vehicles = {

            { name = 'supervolito', label = 'Super Volito', category = 'heli', price = 520000, type = 'heli' },
            { name = 'buzzard2', label = 'Buzzard', category = 'heli', price = 300000, type = 'heli' }
            
        }
    },

    ['lkw'] = {
        name = 'vehicle',
        label = 'LKW Shop',
        position = vector3(900.3514, -1154.7666, 25.1605),
        camPosition = vector3(897.8434, -1167.4882, 26.0311),
        camRotation = vector3(1.0, 0.0, 89.7389),
        vehicleSpawn = vector3(880.9335, -1167.8167, 24.9857),
        vehicleHeading = 289.2863,
        testVehicle = vector3(907.4848, -1169.5574, 25.2455),
        testHeading = 279.5967,

        blip = {
            sprite = 67,
            scale = 0.8,
            text = 'Final Allstars LKW Shop'
        },

        categorys = {
            { name = 'service', label = 'Service' },
            { name = 'commercial', label = 'Commercial' }
        },

        vehicles = {
            { name = 'rallytruck', label = 'Rallytruck', category = 'service', price = 60000, type = 'car' },
            { name = 'pounder2', label = 'pounder2', category = 'commercial', price = 100000, type = 'car' },
        }
    },

    ['boat'] = {
        name = 'vehicle',
        label = 'Boat Shop',
        position = vector3(-725.2249, -1374.4298, 1.5952),
        camPosition = vector3(-738.4078, -1369.8242, 1.8740),
        camRotation = vector3(1.0, 0.0, 322.6995),
        vehicleSpawn = vector3(-730.8514, -1359.7325, 0),
        vehicleHeading = 161.2426,
        testVehicle = vector3(-730.8514, -1359.7325, 0),
        testHeading = 150.2426,

        blip = {
            sprite = 427,
            scale = 0.8,
            text = 'Final Allstars Boot Shop'
        },

        categorys = {
            { name = 'boat', label = 'Wasserfahrzeuge' }
        },

        vehicles = {
            { name = 'seashark', label = 'Jetski', category = 'boat', price = 5000, type = 'boat' },
            { name = 'dinghy3', label = 'Dinghy', category = 'boat', price = 7500, type = 'boat' },
            { name = 'speeder', label = 'Speeder', category = 'boat', price = 50000, type = 'boat' },
        }
    },

    ['bike'] = {
        name = 'vehicle',
        label = 'Bike Shop',
        position = vector3(306.1475, -1162.9221, 29.2919),
        camPosition = vector3(333.7170, -1148.1477, 30.2919),
        camRotation = vector3(1.0, 0.0, 181.8289),
        vehicleSpawn = vector3(333.9467, -1162.8043, 29.2919),
        vehicleHeading = 19.8279,
        testVehicle = vector3(317.0420, -1147.8530, 29.2919),
        testHeading = 3.2387,

        blip = {
            sprite = 348,
            scale = 0.8,
            text = 'Final Allstars Motorrad Shop'
        },

        categorys = {
            { name = 'moto', label = 'Motorräder' }
        },

        vehicles = {
            { name = 'bmx', label = 'BMX', category = 'moto', price = 100, type = 'car' },
            { name = 'faggio', label = 'Faggio Sport', category = 'moto', price = 1000, type = 'car' },
            { name = 'bf400', label = 'BF400', category = 'moto', price = 8000, type = 'car' },
            { name = 'bati', label = 'Bati 801', category = 'moto', price = 12000, type = 'car' },
        }
    }
}