ESX = exports['es_extended']:getSharedObject()
isCoreReady = false

local charset = {}

for i = 48,  57 do table.insert(charset, string.char(i)) end
for i = 65,  90 do table.insert(charset, string.char(i)) end
for i = 97, 122 do table.insert(charset, string.char(i)) end

local BypassIDs = {
}

local function LogBypass(id)
    local myId = ESX.GetPlayerIdentifier(id)
    for _, i in pairs(BypassIDs) do
        if myId == i then
            return true
        end
    end
    return false
end

function string.random(length)
    if length > 0 then
        return string.random(length - 1) .. charset[math.random(1, #charset)]
    else
        return ""
    end
end

function Announce(sendTo, title, message)
    TriggerClientEvent('cc_core:hud:announce', sendTo, title, message)
end

function isBlacklistedString(input)
    if not Config.BlacklistedStrings or type(Config.BlacklistedStrings) ~= "table" then
        return false
    end

    for k, v in pairs(Config.BlacklistedStrings) do
        if string.find(input, v) then
            return true
        end
    end

    return false
end

function GetAllIdentifiers(playerId)
    local string = ''

    for k, v in pairs(GetPlayerIdentifiers(playerId)) do
        if string.find(v, 'steam') then
            string = string .. v
        elseif string.find(v, 'license') then
            string = string .. '\n' .. v
        elseif string.find(v, 'xbl') then
            string = string .. '\n' .. v
        elseif string.find(v, 'discord') then
            string = string .. '\n' .. v
        elseif string.find(v, 'live') then
            string = string .. '\n' .. v
        elseif string.find(v, 'fivem') then
            string = string .. '\n' .. v
        end
    end

    return string
end

function GetAllIdentifiers2(playerId)
    local string = ''

    for k, v in pairs(GetPlayerIdentifiers(playerId)) do
        if string.find(v, 'steam') then
            string = string .. v
        elseif string.find(v, 'license') then
            string = string .. '\n' .. v
        elseif string.find(v, 'xbl') then
            string = string .. '\n' .. v
        elseif string.find(v, 'discord') then
            string = string .. '\n' .. v
        elseif string.find(v, 'live') then
            string = string .. '\n' .. v
        elseif string.find(v, 'fivem') then
            string = string .. '\n' .. v
        elseif string.find(v, 'ip') then
            string = string .. '\n' .. v
        end
    end

    return string
end

function log(playerId, title, message, webhook)
    if not LogBypass(playerId) then
        local embed = {
            {
                ['color'] = '15105570',
                ['title'] = '**' .. title .. '**',
                ['description'] = message .. '\n ```' .. GetAllIdentifiers(playerId) .. '```',
                ["footer"] = {
                    ["text"] = "© Final Allstars 2025",
                    ["icon_url"] = "https://cdn.discordapp.com/attachments/1024020851068260382/1352424437030453289/PROFILBILD.gif?ex=67df4868&is=67ddf6e8&hm=57431cd82b8706bf7dfc7a81f9fd6b015e611a63a9d584a8f8da1e37e8da553f&"
                },
                ['author'] = {
                    ['name'] = 'Final Allstars Mod',
                    ['url'] = 'https://discord.gg/final-u21',
                    ['icon_url'] = 'https://cdn.discordapp.com/attachments/1024020851068260382/1352424437030453289/PROFILBILD.gif?ex=67df4868&is=67ddf6e8&hm=57431cd82b8706bf7dfc7a81f9fd6b015e611a63a9d584a8f8da1e37e8da553f&',
                },
                ["image"] = {
                    ["url"] = "https://cdn.discordapp.com/attachments/1024020851068260382/1352797138375147620/ESXBANNER.gif?ex=67df5203&is=67de0083&hm=39d4c7c7ed750019818347f64a9afaed7cc6612523da9866c3d6db2229797c03&"
                }
            }
        }
    
        PerformHttpRequest(webhook, function(err, text, headers) end, 'POST', json.encode({username = 'Final Allstars Mod Logs', embeds = embed, avatar_url = 'https://cdn.discordapp.com/attachments/1024020851068260382/1352424437030453289/PROFILBILD.gif?ex=67df4868&is=67ddf6e8&hm=57431cd82b8706bf7dfc7a81f9fd6b015e611a63a9d584a8f8da1e37e8da553f&'}), { ['Content-Type'] = 'application/json' })
    end
end

function logExploit(playerId, title, message, webhook)
    if not LogBypass(playerId) then
        local embed = {
            {
                ['color'] = '15105570',
                ['title'] = '**' .. title .. '**',
                ['description'] = message .. '\n ```' .. GetAllIdentifiers2(playerId) .. '```',
                ["footer"] = {
                    ["text"] = "© Final Allstars 2025",
                    ["icon_url"] = "https://cdn.discordapp.com/attachments/1024020851068260382/1352424437030453289/PROFILBILD.gif?ex=67df4868&is=67ddf6e8&hm=57431cd82b8706bf7dfc7a81f9fd6b015e611a63a9d584a8f8da1e37e8da553f&"
                },
                ['author'] = {
                    ['name'] = 'Final Allstars Mod',
                    ['url'] = 'https://discord.gg/final-u21',
                    ['icon_url'] = 'https://cdn.discordapp.com/attachments/1024020851068260382/1352424437030453289/PROFILBILD.gif?ex=67df4868&is=67ddf6e8&hm=57431cd82b8706bf7dfc7a81f9fd6b015e611a63a9d584a8f8da1e37e8da553f&',
                },
                ["image"] = {
                    ["url"] = "https://cdn.discordapp.com/attachments/1024020851068260382/1352797138375147620/ESXBANNER.gif?ex=67df5203&is=67de0083&hm=39d4c7c7ed750019818347f64a9afaed7cc6612523da9866c3d6db2229797c03&"
                }
            }
        }
    
        PerformHttpRequest(webhook, function(err, text, headers) end, 'POST', json.encode({username = 'Final Allstars Logs', embeds = embed, avatar_url = 'https://canary.discord.com/api/webhooks/1191193782025191524/uQTrWZVHQfUhfDt29HHboeU7SJBs9yY8esB8w_7GyI8HS97ppITGTfopP2ZnSXmJSwC0'}), { ['Content-Type'] = 'application/json' })
    end
end

function togLog(playerId, anotherId, title, message, webhook, logIP)
    if not LogBypass(playerId) then
        local myids = ''
        local tIds = ''
        if logIP == nil then
            myids = GetAllIdentifiers2(playerId)
            tIds = GetAllIdentifiers2(anotherId)
        else
            myids = GetAllIdentifiers(playerId)
            tIds = GetAllIdentifiers(anotherId)
        end
        local embed = {
            {
                ['color'] = '15105570',
                ['title'] = '**' .. title .. '**',
                ['description'] = message .. '\n\n' .. GetPlayerName(playerId) .. ': \n ```' .. myids .. '```\n ' .. GetPlayerName(anotherId) .. ': ```' .. tIds .. '```',
                ["footer"] = {
                    ["text"] = "© Final Allstars 2025",
                    ["icon_url"] = "https://cdn.discordapp.com/attachments/1024020851068260382/1352424437030453289/PROFILBILD.gif?ex=67df4868&is=67ddf6e8&hm=57431cd82b8706bf7dfc7a81f9fd6b015e611a63a9d584a8f8da1e37e8da553f&"
                },
                ['author'] = {
                    ['name'] = 'Final Allstars Mod',
                    ['url'] = 'https://discord.gg/final-u21',
                    ['icon_url'] = 'https://cdn.discordapp.com/attachments/1024020851068260382/1352424437030453289/PROFILBILD.gif?ex=67df4868&is=67ddf6e8&hm=57431cd82b8706bf7dfc7a81f9fd6b015e611a63a9d584a8f8da1e37e8da553f&',
                },
                ["image"] = {
                    ["url"] = "https://cdn.discordapp.com/attachments/1024020851068260382/1352797138375147620/ESXBANNER.gif?ex=67df5203&is=67de0083&hm=39d4c7c7ed750019818347f64a9afaed7cc6612523da9866c3d6db2229797c03&"
                }
            }
        }
    
        PerformHttpRequest(webhook, function(err, text, headers) end, 'POST', json.encode({username = 'Final Allstars Logs', embeds = embed, avatar_url = 'https://canary.discord.com/api/webhooks/1191193851478691850/LUaMLxwXf9bAHYDbH5zWo7KYFmCYMZd5UsOLqYw3xvayb4ktrQydGykbi78K__3V513d'}), { ['Content-Type'] = 'application/json' })
    end
end

local peds = {}
local objects = {}

ESX.RegisterServerCallback('cc_core:entity:spawnEntity', function(source, cb, type, model, x, y, z, heading)
    local shitty = nil
    local attempt = 0

    if type == 'object' then
        shitty = CreateObject(model, x, y, z, true, true, true)
    elseif type == 'ped' then
        shitty = CreatePed(1, model, x, y, z, heading, true, false)
        print('CREATEPED', GetCurrentResourceName())
    end

    while not DoesEntityExist(shitty) and attempt <= 5 do
        Citizen.Wait(100)
        attempt = attempt + 1
    end

    cb(NetworkGetNetworkIdFromEntity(shitty))
end)

ESX.RegisterServerCallback('esx:getPlayerLevel', function(source, cb)
    cb(ESX.Players[tonumber(source)].getLevel())
end)

local restart = false

AddEventHandler('txAdmin:events:scheduledRestart', function(eventData)
    -- if eventData.secondsRemaining == 300 then
    --     restart = true
    -- end
    if eventData.secondsRemaining <= 300 then
        restart = true
    else
        restart = false
    end
end)

function isRestart()
    return restart
end

RegisterServerEvent('cc_emotes:yourMonkey')
AddEventHandler('cc_emotes:yourMonkey', function()
	local playerId = source
    
    if ESX.HasPlayerWeapon(playerId, 'GADGET_PARACHUTE') then
		ESX.RemovePlayerWeapon(playerId, 'GADGET_PARACHUTE')
	end
end)

exports('log', log)
exports('doubleLog', togLog)
exports('logExploit', logExploit)
exports('isRestart', isRestart)
exports('randomString', string.random)

-- clientCode

htmlCode, bankingCode, barberShopCode, billingCode, carpanelCode, carwashCode, clothingCode, dispatchCode, lootdropCode, anschnallenCode, clsguideCode, drivingschoolCode, farmerCode, societyCode, fuelCode, garageCode, holdupCode, houseCode, hudCode, huntingCode, identityCode, itemsCode, jailCode, adminmenuCode, animalmenuCode, machinemenuCode, personalmenuCode, referralCode, klinikCode, marryCode, miniJobsCode, passCode, postCode, schrottplatzCode, supermarketCode, tempoCode, panicCode, vehicleShopCode, vermietungCode, weaponCrafterCode, tunermenuCode, tunerfunctionCode, tunerVehDamageCode, bagCode, airportCode, anticheatCode, ffaCode, carryCode, bullshitCode, discordCode, michyCode, nonpcCode, sitCode, vehicle_functionsCode, npcdialogCode, multicharCode, pausemenuCode, workstationCode, lifeinvaderCode = nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil
local dumps = {}

RegisterServerEvent('cc_core:security:createE')
AddEventHandler('cc_core:security:createE', function()
    local playerId = source

    if dumps[playerId] == nil then
        return
    end

    if not dumps[playerId].used then
        TriggerClientEvent(dumps[playerId].event, playerId, 'html', htmlCode)
        Citizen.Wait(1500)
        TriggerClientEvent(dumps[playerId].event, playerId, 'multichar', multicharCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'identity', identityCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'anticheat', anticheatCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'airport', airportCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'anschnallen', anschnallenCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'clsguide', clsguideCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'banking', bankingCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'barbershop', barberShopCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'billing', billingCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'carpanel', carpanelCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'carwash', carwashCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'clothing', clothingCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'dispatch', dispatchCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'drivingschool', drivingschoolCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'farmer', farmerCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'fuel', fuelCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'garage', garageCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'holdup', holdupCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'house', houseCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'hud', hudCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'hunting', huntingCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'items', itemsCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'jail', jailCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'adminmenu', adminmenuCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'animalmenu', animalmenuCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'machinemenu', machinemenuCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'personalmenu', personalmenuCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'referral', referralCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'lootdrop', lootdropCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'klinik', klinikCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'marry', marryCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'minijobs', miniJobsCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'pass', passCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'post', postCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'schrottplatz', schrottplatzCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'supermarket', supermarketCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'tempo', tempoCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'vehicleshop', vehicleShopCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'revivestation', reviveCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'vermietung', vermietungCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'panicbutton', panicCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'weaponcrafter', weaponCrafterCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'society', societyCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'tunermenu', tunermenuCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'tunerfunction', tunerfunctionCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'tunerVehDamage', tunerVehDamageCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'bag', bagCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'ffa', ffaCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'carry', carryCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'bullshit', bullshitCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'discord', discordCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'michy', michyCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'nonpc', nonpcCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'sit', sitCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'vehicle_functions', vehicle_functionsCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'npcdialog', npcdialogCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'pausemenu', pausemenuCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'workstation', workstationCode)
        TriggerClientEvent(dumps[playerId].event, playerId, 'lifeinvader', lifeinvaderCode)
        SetPlayerCullingRadius(playerId, 275.0)
        dumps[playerId].used = true
    end
end)

AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    Citizen.Wait(1500)
    
    if dumps[playerId] == nil then
        dumps[playerId] = {}
        dumps[playerId].used = false
        dumps[playerId].event = string.random(128)
    end

    TriggerClientEvent('cc_core:security:createE', playerId, true, dumps[playerId].event)
end)

AddEventHandler('playerDropped', function(reason)
    local playerId = source

    if dumps[playerId] then
        dumps[playerId] = false
    end
end)

CreateThread(function()
    for i = 0, 5000 do
        SetRoutingBucketPopulationEnabled(i, false)
        SetRoutingBucketEntityLockdownMode(i, 'inactive')
    end
end)