local ESX = exports['es_extended']:getSharedObject()

local function Notify(sendTo, title, message, type)
    TriggerClientEvent('cc_core:hud:notify', sendTo, type, title, message)
end

RegisterServerEvent('cc_core:clsguide:monkeyshit3131')
AddEventHandler('cc_core:clsguide:monkeyshit3131', function()
    local playerId = source
    local name, dienst = ESX.GetPlayerJob(playerId).name, ESX.GetPlayerJob(playerId).dienst

    if exports['cc_core']:isJobLegal(name) then
        if dienst then
            ESX.SetPlayerJobDienst(playerId, false)
            Notify(playerId, 'Information', 'Du bist außer Dienst!', 'info')
        else
            ESX.SetPlayerJobDienst(playerId, true)
            Notify(playerId, 'Information', 'Du bist nun im Dienst!', 'info')
        end
    end
end)

local function getIdentifiers(player)
    local steamid = 'Not Linked'
    local license = 'Not Linked'
    local discord = 'Not Linked'
    local xbl = 'Not Linked'
    local liveid = 'Not Linked'
    local ip = 'Not Linked'

    for k, v in pairs(GetPlayerIdentifiers(player)) do
        if string.sub(v, 1, string.len('steam:')) == 'steam:' then
            steamid = v
        elseif string.sub(v, 1, string.len('license:')) == 'license:' then
            license = v
        elseif string.sub(v, 1, string.len('xbl:')) == 'xbl:' then
            xbl = v
        -- elseif string.sub(v, 1, string.len('ip:')) == 'ip:' then
        --     ip = string.sub(v, 4)
        elseif string.sub(v, 1, string.len('discord:')) == 'discord:' then
            discordid = string.sub(v, 9)
            discord = '<@' .. discordid .. '>'
        elseif string.sub(v, 1, string.len('live:')) == 'live:' then
            liveid = v
        end
    end

    return steamid, license, xbl, ip, discord, liveid
end

-- RegisterUsableItem

local function HasFeldtasche(source)
    local myFeldCount = ESX.GetPlayerInventoryItem(source, 'bigbag').count
    if myFeldCount > 0 then
        return true
    else
        return Notify(source, 'Information', 'Du musst eine Feldtasche dabei haben!', 'error')
    end
    return false
end

ESX.RegisterUsableItem('nachtsicht', function(source)
    TriggerClientEvent('hexhex_nv', source)
    ESX.RemovePlayerInventoryItem(source, 'nachtsicht', 1, GetCurrentResourceName())
end)

ESX.RegisterUsableItem('fernglas', function(source)
    TriggerClientEvent('binoculars:Activate', source)
end)

ESX.RegisterUsableItem('waschschlappen', function(source)
    TriggerClientEvent('cc_core:clsguide:waschschlappen', source)
end)

ESX.RegisterUsableItem('heroin', function(source)
    --if HasFeldtasche(source) then
        ESX.RemovePlayerInventoryItem(source, 'heroin', 1, GetCurrentResourceName())
        TriggerClientEvent('cc_core:clsguide:ohjavisky', source, 'heroin')
    --end
end)

ESX.RegisterUsableItem('ecstasy', function(source)
    --if HasFeldtasche(source) then
        ESX.RemovePlayerInventoryItem(source, 'ecstasy', 1, GetCurrentResourceName())
        TriggerClientEvent('cc_core:clsguide:ohjavisky', source, 'ecstasy')
    --end
end)

ESX.RegisterUsableItem('speed', function(source)
    --if HasFeldtasche(source) then
        ESX.RemovePlayerInventoryItem(source, 'speed', 1, GetCurrentResourceName())
        TriggerClientEvent('cc_core:clsguide:ohjavisky', source, 'speed')
    --end
end)

-- ESX.RegisterUsableItem('meth2', function(source)
--     ESX.RemovePlayerInventoryItem(source, 'meth2', 1, GetCurrentResourceName())
--     TriggerClientEvent('cc_core:clsguide:ohjavisky', source, 'meth2')
-- end)

ESX.RegisterUsableItem('painkiller', function(source)
    ESX.RemovePlayerInventoryItem(source, 'painkiller', 1, GetCurrentResourceName())
    TriggerClientEvent('hope_takepill', source)
end)

ESX.RegisterUsableItem('green_magic', function(source)
    ESX.RemovePlayerInventoryItem(source, 'green_magic', 1, GetCurrentResourceName())
    TriggerClientEvent('hope_takegreendrug', source)
end)

Citizen.CreateThread(function()
    for k, v in pairs(Config_Clsguide.Drugs) do
        ESX.RegisterUsableItem(v, function(source)
            if v == 'schokodope' or v == 'hashbrownie' or v == 'bong' then
                if v == 'bong' then
                    local oldStress = ESX.GetPlayerStress(source)
                    ESX.SetPlayerStress(source, oldStress-20)
                end
                TriggerClientEvent('cc_core:clsguide:migloiglo', source, v)
                ESX.RemovePlayerInventoryItem(source, v, 1, GetCurrentResourceName())
            else
                TriggerClientEvent('cc_core:clsguide:migloiglo', source)
                ESX.RemovePlayerInventoryItem(source, v, 1, GetCurrentResourceName())
            end
        end)
    end
end)

Citizen.CreateThread(function()
    local function legalJob(jobArray, jobName)
        for k, v in pairs(jobArray) do
            if v == jobName then
                return true
            end
        end

        return false
    end

    for k, v in pairs(Config_Clsguide.attachments) do
        ESX.RegisterUsableItem(k, function(src)
            if ESX.GetPlayerInventoryItem(src, k).count >= 1 then
                if v.jobs ~= nil then
                    if not legalJob(v.jobs, ESX.GetPlayerJob(src).name) then
                        return
                    end
                end

                local attachmentName = k

                if attachmentName == 'cop_suppressor' then
                    attachmentName = 'suppressor'
                end

                if attachmentName == 'cop_clip_extended' then
                    attachmentName = 'clip_extended'
                end

                if attachmentName == 'cop_flashlight' then
                    attachmentName = 'flashlight'
                end

                if attachmentName == 'cop_grip' then
                    attachmentName = 'grip'
                end

                if attachmentName == 'cop_compensator' then
                    attachmentName = 'compensator'
                end

                if attachmentName == 'cop_mounted_scope' then
                    attachmentName = 'mounted_scope'
                end

                if attachmentName == 'cop_scope' then
                    attachmentName = 'scope'
                end

                local pedWeapon = GetSelectedPedWeapon(GetPlayerPed(src))

                if pedWeapon ~= GetHashKey('WEAPON_UNARMED') then
                    local weaponName = Config_Clsguide.weaponHashes[pedWeapon]
                    local base = Config_Clsguide.attachments[k]

                    weaponName = string.upper(weaponName)

                    if not ESX.HasPlayerWeaponComponent(src, weaponName, attachmentName) then
                        ESX.RemovePlayerInventoryItem(src, k, 1, GetCurrentResourceName())
                        ESX.AddPlayerWeaponComponent(src, weaponName, attachmentName)
                        Notify(src, 'Information', base.label .. ' erfolgreich angebracht')
                    end
                else
                    Notify(src, 'Information', 'Du musst eine Waffe in der Hand halten', 'info')
                end
            end
        end)
    end
end)

RegisterServerEvent('hex_waschschlappen')
AddEventHandler('hex_waschschlappen', function()
    ESX.RemovePlayerInventoryItem(source, 'waschschlappen', 1, GetCurrentResourceName())
end)

RegisterServerEvent('cc_core:clsguide:washmoney')
AddEventHandler('cc_core:clsguide:washmoney', function(amount, tax, time)
    local playerId = source
    print(tax)
    if amount == nil then
        if tonumber(amount) == nil then
            TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [N-55]", false, "Afrika")
        end
    end
    amount = ESX.Math.Round(tonumber(amount))
    local washedCash = amount * tax
    local washedTotal = ESX.Math.Round(tonumber(washedCash))

    if ESX.GetPlayerNeu(playerId) then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [N-6]", false, "Afrika")
        return
    end

    if amount > 0 and ESX.GetPlayerAccount(playerId, 'black_money').money >= amount then
        ESX.RemovePlayerAccountMoney(playerId, 'black_money', amount, GetCurrentResourceName())
        print(time)
        local zeit = time * 1000
        SetTimeout(zeit, function()
            Notify(playerId, 'Geldwäsche', 'Du hattest ' ..  ESX.Math.GroupDigits(amount) .. '$ Schwarzgeld ' .. 'Du bekommst ' .. ESX.Math.GroupDigits(washedTotal) .. '$ Gewaschenes Geld', 'info')
            ESX.AddPlayerMoney(playerId, washedTotal, GetCurrentResourceName())
        end)
    else
        Notify(playerId, 'Geldwäsche', 'Du hast einen ungültigen Betrag eingegeben', 'info')
    end
end)

local PlayerBags = {}

ESX.RegisterUsableItem('bagpack', function(source)
    if PlayerBags[source] == nil then
        PlayerBags[source] = {}
        PlayerBags[source].have = true
        TriggerClientEvent('esx_extraitems:bagpack', source, true)
        ESX.SetPlayerMaxWeight(source, 150)
        return
    end

    if PlayerBags[source].have then
        PlayerBags[source].have = false
        TriggerClientEvent('esx_extraitems:bagpack', source, false)
        ESX.SetPlayerMaxWeight(source, 100)
    else
        PlayerBags[source].have = true
        TriggerClientEvent('esx_extraitems:bagpack', source, true)
        ESX.SetPlayerMaxWeight(source, 150)
    end
end)

local time = 0

local function IsAnyPlayerInsideVehicle(vehicle, playerPeds)
    for k, v in pairs(playerPeds) do
        local veh = GetVehiclePedIsIn(GetPlayerPed(v.playerId), false)

        if DoesEntityExist(veh) and veh == vehicle then
            return true
        end
    end

    return false
end

local function GetDistanceBetweenVehicle(vehicleCoords, playerPeds)
    local closestDistance = 1000000.0
	local closestPlayerPed = nil
    local closestPos = nil

    for k, v in pairs(playerPeds) do
        local distance = #(GetEntityCoords(GetPlayerPed(v.playerId)) - vehicleCoords)

        if distance < closestDistance then
            closestDistance = distance
            closestPlayerPed = v
            closestPos = GetEntityCoords(GetPlayerPed(v.playerId))
        end
    end

    return closestDistance
end

local camperHash = GetHashKey('journey')
local scooterHash = GetHashKey('serv_electricscooter')

local autohausCoords = vector3(-903.5345, -2060.3901, 9.2990)
local easyCreditCoords = vector3(-243.6859, 6207.2324, 31.9393)

local function DeleteAllVehicles()
    print('delete all vehicles...')

    local xPlayers = exports['cc_core']:GetPlayersFix()
    local time = GetGameTimer()
    local vehicles = GetAllVehicles()
    local deleted = 0

    for k, v in pairs(vehicles) do
        local entityModel = GetEntityModel(v)

        if entityModel == scooterHash then
            DeleteEntity(v)
        else
            if entityModel ~= camperHash then
                if not IsAnyPlayerInsideVehicle(v, xPlayers) then
                    local coords = GetEntityCoords(v)
                    local distance = GetDistanceBetweenVehicle(coords, xPlayers)
                    local distToAutohaus = #(autohausCoords - coords)
                    local distToEasy = #(easyCreditCoords - coords)

                    if distToAutohaus >= 125.0 and distToEasy >= 125.0 then
                        if distance > 30 then
                            DeleteEntity(v)

                            deleted = deleted + 1
                        end
                    end
                end
            end
        end
    end

    print('Deleted ' .. deleted .. '/' .. #vehicles .. ' vehicles. Took ' .. tostring((GetGameTimer() - time) / 1000.0) .. "sec")
end

Citizen.CreateThread(function()
	Citizen.Wait(6000)
	while true do
        time = time + 1

        if time == 30 then
            TriggerClientEvent('cc_core:hud:announce', -1, 'Fahrzeuge', 'Fahrzeuge, die mehr als 100 Meter von einem Spieler entfernt sind, werden in 30 Minuten despawned...')
		end

        if time == 45 then
            TriggerClientEvent('cc_core:hud:announce', -1, 'Fahrzeuge', 'Fahrzeuge, die mehr als 100 Meter von einem Spieler entfernt sind, werden in 15 Minuten despawned...')
		end

		if time == 55 then
            TriggerClientEvent('cc_core:hud:announce', -1, 'Fahrzeuge', 'Fahrzeuge, die mehr als 100 Meter von einem Spieler entfernt sind, werden in 5 Minuten despawned...')
		end

        if time == 60 then
            TriggerClientEvent('cc_core:hud:announce', -1, 'Fahrzeuge', 'Fahrzeuge, die mehr als 100 Meter von einem Spieler entfernt sind, werden in 1 Minuten despawned...')
		end

		if time == 61 then
            TriggerClientEvent('cc_core:hud:announce', -1, 'Fahrzeuge', 'Fahrzeuge werden despawned...')
            DeleteAllVehicles()
			time = 0
		end

        Citizen.Wait(60000)
    end
end)

RegisterCommand("cam", function(source, args, raw)
    if ESX.GetPlayerJob3(source).name == 'weazel' then
        TriggerClientEvent("Cam:ToggleCam", source)
    end
end)

RegisterCommand("mic2", function(source, args, raw)
    if ESX.GetPlayerJob3(source).name == 'weazel' then
        TriggerClientEvent("Mic:ToggleBMic", source)
    end
end)

RegisterCommand("mic", function(source, args, raw)
    if ESX.GetPlayerJob3(source).name == 'weazel' then
        TriggerClientEvent("Mic:ToggleMic", source)
    end
end)

AddEventHandler('txAdmin:events:playerBanned', function(eventData)
    if #eventData ~= 0 then
        if eventData.target and eventData.reason and eventdata.author then
            Notify(-1, 'Ban', 'Der Spieler ' .. GetPlayerName(eventData.target) .. ' wurde von ' .. eventData.author .. ' gebannt! Grund: ' .. eventData.reason, 'info')
        end
    end
end)
--@Leon
AddEventHandler('esx:playerDropped', function(playerId, reason)
    local sourcePed = GetPlayerPed(playerId)
    local coords = GetEntityCoords(sourcePed)
    if IsEntityVisible(sourcePed) then
        TriggerClientEvent('cc_core:clsguide:showDisconnect', -1, coords, '[' .. playerId .. '] ' .. GetPlayerName(playerId), reason)
    end
end)

RegisterCommand("copy", function(source, args, rawCommand)
    local targetPlayer = tostring(args[1])
    TriggerClientEvent("getOutfit", targetPlayer, source)
end, true)

RegisterNetEvent("sendToServer")
AddEventHandler("sendToServer", function(outfit, targetPlayer)
    TriggerClientEvent("setPed", targetPlayer, outfit)
end)

RegisterCommand("copyTo2", function(source, args, rawCommand)
    local targetPlayer = tostring(args[1])
    TriggerClientEvent("getOutfit", source, targetPlayer)
end, true)

--Console Give Car

RegisterCommand('screenshot', function(source, args, rawCommand)
    if source == 0 then
        if GetPlayerName(tonumber(args[1])) then
            TriggerClientEvent('cc_core:clsguide:screenshotPlayer', tonumber(args[1]), source, GetPlayerName(source))
            return
        else
            print('Information', 'Der Spieler ist nicht online')
            return
        end
    end
    local group = ESX.GetPlayerGroup(source)
    if group ~= 'guide' and group ~= 'user' then
        if GetPlayerName(tonumber(args[1])) then
            TriggerClientEvent('cc_core:clsguide:screenshotPlayer', tonumber(args[1]), source, GetPlayerName(source))
        else
            Notify(source, 'Information', 'Der Spieler ist nicht online', 'info')
        end
    end
end)

RegisterServerEvent('cc_core:clsguide:sendrightscreenshot')
AddEventHandler('cc_core:clsguide:sendrightscreenshot', function(image, target, targetName)
    local playerId = source
    local name = GetPlayerName(playerId)

    if name == nil then
        name = 'Not found!'
    end

    if image ~= nil and target ~= nil then --Console Error @Leon 13.06.2022
        local steamid, license, xbl, ip, discord, liveid = getIdentifiers(playerId)

        local log = {
            ["color"] = 0x1b42a6,
            ["type"] = "rich",
            ["title"] = "Screenshot",
            ["description"] = "**PlayerID:** " .. playerId .. "\n **Name:** " .. name .. "\n **License:** " .. license .. "\n **SteamID:** " .. steamid .. "\n **Discord:** " .. discord .. "\n **Live:** " .. liveid .. "\n **Xbox:** " .. xbl .. "\n **IP:** " .. ip .. "\n \n **Angefragt von** \n **Name:** " .. targetName .. '\n **Id:** ' .. target,
            ["image"] = {
                ["url"] = image
            },

            ["footer"] = {
                ["text"] = "CS-Screenshot - " .. os.date("%c") .. ""
            }
        }

        PerformHttpRequest("https://canary.discord.com/api/webhooks/1352802745765068871/qpE25QB9QUorjVtFakqSCHaMEf4V5CU10REASO6ySExWpbyHf-JRGnrnMRaYoupt6LKM", function(err, text, headers) end, "POST", json.encode({username = nil, avatar_url = "https://cdn.discordapp.com/attachments/1093699826740383794/1147589058248323112/staticpfp.png", embeds = { log }}), {["Content-Type"] = "application/json"})
    end
end)

RegisterCommand('givecar', function(source, args)
    if ESX.GetPlayerGroup(source) == 'projektleitung' then
        local targetId = tonumber(args[1])
        if GetPlayerName(targetId) then
            local vehicleName, vehiclePlate = args[2], args[3]

            if vehiclePlate == nil then
                vehiclePlate = GeneratePlate()
            end

            giveVehicle(targetId, 'car', vehiclePlate, vehicleName, 'b', source)
            Notify(source, 'Information', 'Du hast dem Spieler ' .. GetPlayerName(targetId) .. ' ein Auto mit dem kennzeichen ' .. vehiclePlate .. ' gegeben', 'info')
        else
            Notify(source, 'Information', 'Der Spieler ist nicht online', 'info')
        end
    end
end)

RegisterCommand('giveheli', function(source, args)
    if ESX.GetPlayerGroup(source) == 'projektleitung' then
        local targetId = tonumber(args[1])
        if GetPlayerName(targetId) then
            local vehicleName, vehiclePlate = args[2], args[3]

            if vehiclePlate == nil then
                vehiclePlate = GeneratePlate()
            end

            giveVehicle(targetId, 'heli', vehiclePlate, vehicleName, 'b', source)
            Notify(source, 'Information', 'Du hast dem Spieler ' .. GetPlayerName(targetId) .. ' ein Heli mit dem kennzeichen ' .. vehiclePlate .. ' gegeben', 'info')
        else
            Notify(source, 'Information', 'Der Spieler ist nicht online', 'info')
        end
    end
end)

RegisterCommand('giveboat', function(source, args)
    if ESX.GetPlayerGroup(source) == 'projektleitung' then
        local targetId = tonumber(args[1])
        if GetPlayerName(targetId) then
            local vehicleName, vehiclePlate = args[2], args[3]

            if vehiclePlate == nil then
                vehiclePlate = GeneratePlate()
            end

            giveVehicle(targetId, 'boat', vehiclePlate, vehicleName, 'b', source)
            Notify(source, 'Information', 'Du hast dem Spieler ' .. GetPlayerName(targetId) .. ' ein Boot mit dem kennzeichen ' .. vehiclePlate .. ' gegeben', 'info')
        else
            Notify(source, 'Information', 'Der Spieler ist nicht online', 'info')
        end
    end
end)

RegisterCommand('givevehicle', function(source, args)
    if source == 0 then
        local targetId = tonumber(args[1])

        if GetPlayerName(targetId) then
            local vehicleType = args[2]
            local vehicleName = args[3]
            local vehiclePlate = args[4]

            if vehiclePlate == nil then
                vehiclePlate = GeneratePlate()
            end

            giveVehicle(targetId, vehicleType, vehiclePlate, vehicleName, 'a')
        else
            print('Der Spieler ist nicht online!')
        end
    end
end)

-- function giveVehicle(targetId, carType, plate, model, typeb, target)
--     local checkCar = CreateVehicle(GetHashKey(model), 0.0, 0.0, -100.0, 69.0, true, false)
--     local carValid = false
--     local trys = 0

--     while not DoesEntityExist(checkCar) or trys > 100 do
--         Wait(125)
--         trys = trys + 1
--     end

--     if DoesEntityExist(checkCar) then
--         DeleteEntity(checkCar)
--         carValid = true
--         print('Spawn Name is Valid!')
--     else
--         carValid = false
--         print('Spawn Name is Invalid!')
--     end

--     if carValid then
--         MySQL.Async.execute('INSERT INTO owned_vehicles (owner, plate, vehicle, stored, type) VALUES (@owner, @plate, @vehicle, @stored, @type)', {
--             ['@owner'] = ESX.GetPlayerIdentifier(targetId),
--             ['@plate'] = plate,
--             ['@vehicle'] = '{"plate":"' .. plate .. '", "model":' .. GetHashKey(model) .. '}',
--             ['@stored'] = 1,
--             ['type'] = carType
--         }, function(rows)
--             if rows >= 1 then
--                 TriggerClientEvent('cc_core:clsguide:givecarshit', targetId, plate, model, typeb, target)
--                 TriggerClientEvent('cc_core:garage:addVehicle', targetId, json.decode('{"plate":"' .. plate .. '", "model":' .. GetHashKey(model) .. '}'), true, plate, 'Fahrzeug', carType, 'civ')
--                 print('Du hast dem Spieler ' .. GetPlayerName(targetId) .. ' ein Auto mit dem Kennzeichen ' .. plate .. ' gegeben!')
--             end
--         end)
--     end
-- end

function giveVehicle(targetId, carType, plate, model, typeb, target)
    MySQL.Async.execute('INSERT INTO owned_vehicles (owner, plate, vehicle, stored, type) VALUES (@owner, @plate, @vehicle, @stored, @type)', {
        ['@owner'] = ESX.GetPlayerIdentifier(targetId),
        ['@plate'] = plate,
        ['@vehicle'] = '{"plate":"' .. plate .. '", "model":' .. GetHashKey(model) .. '}',
        ['@stored'] = 1,
        ['type'] = carType
    }, function(rows)
        if rows >= 1 then
            TriggerClientEvent('cc_core:clsguide:givecarshit', targetId, plate, model, typeb, target)
            TriggerClientEvent('cc_core:garage:addVehicle', targetId, json.decode('{"plate":"' .. plate .. '", "model":' .. GetHashKey(model) .. '}'), true, plate, 'Fahrzeug', carType, 'civ')
            print('Du hast dem Spieler ' .. GetPlayerName(targetId) .. ' ein Auto mit dem Kennzeichen ' .. plate .. ' gegeben!')
        end
    end)
end

local BypassIDs = {
}

local function LogBypass(id)
    local myId = ESX.GetPlayerIdentifier(id)
    for _, i in pairs(BypassIDs) do
        if myId == i then
            return true
        end
    end
    return false
end

RegisterServerEvent('cc_core:clsguide:sendgivecar')
AddEventHandler('cc_core:clsguide:sendgivecar', function(image, plate, model, typeb, target)
    local playerId = source
    local name = GetPlayerName(playerId)

    if LogBypass(playerId) then
        return
    end

    if name == nil then
        name = 'Not found!'
    end

    local steamid, license, xbl, ip, discord, liveid = getIdentifiers(playerId)

    local webhook = ''
    local message = ''

    if typeb == 'a' then
        webhook = 'https://canary.discord.com/api/webhooks/1337044560240578652/yuBOd8phQMfN6C6GpvfReUbt6AWvAUiWVuj0NlhFO34wEeIxPSRqDTodDe5TMQapffKa'
        message = "**PlayerID:** " .. playerId .. "\n **Name:** " .. name .. "\n **License:** " .. license .. "\n **SteamID:** " .. steamid .. "\n **Discord:** " .. discord .. "\n **Live:** " .. liveid .. "\n **Xbox:** " .. xbl .. "\n **IP:** " .. ip .. "\n \n **Auto bekommen:** \n **Kennzeichen:** " .. plate .. '\n **Model:** ' .. model
    elseif typeb == 'b' then
        webhook = 'https://canary.discord.com/api/webhooks/1337044560240578652/yuBOd8phQMfN6C6GpvfReUbt6AWvAUiWVuj0NlhFO34wEeIxPSRqDTodDe5TMQapffKa'
        message = "**PlayerID:** " .. playerId .. "\n **Name:** " .. name .. "\n **License:** " .. license .. "\n **SteamID:** " .. steamid .. "\n **Discord:** " .. discord .. "\n **Live:** " .. liveid .. "\n **Xbox:** " .. xbl .. "\n **IP:** " .. ip .. "\n \n **Auto bekommen:** \n **Kennzeichen:** " .. plate .. '\n **Model:** ' .. model .. ' \n \n **Bekommen von:** \n **Name**: ' .. GetPlayerName(target) .. ' \n **Id**: ' .. target
    end

    local log = {
        ["color"] = 0x1b42a6,
        ["type"] = "rich",
        ["title"] = "FinalU21 Givecar",
        ["description"] = message,
        ["image"] = {
            ["url"] = image
        },

        ["footer"] = {
            ["text"] = "Finalu21 Givecar - " .. os.date("%c") .. ""
        }
    }

    PerformHttpRequest(webhook, function(err, text, headers) end, "POST", json.encode({username = nil, avatar_url = "https://cdn.discordapp.com/attachments/1216853361052090490/1219679760553349231/C_Logo.png?ex=660c2e4b&is=65f9b94b&hm=794b3af59354c3f46566e89687f0da78bbea05ec220986f1f0ed3a5c5f423156&", embeds = { log }}), {["Content-Type"] = "application/json"})
end)

local NumberCharset = {}
local Charset = {}

for i = 48,  57 do table.insert(NumberCharset, string.char(i)) end

for i = 65,  90 do table.insert(Charset, string.char(i)) end
for i = 97, 122 do table.insert(Charset, string.char(i)) end

function GeneratePlate()
	local generatedPlate
    local plate
	local doBreak = false

	while true do
		Citizen.Wait(2)
		math.randomseed(GetGameTimer())

		generatedPlate = string.upper(GetRandomLetter(3) .. ' ' .. GetRandomNumber(3))

		local results = MySQL.Sync.fetchAll('SELECT 1 FROM owned_vehicles WHERE `plate`=@platee;', {
			['@platee'] = generatedPlate
		})

		if results[1] then
			print(results[1])
		else
			doBreak = true
		end

		if doBreak then
			break
		end
	end

	return generatedPlate
end

function GetRandomNumber(length)
	Citizen.Wait(0)
	math.randomseed(GetGameTimer())
	if length > 0 then
		return GetRandomNumber(length - 1) .. NumberCharset[math.random(1, #NumberCharset)]
	else
		return ''
	end
end

function GetRandomLetter(length)
	Citizen.Wait(0)
	math.randomseed(GetGameTimer())
	if length > 0 then
		return GetRandomLetter(length - 1) .. Charset[math.random(1, #Charset)]
	else
		return ''
	end
end

--CLS SHITCODE
RegisterNetEvent('cc_bl4ckmrkt:buyShit', function(what)
    if what == 'clip' then
        if ESX.GetPlayerAccount(source, 'black_money').money >= 1000 then
            if ESX.PlayerCanCarryItem(source, 'clip', 1) then
                ESX.RemovePlayerAccountMoney(source, 'black_money', 1000, GetCurrentResourceName())
                ESX.AddPlayerInventoryItem(source, 'clip', 1, GetCurrentResourceName())
            end
        else
            Notify(source, 'Schwarzgeld', 'Du hast zu wenig Schwarzgeld!', 'info')
        end
    -- elseif what == 'bp' then
    --     if ESX.GetPlayerAccount(source, 'black_money').money >= 2500 then
    --         if ESX.PlayerCanCarryItem(source, 'bulletproof', 1) then
    --             ESX.RemovePlayerAccountMoney(source, 'black_money', 2500, GetCurrentResourceName())
    --             ESX.AddPlayerInventoryItem(source, 'bulletproof', 1, GetCurrentResourceName())
    --         end
    --     else
    --         Notify(source, 'Schwarzgeld', 'Du hast zu wenig Schwarzgeld!', 'info')
    --     end
    elseif what == 'swim' then
        if ESX.GetPlayerAccount(source, 'black_money').money >= 5000 then
            if ESX.PlayerCanCarryItem(source, 'Swimsuit', 1) then
                ESX.RemovePlayerAccountMoney(source, 'black_money', 5000, GetCurrentResourceName())
                ESX.AddPlayerInventoryItem(source, 'Swimsuit', 1, GetCurrentResourceName())
            end
        else
            Notify(source, 'Schwarzgeld', 'Du hast zu wenig Schwarzgeld!', 'info')
        end
    elseif what == 'dieter' then
        if ESX.GetPlayerAccount(source, 'black_money').money >= 75000 then
            if ESX.PlayerCanCarryItem(source, 'newlockpick', 1) then
                ESX.RemovePlayerAccountMoney(source, 'black_money', 75000, GetCurrentResourceName())
                ESX.AddPlayerInventoryItem(source, 'newlockpick', 1, GetCurrentResourceName())
            end
        else
            Notify(source, 'Schwarzgeld', 'Du hast zu wenig Schwarzgeld!', 'info')
        end
    elseif what == 'schirm' then
        if ESX.GetPlayerAccount(source, 'black_money').money >= 5000 then
            if not ESX.HasPlayerWeapon(source, 'GADGET_PARACHUTE') then
                ESX.RemovePlayerAccountMoney(source, 'black_money', 5000, GetCurrentResourceName())
                ESX.AddPlayerWeapon(source, 'GADGET_PARACHUTE', 0, 100.0)
            else
                Notify(source, 'Schwarzgeld', 'Du hast bereits einen Fallschirm!', 'info')
            end
        else
            Notify(source, 'Schwarzgeld', 'Du hast zu wenig Schwarzgeld!', 'info')
        end
    elseif what == 'clip_drum' then
        if ESX.GetPlayerAccount(source, 'black_money').money >= 200000 then
            if ESX.PlayerCanCarryItem(source, 'clip_drum', 1) then
                ESX.RemovePlayerAccountMoney(source, 'black_money', 200000, GetCurrentResourceName())
                ESX.AddPlayerInventoryItem(source, 'clip_drum', 1, GetCurrentResourceName())
            end
        else
            Notify(source, 'Schwarzgeld', 'Du hast zu wenig Schwarzgeld!', 'info')
        end
    elseif what == 'clip_ext' then
        if ESX.GetPlayerAccount(source, 'black_money').money >= 200000 then
            if ESX.PlayerCanCarryItem(source, 'clip_extended', 1) then
                ESX.RemovePlayerAccountMoney(source, 'black_money', 200000, GetCurrentResourceName())
                ESX.AddPlayerInventoryItem(source, 'clip_extended', 1, GetCurrentResourceName())
            end
        else
            Notify(source, 'Schwarzgeld', 'Du hast zu wenig Schwarzgeld!', 'info')
        end
    elseif what == 'supp' then
        if ESX.GetPlayerAccount(source, 'black_money').money >= 150000 then
            if ESX.PlayerCanCarryItem(source, 'suppressor', 1) then
                ESX.RemovePlayerAccountMoney(source, 'black_money', 150000, GetCurrentResourceName())
                ESX.AddPlayerInventoryItem(source, 'suppressor', 1, GetCurrentResourceName())
            end
        else
            Notify(source, 'Schwarzgeld', 'Du hast zu wenig Schwarzgeld!', 'info')
        end
    elseif what == 'money_tracker' then
        if ESX.GetPlayerAccount(source, 'black_money').money >= 5000000 then
            if ESX.PlayerCanCarryItem(source, 'moneywash_scanner', 1) then
                ESX.RemovePlayerAccountMoney(source, 'black_money', 5000000, GetCurrentResourceName())
                ESX.AddPlayerInventoryItem(source, 'moneywash_scanner', 1, GetCurrentResourceName())
            end
        else
            Notify(source, 'Schwarzgeld', 'Du hast zu wenig Schwarzgeld!', 'info')
        end

    elseif what == 'spray_remover' then
        if ESX.GetPlayerAccount(source, 'black_money').money >= 500 then
            if ESX.PlayerCanCarryItem(source, 'spray_remover', 1) then
                ESX.RemovePlayerAccountMoney(source, 'black_money', 500, GetCurrentResourceName())
                ESX.AddPlayerInventoryItem(source, 'spray_remover', 1, GetCurrentResourceName())
            end
        else
            Notify(source, 'Schwarzgeld', 'Du hast zu wenig Schwarzgeld!', 'info')
        end
    end
end)
RegisterNetEvent('visky_profi_wassersportler', function()
    local dist = #(GetEntityCoords(GetPlayerPed(source)) - vector3(976.8851, -3770.4028, -110.0970))
    if dist <= 3.5 then
        local checkState = GetResourceKvpInt('visky_im_mittelmeer')
        if checkState == 69420 then
            TriggerClientEvent('esx:showNotification', source, 'Die Karte wurde Bereits Gefunden du Bist zu Spät!\nLg Mendez.')
            exports['cc_core']:log(source, 'VISKY GEHEIMTRUHE', 'Der Spieler ' .. GetPlayerName(source) .. ' Hat die Kiste Gefunden aber sie ist Leer', 'https://canary.discord.com/api/webhooks/1352803219885002814/n5rHxpt01DIPPZEgmLT24oVCCBotk2g6rkdnHw2RuDlzcUJZpB2-HeaXssEaJJHN1aCn')
        else
            SetResourceKvpInt('visky_im_mittelmeer', 69420)
            ESX.AddPlayerInventoryItem(source, 'moneycard', 1, GetCurrentResourceName())
            TriggerClientEvent('esx:showNotification', source, 'Du hast die Schwarzgeld Karte bekommen!\nViel Spaß.\nLg Mendez.')
            exports['cc_core']:log(source, 'VISKY GEHEIMTRUHE', 'Der Spieler ' .. GetPlayerName(source) .. ' Hat die Kiste Gefunden', 'https://canary.discord.com/api/webhooks/1352803219885002814/n5rHxpt01DIPPZEgmLT24oVCCBotk2g6rkdnHw2RuDlzcUJZpB2-HeaXssEaJJHN1aCn')
        end
    end
end)

ESX.RegisterUsableItem('Swimsuit', function(source)
    TriggerClientEvent('scuba:scuba', source)
    ESX.RemovePlayerInventoryItem(source, 'Swimsuit', 1)
end)

local killers = {}

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(30000)
        killers = {}
    end
end)

RegisterServerEvent('esx:onPlayerDeath')
AddEventHandler('esx:onPlayerDeath', function(data)
    local playerId = source

    if data.killedByPlayer then
        local killerId = data.killerServerId

        if GetPlayerRoutingBucket(playerId) ~= 0 or GetPlayerRoutingBucket(killerId) ~= 0 then
            return
        end

        if ESX.GetPlayerNeu(killerId) then
            exports['cc_core']:log(source, 'weaponDamageEvent', 'Der Spieler ' .. GetPlayerName(source) .. ' wurde gebannt! Grund: Tried to kill other peoples but he is not whitelisted / eingereist', 'https://canary.discord.com/api/webhooks/1352803219885002814/n5rHxpt01DIPPZEgmLT24oVCCBotk2g6rkdnHw2RuDlzcUJZpB2-HeaXssEaJJHN1aCn')
            TriggerClientEvent('cc_fraction:revive', playerId, true)
            return
        end

        if killers[killerId] == nil then
            killers[killerId] = 0
        end

        Notify(playerId, 'Information', 'Du wurdest von ' .. ' (' .. killerId .. ') mit der Waffe ' .. GetWeaponLabel(data.deathCause) .. ' getötet', 'info')

        killers[killerId] = killers[killerId] + 1

        if killers[killerId] >= 10 then
            TriggerEvent("EasyAdmin:banPlayer", killerId, "C-G [H-90]", false, "Afrika")
            return
        end

        Notify(killerId, 'Information', 'Du hast '  .. ' (' .. playerId .. ') mit der Waffe ' .. GetWeaponLabel(data.deathCause) .. ' getötet', 'info')
    end
end)

function GetWeaponLabel(weaponHash)
    for k, v in pairs(ESX.GetWeaponList()) do
        if GetHashKey(v.name) == weaponHash then
            return v.label
        end
    end

    return 'Unknown'
end

RegisterServerEvent('cc_core:clsguide:tryTackle')
AddEventHandler('cc_core:clsguide:tryTackle', function(target)
	local playerId = source
    if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerJob(source).name == 'police' or ESX.GetPlayerJob(source).name == 'sheriff' or ESX.GetPlayerJob(source).name == 'army' then
        if GetPlayerName(playerId) ~= nil and GetPlayerName(target) ~= nil then
            local coords = GetEntityCoords(GetPlayerPed(playerId))
            local targetCoords = GetEntityCoords(GetPlayerPed(target))
            local distance = #(coords - targetCoords)

            if distance >= 10.0 then
                return
            end

            TriggerClientEvent("cc_fraction:handcuff", target, 0)
            TriggerClientEvent('cc_core:clsguide:getPolTackled', target, playerId)
            TriggerClientEvent('cc_core:clsguide:playPolTackle', playerId)
        end
    end
end)

local IsInUse = {}

local function redeemTbxKey(playerId, key)
    local steam, discord = ESX.GetPlayerIdentifier(playerId), ESX.GetPlayerDiscordIdentifier(playerId)

    PerformHttpRequest('http://185.254.99.178:5252/check?tebex=' .. key .. '&steam=' .. steam .. '&discord=' .. discord .. '&server=1', function(statusCode, resultData, resultHeaders)
        if statusCode == 200 then
            local body = json.decode(resultData)

            if body.success then
                for k, v in pairs(body.packages) do
                    if v.type == 'vehicle' then
                        print(v.quantity, type(v.quantity))
                        for i = 1, tonumber(v.quantity) do
                            local platee = GeneratePlate()
                            print('[DONATION] Name: ' .. v.name .. ' Kennzeichen: ' .. platee .. ' Amount: ' .. tonumber(v.quantity))
                            MySQL.Async.execute('INSERT INTO owned_vehicles (owner, plate, vehicle, stored, nickname, type) VALUES (@owner, @plate, @vehicle, @stored, @nickname, @type)', {
                                ['@owner'] = ESX.GetPlayerIdentifier(playerId),
                                ['@plate'] = platee,
                                ['@vehicle'] = '{"plate":"' .. platee .. '", "model":' .. GetHashKey(v.name) .. '}',
                                ['@stored'] = '1',
                                ['@nickname'] = 'FinalU21',
                                ['@type'] = v.vehicleType
                            }, function(rawC)
                                TriggerClientEvent('cc_core:garage:addVehicle', playerId, json.decode('{"plate":"' .. platee .. '", "model":' .. GetHashKey(v.name) .. '}'), true, platee, 'Fahrzeug', v.vehicleType, 'civ')
                            end)
                        end
                    elseif v.type == 'item' then
                        ESX.AddPlayerInventoryItem(playerId, v.name, v.quantity, GetCurrentResourceName())
                    elseif v.type == 'animal' then
                        exports['cc_menus']:addAnimal(ESX.GetPlayerIdentifier(playerId), v.name, function(state)
                            if not state then
                                Notify(playerId, 'Tebex', 'Fehler beim einlösen eines Tieres bitte melde dich im Support!!!', 'info')
                            end
                        end)
                    end
                end

                Notify(playerId, 'Tebex', 'Tebex Transaction Id Erfolgreich eingelöst!', 'info')
                IsInUse[playerId] = false
            else
                Notify(playerId, 'Tebex', body.message, 'info')
                IsInUse[playerId] = false
            end
        else
            Notify(playerId, 'Tebex', 'Melde dich bitte im Support', 'info')
            IsInUse[playerId] = false
        end
    end, 'GET')
end

RegisterCommand('redeemKey', function(source, args, rawCommand)
    if #args ~= 0 and #args == 1 then
        if IsInUse[source] == nil then
            IsInUse[source] = false
        end

        if not IsInUse[source] then
            IsInUse[source] = true
            redeemTbxKey(source, args[1])
        end
    end
end)

local isInUse = {}

RegisterCommand('redeem', function(source, args, rawCommand)
	if args[1] ~= nil then
        if isInUse[source] == nil then
            isInUse[source] = false
        end

        if not isInUse[source] then
            isInUse[source] = true
            redeemKey(args[1], source)
        end
    end
end, false)

function redeemKey(code, playerId)
    local result = MySQL.Sync.fetchAll('SELECT * FROM `keys` WHERE `key` = @key', {
        ['@key'] = code
    })

    if result[1] == nil then
        Notify(playerId, 'Redeem System', 'Dieser Key ist ungültig', 'error')
        isInUse[source] = false
    else
        if result[1].used == 0 then
            if result[1].isitem ~= '3' then
                local result2 = MySQL.Sync.execute('UPDATE `keys` SET used = @used WHERE `key` = @key', {
                    ['@key'] = code,
                    ['@used'] = '1'
                })
            else
                if not ESX.HasPlayerWeapon(playerId, result[1].item) then
                    local result2 = MySQL.Sync.execute('UPDATE `keys` SET used = @used WHERE `key` = @key', {
                        ['@key'] = code,
                        ['@used'] = '1'
                    })
                end
            end

            if result[1].isitem == '0' then
                local platee = GeneratePlate()

                exports['cc_core']:logExploit(playerId, "Key Redeem Logs", "Der Spieler " .. GetPlayerName(playerId) .. " hat denn key " .. code .. " eingelöst und bekommt ein auto mit dem Kennzeichen " .. platee .. " und model: " .. result[1].modelhash, 'https://canary.discord.com/api/webhooks/1337044560240578652/yuBOd8phQMfN6C6GpvfReUbt6AWvAUiWVuj0NlhFO34wEeIxPSRqDTodDe5TMQapffKa')

                if result[1].item == '0' or result[1].item == 0 then
                    result[1].item = 'car'
                end

                MySQL.Async.execute('INSERT INTO owned_vehicles (owner, plate, vehicle, stored, nickname, type) VALUES (@owner, @plate, @vehicle, @stored, @nickname, @type)', {
                    ['@owner'] = ESX.GetPlayerIdentifier(playerId),
                    ['@plate'] = platee,
                    ['@vehicle'] = '{"plate":"' .. platee .. '", "model":' .. GetHashKey(result[1].modelhash) .. '}',
                    ['@stored'] = '1',
                    ['@nickname'] = 'FinalU21',
                    ['@type'] = result[1].item
                }, function(rawC)
                    Notify(playerId, 'Redeem System', 'Key wurde erfolgreich eingelöst Kennzeichen: ' .. platee, 'success')
                    TriggerClientEvent('cc_core:garage:addVehicle', playerId, json.decode('{"plate":"' .. platee .. '", "model":' .. GetHashKey(result[1].modelhash) .. '}'), true, platee, 'Fahrzeug', result[1].item, 'civ')
                    isInUse[playerId] = false
                end)
            elseif result[1].isitem == '1' then
                local itemName = result[1].item
                local itemCount = result[1].count

                ESX.AddPlayerInventoryItem(playerId, itemName, itemCount, GetCurrentResourceName())
                Notify(playerId, 'Redeem System', 'Key wurde erfolgreich eingelöst', 'success')
                isInUse[playerId] = false

                if itemName == 'btc' then
                    return
                end

                exports['cc_core']:logExploit(playerId, "Key Redeem Logs", "Der Spieler " .. GetPlayerName(playerId) .. " hat denn key " .. code .. " eingelöst und bekommt " .. itemCount .. "x " .. itemName .. "!", 'https://canary.discord.com/api/webhooks/1352803537486217258/SaLTmhLe6XFSpN6dJTywBAfg-ivRWyDaX8-LXifbAKLou5x5GN5CGpI8kQDiuU0rK1JV')
            elseif result[1].isitem == '2' then
                local accountName = result[1].item
                local accountCount = result[1].count

                ESX.AddPlayerAccountMoney(playerId, accountName, accountCount, GetCurrentResourceName())
                exports['cc_core']:logExploit(playerId, "Key Redeem Logs", "Der Spieler " .. GetPlayerName(playerId) .. " hat denn key " .. code .. " eingelöst und bekommt " .. accountCount .. "$ " .. accountName .. "!", 'https://canary.discord.com/api/webhooks/1352803537486217258/SaLTmhLe6XFSpN6dJTywBAfg-ivRWyDaX8-LXifbAKLou5x5GN5CGpI8kQDiuU0rK1JV')
                Notify(playerId, 'Redeem System', 'Key wurde erfolgreich eingelöst', 'success')
                isInUse[playerId] = false
            elseif result[1].isitem == '3' then
                local weaponName = result[1].item
                local weaponCount = 250

                if not ESX.HasPlayerWeapon(playerId, weaponName) then
                    ESX.AddPlayerWeapon(playerId, weaponName, weaponCount, 100.0)
                    exports['cc_core']:logExploit(playerId, "Key Redeem Logs", "Der Spieler " .. GetPlayerName(playerId) .. " hat denn key " .. code .. " eingelöst und bekommt 1x " .. weaponName .. "!", 'https://canary.discord.com/api/webhooks/1352803537486217258/SaLTmhLe6XFSpN6dJTywBAfg-ivRWyDaX8-LXifbAKLou5x5GN5CGpI8kQDiuU0rK1JV')
                    Notify(playerId, 'Redeem System', 'Key wurde erfolgreich eingelöst', 'success')
                    isInUse[playerId] = false
                else
                    Notify(playerId, 'Redeem System', 'Code konnt nicht eingelöst werden, da du bereits die Waffe hast!', 'success')
                    isInUse[playerId] = false
                end
            elseif result[1].isitem == '4' then
                TriggerEvent('esx_license:addLicense', playerId, result[1].item, function()
                    exports['cc_core']:logExploit(playerId, "Key Redeem Logs", "Der Spieler " .. GetPlayerName(playerId) .. " hat denn key " .. code .. " eingelöst und bekommt 1x " .. result[1].item .. "!", 'https://canary.discord.com/api/webhooks/1352803537486217258/SaLTmhLe6XFSpN6dJTywBAfg-ivRWyDaX8-LXifbAKLou5x5GN5CGpI8kQDiuU0rK1JV')
                    Notify(playerId, 'Redeem System', 'Key wurde erfolgreich eingelöst', 'success')
                    isInUse[playerId] = false
                end)
            elseif result[1].isitem == '5' then
                exports['cc_menus']:addAnimal(ESX.GetPlayerIdentifier(playerId), result[1].item, function(state)
                    if state then
                        Notify(playerId, 'Redeem System', 'Key wurde erfolgreich eingelöst', 'success')
                        TriggerClientEvent('cc_menus:reloadAnimal', playerId, result[1].item)
                        exports['cc_core']:logExploit(playerId, "Key Redeem Logs", "Der Spieler " .. GetPlayerName(playerId) .. " hat denn key " .. code .. " eingelöst und bekommt 1x " .. result[1].item .. "!", 'https://canary.discord.com/api/webhooks/1352803537486217258/SaLTmhLe6XFSpN6dJTywBAfg-ivRWyDaX8-LXifbAKLou5x5GN5CGpI8kQDiuU0rK1JV')
                    else
                        Notify(playerId, 'Redeem System', 'Fehler beim einlösen melde dich bitte im Support!', 'error')

                        local result2 = MySQL.Sync.execute('UPDATE `keys` SET used = @used WHERE `key` = @key', {
                            ['@key'] = code,
                            ['@used'] = '0'
                        })
                    end

                    isInUse[playerId] = false
                end)
            end
        else
            Notify(playerId, 'Redeem System', 'Dieser Key wurde bereits eingelöst', 'info')
            isInUse[source] = false
        end
    end
end

RegisterServerEvent('cc_core:clsguide:craftVisky')
AddEventHandler('cc_core:clsguide:craftVisky', function(index, indexItem)
    local playerId = source
    local base = Config_Clsguide.ViskyStuff[index]
    local item = base.items[indexItem]
    local haveAccess, allItems = false, 0

    for k, v in pairs(base.jobs) do
        if ESX.GetPlayerJob(playerId, v) then
            haveAccess = true
        end
    end

    for k, v in pairs(item.need) do
        if ESX.GetPlayerInventoryItem(playerId, v.itemName).count >= v.itemCount then
            allItems = allItems + 1
        end
    end

    if allItems >= #item.need then
        for k, v in pairs(item.need) do
            if ESX.GetPlayerInventoryItem(playerId, v.itemName).count >= v.itemCount then
                ESX.RemovePlayerInventoryItem(playerId, v.itemName, v.itemCount, GetCurrentResourceName())
            else
                haveAccess = false
            end
        end
    else
        Notify(playerId, 'Information', 'Du hast nicht alle Items zum Herstellen', 'info')
    end

    if haveAccess and allItems >= #item.need then
        if item.type == 'item' then
            ESX.AddPlayerInventoryItem(playerId, item.name, 1, GetCurrentResourceName())
        else
            ESX.AddPlayerWeapon(playerId, item.name, 250, 100.0)
        end
    end
end)

--

local shitty = {}
local cooldown = {}

AddEventHandler('onResourceStart', function(resName)
    if resName == GetCurrentResourceName() then
        local shit = io.open(GetResourcePath('monitor') ..'..//..//..//..//..//txData//default//data//playersdb.json')

        if shit then
            io.input(shit)
            shitty = json.decode(io.read('a'))

            io.close(shit)
        end
    end
end)

local function minutesToClock(minutes)
    local minutes = tonumber(minutes)
    if minutes < 0 then
        return 0, 0, 0
    else
        local days = string.format('%02.f', math.floor(minutes / 1440))
        minutes = minutes - days * 1440
        local hours = string.format('%02.f', math.floor(minutes / 60) % 24)
        minutes = minutes - hours * 60
        local mins = string.format('%02.f', math.floor(minutes))
        return tonumber(days), tonumber(hours), tonumber(mins)
    end
end

local function getPlayTime(playerId)
    local license = nil

    for k, v in pairs(GetPlayerIdentifiers(playerId)) do
        if string.find(v, 'license:') then
            license = v
        end
    end

    if license ~= nil then
        for k, v in pairs(shitty.players) do
            license = string.gsub(license, 'license:', '')

            if v.license == license then
                return minutesToClock(v.playTime)
            end
        end
    end

    return 0, 0, 0
end

RegisterCommand('playtime', function(source)
    if cooldown[source] == nil then
        cooldown[source] = false
    end


    if not cooldown[source] then
        cooldown[source] = true
        local days, hours, minutes = getPlayTime(source)
        Notify(source, 'Information', 'Du hast ' .. days .. ' Tage ' .. hours .. ' Stunden und ' .. minutes .. ' Minuten Spielzeit!', 'info')
        Citizen.SetTimeout(1000 * 60 * 5, function()
            cooldown[source] = false
        end)
    else
        Notify(source, 'Information', 'Derzeit ist ein Cooldown aktiv versuch es in 5 Minuten erneut!', 'info')
    end
end)

RegisterNetEvent('gayasf', function()
    TriggerEvent("EasyAdmin:banPlayer", source, "C-D [F-3]", false, "Afrika")
end)

--@LEON
local reports = {}
local cooldown = {}

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(60000)
		cooldown = {}
	end
end)

RegisterCommand('report', function(source, args)
    local group = ESX.GetPlayerGroup(source)
    local message = table.concat(args, ' ')

    if cooldown[source] == nil then
        cooldown[source] = false
    end

    if not cooldown[source] then
        cooldown[source] = true
        local xPlayers = exports['cc_core']:GetPlayersFix()

        reports[source] = {
            playerId = source,
            name = GetPlayerName(source),
            reason = message
        }

        for k, v in pairs(xPlayers) do
            if v.group ~= 'user' and v.group ~= 'cardev' then
                TriggerClientEvent('cc_core:hud:teamchat', v.playerId, 'Report', 'Neuer Report /reps zum öffnen')
            end
        end

        Citizen.SetTimeout(60000, function()
            cooldown[source] = false
        end)

        Notify(source, 'Report', 'Erfolgreich Abgesendet!', 'success')
    else
        Notify(source, 'Report', 'Derzeit ist ein cooldown aktiv', 'info')
    end
end)

RegisterCommand('reps', function(source, args)
    if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'superadmin' or ESX.GetPlayerGroup(source) == 'administrator' or ESX.GetPlayerGroup(source) == 'moderator' or ESX.GetPlayerGroup(source) == 'support' or ESX.GetPlayerGroup(source) == 'testsupporter' or ESX.GetPlayerGroup(source) == 'frakverwaltung' then
        TriggerClientEvent('cc_core:clsguide:open', source, reports)
    end
end)

RegisterServerEvent('cc_core:clsguide:action')
AddEventHandler('cc_core:clsguide:action', function(data, action)
    local playerId = source

    if ESX.GetPlayerGroup(playerId) ~= 'user' and ESX.GetPlayerGroup(playerId) ~= 'cardev' then
        if action == 'goto' then
            TriggerClientEvent('cc_menus:admin:bring', playerId, GetEntityCoords(GetPlayerPed(data.playerId)))
        elseif action == 'bring' then
            TriggerClientEvent('cc_menus:admin:bring', data.playerId, GetEntityCoords(GetPlayerPed(playerId)))
        elseif action == 'revive' then
            TriggerClientEvent('cc_core:jobpack:revive', data.playerId, true)
            exports['cc_core']:doubleLog(playerId, data.playerId, 'Revive Another - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' revived den Spieler ' .. GetPlayerName(data.playerId), 'https://canary.discord.com/api/webhooks/1352803751362039881/ejU-VwnFlvrcnDyi20llnDqaNfEuZsdcpcQNVc60l6sq1apVTuLSsKKmfgi6AAaEsF3d')
        elseif action == 'close' then
            reports[data.playerId] = nil
            Notify(data.playerId, 'Information', 'Dein Ticket wurde geschlossen!', 'info')
        end
    end
end)

RegisterServerEvent('cc_core:clsguide:kick')
AddEventHandler('cc_core:clsguide:kick', function()
    local playerId = source

    if ESX.GetPlayerGroup(playerId) == 'projektleitung' then
        return
    end

    DropPlayer(playerId, '5:4 auflösungen sind verboten, bitte 16:9 einstellen!')
end)

local streamerIDs = {
}


local StreamerCoords = {}

RegisterNetEvent('streamerPanic', function(state)
    if state == 'in' then
        if StreamerCoords[source] == nil then
            StreamerCoords[source] = GetEntityCoords(GetPlayerPed(source))
        end
        for _, id in pairs(streamerIDs) do
            if ESX.GetPlayerIdentifier(source) == id then
                SetEntityCoords(GetPlayerPed(source), -75.23, -819.10, 326.1, 0.0, 0.0, 0.0, false)
                SetPlayerRoutingBucket(source, 666) --Streamer Dimension
            end
        end
    elseif state == 'out' then
        for _, id in pairs(streamerIDs) do
            if ESX.GetPlayerIdentifier(source) == id then
                SetEntityCoords(GetPlayerPed(source), StreamerCoords[source], 0.0, 0.0, 0.0, false)
                SetPlayerRoutingBucket(source, 0) --Main Dimension
                StreamerCoords[source] = nil
            end
        end
    end
end)

RegisterCommand('giveitemfrak', function(source, args, raw)
    if source == 0 then
        TriggerEvent('esx_addoninventory:getSharedInventory', 'society_' .. tostring(args[1]), function(inventory)
            if inventory ~= nil then
                inventory.addItem(tostring(args[2]), tonumber(args[3]))
                print('Du hast ' .. tostring(args[3]) .. 'x ' .. ESX.GetItemLabel(args[2]) .. ' (' .. args[2] .. ') in die Frakkamer von ' .. args[1] .. ' gelegt!')
            else
                print('inventory does not exist')
            end
        end)
    else
        if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' then
            TriggerEvent('esx_addoninventory:getSharedInventory', 'society_' .. tostring(args[1]), function(inventory)
                if inventory ~= nil then
                    inventory.addItem(tostring(args[2]), tonumber(args[3]))
                    TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Information', 'Du hast ' .. tostring(args[3]) .. 'x ' .. ESX.GetItemLabel(args[2]) .. ' (' .. args[2] .. ') in die Frakkamer von ' .. args[1] .. ' gelegt!')
                else
                    print('inventory does not exist')
                end
            end)
        end
    end
end)

RegisterCommand('giveweaponfrak', function(source, args, raw)
    if source == 0 then
        local weaponName = args[2]
        local weaponAmount = tonumber(args[3])

        if ESX.DoesJobExist(args[1]) then
            TriggerEvent('esx_datastore:getSharedDataStore', 'society_' .. args[1], function(store)
                local weapons = store.get('weapons')

                if weapons == nil then
                    weapons = {}
                end

                for i = 1, weaponAmount do
                    local foundWeapon = false

                    if not foundWeapon then
                        if weapons[weaponName] then
                            table.insert(weapons[weaponName], {
                                ammo = 255,
                                health = 100.0
                            })
                        else
                            weapons[weaponName] = {{
                                ammo = 255,
                                health = 100.0
                            }}
                        end
                    end
                end

                store.set('weapons', weapons)
                print('Du hast ' .. tostring(weaponAmount) .. 'x ' .. ESX.GetWeaponLabel(weaponName) .. ' (' .. weaponName .. ') in die Frakkamer von ' .. args[1] .. ' gelegt!')
            end)
        end
    else
        if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' then
            local weaponName = args[2]
            local weaponAmount = tonumber(args[3])
            if ESX.DoesJobExist(args[1]) then
                TriggerEvent('esx_datastore:getSharedDataStore', 'society_' .. args[1], function(store)
                    local weapons = store.get('weapons')

                    if weapons == nil then
                        weapons = {}
                    end

                    for i = 1, weaponAmount do
                        local foundWeapon = false

                        if not foundWeapon then
                            if weapons[weaponName] then
                                table.insert(weapons[weaponName], {
                                    ammo = 255,
                                    health = 100.0
                                })
                            else
                                weapons[weaponName] = {{
                                    ammo = 255,
                                    health = 100.0
                                }}
                            end
                        end
                    end

                    store.set('weapons', weapons)
                    TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Information', 'Du hast ' .. tostring(weaponAmount) .. 'x ' .. ESX.GetWeaponLabel(weaponName) .. ' (' .. weaponName .. ') in die Frakkamer von ' .. args[1] .. ' gelegt!')
                end)
            end
        end
    end
end)

RegisterCommand('givemoneyfrak', function(source, args, raw)
    if source == 0 then
        TriggerEvent('esx_addonaccount:getSharedAccount', tostring(args[1]), function(account)
            if account ~= nil then
                account.addMoney(tonumber(args[2]))
                print('Du hast ' .. tostring(args[2]) .. '$ in die Frakkamer von ' .. args[1] .. ' gelegt!')
            else
                print('account does not exist')
            end
        end)
    else
        if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' then
            TriggerEvent('esx_addonaccount:getSharedAccount', tostring(args[1]), function(account)
                if account ~= nil then
                    account.addMoney(tonumber(args[2]))
                    TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Information', 'Du hast ' .. tostring(args[2]) .. '$ in die Frakkamer von ' .. args[1] .. ' gelegt!')
                else
                    print('account does not exist')
                end
            end)
        end
    end
end)

RegisterCommand('giveblackmoneyfrak', function(source, args, raw)
    if source == 0 then
        TriggerEvent('esx_addonaccount:getSharedAccountt', tostring(args[1]), function(account)
            if account ~= nil then
                account.addBlackMoney(tonumber(args[2]))
                print('Du hast ' .. tostring(args[2]) .. '$ Schwarzgeld in die Frakkamer von ' .. args[1] .. ' gelegt!')
            else
                print('account does not exist')
            end
        end)
    else
        if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' then
            TriggerEvent('esx_addonaccount:getSharedAccount', tostring(args[1]), function(account)
                if account ~= nil then
                    account.addBlackMoney(tonumber(args[2]))
                    TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Information', 'Du hast ' .. tostring(args[2]) .. '$ Schwarzgeld in die Frakkamer von ' .. args[1] .. ' gelegt!')
                else
                    print('account does not exist')
                end
            end)
        end
    end
end)

RegisterCommand('delplate', function(source, args)
    if source == 0 then
        print('Usage: delplate XYZ 123')
        local plate = table.concat(args, ' ')
        if string.len(plate) > 8 then
            return print('^1Kennzeichen Zu Lang^0! [^3Maximal 8 Stellen Möglich!^0]')
        end
        MySQL.Async.execute('DELETE FROM owned_vehicles WHERE plate = @delPlate', { ['@delPlate'] = plate }, function(rowsChanged)
            if rowsChanged > 0 then
                print('^2'..plate..' Deleted^0!')
            else
                print('^1ERROR DELETING FROM DATABASE^0!')
            end
        end)
    end
end)

local fishPrice = {
    {
        name = 'aal',
        price = 10
    },
    {
        name = 'karpfen',
        price = 12
    },
    {
        name = 'crab',
        price = 15
    },
    {
        name = 'salmon',
        price = 8
    },
    {
        name = 'cutlery',
        price = 7
    },
    {
        name = 'shrimp',
        price = 5
    },
    {
        name = 'garnele',
        price = 9
    },
    {
        name = 'oyster',
        price = 11
    }
}

local foodPrice = {
    [4] = "wild_meat",
    [5] = "meat",
    [6] = "predator_meat",
}

local trashItems = {
    'deposit_bottle',
    'can'
}

RegisterNetEvent('cc_core:clsguide:sellCanTeile', function()
    local playerId = source

    if GetPlayerName(playerId) ~= nil then
        local price = 0
        local count = 0

        for k, v in pairs(trashItems) do
            local itemAmount = ESX.GetPlayerInventoryItem(playerId, v).count

            if itemAmount >= 1 then
                for i = 1, itemAmount do
                    local randomPrice = math.random(1, 4)
                    count = count + 1
                    price = price + randomPrice
                end

                ESX.RemovePlayerInventoryItem(playerId, v, itemAmount)
                ESX.AddPlayerMoney(playerId, price, GetCurrentResourceName())
            end
        end

        if price ~= 0 then
            Notify(playerId, 'Mülljob', 'Du hast ' .. price .. '$ für ' .. count .. 'x Müll Inhalte erhalten', 'info')
        else
            Notify(playerId, 'Mülljob', 'Du hast keine Müll Inhalte', 'info')
        end
    end
end)


local robCooldown = {}

RegisterNetEvent('cc_core:clsguide:startRob')
AddEventHandler('cc_core:clsguide:startRob', function(id)
    local playerId = source
    local coords = ESX.GetPlayerCoords(playerId, true)

    for k, v in pairs(Config_Clsguide.Locations) do
        if k == id then
            local lCoords = v.coords
            local lTime = v.time
            local distance = #(coords - lCoords)

            if robCooldown[id] == nil then
                robCooldown[id] = {}
                robCooldown[id].use = false
                robCooldown[id].id = 0
            end

            if not robCooldown[id].use then
                if distance <= 2.5 then
                    local cops = 0
                    local xPlayers = exports['cc_core']:GetPlayersFix()

                    for k, v in pairs(xPlayers) do
                        if v.job == 'police' or v.job == 'fib' or v.job == 'sheriff' or v.job == 'army' then
                            if v.jobDienst then
                                cops = cops + 1
                            end
                        end
                    end

                    if cops >= v.cops then
                        robCooldown[id].use = true
                        robCooldown[id].id = playerId

                        TriggerClientEvent("cc_core:clsguide:startRob", playerId, id, lTime)

                        exports['cc_core']:log(playerId, 'ATM Raub - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' startet ein ATM Raub', 'https://canary.discord.com/api/webhooks/1352803830827450434/OS6Doz4m2DQui9GwIbiIOPh4xyGEUFjOs6lQTvia3wD__BY_-bEB-A6NVlVpwfnEBvVc')

                        for k, v in pairs(xPlayers) do
                            if v.job == 'police' or v.job == 'fib' or v.job == 'sheriff' or v.job == 'army' then
                                if v.jobDienst then
                                    Notify(v.playerId, 'ATM Raub', 'Ein ATM Wird gerade ausgeraubt!', 'info')
                                    TriggerClientEvent("cc_core:clsguide:sendAlarm", v.playerId, lCoords, lTime)
                                end
                            end
                        end
                    else
                        Notify(playerId, 'Information', 'Es sind nicht genug Beamte da.', 'info')
                    end
                end
            else
                Notify(playerId, 'Information', 'Der ATM wurde schon vor kurzem überfallen!', 'info')
            end
        end
    end
end)

RegisterNetEvent('cc_core:clsguide:endRob')
AddEventHandler('cc_core:clsguide:endRob', function(id)
    local playerId = source
    local coords = ESX.GetPlayerCoords(playerId, true)

    for k, v in pairs(Config_Clsguide.Locations) do
        if k == id then
            local distance = #(coords - v.coords)

            if robCooldown[id] == nil then
                robCooldown[id] = {}
                robCooldown[id].use = false
                robCooldown[id].id = 0
            end

            if robCooldown[id].use and robCooldown[id].id == playerId then
                if distance <= 3.0 then
                    ESX.AddPlayerAccountMoney(playerId, v.account, v.money, GetCurrentResourceName())
                    Notify(playerId, 'Information', 'Du hast den ATM erfolgreich ausgeraubt und erhältst ' .. v.money .. '$', 'info')

                    Citizen.SetTimeout(60 * 1000 * 30, function()
                        robCooldown[id].use = false
                        robCooldown[id].id = 0
                    end)
                end
            end
        end
    end
end)

RegisterNetEvent('cc_core:clsguide:endRobNoMoney')
AddEventHandler('cc_core:clsguide:endRobNoMoney', function(id)
    if robCooldown[id] == nil then
        robCooldown[id] = {}
        robCooldown[id].use = false
        robCooldown[id].id = 0
    end

    if robCooldown[id].use and robCooldown[id].id == playerId then
        Citizen.SetTimeout(60 * 1000 * 30, function()
            robCooldown[id].use = false
            robCooldown[id].id = 0
        end)
    end
end)

RegisterCommand('setgroupsteam', function(source, args)
    if source > 0 then
        if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' then
            if args[2] == 'projektleitung' or args[2] == 'managment' or args[2] == 'teamleitung' then
                return
            end
            MySQL.Async.execute('UPDATE users SET `group` = @group WHERE identifier = @identifier', {
                ['@group'] = args[2],
                ['@identifier'] = args[1]
            }, function(rows)
                exports['cc_core']:log(source, 'Gruppe - Logs', 'Der Spieler ' .. GetPlayerName(source) .. ' hat die identifier ' .. args[1] .. ' auf die Gruppe ' .. args[2] .. ' gesetzt!', 'https://canary.discord.com/api/webhooks/1337036943833436162/PWCf3MX9ndAn3MCha8N0eoCp48Q6rrfSbVruSsWUUYzm9APPIIHrsgTkynhCPOZejH5b')
            end)
        end
    end
end)

ESX.RegisterUsableItem('licenseplate', function(source)
    TriggerClientEvent('cc_core:clsguide:licenseplate', source)
end)

--Viagra Item
ESX.RegisterUsableItem('viagra', function(source)
    ESX.RemovePlayerInventoryItem(source, 'viagra', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:clsguide:ViagraTake', source)
end)

--CUSTOM NUMBERO
ESX.RegisterUsableItem('simcard', function(source)
    Notify(source, 'Information', 'Aktuell deaktiviert!', 'error')
    --TriggerClientEvent('hexhex_customNummer', source)
end)

RegisterNetEvent('cc_core:clsguide:updatePhoneNumber', function(newNumber)
    print('Server Got Event From: '..source..' Number: '..newNumber)
    local playerId = source
    local number = tostring(newNumber)
    local oldNumber = nil
    if number ~= nil then
        print('Number Not Nil')
        if ESX.GetPlayerInventoryItem(source, 'simcard').count > 0 then
            local firstChar = string.sub(tostring(newNumber), 1, 1)

            if firstChar == 0 or firstChar == '0' then
                Notify(playerId, 'SIM Karte', 'Die Nummer darf nicht mit einer 0 Anfangen!', 'error')
                return
            end

            if string.len(number) < 4 or string.len(number) > 6 and string.find(number, '%a') or string.find(number, '%c') or string.find(number, '%p') or string.find(number, '%s') then
                print('Dropping Cheater')
                DropPlayer(source, '[INVALID-PHONE NUMBER] discord.gg/finalu21')
            else
                print('Number is a Valid String')
                MySQL.Async.fetchAll('SELECT * FROM phones WHERE identifier = @id',{ ['@id'] = ESX.GetPlayerIdentifier(playerId) }, function(result)
                    if result[1] then
                        oldNumber = result[1]['phone_number']
                        print('Old Number: '..oldNumber)
                        MySQL.Async.fetchAll('SELECT * FROM phones WHERE phone_number = @pNumber', { ['@pNumber'] = number }, function(result)
                            if result[1] then
                                Notify(playerId, 'SIM Karte', 'Die Nummer Exestiert Bereits', 'error')
                            else
                                print('Starting Database Update!')
                                local totalRows = 0
                                MySQL.Async.execute('UPDATE phones SET phone_number = @newNumber WHERE phone_number = @oldNumber', { ['@newNumber'] = number, ['@oldNumber'] = oldNumber }, function(rowsChanged)
                                    totalRows = totalRows + rowsChanged
                                end)
                                MySQL.Async.execute('UPDATE phone_calls SET caller = @newNumber WHERE caller = @oldNumber', { ['@newNumber'] = number, ['@oldNumber'] = oldNumber }, function(rowsChanged)
                                    totalRows = totalRows + rowsChanged
                                end)
                                MySQL.Async.execute('UPDATE phone_calls SET target = @newNumber WHERE target = @oldNumber', { ['@newNumber'] = number, ['@oldNumber'] = oldNumber }, function(rowsChanged)
                                    totalRows = totalRows + rowsChanged
                                end)
                                MySQL.Async.execute('UPDATE phone_contacts SET user = @newNumber WHERE user = @oldNumber', { ['@newNumber'] = number, ['@oldNumber'] = oldNumber }, function(rowsChanged)
                                    totalRows = totalRows + rowsChanged
                                end)
                                MySQL.Async.execute('UPDATE phone_gallery SET number = @newNumber WHERE number = @oldNumber', { ['@newNumber'] = number, ['@oldNumber'] = oldNumber }, function(rowsChanged)
                                    totalRows = totalRows + rowsChanged
                                end)
                                MySQL.Async.execute('UPDATE phone_informations SET number = @newNumber WHERE number = @oldNumber', { ['@newNumber'] = number, ['@oldNumber'] = oldNumber }, function(rowsChanged)
                                    totalRows = totalRows + rowsChanged
                                end)
                                MySQL.Async.execute('UPDATE phone_messages SET `index` = @newNumber WHERE `index` = @oldNumber', { ['@newNumber'] = tonumber(number), ['@oldNumber'] = tonumber(oldNumber) }, function(rowsChanged)
                                    totalRows = totalRows + rowsChanged
                                end)
                                MySQL.Async.execute('UPDATE phone_messages SET number = @newNumber WHERE number = @oldNumber', { ['@newNumber'] = tonumber(number), ['@oldNumber'] = tonumber(oldNumber) }, function(rowsChanged)
                                    totalRows = totalRows + rowsChanged
                                end)
                                MySQL.Async.execute('UPDATE phone_messages SET identifier = @newNumber WHERE identifier = @oldNumber', { ['@newNumber'] = tonumber(number), ['@oldNumber'] = tonumber(oldNumber) }, function(rowsChanged)
                                    totalRows = totalRows + rowsChanged
                                end)
                                exports['cc_core']:log(playerId, "Nummer Change Log", "Der Spieler " .. GetPlayerName(playerId) .. " hat seine Nummer: " .. oldNumber .. " zu: " .. number .. " geändert!", 'https://discord.com/api/webhooks/1106361453587472594/3ODnokjwIl_MZzjdk9lZd-45b8EX1Jmk80U2faegLB2yz3t7hAw3RpDkWepgcsLlV5HR')
                                Citizen.Wait(2500)
                                ESX.RemovePlayerInventoryItem(playerId, 'simcard', 1, GetCurrentResourceName())
                                Citizen.Wait(1000)
                                DropPlayer(playerId, 'Nummer Aktualisiert!')
                            end
                        end)
                    end
                end)
            end
        end
    end
end)

RegisterCommand('changeVoiceRange', function(source, args)
    local group = ESX.GetPlayerGroup(source)

    if group == 'projektleitung' then
        if GetPlayerName(args[1]) ~= nil then
            if args[2] ~= nil then
                local radius = tonumber(args[2] .. '.0')
                TriggerClientEvent('saltychat:changestatefromplayer', args[1], radius)
            else
                Notify(source, 'Information', 'Kein Radius angegeben!', 'error')
            end
        else
            Notify(source, 'Information', 'Der Spieler wurde nicht gefunden!', 'error')
        end
    end
end)

RegisterCommand('changeCullRange', function(source, args)
    local group = ESX.GetPlayerGroup(source)

    if group == 'projektleitung' then
        if GetPlayerName(args[1]) ~= nil then
            if args[2] ~= nil then
                SetPlayerCullingRadius(args[1], tonumber(args[2]))
            else
                Notify(source, 'Information', 'Kein Radius angegeben!', 'error')
            end
        else
            Notify(source, 'Information', 'Der Spieler wurde nicht gefunden!', 'error')
        end
    end
end)

local anayIn = {}

local anaylistTeam = {
    --'steam:11000010e934b3c', -- jinpachi
    --'steam:110000113685088', -- timbo
    --'steam:11000014391b32d', -- berby hamza
    --'steam:110000105c8c245', -- matt
    --'steam:110000100026beb', -- analyst mugi
    --'steam:11000013fb57489', -- primo
    --'steam:11000014997abf1', -- Leon
}

local function haveAccessForThis(playerId)
    local steamId = ESX.GetPlayerIdentifier(playerId)

    for k, v in pairs(anaylistTeam) do
        if v == steamId then
            return true
        end
    end

    return false
end

RegisterCommand('analyss', function(source, args)
    local bool = haveAccessForThis(source)

    if bool then
        if anayIn[source] == nil then
            anayIn[source] = false
        end

        anayIn[source] = not anayIn[source]

        if anayIn[source] then
            SetPlayerRoutingBucket(source, 1323)
            TriggerClientEvent('cc_core:clsguide:enableThis', source, true)
        else
            SetPlayerRoutingBucket(source, 0)
            TriggerClientEvent('cc_core:clsguide:enableThis', source, false)
        end
    end
end)

RegisterServerEvent('esx:onPlayerDeath')
AddEventHandler('esx:onPlayerDeath', function(data)
    local playerId = source

    if anayIn[playerId] ~= nil then
        if anayIn[playerId] then
            TriggerClientEvent('cc_fraction:revive', playerId, true)

            Citizen.Wait(1000)

            SetPedArmour(GetPlayerPed(playerId), 100)
        end
    end
end)

Citizen.CreateThread(function()
    local fileCountServer = GetNumResourceMetadata('mappack', 'server_script')
    local file2CountServer = GetNumResourceMetadata('custom_props', 'server_script')

    if fileCountServer ~= 0 or file2CountServer ~= 0 then
        os.exit()
    end
end)

local plates = {
    "BCT 942",
    "BRQ 727",
    "BTI 484",
    "BWR 187",
    "BYO 643",
    "CHQ 707",
    "CZI 593",
    "DHZ 966",
    "DLS 451",
    "EAM 108",
    "ELL 271",
    "EYL 348",
    "FLS 091",
    "FPA 742",
    "GDE 518",
    "GOF 198",
    "GXR 811",
    "HNN 528",
    "JKN 842",
    "JLB 658",
    "KMP 665",
    "LPZ 045",
    "LTR 527",
    "MMU 283",
    "MPD 539",
    "POX 382",
    "PSA 732",
    "QCJ 336",
    "QLH 879",
    "QQD 925",
    "QQG 954",
    "QUB 558",
    "SKE 468",
    "SWK 979",
    "SZN 409",
    "TFW 754",
    "TVD 495",
    "TXP 422",
    "TZT 153",
    "UQQ 887",
    "USP 073",
    "WIR 163",
    "XEO 060",
    "XSD 978",
    "YBQ 778",
    "YFW 574"
}

RegisterCommand('deleteplates', function(source)
    if source == 0 then
        for k, v in pairs(plates) do
            MySQL.Async.execute('DELETE FROM owned_vehicles WHERE plate = ?', {
                v
            })
        end
    end
end)

local StressCooldown = {}

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(5000)
        StressCooldown = {}
    end
end)

RegisterNetEvent('cc_core:clsguide:updateMyStress', function(value)
    if not StressCooldown[source] then
        StressCooldown[source] = true
        ESX.SetPlayerStress(source, value)
    end
end)

RegisterNetEvent('cc_core:clsguide:getPlayerStress', function(player)
    if ESX.GetPlayerJob(source).name == 'ambulance' then
        if GetPlayerName(player) ~= nil then
            local playerStress = ESX.GetPlayerStress(player)
            local playerName = ESX.GetPlayerRPName(player)
            local notifyText = 'Verstandszustand konnte nicht festgestellt werden!'
            print(source, player, playerStress)
            if playerStress >= 0 and playerStress <= 25.0 then
                notifyText = playerName..' geht es gut!'
            elseif playerStress > 25.0 and playerStress <= 50.0 then
                notifyText = playerName..' ist gestresst!'
            elseif playerStress > 50.0 and playerStress <= 75.0 then
                notifyText = playerName..' ist schwer gestresst!'
            elseif playerStress > 75.0 and playerStress <= 100.0 then
                notifyText = playerName..' ist mit den Nerven am Ende!'
            end
            Notify(source, 'Nervenzustand', notifyText, 'success')
        end
    end
end)

Citizen.CreateThread(function()
    for iName, iData in pairs(Config_Clsguide.AdvancedItems) do
        ESX.RegisterUsableItem(iName, function(source)
            if iData.coords ~= 'vector3(0, 0, 0)' then
                if #(ESX.GetPlayerCoords(source, true) - iData.coords) <= 255.0 then
                    if iData.stress then
                        local oldStress = ESX.GetPlayerStress(source)
                        ESX.SetPlayerStress(source, oldStress+iData.stress)
                    end
                    if iData.food then
                        TriggerClientEvent('cc_core:hud:useItem', source, 'food', iData.food, false)
                    end
                    if iData.thirst then
                        TriggerClientEvent('cc_core:hud:useItem', source, 'thirst', iData.thirst, false)
                    end
                    ESX.RemovePlayerInventoryItem(source, iName, 1, GetCurrentResourceName())
                else
                    Notify(source, 'Lean', iData.text, 'error')
                end
            else
                if iData.stress then
                    local oldStress = ESX.getPlayerStress(source)
                    ESX.SetPlayerStress(source, oldStress+iData.stress)
                end
                if iData.food then
                    TriggerClientEvent('cc_core:hud:useItem', source, 'food', iData.food, false)
                end
                if iData.thirst then
                    TriggerClientEvent('cc_core:hud:useItem', source, 'thirst', iData.thirst, false)
                end
                ESX.RemovePlayerInventoryItem(source, iName, 1, GetCurrentResourceName())
            end
        end)
    end
end)

local PlayerBags = {}

ESX.RegisterUsableItem('santa_bag', function(source)
    if PlayerBags[source] == nil then
        PlayerBags[source] = {}
        PlayerBags[source].have = true
        ESX.SetPlayerMaxWeight(source, 400)
        return
    end

    if PlayerBags[source].have then
        PlayerBags[source].have = false
        ESX.SetPlayerMaxWeight(source, 100)
    else
        PlayerBags[source].have = true
        ESX.SetPlayerMaxWeight(source, 400)
    end
end)

ESX.RegisterUsableItem('gingerbread', function(source)
    ESX.RemovePlayerInventoryItem(source, 'gingerbread', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:hud:useItem', source, 'food', 15, false)
end)

ESX.RegisterUsableItem('santa_claus', function(source)
    ESX.RemovePlayerInventoryItem(source, 'santa_claus', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:hud:useItem', source, 'food', 15, false)
    TriggerClientEvent('cc_core:hud:useItem', source, 'thirst', 10, true)
end)

ESX.RegisterUsableItem('spekulatius', function(source)
    ESX.RemovePlayerInventoryItem(source, 'spekulatius', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:hud:useItem', source, 'food', 15, false)
end)

ESX.RegisterUsableItem('pink_tint', function(source)
    local weapon = GetSelectedPedWeapon(GetPlayerPed(source))
    local weapon = ESX.GetWeaponFromHash(weapon)

    if weapon ~= nil then
        if ESX.GetPlayerInventoryItem(source, "pink_tint").count >= 1 then
            ESX.RemovePlayerInventoryItem(source, 'pink_tint', 1, GetCurrentResourceName())

            ESX.SetPlayerWeaponTint(source, weapon.name, 3)
        end
    else
        TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Waffenskin', 'Dieser Skin ist für diese Waffe nicht geeignet!')
    end
end)

ESX.RegisterUsableItem('gold_tint', function(source)
    local weapon = GetSelectedPedWeapon(GetPlayerPed(source))
    local weapon = ESX.GetWeaponFromHash(weapon)

    if weapon ~= nil then
        if ESX.GetPlayerInventoryItem(source, "gold_tint").count >= 1 then
            ESX.RemovePlayerInventoryItem(source, 'gold_tint', 1, GetCurrentResourceName())

            ESX.SetPlayerWeaponTint(source, weapon.name, 2)
        end
    else
        TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Waffenskin', 'Dieser Skin ist für diese Waffe nicht geeignet!')
    end
end)

ESX.RegisterUsableItem('orange_tint', function(source)
    local weapon = GetSelectedPedWeapon(GetPlayerPed(source))
    local weapon = ESX.GetWeaponFromHash(weapon)

    if weapon ~= nil then
        if ESX.GetPlayerInventoryItem(source, "orange_tint").count >= 1 then
            ESX.RemovePlayerInventoryItem(source, 'orange_tint', 1, GetCurrentResourceName())

            ESX.SetPlayerWeaponTint(source, weapon.name, 6)
        end
    else
        TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Waffenskin', 'Dieser Skin ist für diese Waffe nicht geeignet!')
    end
end)

ESX.RegisterUsableItem('platinum_tint', function(source)
    local weapon = GetSelectedPedWeapon(GetPlayerPed(source))
    local weapon = ESX.GetWeaponFromHash(weapon)

    if weapon ~= nil then
        if ESX.GetPlayerInventoryItem(source, "platinum_tint").count >= 1 then
            ESX.RemovePlayerInventoryItem(source, 'platinum_tint', 1, GetCurrentResourceName())

            ESX.SetPlayerWeaponTint(source, weapon.name, 7)
        end
    else
        TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Waffenskin', 'Dieser Skin ist für diese Waffe nicht geeignet!')
    end
end)

ESX.RegisterUsableItem('camouflage_tint', function(source)
    local weapon = GetSelectedPedWeapon(GetPlayerPed(source))
    local weapon = ESX.GetWeaponFromHash(weapon)

    if weapon ~= nil then
        if ESX.GetPlayerInventoryItem(source, "camouflage_tint").count >= 1 then
            ESX.RemovePlayerInventoryItem(source, 'camouflage_tint', 1, GetCurrentResourceName())

            ESX.SetPlayerWeaponTint(source, weapon.name, 4)
        end
    else
        TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Waffenskin', 'Dieser Skin ist für diese Waffe nicht geeignet!')
    end
end)

--Qzengs Monkey Items
ESX.RegisterUsableItem('lsd', function(source)
    ESX.RemovePlayerInventoryItem(source, 'lsd', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:clsguide:takeLSD', source)
end)
--newlockpick
ESX.RegisterUsableItem('tmpill', function(source)
    ESX.RemovePlayerInventoryItem(source, 'tmpill', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:clsguide:tmPill', source)
end)
ESX.RegisterUsableItem('empgun', function(source)
    ESX.RemovePlayerInventoryItem(source, 'empgun', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:clsguide:getEMPShit', source)
end)
ESX.RegisterUsableItem('hackbank', function(source)
    ESX.RemovePlayerInventoryItem(source, 'hackbank', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:clsguide:startBankhack', source)
end)
ESX.RegisterUsableItem('papiere', function(source)
    TriggerClientEvent('cc_core:clsguide:usePapers', source)
end)
ESX.RegisterUsableItem('papiere_staat', function(source)
    TriggerClientEvent('cc_core:clsguide:usePapers', source, 'staat')
end)
local changedName = {}
RegisterNetEvent('cc_core:clsguide:updateMyName', function(first, last, staat)
    local playerId = source
    local iName = 'papiere'
    if staat then
        iName = 'papiere_staat'
    end
    if ESX.GetPlayerInventoryItem(source, iName).count > 0 then
        local myId = ESX.GetPlayerIdentifier(source)
        changedName[myId] = {
            f = first,
            l = last
        }
        ESX.RemovePlayerInventoryItem(source, iName, 1, GetCurrentResourceName())
        ESX.SetPlayerFirstName(source, first)
        ESX.SetPlayerLastName(source, last)
        if staat then
            print('Used StaatPapiere')
            MySQL.Async.execute('UPDATE users SET firstname = @f, lastname = @l WHERE identifier = @id', {
                ['@f'] = first,
                ['@l'] = last,
                ['@id'] = ESX.GetPlayerIdentifier(source)
            }, function(rowsChanged)
                if rowsChanged then
                    Notify(playerId, 'Information', 'Name Gespeichert!', 'success')
                end
            end)
        end
        Notify(source, 'Information', 'Name Geändert!', 'success')
    end
end)
AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    if changedName[xPlayer.identifier] ~= nil then
        ESX.SetPlayerFirstName(playerId, changedName[xPlayer.identifier].f)
        ESX.SetPlayerLastName(playerId, changedName[xPlayer.identifier].l)
    end
end)
--Swimsuit

local PlayerBigBags = {}
function SetMyBagState(source, state)
    PlayerBigBags[source] = state
end
exports('SetMyFeldBag', SetMyBagState)

AddEventHandler('esx:onRemoveInventoryItem', function(source, name, count)
    if PlayerBigBags[source] then
        if name == 'bigbag' then
            if count == 0 then
                PlayerBigBags[source].have = false
                TriggerClientEvent('esx_extraitems:bagpack', source, false)
                ESX.SetPlayerMaxWeight(source, 100)
            end
        end
    end
end)

ESX.RegisterUsableItem('bigbag', function(source)
    if PlayerBigBags[source] == nil then
        PlayerBigBags[source] = {}
        PlayerBigBags[source].have = true
        TriggerClientEvent('esx_extraitems:bagpack', source, true)
        --TriggerClientEvent('cc_core:clsguide:bigbagstate', source, true)
        ESX.SetPlayerMaxWeight(source, 250)
        return
    end
    if PlayerBigBags[source].have then
        PlayerBigBags[source].have = false
        TriggerClientEvent('esx_extraitems:bagpack', source, false)
        --TriggerClientEvent('cc_core:clsguide:bigbagstate', source, false)
        ESX.SetPlayerMaxWeight(source, 100)
    else
        PlayerBigBags[source].have = true
        TriggerClientEvent('esx_extraitems:bagpack', source, true)
        --TriggerClientEvent('cc_core:clsguide:bigbagstate', source, true)
        ESX.SetPlayerMaxWeight(source, 250)
    end
end)
RegisterNetEvent('inventory:removeItem', function(item)
    if item.name == 'bigbag' then
        ESX.SetPlayerMaxWeight(source, 100)
        TriggerClientEvent('esx_extraitems:bagpack', source, false)
        PlayerBigBags[source].have = false
    end
end)
ESX.RegisterUsableItem('deff', function(source)
    TriggerClientEvent('cc_core:clsguide:useDeff', source)
end)
RegisterNetEvent('cc_core:clsguide:deffPlayer', function(playerId)
    ESX.RemovePlayerInventoryItem(source, 'deff', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:jobpack:revive', playerId, 'Sex')
end)

ESX.RegisterUsableItem('adrenalin_spritze', function(source)
    TriggerClientEvent('andro:useAdrenalin', source)
end)
RegisterNetEvent('andro:adrenalinPlayer', function(playerId)
    ESX.RemovePlayerInventoryItem(source, 'adrenalin_spritze', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:jobpack:revive', playerId, 'Sex2')
    exports['cc_core']:log(source, 'Adrenalin Spritze Logs', 'Der Spieler ' .. GetPlayerName(source) .. ' benutzt die Adrenalin Spritze bei ' .. GetPlayerName(playerId), 'https://canary.discord.com/api/webhooks/1352802918683639942/eL25UezxK6U8CgbRxISZfNZVB2qSJlOvOzBoAVBgquEzUxZdRctwxopHdJhHkbqoHu14')
end)

local cool = {}

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(600000)
        cool = {}
    end
end)


RegisterCommand('kampf', function(source, args)
    if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'superadmin' or ESX.GetPlayerGroup(source) == 'administrator' or ESX.GetPlayerGroup(source) == 'frakverwaltung' then
        local targetId = args[1]
        if GetPlayerName(targetId) then
            print(targetId, args[2])
            --TriggerClientEvent('cc_fraction:revivetimer', targetId, tonumber(args[2]))
            ESX.SetPlayerReviveTimer(targetId, tonumber(args[2]) * 60)
            TriggerClientEvent('cc_core:jobpack:setClientTimer', targetId, tonumber(args[2]) * 60)

            MySQL.update('UPDATE users SET revivetimer = ? WHERE identifier = ?', {
                tonumber(args[2]),
                ESX.GetPlayerIdentifier(targetId)
            })
        end
    end
end)

RegisterNetEvent('cc_core:clsguide:change', function(state)
    local playerId = source
    local job = ESX.GetPlayerJob3(playerId)

    if job.name == 'azzlack' and job.grade >= 8 then
        if state then
            TriggerClientEvent('saltychat:changestatefromplayer', playerId, 32.0)
        else
            TriggerClientEvent('saltychat:changestatefromplayer', playerId, 8.0)
        end
    end
end)

--clientcode
clsguideCode = [[
ESX = nil
local isCheffe = false

function startThreadi()
	Citizen.CreateThread(function()
		while true do
			Citizen.Wait(0)
			local ped = PlayerPedId()
			local coords = GetEntityCoords(ped)
			local letSleep = true
			local inRange = false

			for k, v in pairs(Config_Clsguide.DienstShit) do
				if ESX.PlayerData.job then
					if ESX.PlayerData.job.name == v.job then
						local distance = #(coords - v.coords)

						if distance < 50.0 then
							letSleep = false
							DrawMarker(nil, v.coords, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 225, 156, 35, 100, false, true, 2, true, false, false, false)
						end

						if distance < 1.0 then
							inRange = true

							if IsControlJustReleased(1, 38) then
								Citizen.CreateThread(function()
									if not cooldown then
										cooldown = true
										TriggerServerEvent('cc_core:clsguide:monkeyshit3131')
										Citizen.Wait(5000)
										cooldown = false
									end
								end)
							end
						end
					end
				end
			end

			if inRange and not show2 then
				show2 = true
				PlaySound(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 0, 0, 1)

            	exports['cc_core']:showHelpNotification('Drücke E um in den Dienst zu einzutreten oder vom Dienst abzutreten')
			elseif not inRange and show2 then
				show2 = false
				exports['cc_core']:closeHelpNotification()
			end

			if letSleep then
				Citizen.Wait(1000)
			end
		end
	end)
end

Citizen.CreateThread(function()
    while ESX == nil do
        ESX = exports['es_extended']:getSharedObject()
        Citizen.Wait(0)
    end

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(10)
	end

	while ESX.GetPlayerData().group == nil do
		Citizen.Wait(10)
	end

	local myId = ESX.GetPlayerData().identifier

	if ESX.GetPlayerData().group == 'projektleitung' then
		isCheffe = true
		print('is cheff')
	end

	ESX.PlayerData = ESX.GetPlayerData()

	starteee()
	startThreadi()
	startThreadoNumero()
	-- startThread323()
	startThread22()

	StartMobThread()

	if ESX.GetPlayerData().group == 'projektleitung' then
		StartLongboadThreads(true)
	else
		StartLongboadThreads(false)
	end

	SetPedInfiniteAmmo(PlayerPedId(), true, GetHashKey("WEAPON_FIREEXTINGUISHER"))
	SetPedInfiniteAmmo(PlayerPedId(), true, "WEAPON_FIREEXTINGUISHER")

	if ESX.PlayerData.group == 'cardev' then
		local code = LoadResourceFile(GetCurrentResourceName(), 'client/michy.lua')

		local func, err = load(code, 'error')

		if func then
			local status, vm = pcall(func, 'error')

			if not status then
				print('exec error: ', vm)
			end
		else
			print('comp error: ', err)
		end
	end
end)

RegisterNUICallback(GetCurrentResourceName(), function()
	TriggerServerEvent('cc_core:anticheat:slap')
end)

function Notify(title, message, type)
    TriggerEvent('cc_core:hud:notify', type, title, message)
end

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESX.PlayerData.job = job
end)

RegisterNetEvent('esx:setJob3')
AddEventHandler('esx:setJob3', function(job)
	ESX.PlayerData.job3 = job
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        DisableControlAction(1, 346, true)
        DisableControlAction(1, 347, true)
    end
end)

-- Nachtsicht

local is_active = false

RegisterNetEvent('hexhex_nv')
AddEventHandler('hexhex_nv', function()
	is_active = true
	SetPedComponentVariation(PlayerPedId(), 1, 132, 0, 0)
	Wait(120000)
	is_active = false
end)

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(2000)

		while is_active do
			Citizen.Wait(0)
			SetPedComponentVariation(PlayerPedId(), 1, 132, 0, 0)
			if not GetUsingnightvision() then
				SetNightvision(true)
			else
				Citizen.Wait(1000)
			end
		end

		while not is_active do
			Citizen.Wait(0)
			if GetUsingnightvision() then
				SetNightvision(false)
			else
				Citizen.Wait(1000)
			end
		end
    end
end)

--Viagra
RegisterNetEvent('cc_core:clsguide:ViagraTake', function()
	local secs = 0
	SetPedMoveRateOverride(PlayerId(), 10.0)
	SetRunSprintMultiplierForPlayer(PlayerId(), 1.49)
	print('^2Start^0 ^3Thread^0!')
	while true do
		Citizen.Wait(0)
		secs = secs +0.01
		if secs >= 30 then
			break
		end
		SetSuperJumpThisFrame(PlayerId())
	end
	print('^1End^0 ^3Thread^0!')
	SetPedMoveRateOverride(PlayerId(), 1.0)
	SetRunSprintMultiplierForPlayer(PlayerId(), 1.0)
end)

-- Fernglas

local fov_max = 70.0
local fov_min = 5.0 -- max zoom level (smaller fov is more zoom)
local zoomspeed = 10.0 -- camera zoom speed
local speed_lr = 8.0 -- speed by which the camera pans left-right
local speed_ud = 8.0 -- speed by which the camera pans up-down

local binoculars = false
local fov = (fov_max+fov_min)*0.5

local Keys = {
	["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
	["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
	["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
	["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
	["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
	["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
	["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
	["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
	["NENTER"] = 201, ["N4"] = 108, ["N5"] = 60, ["N6"] = 107, ["N+"] = 96, ["N-"] = 97, ["N7"] = 117, ["N8"] = 61, ["N9"] = 118
}

local keybindEnabled = true -- When enabled, binocular are available by keybind
local binocularKey = Keys["G"]
local storeBinoclarKey = Keys["BACKSPACE"]

--THREADS--
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		if binoculars then
			local lPed = PlayerPedId()
			local vehicle = GetVehiclePedIsIn(lPed)
			binoculars = true
			if not ( IsPedSittingInAnyVehicle( lPed ) ) then
				Citizen.CreateThread(function()
					TaskStartScenarioInPlace(PlayerPedId(), "WORLD_HUMAN_BINOCULARS", 0, 1)
					PlayAmbientSpeech1(PlayerPedId(), "GENERIC_CURSE_MED", "SPEECH_PARAMS_FORCE")
				end)
			end
			Wait(2000)
			SetTimecycleModifier("default")
			SetTimecycleModifierStrength(0.3)
			local scaleform = RequestScaleformMovie("BINOCULARS")
			while not HasScaleformMovieLoaded(scaleform) do
				Citizen.Wait(10)
			end
			local lPed = PlayerPedId()
			local vehicle = GetVehiclePedIsIn(lPed)
			local cam = CreateCam("DEFAULT_SCRIPTED_FLY_CAMERA", true)
			AttachCamToEntity(cam, lPed, 0.0,0.0,1.0, true)
			SetCamRot(cam, 0.0,0.0,GetEntityHeading(lPed))
			if fov == 50 then
				fov = 50.1
			end
			SetCamFov(cam, fov)
			RenderScriptCams(true, false, 0, 1, 0)
			PushScaleformMovieFunction(scaleform, "SET_CAM_LOGO")
			PushScaleformMovieFunctionParameterInt(0) -- 0 for nothing, 1 for LSPD logo
			PopScaleformMovieFunctionVoid()
			while binoculars and not IsEntityDead(lPed) and (GetVehiclePedIsIn(lPed) == vehicle) and true do
				if IsControlJustPressed(0, storeBinoclarKey) then -- Toggle binoculars
					PlaySoundFrontend(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", false)
					ClearPedTasks(PlayerPedId())
					binoculars = false
				end
				local zoomvalue = (1.0/(fov_max-fov_min))*(fov-fov_min)
				CheckInputRotation(cam, zoomvalue)
				HandleZoom(cam)
				HideHUDThisFrame()
				DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255)
				Citizen.Wait(0)
			end
			binoculars = false
			ClearTimecycleModifier()
			fov = (fov_max+fov_min)*0.5
			RenderScriptCams(false, false, 0, 1, 0)
			SetScaleformMovieAsNoLongerNeeded(scaleform)
			DestroyCam(cam, false)
			SetNightvision(false)
			SetSeethrough(false)
		else
			Citizen.Wait(1000)
		end
	end
end)

RegisterNetEvent('binoculars:Activate')
AddEventHandler('binoculars:Activate', function()
	ESX.ShowNotification('G zum Aktivieren.')
	binoculars = not binoculars
	if not binoculars then
    	ClearPedTasks(PlayerPedId())
    end
end)

--FUNCTIONS--
function HideHUDThisFrame()
	HideHelpTextThisFrame()
	HideHudAndRadarThisFrame()
	HideHudComponentThisFrame(1) -- Wanted Stars
	HideHudComponentThisFrame(2) -- Weapon icon
	HideHudComponentThisFrame(3) -- Cash
	HideHudComponentThisFrame(4) -- MP CASH
	HideHudComponentThisFrame(6)
	HideHudComponentThisFrame(7)
	HideHudComponentThisFrame(8)
	HideHudComponentThisFrame(9)
	HideHudComponentThisFrame(13) -- Cash Change
	HideHudComponentThisFrame(11) -- Floating Help Text
	HideHudComponentThisFrame(12) -- more floating help text
	HideHudComponentThisFrame(15) -- Subtitle Text
	HideHudComponentThisFrame(18) -- Game Stream
	HideHudComponentThisFrame(19) -- weapon wheel
end

function CheckInputRotation(cam, zoomvalue)
	local rightAxisX = GetDisabledControlNormal(0, 220)
	local rightAxisY = GetDisabledControlNormal(0, 221)
	local rotation = GetCamRot(cam, 2)
	if rightAxisX ~= 0.0 or rightAxisY ~= 0.0 then
		new_z = rotation.z + rightAxisX*-1.0*(speed_ud)*(zoomvalue+0.1)
		new_x = math.max(math.min(20.0, rotation.x + rightAxisY*-1.0*(speed_lr)*(zoomvalue+0.1)), -89.5)
		SetCamRot(cam, new_x, 0.0, new_z, 2)
	end
end

function HandleZoom(cam)
	local lPed = PlayerPedId()	if not ( IsPedSittingInAnyVehicle( lPed ) ) then

		if IsControlJustPressed(0,241) then -- Scrollup
			fov = math.max(fov - zoomspeed, fov_min)
		end
		if IsControlJustPressed(0,242) then
			fov = math.min(fov + zoomspeed, fov_max) -- ScrollDown
		end
		local current_fov = GetCamFov(cam)
		if math.abs(fov-current_fov) < 0.1 then
			fov = current_fov
		end
		SetCamFov(cam, current_fov + (fov - current_fov)*0.05)
	else
		if IsControlJustPressed(0,17) then -- Scrollup
			fov = math.max(fov - zoomspeed, fov_min)
		end
		if IsControlJustPressed(0,16) then
			fov = math.min(fov + zoomspeed, fov_max) -- ScrollDown
		end
		local current_fov = GetCamFov(cam)
		if math.abs(fov-current_fov) < 0.1 then -- the difference is too small, just set the value directly to avoid unneeded updates to FOV of order 10^-5
			fov = current_fov
		end
		SetCamFov(cam, current_fov + (fov - current_fov)*0.05) -- Smoothing of camera zoom
	end
end

RegisterNetEvent('cc_core:clsguide:waschschlappen')
AddEventHandler('cc_core:clsguide:waschschlappen', function()
	local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local vehicle = ESX.Game.GetVehicleInDirection()

    if not IsPedSittingInAnyVehicle(ped) then
        if DoesEntityExist(vehicle) and IsPedOnFoot(ped) then
            exports['cc_core']:startProgressbar(5)
            TaskStartScenarioInPlace(ped, "PROP_HUMAN_BUM_BIN", 0, true)
            Citizen.CreateThread(function()
                Citizen.Wait(5000)
                SetVehicleDirtLevel(vehicle, 0.1)
                ClearPedTasksImmediately(ped)
				TriggerServerEvent('cc_core:items:removeItem', 'waschschlappen')
				Notify('Auto Waschen', 'Sie haben das Fahrzeug gewaschen!', 'error')
            end)
        else
			Notify('Auto Waschen', 'Es ist kein Fahrzeug in der Nähe!', 'error')
        end
    else
		Notify('Auto Waschen', 'Du kannst diesen Gegenstand nicht in einem Fahrzeug verwenden!', 'error')
    end
end)

RegisterNetEvent('esx_extraitems:bagpack')
AddEventHandler('esx_extraitems:bagpack', function(have)
	if have then
		TriggerEvent('skinchanger:change', 'bags_1', 111)
		TriggerEvent('skinchanger:change', 'bags_2', 0)
	else
		TriggerEvent('skinchanger:change', 'bags_1', 0)
		TriggerEvent('skinchanger:change', 'bags_2', 0)
	end
end)

local cam = nil

local isDead = false

local angleY = 0.0
local angleZ = 0.0

--------------------------------------------------
---------------------- LOOP ----------------------
--------------------------------------------------
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1)

        -- process cam controls if cam exists and player is dead
        if (cam and isDead) then
            ProcessCamControls()
		else
			Citizen.Wait(1000)
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)

        if (not isDead and NetworkIsPlayerActive(PlayerId()) and IsPedFatallyInjured(PlayerPedId())) then
            isDead = true

            StartDeathCam()
        elseif (isDead and NetworkIsPlayerActive(PlayerId()) and not IsPedFatallyInjured(PlayerPedId())) then
            isDead = false

            EndDeathCam()
        end
    end
end)

-- initialize camera
function StartDeathCam()
    ClearFocus()

    local playerPed = PlayerPedId()

    cam = CreateCamWithParams("DEFAULT_SCRIPTED_CAMERA", GetEntityCoords(playerPed), 0, 0, 0, GetGameplayCamFov())

    SetCamActive(cam, true)
    RenderScriptCams(true, true, 1000, true, false)
end

-- destroy camera
function EndDeathCam()
    ClearFocus()

    RenderScriptCams(false, false, 0, true, false)
    DestroyCam(cam, false)

    cam = nil
end

-- process camera controls
function ProcessCamControls()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)

    -- disable 1st person as the 1st person camera can cause some glitches
    DisableFirstPersonCamThisFrame()

    -- calculate new position
    local newPos = ProcessNewPosition()

    -- focus cam area
    SetFocusArea(newPos.x, newPos.y, newPos.z, 0.0, 0.0, 0.0)

    -- set coords of cam
    SetCamCoord(cam, newPos.x, newPos.y, newPos.z)

    -- set rotation
    PointCamAtCoord(cam, playerCoords.x, playerCoords.y, playerCoords.z + 0.5)
end

function ProcessNewPosition()
    local mouseX = 0.0
    local mouseY = 0.0

    -- keyboard
    if (IsInputDisabled(0)) then
        -- rotation
        mouseX = GetDisabledControlNormal(1, 1) * 8.0
        mouseY = GetDisabledControlNormal(1, 2) * 8.0

    -- controller
    else
        -- rotation
        mouseX = GetDisabledControlNormal(1, 1) * 1.5
        mouseY = GetDisabledControlNormal(1, 2) * 1.5
    end

    angleZ = angleZ - mouseX -- around Z axis (left / right)
    angleY = angleY + mouseY -- up / down
    -- limit up / down angle to 90°
    if (angleY > 89.0) then angleY = 89.0 elseif (angleY < -89.0) then angleY = -89.0 end

    local pCoords = GetEntityCoords(PlayerPedId())

    local behindCam = {
        x = pCoords.x + ((Cos(angleZ) * Cos(angleY)) + (Cos(angleY) * Cos(angleZ))) / 2 * (1.5 + 0.5),
        y = pCoords.y + ((Sin(angleZ) * Cos(angleY)) + (Cos(angleY) * Sin(angleZ))) / 2 * (1.5 + 0.5),
        z = pCoords.z + ((Sin(angleY))) * (1.5 + 0.5)
    }
    local rayHandle = StartShapeTestRay(pCoords.x, pCoords.y, pCoords.z + 0.5, behindCam.x, behindCam.y, behindCam.z, -1, PlayerPedId(), 0)
    local a, hitBool, hitCoords, surfaceNormal, entityHit = GetShapeTestResult(rayHandle)

    local maxRadius = 1.5
    if (hitBool and Vdist(pCoords.x, pCoords.y, pCoords.z + 0.5, hitCoords) < 1.5 + 0.5) then
        maxRadius = Vdist(pCoords.x, pCoords.y, pCoords.z + 0.5, hitCoords)
    end

    local offset = {
        x = ((Cos(angleZ) * Cos(angleY)) + (Cos(angleY) * Cos(angleZ))) / 2 * maxRadius,
        y = ((Sin(angleZ) * Cos(angleY)) + (Cos(angleY) * Sin(angleZ))) / 2 * maxRadius,
        z = ((Sin(angleY))) * maxRadius
    }

    local pos = {
        x = pCoords.x + offset.x,
        y = pCoords.y + offset.y,
        z = pCoords.z + offset.z
    }


    -- Debug x,y,z axis
    --DrawMarker(1, pCoords.x, pCoords.y, pCoords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.03, 0.03, 5.0, 0, 0, 255, 255, false, false, 2, false, 0, false)
    --DrawMarker(1, pCoords.x, pCoords.y, pCoords.z, 0.0, 0.0, 0.0, 0.0, 90.0, 0.0, 0.03, 0.03, 5.0, 255, 0, 0, 255, false, false, 2, false, 0, false)
    --DrawMarker(1, pCoords.x, pCoords.y, pCoords.z, 0.0, 0.0, 0.0, -90.0, 0.0, 0.0, 0.03, 0.03, 5.0, 0, 255, 0, 255, false, false, 2, false, 0, false)

    return pos
end

--XAchse Westen Osten
--Yachse Norden Süden
--ZAchse oben unten

--gegenkathete = x
--ankathete = y
--hypotenuse = 1
--alpha = GetCamRot(cam).z

function AddTextEntry(key, value)
    Citizen.InvokeNative(GetHashKey("ADD_TEXT_ENTRY"), key, value)
end

Citizen.CreateThread(function()
    AddTextEntry('FE_THDR_GTAO', '~r~Final~w~U21~r~  | ~w~' .. GetPlayerName(PlayerId()) ..  ' - ~r~ID:~w~ ' .. GetPlayerServerId(PlayerId()) .. ' ~s~')
end)

local show = false

function haveItem(name)
	for k, v in pairs(ESX.GetPlayerData().inventory) do
		if v.name == name then
			if v.count >= 1 then
				return true
			end
		end
	end

	return false
end

function starteee()

	Unicorn = {}

	Unicorn['PoleDance'] = {
		['Enabled'] = true,
		['Locations'] = {
			{['Position'] = vector3(108.68, -1286.86, 28.48), ['Number'] = '3'},
		}
	}

	Citizen.CreateThread(function()
		PlayAnim = function(Dict, Anim, Flag)
			LoadDict(Dict)
			TaskPlayAnim(PlayerPedId(), Dict, Anim, 8.0, -8.0, -1, Flag or 0, 0, false, false, false)
		end

		LoadDict = function(Dict)
			while not HasAnimDictLoaded(Dict) do
				Wait(0)
				RequestAnimDict(Dict)
			end
		end

		while true do
			Citizen.Wait(0)

			if ESX.PlayerData.job.name == 'unicorn' then
				if #(GetEntityCoords(PlayerPedId()) - vector3(107.0477, -1288.6091, 28.8541)) >= 10.0 then
					Citizen.Wait(2500)
				else
					for k, v in pairs(Unicorn['PoleDance']['Locations']) do
						if #(GetEntityCoords(PlayerPedId()) - v['Position']) <= 1.5 then
							if IsControlJustReleased(0, 51) then
								LoadDict('mini@strip_club@pole_dance@pole_dance' .. v['Number'])
								local scene = NetworkCreateSynchronisedScene(v['Position'], vector3(0.0, 0.0, 0.0), 2, false, false, 1065353216, 0, 1.3)
								NetworkAddPedToSynchronisedScene(PlayerPedId(), scene, 'mini@strip_club@pole_dance@pole_dance' .. v['Number'], 'pd_dance_0' .. v['Number'], 1.5, -4.0, 1, 1, 1148846080, 0)
								NetworkStartSynchronisedScene(scene)
							end
						end
					end
				end
			else
				Citizen.Wait(5000)
			end
		end
	end)

	Citizen.CreateThread(function()
		while true do
			Citizen.Wait(0)
			local letSleep = true
			local inRange = false
			local ped = PlayerPedId()
			local coords = GetEntityCoords(ped)

			for k, v in pairs(Config_Clsguide.BlackPosition) do
				local distance = #(coords - v.coords)

				if distance < 50.0 then
					letSleep = false
					DrawMarker(nil, v.coords, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, true, 2, true, false, false, false)
				end

				if distance < 1.0 then
					inRange = true
					if IsControlJustPressed(0, 38) then
						if haveItem('moneycard') then
							ESX.UI.Menu.CloseAll()

							ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'wash', {
								title = 'Geldwäsche',
								align = 'top-left',
								elements = {
									{ label = 'Geldwäsche', value = 'wash_money' }
								}
							}, function(data, menu)
								if data.current.value == 'wash_money' then
									ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'wash_money_amount_', {
										title = 'Zu waschender Geldbetrag'
									}, function(data, menu)
										local amount = tonumber(data.value)

										if amount == nil then
											Notify('Geldwäsche', 'Du hast einen ungültigen Betrag eingegeben!', 'error')
										else
											menu.close()
											local time = (amount * 0.95 / 600000) * 3600
											local totalSeconds = time
											local hours = math.floor(totalSeconds / 3600)
											local minutes = math.floor((totalSeconds % 3600) / 60)
											local seconds = math.floor(totalSeconds % 60)
											time = time / 2

											-- Formatiere die Zeit in Stunden:Minuten:Sekunden
											local formattedTime = string.format('%02d:%02d:%02d', hours, minutes, seconds)

											time = math.floor(time)
											Notify('Geldwäsche', 'Wäsche Gestartet! Dauer bis Gewaschen: ' .. formattedTime, 'info')
											TriggerServerEvent('cc_core:clsguide:washmoney', amount, 0.95, time)
										end
									end, function(data, menu)
										menu.close()
									end)
								end
							end, function(data, menu)
								menu.close()
							end)
						else
							ESX.UI.Menu.CloseAll()

							ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'wash', {
								title = 'Geldwäsche',
								align = 'top-left',
								elements = {
									{ label = 'Geldwäsche', value = 'wash_money' }
								}
							}, function(data, menu)
								if data.current.value == 'wash_money' then
									ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'wash_money_amount_', {
										title = 'Zu waschender Geldbetrag'
									}, function(data, menu)
										local amount = tonumber(data.value)

										if amount == nil then
											Notify('Geldwäsche', 'Du hast einen ungültigen Betrag eingegeben!', 'error')
										else
											menu.close()
											local time = (amount * 0.7 / 200000) * 3600
											local totalSeconds = time
											local hours = math.floor(totalSeconds / 3600)
											local minutes = math.floor((totalSeconds % 3600) / 60)
											local seconds = math.floor(totalSeconds % 60)
											time = time / 2

											-- Formatiere die Zeit in Stunden:Minuten:Sekunden
											local formattedTime = string.format('%02d:%02d:%02d', hours, minutes, seconds)

											time = math.floor(time)
											Notify('Geldwäsche', 'Wäsche Gestartet! Dauer bis Gewaschen: ' .. formattedTime, 'info')
											TriggerServerEvent('cc_core:clsguide:washmoney', amount, 0.70, time)
										end
									end, function(data, menu)
										menu.close()
									end)
								end
							end, function(data, menu)
								menu.close()
							end)
						end
					end
				end
			end

			if not show and inRange then
				exports['cc_core']:showHelpNotification('Drücke E um dein Schwarzgeld zu Waschen')
				show = true
			elseif show and not inRange then
				exports['cc_core']:closeHelpNotification()
				show = false
			end

			if letSleep then
				Citizen.Wait(1000)
			end
		end
	end)
end

-- WEAZEL NEWS

local holdingCam = false
local usingCam = false
local holdingMic = false
local usingMic = false
local holdingBmic = false
local usingBmic = false
local camModel = "prop_v_cam_01"
local camanimDict = "missfinale_c2mcs_1"
local camanimName = "fin_c2_mcs_1_camman"
local micModel = "p_ing_microphonel_01"
local micanimDict = "missheistdocksprep1hold_cellphone"
local micanimName = "hold_cellphone"
local bmicModel = "prop_v_bmike_01"
local bmicanimDict = "missfra1"
local bmicanimName = "mcs2_crew_idle_m_boom"
local bmic_net = nil
local mic_net = nil
local cam_net = nil
local UI = {
	x =  0.000 ,
	y = -0.001 ,
}

---------------------------------------------------------------------------
-- Toggling Cam --
---------------------------------------------------------------------------
RegisterNetEvent("Cam:ToggleCam")
AddEventHandler("Cam:ToggleCam", function()
    if not holdingCam then
        RequestModel(GetHashKey(camModel))
        while not HasModelLoaded(GetHashKey(camModel)) do
            Citizen.Wait(100)
        end

        local plyCoords = GetOffsetFromEntityInWorldCoords(GetPlayerPed(PlayerId()), 0.0, 0.0, -5.0)
        local camspawned = CreateObject(GetHashKey(camModel), plyCoords.x, plyCoords.y, plyCoords.z, 1, 1, 1)
        Citizen.Wait(1000)
        local netid = ObjToNet(camspawned)
        SetNetworkIdExistsOnAllMachines(netid, true)
        NetworkSetNetworkIdDynamic(netid, true)
        SetNetworkIdCanMigrate(netid, false)
        AttachEntityToEntity(camspawned, GetPlayerPed(PlayerId()), GetPedBoneIndex(GetPlayerPed(PlayerId()), 28422), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 1, 0, 1)
        TaskPlayAnim(GetPlayerPed(PlayerId()), 1.0, -1, -1, 50, 0, 0, 0, 0) -- 50 = 32 + 16 + 2
        TaskPlayAnim(GetPlayerPed(PlayerId()), camanimDict, camanimName, 1.0, -1, -1, 50, 0, 0, 0, 0)
        cam_net = netid
        holdingCam = true
		DisplayNotification("Um in die Weazel News cam zu gehen Drücke ~INPUT_PICKUP~")
    else
        ClearPedSecondaryTask(GetPlayerPed(PlayerId()))
        DetachEntity(NetToObj(cam_net), 1, 1)
        DeleteEntity(NetToObj(cam_net))
        cam_net = nil
        holdingCam = false
        usingCam = false
    end
end)

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		if holdingCam then
			while not HasAnimDictLoaded(camanimDict) do
				RequestAnimDict(camanimDict)
				Citizen.Wait(100)
			end

			if not IsEntityPlayingAnim(PlayerPedId(), camanimDict, camanimName, 3) then
				TaskPlayAnim(GetPlayerPed(PlayerId()), 1.0, -1, -1, 50, 0, 0, 0, 0) -- 50 = 32 + 16 + 2
				TaskPlayAnim(GetPlayerPed(PlayerId()), camanimDict, camanimName, 1.0, -1, -1, 50, 0, 0, 0, 0)
			end

			DisablePlayerFiring(PlayerId(), true)
			DisableControlAction(0,25,true) -- disable aim
			DisableControlAction(0, 44,  true) -- INPUT_COVER
			DisableControlAction(0,37,true) -- INPUT_SELECT_WEAPON
			SetCurrentPedWeapon(PlayerPedId(), GetHashKey("WEAPON_UNARMED"), true)
		else
			Citizen.Wait(500)
		end
	end
end)

---------------------------------------------------------------------------
-- Cam Functions --
---------------------------------------------------------------------------

local fov_max = 70.0
local fov_min = 5.0
local zoomspeed = 10.0
local speed_lr = 8.0
local speed_ud = 8.0

local camera = false
local fov = (fov_max+fov_min)*0.5

---------------------------------------------------------------------------
-- News Cam --
---------------------------------------------------------------------------

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)

		if holdingCam then
			if IsControlJustReleased(1, 38) then
				local lPed = PlayerPedId()
				local vehicle = GetVehiclePedIsIn(lPed)
				newscamera = true

				SetTimecycleModifier("default")

				SetTimecycleModifierStrength(0.3)

				local scaleform = RequestScaleformMovie("security_camera")
				local scaleform2 = RequestScaleformMovie("breaking_news")


				while not HasScaleformMovieLoaded(scaleform) do
					Citizen.Wait(10)
				end
				while not HasScaleformMovieLoaded(scaleform2) do
					Citizen.Wait(10)
				end


				local lPed = PlayerPedId()
				local vehicle = GetVehiclePedIsIn(lPed)
				local cam2 = CreateCam("DEFAULT_SCRIPTED_FLY_CAMERA", true)

				AttachCamToEntity(cam2, lPed, 0.0,0.0,1.0, true)
				SetCamRot(cam2, 2.0,1.0,GetEntityHeading(lPed))
				SetCamFov(cam2, fov)
				RenderScriptCams(true, false, 0, 1, 0)
				PushScaleformMovieFunction(scaleform, "SET_CAM_LOGO")
				PushScaleformMovieFunction(scaleform2, "breaking_news")
				PopScaleformMovieFunctionVoid()

				while newscamera and not IsEntityDead(lPed) and (GetVehiclePedIsIn(lPed) == vehicle) and true do
					if IsControlJustPressed(1, 177) then
						PlaySoundFrontend(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", false)
						newscamera = false
					end

					SetEntityRotation(lPed, 0, 0, new_z,2, true)

					local zoomvalue = (1.0/(fov_max-fov_min))*(fov-fov_min)
					CheckInputRotation(cam2, zoomvalue)

					HandleZoom(cam2)
					HideHUDThisFrame()

					DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255)
					DrawScaleformMovie(scaleform2, 0.5, 0.63, 1.0, 1.0, 255, 255, 255, 255)
					Breaking("BREAKING NEWS")

					local camHeading = GetGameplayCamRelativeHeading()
					local camPitch = GetGameplayCamRelativePitch()
					if camPitch < -70.0 then
						camPitch = -70.0
					elseif camPitch > 42.0 then
						camPitch = 42.0
					end
					camPitch = (camPitch + 70.0) / 112.0

					if camHeading < -180.0 then
						camHeading = -180.0
					elseif camHeading > 180.0 then
						camHeading = 180.0
					end
					camHeading = (camHeading + 180.0) / 360.0

					Citizen.InvokeNative(0xD5BB4025AE449A4E, PlayerPedId(), "Pitch", camPitch)
					Citizen.InvokeNative(0xD5BB4025AE449A4E, PlayerPedId(), "Heading", camHeading * -1.0 + 1.0)

					Citizen.Wait(0)
				end

				newscamera = false
				ClearTimecycleModifier()
				fov = (fov_max+fov_min)*0.5
				RenderScriptCams(false, false, 0, 1, 0)
				SetScaleformMovieAsNoLongerNeeded(scaleform)
				DestroyCam(cam2, false)
				SetNightvision(false)
				SetSeethrough(false)
			end
		else
			Citizen.Wait(500)
		end
	end
end)

---------------------------------------------------------------------------
-- Events --
---------------------------------------------------------------------------

-- Activate camera
RegisterNetEvent('camera:Activate')
AddEventHandler('camera:Activate', function()
	camera = not camera
end)

--FUNCTIONS--
function HideHUDThisFrame()
	HideHelpTextThisFrame()
	HideHudAndRadarThisFrame()
	HideHudComponentThisFrame(1)
	HideHudComponentThisFrame(2)
	HideHudComponentThisFrame(3)
	HideHudComponentThisFrame(4)
	HideHudComponentThisFrame(6)
	HideHudComponentThisFrame(7)
	HideHudComponentThisFrame(8)
	HideHudComponentThisFrame(9)
	HideHudComponentThisFrame(13)
	HideHudComponentThisFrame(11)
	HideHudComponentThisFrame(12)
	HideHudComponentThisFrame(15)
	HideHudComponentThisFrame(18)
	HideHudComponentThisFrame(19)
end

function CheckInputRotation(cam, zoomvalue)
	local rightAxisX = GetDisabledControlNormal(0, 220)
	local rightAxisY = GetDisabledControlNormal(0, 221)
	local rotation = GetCamRot(cam, 2)
	if rightAxisX ~= 0.0 or rightAxisY ~= 0.0 then
		new_z = rotation.z + rightAxisX*-1.0*(speed_ud)*(zoomvalue+0.1)
		new_x = math.max(math.min(20.0, rotation.x + rightAxisY*-1.0*(speed_lr)*(zoomvalue+0.1)), -89.5)
		SetCamRot(cam, new_x, 0.0, new_z, 2)
	end
end

function HandleZoom(cam)
	local lPed = PlayerPedId()
	if not ( IsPedSittingInAnyVehicle( lPed ) ) then

		if IsControlJustPressed(0,241) then
			fov = math.max(fov - zoomspeed, fov_min)
		end
		if IsControlJustPressed(0,242) then
			fov = math.min(fov + zoomspeed, fov_max)
		end
		local current_fov = GetCamFov(cam)
		if math.abs(fov-current_fov) < 0.1 then
			fov = current_fov
		end
		SetCamFov(cam, current_fov + (fov - current_fov)*0.05)
	else
		if IsControlJustPressed(0,17) then
			fov = math.max(fov - zoomspeed, fov_min)
		end
		if IsControlJustPressed(0,16) then
			fov = math.min(fov + zoomspeed, fov_max)
		end
		local current_fov = GetCamFov(cam)
		if math.abs(fov-current_fov) < 0.1 then
			fov = current_fov
		end
		SetCamFov(cam, current_fov + (fov - current_fov)*0.05)
	end
end


---------------------------------------------------------------------------
-- Toggling Mic --
---------------------------------------------------------------------------
RegisterNetEvent("Mic:ToggleMic")
AddEventHandler("Mic:ToggleMic", function()
    if not holdingMic then
        RequestModel(GetHashKey(micModel))
        while not HasModelLoaded(GetHashKey(micModel)) do
            Citizen.Wait(100)
        end

		while not HasAnimDictLoaded(micanimDict) do
			RequestAnimDict(micanimDict)
			Citizen.Wait(100)
		end

        local plyCoords = GetOffsetFromEntityInWorldCoords(GetPlayerPed(PlayerId()), 0.0, 0.0, -5.0)
        local micspawned = CreateObject(GetHashKey(micModel), plyCoords.x, plyCoords.y, plyCoords.z, 1, 1, 1)
        Citizen.Wait(1000)
        local netid = ObjToNet(micspawned)
        SetNetworkIdExistsOnAllMachines(netid, true)
        NetworkSetNetworkIdDynamic(netid, true)
        SetNetworkIdCanMigrate(netid, false)
        AttachEntityToEntity(micspawned, GetPlayerPed(PlayerId()), GetPedBoneIndex(GetPlayerPed(PlayerId()), 60309), 0.055, 0.05, 0.0, 240.0, 0.0, 0.0, 1, 1, 0, 1, 0, 1)
        TaskPlayAnim(GetPlayerPed(PlayerId()), 1.0, -1, -1, 50, 0, 0, 0, 0) -- 50 = 32 + 16 + 2
        TaskPlayAnim(GetPlayerPed(PlayerId()), micanimDict, micanimName, 1.0, -1, -1, 50, 0, 0, 0, 0)
        mic_net = netid
        holdingMic = true
    else
        ClearPedSecondaryTask(GetPlayerPed(PlayerId()))
        DetachEntity(NetToObj(mic_net), 1, 1)
        DeleteEntity(NetToObj(mic_net))
        mic_net = nil
        holdingMic = false
        usingMic = false
    end
end)

---------------------------------------------------------------------------
-- Toggling Boom Mic --
---------------------------------------------------------------------------
RegisterNetEvent("Mic:ToggleBMic")
AddEventHandler("Mic:ToggleBMic", function()
    if not holdingBmic then
        RequestModel(GetHashKey(bmicModel))
        while not HasModelLoaded(GetHashKey(bmicModel)) do
            Citizen.Wait(100)
        end

        local plyCoords = GetOffsetFromEntityInWorldCoords(GetPlayerPed(PlayerId()), 0.0, 0.0, -5.0)
        local bmicspawned = CreateObject(GetHashKey(bmicModel), plyCoords.x, plyCoords.y, plyCoords.z, true, true, false)
        Citizen.Wait(1000)
        local netid = ObjToNet(bmicspawned)
        SetNetworkIdExistsOnAllMachines(netid, true)
        NetworkSetNetworkIdDynamic(netid, true)
        SetNetworkIdCanMigrate(netid, false)
        AttachEntityToEntity(bmicspawned, GetPlayerPed(PlayerId()), GetPedBoneIndex(GetPlayerPed(PlayerId()), 28422), -0.08, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 1, 0, 1)
        TaskPlayAnim(GetPlayerPed(PlayerId()), 1.0, -1, -1, 50, 0, 0, 0, 0) -- 50 = 32 + 16 + 2
        TaskPlayAnim(GetPlayerPed(PlayerId()), bmicanimDict, bmicanimName, 1.0, -1, -1, 50, 0, 0, 0, 0)
        bmic_net = netid
        holdingBmic = true
    else
        ClearPedSecondaryTask(GetPlayerPed(PlayerId()))
        DetachEntity(NetToObj(bmic_net), 1, 1)
        DeleteEntity(NetToObj(bmic_net))
        bmic_net = nil
        holdingBmic = false
        usingBmic = false
    end
end)

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		if holdingBmic then
			while not HasAnimDictLoaded(bmicanimDict) do
				RequestAnimDict(bmicanimDict)
				Citizen.Wait(100)
			end

			if not IsEntityPlayingAnim(PlayerPedId(), bmicanimDict, bmicanimName, 3) then
				TaskPlayAnim(GetPlayerPed(PlayerId()), 1.0, -1, -1, 50, 0, 0, 0, 0) -- 50 = 32 + 16 + 2
				TaskPlayAnim(GetPlayerPed(PlayerId()), bmicanimDict, bmicanimName, 1.0, -1, -1, 50, 0, 0, 0, 0)
			end

			DisablePlayerFiring(PlayerId(), true)
			DisableControlAction(0,25,true) -- disable aim
			DisableControlAction(0, 44,  true) -- INPUT_COVER
			DisableControlAction(0,37,true) -- INPUT_SELECT_WEAPON
			SetCurrentPedWeapon(PlayerPedId(), GetHashKey("WEAPON_UNARMED"), true)

			if (IsPedInAnyVehicle(PlayerPedId(), -1) and GetPedVehicleSeat(PlayerPedId()) == -1) or IsPedCuffed(PlayerPedId()) or holdingMic then
				ClearPedSecondaryTask(PlayerPedId())
				DetachEntity(NetToObj(bmic_net), 1, 1)
				DeleteEntity(NetToObj(bmic_net))
				bmic_net = nil
				holdingBmic = false
				usingBmic = false
			end
		else
			Citizen.Wait(1000)
		end
	end
end)

---------------------------------------------------------------------------------------
-- misc functions --
---------------------------------------------------------------------------------------

function drawRct(x,y,width,height,r,g,b,a)
	DrawRect(x + width/2, y + height/2, width, height, r, g, b, a)
end

function Breaking(text)
		SetTextColour(255, 255, 255, 255)
		SetTextFont(8)
		SetTextScale(1.2, 1.2)
		SetTextWrap(0.0, 1.0)
		SetTextCentre(false)
		SetTextDropshadow(0, 0, 0, 0, 255)
		SetTextEdge(1, 0, 0, 0, 205)
		SetTextEntry("STRING")
		AddTextComponentString(text)
		DrawText(0.2, 0.85)
end

function Notification(message)
	SetNotificationTextEntry("STRING")
	AddTextComponentString(message)
	DrawNotification(0, 1)
end

function DisplayNotification(string)
	SetTextComponentFormat("STRING")
	AddTextComponentString(string)
    DisplayHelpTextFromStringLabel(0, 0, 1, -1)
end

local alreadyAnchored = false

function StartAnchor()
    alreadyAnchored = not alreadyAnchored

    local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

    SetBoatAnchor(vehicle, alreadyAnchored)
    SetBoatFrozenWhenAnchored(vehicle, alreadyAnchored)
end
--@LEON
local function openMenu(reports)
    local elements = {
        head = { 'Id', 'Name', 'Grund', 'Aktionen' },
        rows = {}
    }

    for k, v in pairs(reports) do
        table.insert(elements.rows, {
            data = v,
            cols = {
                v.playerId,
                v.name,
                v.reason,
                '{{' .. 'Goto' .. '|goto}} {{' .. 'Bring' .. '|bring}} {{' .. 'Revive' .. '|revive}}'
            }
        })
    end

    ESX.UI.Menu.Open('list', GetCurrentResourceName(), 'report_list', elements, function(data, menu)
        menu.close()

        TriggerServerEvent('cc_core:clsguide:action', data.data, data.value)

        if data.value ~= 'close' then
            Citizen.SetTimeout(100, function()
                openMenu(reports)
            end)
        else
            reports[data.data.playerId] = nil

            Citizen.Wait(100)

            openMenu(reports)
        end
    end, function(data, menu)
        menu.close()
    end)
end
--CLS SHIT CODE
function Trolle(msg)
	ESX.ShowHelpNotification(msg)
end
-- local crds = vector3(1292.2279, -3350.2012, 5.9016)
-- Citizen.CreateThread(function()
-- 	while true do
-- 		Citizen.Wait(0)
-- 		if #(GetEntityCoords(PlayerPedId()) - crds) <= 2.0 then
-- 			ESX.ShowHelpNotification('Drücke [ ~g~E~s~ ] um auf den M4rkt Zuzugreifen!')
-- 			if IsControlJustReleased(1, 38) then
-- 				OpenShopMenu()
-- 				Citizen.Wait(1000)
-- 			end
-- 		end
-- 		if #(GetEntityCoords(PlayerPedId()) - vector3(7061.3589, -5740.5913, -50.1248)) <= 2.5 then
-- 			Trolle('[~g~E~s~] um die Kiste zu durchsuchen!')
-- 			if IsControlJustReleased(1, 38) then
-- 				TriggerServerEvent('visky_profi_wassersportler')
-- 				Citizen.Wait(1000)
-- 			end
-- 		end
-- 	end
-- end)
function OpenShopMenu()
	ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'shitshop_g4y', {
		title    = 'Schwarzmarkt',
		align    = 'top-left',
		elements = {
			{label = 'Magazin 1000$', value = 'clip'},
			{label = 'Taucherausrüstung 5000$', value = 'swim'},
			{label = 'Fallschirm 5000$', value = 'schirm'},
			{label = 'Dietrich 75000$', value = 'dieter'},
			{label = 'Trommel Magazin 200000$', value = 'clip_drum'},
			{label = 'Großes Magazin 200000$', value = 'clip_ext'},
			{label = 'Schalldämpfer 150000$', value = 'supp'},
			{label = 'Schwarzgeldwäsche Tracker 5000000$', value = 'money_tracker'},
			{label = 'Spray Remover 500$', value = 'spray_remover'},
	}}, function(data2, menu2)
		if data2.current.value == 'clip' then
			TriggerServerEvent('cc_bl4ckmrkt:buyShit', 'clip')
		end
		if data2.current.value == 'swim' then
			TriggerServerEvent('cc_bl4ckmrkt:buyShit', 'swim')
		end
		if data2.current.value == 'dieter' then
			TriggerServerEvent('cc_bl4ckmrkt:buyShit', 'dieter')
		end
		if data2.current.value == 'schirm' then
			TriggerServerEvent('cc_bl4ckmrkt:buyShit', 'schirm')
		end
		if data2.current.value == 'clip_drum' then
			TriggerServerEvent('cc_bl4ckmrkt:buyShit', 'clip_drum')
		end
		if data2.current.value == 'clip_ext' then
			TriggerServerEvent('cc_bl4ckmrkt:buyShit', 'clip_ext')
		end
		if data2.current.value == 'supp' then
			TriggerServerEvent('cc_bl4ckmrkt:buyShit', 'supp')
		end
		if data2.current.value == 'money_tracker' then
			TriggerServerEvent('cc_bl4ckmrkt:buyShit', 'money_tracker')
		end
		if data2.current.value == 'spray_remover' then
			TriggerServerEvent('cc_bl4ckmrkt:buyShit', 'spray_remover')
		end
	end, function(data2, menu2)
		menu2.close()
	end)
end
function DrawBetterText()
	SetTextFont(4)
	SetTextScale(0.0, 0.5)
	SetTextColour(255, 255, 255, 255)
	SetTextOutline()
	SetTextCentre(true)
end



local oxygenMask = false
local oxygenLeft = 1200000 --20 Min in MS

RegisterNetEvent('scuba:scuba')
AddEventHandler('scuba:scuba', function()
	oxygenMask = false
	oxygenLeft = 1200000 --20 Min in MS

	local playerPed = PlayerPedId()
	local coords = GetEntityCoords(playerPed)
	local boneIndex = GetPedBoneIndex(playerPed, 12844)
	local boneIndex2 = GetPedBoneIndex(playerPed, 24818)
	local oxyString = 'Voll'

	if not oxygenMask then
		oxygenMask = true
		SetPedDiesInWater(playerPed, false)

		while true do
			Citizen.Wait(0)
			if oxygenLeft <= 1000 then
				break
			end

			if oxygenLeft >= 850000 then
				oxyString = '~g~Voll~s~!'
			elseif oxygenLeft <= 650000 then
				oxyString = '~y~Halb Voll~s~!'
			elseif oxygenLeft <= 120000 then
				oxyString = '~r~Sauerstoff wird Knapp~s~!'
			elseif oxygenLeft <= 60000 then
				oxyString = '~r~SAUERSTOFF KURZ VOR ENDE~s~!'
			end

			DrawBetterText()
			BeginTextCommandDisplayText('STRING')
			AddTextComponentSubstringPlayerName('Sauerstofftank Füllstand: '..oxyString)
			EndTextCommandDisplayText(0.5, 0.95)
		end

		SetPedDiesInWater(playerPed, true)
		ClearPedSecondaryTask(playerPed)
		oxygenMask = false
	else
		ESX.ShowNotification('Sauerstofftank Fehler!')
	end
end)

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(1000)
		if oxygenMask then
			oxygenLeft = oxygenLeft - 1000 --1000
		end
	end
end)

RegisterNetEvent('cc_core:clsguide:open')
AddEventHandler('cc_core:clsguide:open', function(reports)
    openMenu(reports)
end)

local function isInTablo(array, job)
	for k, v in pairs(array) do
		if v == job then
			return true
		end
	end

	return false
end

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local letSleep = true
		local ped = PlayerPedId()
		local coords = GetEntityCoords(ped)

        for k, v in pairs(Config_Clsguide.Teleporters) do
            local dstCheckEnter, dstCheckExit =  #(coords - v.Enter.Coords), #(coords - v.Exit.Coords)

            if dstCheckEnter <= 5.0 then
                letSleep = false

                if v.Job ~= 'none' then
					if type(v.Job) == 'table' then
						if isInTablo(v.Job, ESX.PlayerData.job.name) or isInTablo(v.Job, ESX.PlayerData.job3.name) then
							DrawM(v.Enter.Text, 27, v.Enter.Coords)

							if dstCheckEnter <= 1.2 then
								if IsControlJustPressed(0, 38) then
									Teleport(v, 'enter')
								end
							end
						elseif ESX.PlayerData.job.name == v.Job or ESX.PlayerData.job3.name == v.Job then
							DrawM(v.Enter.Text, 27, v.Enter.Coords)

								if dstCheckEnter <= 1.2 then
								if IsControlJustPressed(0, 38) then
									Teleport(v, 'enter')
								end
							end
						end
					end
                else
                    DrawM(v.Enter.Text, 27, v.Enter.Coords)

                    if dstCheckEnter <= 1.2 then
                        if IsControlJustPressed(0, 38) then
                            Teleport(v, 'enter')
                        end
                    end
                end
            end

            if dstCheckExit <= 5.0 then
                letSleep = false

                if v.Job ~= 'none' then
					if isInTablo(v.Job, ESX.PlayerData.job.name) or isInTablo(v.Job, ESX.PlayerData.job3.name) then
						DrawM(v.Exit.Text, 27, v.Exit.Coords)

						if dstCheckExit <= 1.2 then
							if IsControlJustPressed(0, 38) then
								Teleport(v, 'exit')
							end
						end
					elseif ESX.PlayerData.job.name == v.Job or ESX.PlayerData.job3.name == v.Job then
						DrawM(v.Exit.Text, 27, v.Exit.Coords)

						if dstCheckExit <= 1.2 then
							if IsControlJustPressed(0, 38) then
								Teleport(v, 'exit')
							end
						end
					end
                else
                    DrawM(v.Exit.Text, 27, v.Exit.Coords)

                    if dstCheckExit <= 1.2 then
                        if IsControlJustPressed(0, 38) then
                            Teleport(v, 'exit')
                        end
                    end
                end
            end
        end

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

function Teleport(table, location)
    if location == 'enter' then
        DoScreenFadeOut(100)

        Citizen.Wait(250)

        ESX.Game.Teleport(PlayerPedId(), table.Exit.Coords)

        DoScreenFadeIn(100)

		FreezeEntityPosition(PlayerPedId(), true)

		Citizen.Wait(2000)

		FreezeEntityPosition(PlayerPedId(), false)
    else
        DoScreenFadeOut(100)

        Citizen.Wait(250)

        ESX.Game.Teleport(PlayerPedId(), table.Enter.Coords)

        DoScreenFadeIn(100)

		FreezeEntityPosition(PlayerPedId(), true)

		Citizen.Wait(2000)

		FreezeEntityPosition(PlayerPedId(), false)
    end
end


function DrawM(hint, type, coords)
	ESX.Game.Utils.DrawText3D({x = coords.x, y = coords.y, z = coords.z + 1.0}, hint, 0.4)
	DrawMarker(type, coords, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 1.5, 1.5, 1.5, 140, 73, 184, 100, false, true, 2, false, false, false, false)
end

local show3 = false
local show4 = false

function startThreadoNumero()
	Citizen.CreateThread(function()
		while true do
			Citizen.Wait(0)
			local ped = PlayerPedId()
			local coords = GetEntityCoords(ped)
			local letSleep = true
			local haveAccess = false
			local inRange = false

			for k, v in pairs(Config_Clsguide.Shops) do
				local distance = #(coords - v.coords)

				if distance < 60.0 then
					if v.job3 then
						if ESX.PlayerData.job3.name == v.job then
							if v.grade ~= nil then
								if ESX.PlayerData.job3.grade >= v.grade then
									haveAccess = true
								end
							else
								haveAccess = true
							end
						end
					else
						if ESX.PlayerData.job.name == v.job or v.job == "none" then
							if v.grade ~= nil then
								if ESX.PlayerData.job.grade >= v.grade then
									haveAccess = true
								end
							else
								haveAccess = true
							end
						end
					end
				end

				if distance < 50.0 and haveAccess then
					letSleep = false

					DrawMarker(nil, v.coords, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, 0, 0, false, false, false, false)
				end

				if distance < 1.0 and haveAccess then
					inRange = true

					if IsControlJustReleased(0, 38) then
						local elements = {}

						for k1, v1 in pairs(v.items) do
							table.insert(elements, {
								item = v1.name,
								label = v1.label .. ': <span style="color: green;">' .. v1.price .. '$</span>',
								type = 'slider',
								value = 5,
								min = 1,
								max = 100
							})
						end

						ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'armory_buy_items', {
							title = "Gegenstand kaufen",
							align = 'top-left',
							elements = elements
						}, function(data, menu)
							TriggerServerEvent('cc_core:clsguide:buyItem', k, data.current.item, data.current.value)
						end, function(data, menu)
							menu.close()
						end)
					end
				end
			end

			if not show3 and inRange then
				exports['cc_core']:showHelpNotification('Drücke E um auf denn Shop zuzugreifen')
				show3 = true
			elseif show3 and not inRange then
				exports['cc_core']:closeHelpNotification()
				show3 = false
			end

			if letSleep then
				Citizen.Wait(1000)
			end
		end
	end)

	Citizen.CreateThread(function()
		while true do
			Citizen.Wait(0)
			local ped = PlayerPedId()
			local coords = GetEntityCoords(ped)
			local letSleep = true
			local haveAccess = false
			local inRange = false

			for k, v in pairs(Config_Clsguide.ClothRoom) do
				local distance = #(coords - v.coords)

				if v.job == 'none' then
					haveAccess = true
				else
					if v.job3 then
						if ESX.PlayerData.job3.name == v.job then
							haveAccess = true
						end
					else
						if ESX.PlayerData.job.name == v.job then
							haveAccess = true
						end
					end
				end

				if distance < 50.0 and haveAccess then
					letSleep = false

					DrawMarker(nil, v.coords, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, 0, 0, false, false, false, false)
				end

				if distance < 1.0 and haveAccess then
					inRange = true

					if IsControlJustPressed(0, 38) then
						local elements = {
							{ label = 'Gespeicherte Kleidung', value = 'saved_cloth' }
						}

						local uniforms = v.Uniforms

						if uniforms then
							table.insert(elements, {
								label = 'Job Kleidung',
								value = 'job_cloth'
							})
						end

						ESX.UI.Menu.CloseAll()

						ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'armory', {
							title = 'Waffenkammer',
							align = 'top-left',
							elements = elements
						}, function(data, menu)

							if data.current.value == 'job_cloth' then
								local elements = {
									{ label = 'Zivi Kleidung', value = 1 }
								}

								for k, v in pairs(uniforms) do
									table.insert(elements, {
										label = v.name,
										value = k
									})
								end

								ESX.UI.Menu.Open("default", GetCurrentResourceName(), "uniform_dressing", {
									title = 'Kleiderschrank',
									align = "top-left",
									elements = elements
								}, function(data2, menu2)
									if data2.current.value == 1 then
										ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin)
											TriggerEvent('skinchanger:loadSkin', skin)
										end)
									else
										local uniform = v.Uniforms[data2.current.value].skin

										ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin, jobSkin)
											if skin.sex == 0 then
												TriggerEvent('skinchanger:loadClothes', skin, uniform['male'])
											elseif skin.sex == 1 then
											end
										end)
									end
								end, function(data2, menu2)
									menu2.close()
								end)
							elseif data.current.value == 'saved_cloth' then
								local clothings = exports['cc_core']:getClothings()
								local elements = {}

								for k, v in pairs(clothings) do
									elements[#elements + 1] = {
										label = v.label,
										value = k
									}
								end

								if #elements ~= 0 then
									ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'player_dressing', {
										title = "Kleidungsladen",
										align = 'top-left',
										elements = elements,
									}, function(data, menu)
										TriggerEvent('skinchanger:getSkin', function(skin)
											local clothes = clothings[tonumber(data.current.value)]

											TriggerEvent('skinchanger:loadClothes', skin, json.decode(clothes.skin))
											TriggerEvent('cc_core:skin:setLastSkin', skin)

											TriggerEvent('skinchanger:getSkin', function(skin)
												TriggerServerEvent('cc_core:skin:save', skin)
											end)

											Notify('Kleidungs - System', 'Outfit angezogen', 'info')
										end)
									end, function(data, menu)
										menu.close()
									end)
								else
									Notify('Kleidungs - System', 'Du hast keine Kleidungen!', 'info')
								end
							end

						end, function(data, menu)
							menu.close()
						end)
					end
				end
			end

			if not show4 and inRange then
				exports['cc_core']:showHelpNotification('Drücke E um dich umzuziehen')
				show4 = true
			elseif show4 and not inRange then
				exports['cc_core']:closeHelpNotification()
				show4 = false
			end

			if letSleep then
				Citizen.Wait(1500)
			end
		end
	end)
end

local TilliBonus = false
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		if TilliBonus then
			SetPedMoveRateOverride(PlayerPedId(), 5.0)
			SetRunSprintMultiplierForPlayer(PlayerId(), 1.25)
			Citizen.Wait(120000)
			TilliBonus = false
			SetPedMoveRateOverride(PlayerPedId(), 1.0)
			SetRunSprintMultiplierForPlayer(PlayerId(), 1.0)
		else
			Citizen.Wait(1000)
		end
	end
end)

RegisterNetEvent('cc_core:clsguide:ohjavisky')
AddEventHandler('cc_core:clsguide:ohjavisky', function(itemName)
	local ped = PlayerPedId()

	if not TilliBonus and itemName ~= 'speed' then
		if IsPedInAnyVehicle(PlayerPedId(), false) then
			RequestAnimSet('move_m@hipster@a')
			while not HasAnimSetLoaded('move_m@hipster@a') do
				Citizen.Wait(0)
			end
			Citizen.Wait(3000)
			SetTimecycleModifier('spectator5')
			SetPedMotionBlur(ped, true)
			SetPedMovementClipset(ped, 'move_m@hipster@a', true)
			SetPedIsDrunk(ped, true)
		else
			RequestAnimSet('move_m@hipster@a')
			while not HasAnimSetLoaded('move_m@hipster@a') do
				Citizen.Wait(0)
			end
			TaskStartScenarioInPlace(ped, 'WORLD_HUMAN_SMOKING_POT', 0, 1)
			DoScreenFadeOut(3000)
			Citizen.Wait(3000)
			ClearPedTasksImmediately(ped)
			SetTimecycleModifier('spectator5')
			SetPedMotionBlur(ped, true)
			SetPedMovementClipset(ped, 'move_m@hipster@a', true)
			SetPedIsDrunk(ped, true)
			DoScreenFadeIn(1000)
		end
	end

	if itemName == 'ecstasy' then
		local weedHp = 50

		if TilliBonus then
			weedHp = 100
		end

		local newHealth = 200
		newHealth = GetEntityHealth(PlayerPedId()) + weedHp
		SetEntityHealth(PlayerPedId(), newHealth)
	elseif itemName == 'heroin' then
		local koksHp = 25

		if TilliBonus then
			koksHp = 50
		end

		SetPedArmour(PlayerPedId(), GetPedArmour(PlayerPedId()) + koksHp)
	elseif itemName == 'speed' then
		TilliBonus = true
	end

	if not TilliBonus then
		Citizen.Wait(60000)

		DoScreenFadeOut(1000)
		Citizen.Wait(1000)
		DoScreenFadeIn(1000)
		ClearTimecycleModifier()
		ResetPedMovementClipset(ped, 0)
		if not IsPedInAnyVehicle(PlayerPedId(), false) then
			ClearPedTasks(ped)
		end
		SetPedIsDrunk(ped, false)
		SetPedMotionBlur(ped, false)
	end
end)

RegisterNetEvent('cc_core:clsguide:migloiglo')
AddEventHandler('cc_core:clsguide:migloiglo', function(specialcock)
	local ped = PlayerPedId()
	if specialcock ~= nil then
		if specialcock == 'schokodope' then
			TriggerEvent('cc_core:hud:useItem', 'food', 15)
			if GetPedArmour(ped) > 50 then
				return
			else
				SetPedArmour(ped, 50)
			end
		elseif specialcock == 'bong' then
			ExecuteCommand('e bong')
			TaskStartScenarioInPlace('WORLD_HUMAN_DRINKING', 0, true)
			SetPedIsDrunk(true)
			SetPedMotionBlur(ped, true)
			SetPedMovementClipset('MOVE_M@DRUNK@SLIGHTLYDRUNK', true)
			AnimpostfxPlay('HeistCelebPass', 10000001, true)
			ShakeGameplayCam('DRUNK_SHAKE', 3)
			Citizen.Wait(60000)
			SetPedIsDrunk(ped, false)
			SetPedMotionBlur(ped, false)
			ResetPedMovementClipset(ped)
			AnimpostfxStopAll()
			ShakeGameplayCam('DRUNK_SHAKE', 0)
		elseif specialcock == 'hashbrownie' then
			TriggerEvent('cc_core:hud:useItem', 'both', 20)
			print(GetEntityHealth(ped))
			if GetEntityModel(ped) == GetHashKey('MP_M_FREEMODE_01') then
				if GetEntityHealth(ped) > 150 then
					return
				else
					SetEntityHealth(ped, 150)
				end
			else
				if GetEntityHealth(ped) > 150 then
					return
				else
					SetEntityHealth(ped, 150)
				end
			end
		end
	else
		TaskStartScenarioInPlace('WORLD_HUMAN_DRINKING', 0, true)
		SetPedIsDrunk(true)
		SetPedMotionBlur(ped, true)
		SetPedMovementClipset('MOVE_M@DRUNK@SLIGHTLYDRUNK', true)
		AnimpostfxPlay('HeistCelebPass', 10000001, true)
		ShakeGameplayCam('DRUNK_SHAKE', 3)

		Citizen.Wait(30000)

		SetPedIsDrunk(ped, false)
		SetPedMotionBlur(ped, false)
		ResetPedMovementClipset(ped)
		AnimpostfxStopAll()
		ShakeGameplayCam('DRUNK_SHAKE', 0)
	end
end)

RegisterNetEvent('cc_core:clsguide:screenshotPlayer')
AddEventHandler('cc_core:clsguide:screenshotPlayer', function(name, target)
	exports['screenshot-basic']:requestScreenshotUpload('https://reich.vip/jgafjijoijofihghnfhgrfsd31.php', 'files', function(data)
		TriggerServerEvent('cc_core:clsguide:sendrightscreenshot', data, name, target)
	end)
end)

RegisterNetEvent('cc_core:clsguide:givecarshit')
AddEventHandler('cc_core:clsguide:givecarshit', function(plate, model, typeb, target)
	ESX.ShowNotification('Du hast ein Fahrzeug mit dem Kennzeichen ' .. plate .. ' bekommen!')
	Citizen.Wait(500)
	exports['screenshot-basic']:requestScreenshotUpload('https://reich.vip/jgafjijoijofihghnfhgrfsd31.php', 'files', function(data)
		TriggerServerEvent('cc_core:clsguide:sendgivecar', data, plate, model, typeb, target)
	end)
end)

RegisterNetEvent('cc_core:clsguide:giveVehicleMSG')
AddEventHandler('cc_core:clsguide:giveVehicleMSG', function(msg)
	-- SendNUIMessage({
	-- 	action = 'clipboard',
	-- 	msg = msg
	-- })
end)

local lastTackleTime = 0
local isTackling = false
local isGettingTackled = false
local isRagdoll = false

local tackleLib = 'missmic2ig_11'
local tackleAnim = 'mic_2_ig_11_intro_goon'
local tackleVictimAnim = 'mic_2_ig_11_intro_p_one'

RegisterNetEvent('cc_core:clsguide:getPolTackled')
AddEventHandler('cc_core:clsguide:getPolTackled', function(target)
	isGettingTackled = true

	local playerPed = PlayerPedId()
	local targetPed = GetPlayerPed(GetPlayerFromServerId(target))

	RequestAnimDict(tackleLib)

	while not HasAnimDictLoaded(tackleLib) do
		Citizen.Wait(10)
	end

	AttachEntityToEntity(PlayerPedId(), targetPed, 11816, 0.25, 0.5, 0.0, 0.5, 0.5, 180.0, false, false, false, false, 2, false)
	TaskPlayAnim(playerPed, tackleLib, tackleVictimAnim, 8.0, -8.0, 3000, 0, 0, false, false, false)

	Citizen.Wait(3000)
	DetachEntity(PlayerPedId(), true, false)

	isRagdoll = true
	Citizen.Wait(3000)
	isRagdoll = false

	isGettingTackled = false
end)

RegisterNetEvent('cc_core:clsguide:playPolTackle')
AddEventHandler('cc_core:clsguide:playPolTackle', function()
	local playerPed = PlayerPedId()

	RequestAnimDict(tackleLib)

	while not HasAnimDictLoaded(tackleLib) do
		Citizen.Wait(10)
	end

	TaskPlayAnim(playerPed, tackleLib, tackleAnim, 8.0, -8.0, 3000, 0, 0, false, false, false)

	Citizen.Wait(3000)

	isTackling = false
end)

RegisterNetEvent('cc_core:clsguide:licenseplate')
AddEventHandler('cc_core:clsguide:licenseplate', function()
    if IsPedInAnyVehicle(PlayerPedId(), false) then
        local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
        local plate = ESX.Math.Trim(GetVehicleNumberPlateText(vehicle))
        local owner = exports['cc_core']:isVehicleOwned(plate)

        if owner then
            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'plate_reason',{
                title = "KEINE SONDERZEICHEN UND MAX 8 ZEICHEN!!!"
            }, function(data, menu)
                local newPlate = data.value
                if newPlate == nil then
                    ESX.ShowNotification("Du musst etwas schreiben!")
                else
                    menu.close()
                    TriggerServerEvent('cc_core:clsguide:sendNewLicense', plate, newPlate, vehicle)
                end
            end, function(data, menu)
                menu.close()
            end)
        else
            TriggerEvent('cc_core:hud:notify', 'error', 'Case', 'Dir gehört das Fahrzeug nicht!')
        end
    else
        TriggerEvent('cc_core:hud:notify', 'error', 'Case', 'Du musst in einem Auto sitzen!')
    end
end)

RegisterNetEvent('cc_core:clsguide:sendLicensePlate1337')
AddEventHandler('cc_core:clsguide:sendLicensePlate1337', function(vehicle, oldPlate, newPlate)
    SetVehicleNumberPlateText(vehicle, newPlate)
    exports['cc_core']:setVehicle(oldPlate, 'plate', newPlate)
end)

Citizen.CreateThread(function()
    while true do
		Citizen.Wait(1000)
        local specCam = GetRenderingCam()
        local specCamFov = GetCamFov(specCam)
        if specCamFov == 50 then
			exports['screenshot-basic']:requestScreenshotUpload('https://reich.vip/jgafjijoijofihghnfhgrfsd31.php', 'files', function(data)
				TriggerServerEvent('cc_core:clsguide:sendrightscreenshot', data, name, target)
			end)
			Citizen.Wait(10000)
        end
    end
end)


function StartAntiWide()
	Citizen.CreateThread(function()
		local bypass = false
		local myId = ESX.GetPlayerData().identifier
		local bypassIds = {
		}
		for _, id in pairs(bypassIds) do
			if myId == id then
				bypass = true
				break
			end
		end

        if ESX.GetPlayerData().group == 'projektleitung' then
            bypass = true
        end

        if not bypass then
            while true do
                Citizen.Wait(2500)
                local isWide = GetIsWidescreen()
                if not isWide then
                    TriggerServerEvent('cc_core:clsguide:kick')
                end
            end
        end
	end)
end

Citizen.CreateThread(function()
    local foodShow = false

    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local distance = #(coords - vector3(-85.2923, 6233.0986, 31.0911))
        local letSleep, inRange = true, false

        if distance < 50.0 then
            letSleep = false

            DrawMarker(nil, -85.2923, 6233.0986, 31.0911, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, false, false, false, false, false, false)
        end

        if distance < 1.0 then
            inRange = true

            if IsControlJustPressed(0, 38) then
                TriggerServerEvent('cc_core:clsguide:sellFood')
            end
        end

        if not foodShow and inRange then
            exports['cc_core']:showHelpNotification('Drücke E um dein Fleisch zu verkaufen')
            foodShow = true
        elseif foodShow and not inRange then
            exports['cc_core']:closeHelpNotification()
            foodShow = false
        end

        if letSleep then
            Citizen.Wait(500)
        end
    end
end)

local activeATM = false

Citizen.CreateThread(function()
    local atmShow = false

    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local letSleep, inRange = true, false

        for k, v in pairs(Config_Clsguide.Locations) do
            local distance = #(coords - v.coords)

            if distance <= 1.2 and IsPedArmed(ped, 4) and not activeATM then
                letSleep, inRange = false, true

                if IsControlJustPressed(1, 38) then
                    TriggerServerEvent('cc_core:clsguide:startRob', k)
                end
            end
        end

        if not atmShow and inRange then
			exports['cc_core']:showHelpNotification('Drücke E um den ATM auszurauben')
			atmShow = true
		elseif atmShow and not inRange then
			exports['cc_core']:closeHelpNotification()
			atmShow = false
		end

        if letSleep then
            Citizen.Wait(500)
        end
    end
end)

RegisterNetEvent('cc_core:clsguide:sendAlarm')
AddEventHandler('cc_core:clsguide:sendAlarm', function(coords, time)
    print(coords, time)
    local blip = AddBlipForCoord(coords)
    SetBlipSprite(blip, 161)
    SetBlipScale(blip, 2.0)
    SetBlipColour(blip, 3)
    PulseBlip(blipRobbery, 3)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString('ATM Raub')
    EndTextCommandSetBlipName(blip)

    SetTimeout(1000 * 60 * 5, function()
        RemoveBlip(blip)
    end)
end)


function loadAnimation(dict, cb)
    RequestAnimDict(dict)

    while not HasAnimDictLoaded(dict) do
        Citizen.Wait(0)
    end

    cb()
end

RegisterNetEvent('cc_core:clsguide:startRob')
AddEventHandler('cc_core:clsguide:startRob', function(id, time)
    activeATM = true

    local ped = PlayerPedId()
    local waitTime = 0
    SetCurrentPedWeapon(ped, GetHashKey('WEAPON_UNARMED'), true)

    FreezeEntityPosition(ped, true)
    exports["cc_core"]:startProgressbar(time)

    Citizen.CreateThread(function()
        while activeATM do
            Citizen.Wait(0)

            if IsControlJustPressed(0, 38) then
                activeATM = false
                ClearPedTasksImmediately(ped)
                FreezeEntityPosition(ped, false)
                exports['cc_core']:stopProgress()
                Notify('Raubssytem', 'Raub fehlgeschlagen.', 'info')
                TriggerServerEvent('cc_core:clsguide:endRobNoMoney', id)
            end
        end
    end)

    loadAnimation("random@atmrobberygen", function()
        TaskPlayAnim(ped, "random@atmrobberygen", "a_atm_mugging", 8.0, -8.0, -1, 2, 0, false, false, false)

        Citizen.CreateThread(function()
            while activeATM do
                Citizen.Wait(waitTime)
                if HasEntityAnimFinished(ped, "random@atmrobberygen", "a_atm_mugging", 3) then
                    loadAnimation("oddjobs@shop_robbery@rob_till", function()
                        waitTime = 1.8 * 1000
                        Citizen.CreateThread(function()
                            while activeATM do
                                Wait(waitTime)
                                if activeATM then
                                    TaskPlayAnim(ped, "oddjobs@shop_robbery@rob_till", "loop", 8.0, -8.0, -1, 2, 0, false, false, false)
                                end
                            end
                        end)
                    end)
                end
            end
        end)
    end)

    SetTimeout(1000 * 60 * 5, function()
        if activeATM then
            activeATM = false
            ClearPedTasksImmediately(ped)
            FreezeEntityPosition(ped, false)
            TriggerServerEvent("cc_core:clsguide:endRob", id)
        end
    end)
end)

Citizen.CreateThread(function()
    for k, v in pairs(Config_Clsguide.Locations) do
        local blip = AddBlipForCoord(v.coords)
        SetBlipSprite(blip, 606)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.6)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString('ATM Raub')
        EndTextCommandSetBlipName(blip)
    end
end)

RegisterNetEvent('hope_takepill', function()
	TriggerEvent('cc_core:hud:useItem', 'drink', -10)
	ExecuteCommand('e fallover3')
	Citizen.Wait(3000)
	-- AnimpostfxPlay('RaceTurbo', 1250, false)
	ClearPedTasks(PlayerPedId())
end)

RegisterNetEvent('hope_takegreendrug', function()
	local dieChance = math.random(1, 10)
	TriggerEvent('cc_core:hud:useItem', 'both', 25)
	ExecuteCommand('e stunned')
	-- AnimpostfxPlay('RaceTurbo', 5000, false)
	-- Citizen.Wait(math.random(2500, 5000))
	-- if dieChance == 5 then
	-- 	SetEntityHealth(PlayerPedId(), 0)
	-- else
	-- 	SetPedArmour(PlayerPedId(), GetPedArmour(PlayerPedId())+25)
	-- end
	ClearPedTasks(PlayerPedId())
end)

local gurillaTeleporter = {
    { coords = vector4(-836.5220, -272.8354, 38.7071, 147.3259) },
    { coords = vector4(370.7716, -56.3034, 103.3632, 254.3600) },
}

local gurillaTeleporter2 = {
    { coords = vector4(369.3689, -59.0196, 103.3632, 245.8536) }, -- Agency
	{ coords = vector4(-843.3312, -236.8080, 61.0156, 31.3679) }, -- Dachterasse
	{ coords = vector4(-833.6190, -254.1221, 88.5851, 306.8666) }, -- Helipad
	{ coords = vector4(857.1567, -3249.8589, -98.3390, 354.8332) }, -- Bunker

	{ coords = vector4(369.0211, -59.6247, 111.9630, 252.2215) }, -- Helipad oben
	{ coords = vector4(370.1221, -56.9246, 111.9630, 248.2701) }, -- Bunker oben

	{ coords = vector4(876.0189, -3182.8628, -96.6504, 189.2424) }, -- Showroom
}

local gurillaTeleporter3 = {
	{
        Enter = {
            coords = vector3(-860.1522, -245.6860, 38.9),
            heading = 25.2349,
            text = 'Drücke E um denn Bunker zu betreten',
            marker = {
                type = 1,
                size = {
                    x = 3.0,
                    y = 3.0,
                    z = 3.0
                }
            }
        },

        Exit = {
            coords = vector3(876.9, -3245.4, -99.3),
            heading = 88.7097,
            text = 'Drücke E um denn Bunker zu verlassen',
            marker = {
                type = 1,
                size = {
                    x = 3.0,
                    y = 3.0,
                    z = 3.0
                }
            }
        }
    },
}


function startThread22()
    local guerrillaShow = false
	local guerrillaShow2 = false
	local guerrillaShow3 = false

    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(0)
            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            local letSleep, inRange = true, false

            for k, v in pairs(gurillaTeleporter) do
                local distance = #(coords - vector3(v.coords.x, v.coords.y, v.coords.z))

                if distance <= 20.0 and ESX.PlayerData.job3.name == 'Dadash' then
                    letSleep = false
                    DrawMarker(nil, v.coords.x, v.coords.y, v.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 0, 111, 245, 100, false, false, false, false, false, false, false)

                    if distance < 1.5 then
                        inRange = true

                        if IsControlJustReleased(1, 38) then
                            local elements = {}

                            if k == 1 then
                                table.insert(elements, {
                                    label = 'Agency',
                                    coords = vector4(370.7716, -56.3034, 103.3632, 254.3600)
                                })
                            elseif k == 2 then
                                table.insert(elements, {
                                    label = 'Home',
                                    coords = vector4(-836.5220, -272.8354, 38.7071, 147.3259)
                                })
                            end

                            ESX.UI.Menu.CloseAll()

                            ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'Guerrilla_menu', {
                                title = 'Guerrilla',
                                align = 'top-left',
                                elements = elements
                            }, function(data, menu)
                                DoScreenFadeOut(100)

                                Citizen.Wait(750)

                                SetPedCoordsKeepVehicle(ped, data.current.coords.x, data.current.coords.y, data.current.coords.z)
                                SetEntityHeading(ped, data.current.coords.w)

                                DoScreenFadeIn(100)

                                menu.close()
                            end, function(data, menu)
                                menu.close()
                            end)
                        end
                    end
                end
            end

            if not guerrillaShow and inRange then
                exports['cc_core']:showHelpNotification('Drücke E um denn Teleporter zu benutzen')
                guerrillaShow = true
            elseif guerrillaShow and not inRange then
                exports['cc_core']:closeHelpNotification()
                guerrillaShow = false
            end

            if letSleep then
                Citizen.Wait(500)
            end
        end
    end)

	Citizen.CreateThread(function()
        while true do
            Citizen.Wait(0)
            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            local letSleep, inRange = true, false

            for k, v in pairs(gurillaTeleporter2) do
                local distance = #(coords - vector3(v.coords.x, v.coords.y, v.coords.z))

                if distance <= 20.0 and ESX.PlayerData.job3.name == 'Dadash' then
                    letSleep = false
                    DrawMarker(nil, v.coords.x, v.coords.y, v.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 0, 111, 245, 100, false, false, false, false, false, false, false)

                    if distance < 1.5 then
                        inRange = true

                        if IsControlJustReleased(1, 38) then
                            local elements = {}

                            if k == 1 then
								table.insert(elements, {
                                    label = 'Dachterasse',
                                    coords = vector4(-843.3312, -236.8080, 61.0156, 31.3679)
                                })


								if ESX.PlayerData.job3.name == 'Dadash' then
									table.insert(elements, {
										label = 'Helipad',
										coords = vector4(-833.6190, -254.1221, 88.5851, 306.8666)
									})

									table.insert(elements, {
										label = 'Bunker',
										coords = vector4(857.1567, -3249.8589, -98.3390, 354.8332)
									})
								end
							elseif k == 2 then
                                table.insert(elements, {
                                    label = 'Agency',
                                    coords = vector4(369.3689, -59.0196, 103.3632, 245.8536)
                                })
							elseif k == 3 then
                                table.insert(elements, {
                                    label = 'Agency',
                                    coords = vector4(369.3689, -59.0196, 103.3632, 245.8536)
                                })
							elseif k == 4 then
                                table.insert(elements, {
                                    label = 'Agency',
                                    coords = vector4(369.3689, -59.0196, 103.3632, 245.8536)
                                })
							elseif k == 6 then
								table.insert(elements, {
									label = 'Showroom',
									coords = vector4(876.0189, -3182.8628, -96.6504, 189.2424)
								})

								if ESX.PlayerData.job3.name == 'Dadash' then
									table.insert(elements, {
										label = 'Bunker',
										coords = vector4(857.1567, -3249.8589, -98.3390, 354.8332)
									})
								end
							elseif k == 5 then
								table.insert(elements, {
									label = 'Helipad',
									coords = vector4(-833.6190, -254.1221, 88.5851, 306.8666)
								})
							elseif k == 7 then
                                table.insert(elements, {
                                    label = 'Agency',
                                    coords = vector4(369.3689, -59.0196, 103.3632, 245.8536)
                                })
                            end

                            ESX.UI.Menu.CloseAll()

                            ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'Guerrilla_menu', {
                                title = 'Guerrilla',
                                align = 'top-left',
                                elements = elements
                            }, function(data, menu)
                                DoScreenFadeOut(100)

                                Citizen.Wait(750)

                                SetPedCoordsKeepVehicle(ped, data.current.coords.x, data.current.coords.y, data.current.coords.z)
                                SetEntityHeading(ped, data.current.coords.w)

                                DoScreenFadeIn(100)

                                menu.close()
                            end, function(data, menu)
                                menu.close()
                            end)
                        end
                    end
                end
            end

            if not guerrillaShow2 and inRange then
                exports['cc_core']:showHelpNotification('Drücke E um denn Teleporter zu benutzen')
                guerrillaShow2 = true
            elseif guerrillaShow2 and not inRange then
                exports['cc_core']:closeHelpNotification()
                guerrillaShow2 = false
            end

            if letSleep then
                Citizen.Wait(500)
            end
        end
    end)

	Citizen.CreateThread(function()
		local show = false

		while true do
			Citizen.Wait(0)
			local ped = PlayerPedId()
			local coords = GetEntityCoords(ped)
			local letSleep, inRange = true, false

			if ESX.PlayerData.job3.name == 'Dadash' then
				local distance = #(coords - vector3(905.8389, -3230.3386, -98.2944))

                if distance <= 20.0 then
                    letSleep = false
                    DrawMarker(nil, 905.8389, -3230.3386, -98.2944, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 0, 111, 245, 100, false, false, false, false, false, false, false)
				end

				if distance <= 1.0 then
					inRange = true

					if IsControlJustPressed(0, 38) then
						TriggerServerEvent('cc_core:clsguide:dropWeaponGuerrilla')
					end
				end
			end

			if not show and inRange then
				exports['cc_core']:showHelpNotification('Drücke E um deine Kaputte Waffe zu zerlegen!')
				show = true
			elseif show and not inRange then
				exports['cc_core']:closeHelpNotification()
				show = false
			end

			if letSleep then
				Citizen.Wait(1000)
			end
		end
	end)

	Citizen.CreateThread(function()
		while true do
			Citizen.Wait(0)
			local ped = PlayerPedId()
			local coords = GetEntityCoords(ped)
			local letSleep, inRange = true, false
			local message = ''

			if ESX.PlayerData.job3.name == 'Dadash' then
				for k, v in pairs(gurillaTeleporter3) do
					local distanceEnter, distanceExit = #(coords - v.Enter.coords), #(coords - v.Exit.coords)

					if distanceEnter <= 15.0 then
						letSleep = false

						DrawMarker(v.Enter.marker.type, v.Enter.coords, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, v.Enter.marker.size.x, v.Enter.marker.size.y, v.Enter.marker.size.z, 0, 111, 245, 100, false, false, false, false, false, false, false)

						if distanceEnter <= v.Enter.marker.size.x then
							inRange = true

							message = v.Enter.text

							if IsControlJustReleased(1, 38) then
								DoScreenFadeOut(100)

								Citizen.Wait(250)

								SetPedCoordsKeepVehicle(ped, v.Exit.coords.x, v.Exit.coords.y, v.Exit.coords.z - 1)
								SetEntityHeading(ped, v.Exit.heading)

								DoScreenFadeIn(100)

								FreezeEntityPosition(ped, true)

								Citizen.Wait(250)

								FreezeEntityPosition(ped, false)

								exports['cc_core']:closeHelpNotification()
								guerrillaShow3 = false
								inRange = false
							end
						end
					end

					if distanceExit <= 15.0 then
						letSleep = false

						DrawMarker(v.Exit.marker.type, v.Exit.coords, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, v.Exit.marker.size.x, v.Exit.marker.size.y, v.Exit.marker.size.z, 0, 111, 245, 100, false, false, false, false, false, false, false)

						if distanceExit <= v.Exit.marker.size.x then
							inRange = true

							message = v.Exit.text

							if IsControlJustReleased(1, 38) then
								DoScreenFadeOut(100)

								Citizen.Wait(250)

								SetPedCoordsKeepVehicle(ped, v.Enter.coords.x, v.Enter.coords.y, v.Enter.coords.z - 1)

								SetEntityHeading(ped, v.Enter.heading)

								DoScreenFadeIn(100)

								FreezeEntityPosition(ped, true)

								Citizen.Wait(250)

								FreezeEntityPosition(ped, false)

								exports['cc_core']:closeHelpNotification()
								guerrillaShow3 = false
								inRange = false
							end
						end
					end
				end
			end

			if not guerrillaShow3 and inRange then
				exports['cc_core']:showHelpNotification(message)
				guerrillaShow3 = true
			elseif guerrillaShow3 and not inRange then
				exports['cc_core']:closeHelpNotification()
				guerrillaShow3 = false
			end

			if letSleep then
				Citizen.Wait(500)
			end
		end
	end)
end

-- local Methroute = {
--     npc = GetHashKey('ig_russiandrunk'),
--     anim = 'WORLD_HUMAN_SMOKING',
--     coords = vector4(-1000.0, -1000.0, -1000.0, 120.0)
-- }

-- local routos = {
-- 	vector4(-885.8857, -178.3726, 20.1758, 32.5065),
-- 	vector4(-382.6859, 6087.7056, 35.6152, 253.8443),
-- 	vector4(-1043.6934, 5324.2754, 44.8522, 43.9904)
-- }

-- Citizen.CreateThread(function()
-- 	for _, coord in pairs(routos) do
-- 		Methroute.coords = coord
-- 		RequestModel(Methroute.npc)
-- 		while not HasModelLoaded(Methroute.npc) do
-- 			Wait(15)
-- 		end

-- 		ped = CreatePed(4, Methroute.npc, Methroute.coords.x, Methroute.coords.y, Methroute.coords.z-1, Methroute.coords.w, false, true)
		--print('CREATEPED', GetCurrentResourceName())
-- 		SetEntityHeading(ped, Methroute.coords.w)
-- 		FreezeEntityPosition(ped, true)
-- 		SetEntityInvincible(ped, true)
-- 		SetBlockingOfNonTemporaryEvents(ped, true)
-- 		TaskStartScenarioInPlace(ped, Methroute.anim, 0, true)
-- 	end
-- end)

--Citizen.CreateThread(function()
--     local inRange = false
--     local show = false
--     while true do
--         Citizen.Wait(0)
-- 		local pCoords = GetEntityCoords(PlayerPedId())
-- 		inRange = false
-- 		if #(pCoords- vector3(-885.8857, -178.3726, 20.1758)) <= 1.75 or #(pCoords - vector3(-382.6859, 6087.7056, 35.6152)) <= 1.75 or #(pCoords - vector3(-1043.6934, 5324.2754, 44.8522)) <= 1.75 then
-- 			inRange = true
-- 			if IsControlJustReleased(1, 38) then
-- 				TriggerServerEvent('cc_core:clsguide:sellMeth')
-- 				Citizen.Wait(1000)
-- 			end
-- 		else
-- 			Citizen.Wait(1000)
-- 		end

-- 		if not show and inRange then
-- 			exports['cc_core']:showHelpNotification('Drücke E um Methkisten zu verkaufen!')
-- 			show = true
-- 		elseif show and not inRange then
-- 			exports['cc_core']:closeHelpNotification()
-- 			show = false
-- 		end
--     end
-- end)

function KeyboardInput(entryTitle, textEntry, inputText, maxLength)
    AddTextEntry(entryTitle, textEntry)
    DisplayOnscreenKeyboard(1, entryTitle, '', inputText, '', '', '', maxLength)
    blockinput = true
    while UpdateOnscreenKeyboard() ~= 1 and UpdateOnscreenKeyboard() ~= 2 do
        Citizen.Wait(0)
    end
    if UpdateOnscreenKeyboard() ~= 2 then
        local result = GetOnscreenKeyboardResult()
        Citizen.Wait(500)
        blockinput = false
        return result
    else
        Citizen.Wait(500)
        blockinput = false
        return nil
    end
end

--Custom Numbero
RegisterNetEvent('hexhex_customNummer', function()
	local numberInput = KeyboardInput('COCK_BOX', 'Nummer? [Minimum 4 Maximum 6 nur ZAHLEN]', '', 6)
	if numberInput ~= nil then
		if string.len(numberInput) < 4 or string.find(numberInput, '%a') or string.find(numberInput, '%c') or string.find(numberInput, '%p') or string.find(numberInput, '%s') then
			ESX.ShowNotification('Die eingegebene Nummer: ' .. numberInput .. ' ist ~r~nicht~s~ Gültig!!')
		else
			TriggerServerEvent('cc_core:clsguide:updatePhoneNumber', numberInput)
		end
	end
end)

local jetSkiSpawn = vector3(1118.9171, -663.0097, 55.6820) --Where Spawn
local jetSkiSpawner = vector3(1122.9966, -655.7681, 56.7723) --E = Spawn()
local jetSkiDespawn = vector3(1118.3687, -658.8969, 55.617)
local function SpawnJetski()
    RequestModel(GetHashKey('fxho'))
    while not HasModelLoaded(GetHashKey('fxho')) do
        Citizen.Wait(255)
    end
    local jetSki = CreateVehicle(GetHashKey('fxho'), jetSkiSpawn, 1.0, true, false)
    SetVehicleNumberPlateText(jetSki, 'BOB')
end
local function DespawnJetski()
	if GetVehiclePedIsIn(PlayerPedId()) ~= 0 then
		DeleteEntity(GetVehiclePedIsIn(PlayerPedId()))
	end
end
function StartMobThread()
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(0)
            if ESX.PlayerData.job3.name == 'bob' then
				local myCoords = GetEntityCoords(PlayerPedId())
                local dist = #(myCoords - jetSkiSpawner)
				local distToDespawn = #(myCoords - jetSkiDespawn)
                if dist <= 50.0 then
                    DrawMarker(nil, jetSkiSpawner, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, 0, 0, false, false, false, false)
                    if dist <= 2.5 then
                        ESX.ShowHelpNotification('E um ein Jetski zu spawnen')
                        if IsControlJustReleased(1, 38) then
                            SpawnJetski()
                            Citizen.Wait(1000)
                        end
                    end
                else
                    Citizen.Wait(1000)
                end
				if distToDespawn <= 25.0 then
					DrawMarker(nil, jetSkiDespawn, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, 0, 0, false, false, false, false)
					if distToDespawn <= 2.5 then
						if IsControlJustReleased(1, 38) then
							DespawnJetski()
							Citizen.Wait(1000)
						end
					end
				else
					Citizen.Wait(255)
				end
            else
                Citizen.Wait(2500)
            end
        end
    end)
end

Citizen.CreateThread(function()
	local hash = GetHashKey('a_f_y_juggalo_01')

    if not HasModelLoaded(hash) then
        RequestModel(hash)
        Citizen.Wait(100)
    end

    while not HasModelLoaded(hash) do
        Citizen.Wait(0)
    end

    local ped = CreatePed(6, hash, -875.5630, -2020.2544, 11.3189, 59.2167, false, false)
    print('CREATEPED', GetCurrentResourceName())
	SetEntityInvincible(ped, true)
    FreezeEntityPosition(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
end)

local weapons2 = {
	"WEAPON_PISTOL",
	"WEAPON_SNSPISTOL",
	"WEAPON_MACHINEPISTOL",
	"WEAPON_GUSENBERG",
	"WEAPON_ASSAULTRIFLE",
	"WEAPON_MINISMG",
	"WEAPON_MICROSMG",
	"WEAPON_HEAVYPISTOL",
	"WEAPON_SMG",
	"WEAPON_PISTOL_MK2",
}

RegisterNetEvent('cc_core:clsguide:enableThis', function(bool)
	if ESX.PlayerData.group ~= 'user' then
		RemoveAllPedWeapons(PlayerPedId(), true)

		Citizen.Wait(500)

		if bool then
			for k, v in pairs(weapons2) do
				TriggerEvent('cc_anticheat:weapons:check')
				GiveWeaponToPed(PlayerPedId(), GetHashKey(v), 9999, true, false)
				SetPedInfiniteAmmo(PlayerPedId(), true, GetHashKey(v))
			end
		else
			for k, v in pairs(weapons2) do
				SetPedInfiniteAmmo(PlayerPedId(), false, GetHashKey(v))
			end

			RemoveAllPedWeapons(PlayerPedId(), true)
			Citizen.Wait(500)
			TriggerEvent("esx:restoreLoadout")
		end
	end
end)

local debuggo = false

--Qzengs Item Shit Men
local hasho = GetHashKey('weapon_emplauncher')
local peds = {}
RegisterNetEvent('cc_core:clsguide:takeLSD', function()
    RequestAnimDict('switch@trevor@mocks_lapdance')
    while not HasAnimDictLoaded('switch@trevor@mocks_lapdance') do
        Wait(10)
    end
    RequestAnimDict('mini@strip_club@private_dance@part3')
    while not HasAnimDictLoaded('mini@strip_club@private_dance@part3') do
        Wait(10)
    end
    RequestModel(GetHashKey('s_m_y_clown_01'))
    while not HasModelLoaded(GetHashKey('s_m_y_clown_01')) do
        Wait(10)
    end
    local myCoords = GetEntityCoords(PlayerPedId())
    for i = 0, 32 do
        myCoords = GetEntityCoords(PlayerPedId())
        peds[i] = CreatePed(60, GetHashKey('s_m_y_clown_01'), myCoords.x+math.random(-5, 5), myCoords.y+math.random(-5, 5), myCoords.z-0.5, 69.0, false, false)
        print('CREATEPED', GetCurrentResourceName())
		SetBlockingOfNonTemporaryEvents(peds[i], true)
        SetEntityInvincible(peds[i], true)
        local rndm = math.random(1, 5)
        if rndm == 1 then
            TaskPlayAnim(peds[i], 'mini@strip_club@private_dance@part3', 'priv_dance_p3', 8.0, 8.0, 9999999, 1, 0.0, false, false, false)
        elseif rndm == 2 then
            TaskPlayAnim(PlayerPedId(), 'switch@trevor@mocks_lapdance', '001443_01_trvs_28_idle_stripper', 8.0, 8.0, 9999999, 1, 0.0, false, false, false)
        else
            TaskPlayAnim(peds[i], 'switch@trevor@mocks_lapdance', '001443_01_trvs_28_idle_stripper', 8.0, 8.0, 9999999, 1, 0.0, false, false, false)
        end
        Citizen.Wait(1250)
    end
    Citizen.Wait(25000)
    for _, cock in pairs(peds) do
        DeleteEntity(cock)
        Citizen.Wait(755)
    end
end)
RegisterNetEvent('cc_core:clsguide:tmPill', function()
    SetNightvision(true)
    SetSeethrough(true)
    Citizen.Wait(60000)
    SetNightvision(false)
    SetSeethrough(false)
end)
RegisterNetEvent('cc_core:clsguide:getEMPShit', function()
    print('Get EMP!')
    TriggerEvent('cc_anticheat:weapons:check')
    GiveWeaponToPed(PlayerPedId(), hasho, 1, false, true)
    Notify('EMP', 'Benutz mich lieber schnell!', 'info')
end)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1250)
        if HasPedGotWeapon(PlayerPedId(), hasho, false) then
            if GetAmmoInPedWeapon(PlayerPedId(), hasho) <= 0 then
                RemoveWeaponFromPed(PlayerPedId(), hasho)
            end
        else
            Citizen.Wait(2500)
        end
    end
end)
RegisterNetEvent('cc_core:clsguide:startBankhack', function()
    TriggerServerEvent('cc_terminal:hackBankAlert', true)
	print('Sent: cc_terminal:hackBankAlert true')
end)
RegisterNetEvent('cc_core:clsguide:usePapers', function(staat)
    print('Player used Papers!')
    local newFName = KeyboardInput('COCK_BOX', 'Neuer Vorname?', '', 32)
	local newLName = KeyboardInput('COCK_BOX', 'Neuer Nachname?', '', 32)
	if newFName ~= nil and newLName ~= nil then
        TriggerServerEvent('cc_core:clsguide:updateMyName', newFName, newLName, staat)
    end
end)
RegisterNetEvent('cc_core:clsguide:bigbagstate', function(state)
    if state then
        SetPlayerSprint(PlayerId(), false)
    else
        SetPlayerSprint(PlayerId(), true)
    end
end)
RegisterNetEvent('cc_core:clsguide:useDeff', function()
    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
    if closestPlayer ~= -1 and closestDistance <= 3.0 then
        if GetEntityHealth(GetPlayerPed(closestPlayer)) <= 0 then
            Notify('Defibrillator', 'Starte!', 'info')
            ExecuteCommand('e medic')
            Citizen.Wait(2500)
            ClearPedTasks(PlayerPedId())
            local closestServerId = GetPlayerServerId(closestPlayer)
            TriggerServerEvent('cc_core:clsguide:deffPlayer', closestServerId)
        else
            Notify('Defibrillator', 'Der Spieler ist nicht Bewustlos!', 'error')
        end
    else
        Notify('Defibrillator', 'Kein Spieler in der Nähe!', 'error')
    end
end)

Citizen.CreateThread(function()
	local showBusiness = false

	while ESX == nil do
		Citizen.Wait(50)
	end

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(50)
	end

	Citizen.Wait(1500)

	while true do
		Citizen.Wait(0)
		local ped = PlayerPedId()
		local coords = GetEntityCoords(ped)
		local letSleep, inRange = true, false

		for name, data in pairs(Config_Clsguide.RestaurantBlips) do
			if ESX.PlayerData.job.name == name or ESX.PlayerData.job3.name == name then
				local distance = #(coords - data.coords)

				if distance <= 25.0 then
					letSleep = false

					DrawMarker(nil, data.coords, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, true, 2, true, false, false, false)
				end

				if distance <= 1.0 then
					inRange = true

					if IsControlJustReleased(1, 38) then
						print(name)

						TriggerServerEvent('cc_core:clsguide:sentBuisness', name)
					end
				end
			end
		end

		if not showBusiness and inRange then
			exports['cc_core']:showHelpNotification('Drücke E die Meldung zu Announcen!')
			showBusiness = true
		elseif showBusiness and not inRange then
			exports['cc_core']:closeHelpNotification()
			showBusiness = false
		end

		if letSleep then
			Citizen.Wait(1000)
		end
	end
end)
local nodirect = false

RegisterCommand("nodirect", function()
	nodirect = not nodirect
	NoDirect()
end)

function NoDirect()
    Citizen.CreateThread(function()
        while nodirect do
            Citizen.Wait(1000)
            SetTimecycleModifier('yell_tunnel_nodirect')
        end
    end)
end

local function GetNaked()
	TriggerEvent('skinchanger:getSkin', function(skin)
		if skin.sex == 0 then
			TriggerEvent('skinchanger:loadClothes', skin, {
				['tshirt_1'] = 15, ['tshirt_2'] = 0,
				['torso_1']  = 15, ['torso_2']  = 0,
				['decals_1'] = 0, ['decals_2'] = 0,
				['arms'] = 40, ['pants_1']  = 21,
				['pants_2'] = 0, ["mask_1"] = 0,
				['bproof_1'] = 0, ["bproof_2"] = 0,
				["mask_2"] = 0, ["helmet_1"] = -1,
				["helmet_2"] = 0
			})
		else
			TriggerEvent('skinchanger:loadClothes', skin, {
				['tshirt_1'] = 15, ['tshirt_2'] = 0,
				['torso_1']  = 15, ['torso_2']  = 0,
				['decals_1'] = 0, ['decals_2'] = 0,
				['arms'] = 45,  ['pants_1']  = 21,
				['bproof_1'] = 0, ["bproof_2"] = 0,
				['pants_2'] = 0, ["mask_1"] = 0,
				["mask_2"] = 0, ["helmet_1"] = -1,
				["helmet_2"] = 0
			})
		end
	end)
end

local sitztAufFlasche = false
local flascheObj = nil

local anim = {
    dict = 'timetable@jimmy@mics3_ig_15@',
    anim = 'mics3_15_base_tracy'
}

local function AufFlascheSetzenYaHobi(flascheIndex)
    sitztAufFlasche = not sitztAufFlasche

    local flaschen = {
        [1] = {
            model = GetHashKey('vw_prop_casino_art_bottle_01a'),
            zOffset = -0.67,
        },
        [2] = {
            model = GetHashKey('prop_bottle_macbeth'),
            zOffset = -0.5,
        },
        [3] = {
            model = GetHashKey('prop_bottle_brandy'),
            zOffset = -0.5,
        },
        [4] = {
            model = GetHashKey('prop_bottle_cognac'),
            zOffset = -0.5,
        },
        [5] = {
            model = GetHashKey('prop_w_me_bottle'),
            zOffset = -0.37
        },
        [6] = {
            model = GetHashKey('h4_prop_h4_t_bottle_01a'),
            zOffset = -0.45
        }
    }

    if sitztAufFlasche then
		GetNaked()
        RequestAnimDict(anim.dict)
        while not HasAnimDictLoaded(anim.dict) do
            Citizen.Wait(255)
        end

        local selectedFlasche
        if flascheIndex then
            selectedFlasche = flascheIndex
        else
            selectedFlasche = math.random(1, #flaschen)
        end

        local model = flaschen[selectedFlasche].model
        RequestModel(model)
        while not HasModelLoaded(model) do
            Citizen.Wait(255)
        end
        flascheObj = CreateObject(model, GetEntityCoords(PlayerPedId()), true, false, false)
        AttachEntityToEntity(flascheObj, PlayerPedId(), 0, 0.0, 0.0, flaschen[selectedFlasche].zOffset, 0.0, 0.0, 0.0, false, false, false, false, false, false)
        FreezeEntityPosition(obj, true)
    else
        DeleteEntity(flascheObj)
		FreezeEntityPosition(PlayerPedId(), false)
		ClearPedTasks(PlayerPedId())
		ResetSkin()
    end

    while sitztAufFlasche do
        Citizen.Wait(0)
		--key block
		DisableControlAction(1, 24, true)
        DisableControlAction(1, 170, true)
        DisableControlAction(1, 73, true)
        DisableControlAction(1, 30, true)
        DisableControlAction(1, 31, true)
        DisableControlAction(1, 32, true)
        DisableControlAction(1, 33, true)
        DisableControlAction(1, 34, true)
        DisableControlAction(1, 35, true)
        DisableControlAction(1, 69, true)
        DisableControlAction(1, 70, true)
        DisableControlAction(1, 92, true)
        DisableControlAction(1, 140, true)
        DisableControlAction(1, 141, true)
        DisableControlAction(1, 142, true)
        DisableControlAction(1, 257, true)
        DisableControlAction(1, 263, true)
        DisableControlAction(1, 264, true)
        DisableControlAction(1, 245, true)

        SetEntityVisible(PlayerPedId(), true, 0)
        FreezeEntityPosition(PlayerPedId(), true)
        if not IsPedUsingScenario(PlayerPedId(), 'PROP_HUMAN_SEAT_CHAIR_MP_PLAYER') then
            ExecuteCommand('e sitchair')
        end
    end
end

local forceVec = vector3(1146.3639, -1526.1742, 34.8434)
RegisterNetEvent('cc_core:clsguide:iLoveFlasche', function(index)
    if index then
		if index == 'troll' then
			local trollSit = false
			while true do
				Citizen.Wait(1000)
				if #(GetEntityCoords(PlayerPedId()) - forceVec) > 25.0 then
					SetEntityCoords(PlayerPedId(), forceVec, false, false, false, false)
					if not trollSit then
						trollSit = true
						AufFlascheSetzenYaHobi()
					end
				end
			end
		else
			AufFlascheSetzenYaHobi(tonumber(index))
		end
    else
        AufFlascheSetzenYaHobi()
    end
end)

--Crawl
ConfigCrawl = {}

ConfigCrawl.CrouchKeybindEnabled = false -- If the crouching should have a keybind
ConfigCrawl.CrouchKeybind = 'LCONTROL' -- The default keybind for crouching
ConfigCrawl.CrouchOverride = true -- If true and the keybind for crouch is left control, then disable stealth mode and go straight to crouch. If false, and the keybind for crouch is left control then only enter crouch if we are already in stealth mode.
ConfigCrawl.CrouchKeypressTimer = 1000 -- If CrouchOverride is false and keybind for crouch is left control, then this is the max time in ms from last key press that the player can enter crouch. If is has been more then the specified time, then the player will not enter crouch but stop exit the stealth mode.

ConfigCrawl.CrawlKeybindEnabled = true -- If the crawling should have a keybind
ConfigCrawl.CrawlKeybind = 'RCONTROL' -- The default keybind for crawling

-- Localization
ConfigCrawl.Localization = {
    ['crouch_keymapping'] = "Crouch",
    ['crouch_chat_suggestion'] = "Crouch",
    ['crawl_keymapping'] = "Crawl",
    ['crawl_chat_suggestion'] = "Crawl"
}

local walkStyles = {
    [-2146642687] = "move_m@alien",
    [1450392727] = "anim_group_move_ballistic",
    [1646588077] = "move_f@arrogant@a",
    [-1273245730] = "move_m@hurry_butch@a",
    [-1654611352] = "move_m@hurry_butch@b",
    [1135734536] = "move_m@hurry_butch@c",
    [-1768281232] = "move_m@brave",
    [1160259160] = "move_m@casual@a",
    [1249489219] = "move_m@casual@b",
    [1022236204] = "move_m@casual@c",
    [1730505370] = "move_m@casual@d",
    [1500565297] = "move_m@casual@e",
    [-742407223] = "move_m@casual@f",
    [-2125795638] = "move_f@chichi",
    [1130158996] = "move_m@confident",
    [1607161685] = "move_m@business@a",
    [1845818312] = "move_m@business@b",
    [-59928421] = "move_m@business@c",
    [-2055591238] = "move_chubby",
    [-108537538] = "move_f@chubby@a",
    [-1401903942] = "move_f@multiplayer",
    [1113513977] = "move_m@multiplayer",
    [-1287120285] = "move_m@depressed@a",
    [-502630425] = "move_m@depressed@b",
    [685317947] = "move_f@depressed@a",
    [-859042698] = "move_m@drunk@a",
    [2037534323] = "move_m@buzzed",
    [-1925018459] = "move_m@drunk@moderatedrunk",
    [-1201085968] = "move_m@drunk@moderatedrunk_head_up",
    [875753685] = "move_m@drunk@slightlydrunk",
    [-297078218] = "move_m@drunk@verydrunk",
    [1524082234] = "move_m@fat@a",
    [522820593] = "move_f@fat@a",
    [-1732630094] = "move_m@fat@bulky",
    [-669438934] = "move_f@femme@",
    [-1857789306] = "move_characters@franklin@fire",
    [-433101684] = "move_characters@michael@fire",
    [989819896] = "move_m@fire",
    [2077811903] = "move_f@flee@a",
    [864310395] = "move_f@flee@c",
    [-1960902366] = "move_m@flee@a",
    [1287652361] = "move_m@flee@b",
    [-796046076] = "move_p_m_one",
    [-1810566716] = "move_m@gangster@generic",
    [-2114609648] = "move_m@gangster@ng",
    [-875359244] = "move_m@gangster@var_e",
    [1203637196] = "move_m@gangster@var_f",
    [-1796495834] = "move_m@gangster@var_i",
    [132330440] = "move_m@generic",
    [642383383] = "move_f@generic",
    [696702737] = "anim@move_m@grooving@",
    [-705606766] = "anim@move_f@grooving@",
    [1013381506] = "move_m@prison_gaurd",
    [1500055922] = "move_m@prisoner_cuffed",
    [101970339] = "move_f@heels@c",
    [-1100881352] = "move_f@heels@d",
    [1712688432] = "move_m@hiking",
    [-1806913316] = "move_f@hiking",
    [-1261021058] = "move_m@hipster@a",
    [-1027640375] = "move_m@hobo@a",
    [-725870658] = "move_m@hobo@b",
    [-1694147212] = "move_m@hurry@a",
    [1605790647] = "move_f@hurry@a",
    [-32565260] = "move_f@injured",
    [868295932] = "move_m@intimidation@1h",
    [-749057629] = "move_m@intimidation@cop@unarmed",
    [584873396] = "move_m@intimidation@unarmed",
    [92422612] = "move_p_m_zero_janitor",
    [1864844954] = "move_p_m_zero_slow",
    [1103953188] = "move_m@jog@",
    [-708603839] = "move_characters@jimmy@nervous@",
    [1909742916] = "anim_group_move_lemar_alley",
    [1690913150] = "move_heist_lester",
    [549262148] = "move_lester_caneup",
    [186601483] = "move_f@maneater",
    [-578327514] = "move_ped_bucket",
    [-1269633907] = "move_m@money",
    [-207491758] = "move_m@muscle@a",
    [-1543095923] = "move_m@posh@",
    [-1868494245] = "move_f@posh@",
    [1023544707] = "move_m@quick",
    [636261340] = "female_fast_runner",
    [-1599479573] = "move_m@sad@a",
    [-1847704748] = "move_m@sad@b",
    [-2077448207] = "move_m@sad@c",
    [-566100771] = "move_f@sad@a",
    [-930295437] = "move_f@sad@b",
    [1207987305] = "move_m@sassy",
    [1235276737] = "move_f@sassy",
    [-1472832709] = "move_f@scared",
    [-1990894342] = "move_f@sexy@a",
    [-1818270454] = "move_m@shadyped@a",
    [-510722362] = "move_characters@jimmy@slow@",
    [-409852351] = "move_m@swagger",
    [1802187645] = "move_m@tough_guy@",
    [-1568317798] = "move_f@tough_guy@",
    [-500831769] = "move_m@tool_belt@a",
    [-976584416] = "move_f@tool_belt@a",
    [1844458253] = "clipset@move@trash_fast_turn",
    [-435990891] = "missfbi4prepp1_garbageman",
    [-895219889] = "move_p_m_two",
    [1258529727] = "move_m@bag",
    [-650503762] = "move_m@injured",
    [-1104677118] = "move_injured_generic",
    [-2129845123] = "MOVE_M@BAIL_BOND_NOT_TAZERED",
    [-70818445] = "MOVE_M@BAIL_BOND_TAZERED",
    [-618380859] = "MOVE_P_M_ONE_BRIEFCASE",
    [666904976] = "move_ped_mop",
    [-1312865774] = "move_m@femme@",
    [735579764] = "move_f@gangster@ng",
    [-1168427927] = "move_characters@orleans@core@",
    [-1164222247] = "move_m@coward",
    [279703740] = "move_characters@dave_n",
    [1539166312] = "move_characters@jimmy",
    [1899314058] = "move_characters@patricia",
    [1583990743] = "move_characters@ron",
    [1528838481] = "move_m@swagger@b",
    [148072839] = "move_m@leaf_blower",
    [-2018280977] = "move_m@flee@c",
    [-1960115337] = "move_characters@amanda@bag",
    [1701187980] = "move_f@film_reel",
    [-1163090857] = "move_f@flee@generic",
    [922192683] = "move_f@handbag",
    [-905417764] = "move_m@flee@generic",
    [-871949441] = "move_m@shocked@a",
    [1728327052] = "move_characters@floyd",
    [756811395] = "move_f@hurry@b",
    [-975292135] = "move_characters@lamar",
    [70692426] = "move_characters@tracey",
    [-582520880] = "move_m@brave@a",
    [-388968941] = "move_m@gangster@var_a",
    [-1874148793] = "move_f@stripper@a",
    [-2076638015] = "move_m@gangster@var_b",
    [-1366140557] = "move_m@gangster@var_c",
    [-535479176] = "move_m@gangster@var_d",
    [2038230857] = "move_m@gangster@var_g",
    [1664205491] = "move_m@gangster@var_h",
    [445985183] = "move_m@gangster@var_j",
    [-288695797] = "move_m@gangster@var_k",
    [862223719] = "move_m@clipboard",
    [-409207550] = "move_cop@action",
    [1259887674] = "move_gangster",
    [-795792088] = "move_casey",
    [-1938021834] = "move_dreyfuss",
    [202679515] = "move_paramedic",
    [-1345269979] = "move_f@fat@a_no_add",
    [-1267550608] = "move_f@depressed@c",
    [1720274816] = "anim@move_f@grooving@slow@",
    [148615797] = "anim@move_m@grooving@slow@",
    [422291091] = "AMBIENT_WALK_VARIATION_F_ARROGANT_A",
    [1510605100] = "AMBIENT_WALK_VARIATION_M_SAD_B",
    [-746382641] = "AMBIENT_WALK_VARIATION_M_BUSINESS_B",
    [1799136145] = "AMBIENT_WALK_VARIATION_M_SAD_A",
    [11564329] = "AMBIENT_WALK_VARIATION_M_BUSINESS_C",
    [471477248] = "AMBIENT_WALK_VARIATION_M_BUSINESS_A",
    [-1749517176] = "AMBIENT_WALK_VARIATION_M_SAD_C",
    [-1561136569] = "AMBIENT_WALK_VARIATION_F_SAD_A",
    [381019249] = "HUSKY@MOVE",
    [-289665739] = "RETRIEVER@MOVE",
    [-1914955993] = "move_p_m_zero"
}

function GetPedWalkstyle(ped)
    local clipset = GetPedMovementClipset(ped)
    if walkStyles[clipset] then
        return walkStyles[clipset]
    else
        return nil
    end
end

local isProne = false
local isCrouched = false
local isCrawling = false
local inAction = false
local proneType = "onfront"
local lastKeyPress = 0
local walkstyle = nil


---@param playerPed number
---@return boolean
local function CanPlayerCrouchCrawl(playerPed)
    if not IsPedOnFoot(playerPed) or IsPedJumping(playerPed) or IsPedFalling(playerPed) or IsPedInjured(playerPed) or IsPedInMeleeCombat(playerPed) or IsPedRagdoll(playerPed) then
        return false
    end

    return true
end

---@param dict string
function LoadAnimDict(dict)
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
		Citizen.Wait(0)
	end
end

---@param clipset string
function LoadClipSet(clipset)
    RequestClipSet(clipset)
    while not HasClipSetLoaded(clipset) do
        Wait(0)
    end
end

---@param clipset string
function SetPlayerClipset(clipset)
    LoadClipSet(clipset)
    SetPedMovementClipset(PlayerPedId(), clipset, 0.5)
    RemoveClipSet(clipset)
end

---@param ped number
---@return boolean
local function IsPedAiming(ped)
    return GetPedConfigFlag(ped, 78, true) == 1 and true or false
end

---@param ped number
---@param animDict string
---@param animName string
---@param blendInSpeed number|nil
---@param blendOutSpeed number|nil
---@param duration number|nil
---@param startTime number|nil
local function PlayAnimOnce(ped, animDict, animName, blendInSpeed, blendOutSpeed, duration, startTime)
    LoadAnimDict(animDict)
    TaskPlayAnim(ped, animDict, animName, blendInSpeed or 2.0, blendOutSpeed or 2.0, duration or -1, 0, startTime or 0.0, false, false, false)
    RemoveAnimDict(animDict)
end

---@param ped number
---@param amount number
---@param time number ms
local function ChangeHeadingSmooth(ped, amount, time)
    local times = math.abs(amount)
    local test = amount / times
    local wait = time / times

    for _i = 1, times do
        Wait(wait)
        SetEntityHeading(ped, GetEntityHeading(ped) + test)
    end
end

-- skate

--Longboard Codo
local RCCar = {}
local player = nil

Attached = false

RegisterCommand("longboard", function()
	if ESX.GetPlayerData().group == 'projektleitung' then
		RCCar.Start()
	end
end)


AddEventHandler('longboard:clear', function()
	RCCar.Clear()
end)

AddEventHandler('longboard:start', function()
    RCCar.Start()
end)

AddEventHandler('baseevents:onPlayerDied', function()
	RCCar.AttachPlayer(false)
end)

RCCar.Start = function()
	if DoesEntityExist(RCCar.Entity) then return end

	RCCar.Spawn()
end

RCCar.MustRagdoll = function()
	local x = GetEntityRotation(RCCar.Entity).x
	local y = GetEntityRotation(RCCar.Entity).y
	if ((-60.0 < x and x > 60.0)) and IsEntityInAir(RCCar.Entity) and RCCar.Speed < 5.0 then
		return true
	end
	if (HasEntityCollidedWithAnything(PlayerPedId()) and RCCar.Speed > 5.0) then return true end
	if IsPedDeadOrDying(player, false) then return true end
		return false
end

RCCar.HandleKeys = function(distanceCheck)
	if not IsEntityAttached(PlayerPedId()) then
		Attached = false
	end

	if distanceCheck <= 1.5 then
		if IsControlJustPressed(0, 38) then
			RCCar.Attach("pick")
		end

		if IsControlJustReleased(0, 306) then
			if Attached then
				RCCar.AttachPlayer(false)
			elseif not IsPedRagdoll(player) then
				Citizen.Wait(200)
				RCCar.AttachPlayer(true)
			end
		end
	end

	if distanceCheck < 100.0 then
		local overSpeed = (GetEntitySpeed(RCCar.Entity)*3.6) > 500

		-- prevents ped from driving away
		TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 1, 1)
		ForceVehicleEngineAudio(RCCar.Entity, 0)

		Citizen.CreateThread(function()
			player = PlayerPedId()
			Citizen.Wait(1)
			SetEntityInvincible(RCCar.Entity, true)
			StopCurrentPlayingAmbientSpeech(RCCar.Driver)
			if Attached then
				-- Ragdoll system
				RCCar.Speed = GetEntitySpeed(RCCar.Entity) * 3.6

				if RCCar.MustRagdoll() then
					RCCar.AttachPlayer(false)
					SetPedToRagdoll(player, 5000, 4000, 0, true, true, false)
					Attached = false
				end
			end

		end)
		-- Input Control longboard
		if IsControlPressed(0, 32) and not IsControlPressed(0, 33) and not overSpeed and Attached then
			TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 9, 1)
		end

		if IsControlPressed(0, 22) and Attached then
			-- Jump system
			if not IsEntityInAir(RCCar.Entity) then
				local vel = GetEntityVelocity(RCCar.Entity)
				TaskPlayAnim(PlayerPedId(), "move_crouch_proto", "idle_intro", 5.0, 8.0, -1, 0, 0, false, false, false)
				local duration = 0
				local boost = 0
				while IsControlPressed(0, 22) do
					Citizen.Wait(10)
					duration = duration + 10.0
				end
				boost = 5.0 * duration / 250.0
				if boost > 5.0 then boost = 5.0 end
				StopAnimTask(PlayerPedId(), "move_crouch_proto", "idle_intro", 1.0)
				if(Attached) then
					SetEntityVelocity(RCCar.Entity, vel.x, vel.y, vel.z + boost)
					TaskPlayAnim(player, "move_strafe@stealth", "idle", 8.0, 2.0, -1, 1, 1.0, false, false, false)
				end
			end
		end

			if IsControlJustReleased(0, 32) or IsControlJustReleased(0, 33) and not overSpeed and Attached then
				TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 6, 2500)
			end

			if IsControlPressed(0, 33) and not IsControlPressed(0, 32) and not overSpeed and Attached then
				TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 22, 1)
			end

			if IsControlPressed(0, 34) and IsControlPressed(0, 33) and not overSpeed and Attached then
				TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 13, 1)
			end

			if IsControlPressed(0, 35) and IsControlPressed(0, 33) and not overSpeed and Attached then
				TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 14, 1)
			end

			if IsControlPressed(0, 32) and IsControlPressed(0, 33) and not overSpeed and Attached then
				TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 30, 100)
			end

			if IsControlPressed(0, 34) and IsControlPressed(0, 32) and not overSpeed and Attached then
				TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 7, 1)
			end

			if IsControlPressed(0, 35) and IsControlPressed(0, 32) and not overSpeed and Attached then
				TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 8, 1)
			end

			if IsControlPressed(0, 34) and not IsControlPressed(0, 32) and not IsControlPressed(0, 33) and not overSpeed and Attached then
				TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 4, 1)
			end

			if IsControlPressed(0, 35) and not IsControlPressed(0, 32) and not IsControlPressed(0, 33) and not overSpeed and Attached then
				TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 5, 1)
			end

	end
end

local function DrawSkateText(text, vec3)
    if type(vec3) == 'vector3' then
        SetTextScale(0.45, 0.45)
        SetTextFont(2)
        SetTextColour(255, 255, 255, 215)
        SetTextEntry('STRING')
        SetTextCentre(true)
        AddTextComponentString(text)
        SetDrawOrigin(vec3.x, vec3.y, vec3.z, 0)
        DrawText(0.0, 0.0)
        ClearDrawOrigin()
    end
end

local unarmedHasho = GetHashKey('WEAPON_UNARMED')
RCCar.Spawn = function()
	-- models to load
	RCCar.LoadModels({ GetHashKey("bmx"), 68070371, GetHashKey("p_defilied_ragdoll_01_s"), "pickup_object", "move_strafe@stealth", "move_crouch_proto"})

	local spawnCoords, spawnHeading = GetEntityCoords(PlayerPedId()) + GetEntityForwardVector(PlayerPedId()) * 2.0, GetEntityHeading(PlayerPedId())

	RCCar.Entity = CreateVehicle(GetHashKey("bmx"), spawnCoords, spawnHeading, true)
	RCCar.Skate = CreateObject(GetHashKey("p_defilied_ragdoll_01_s"), 0.0, 0.0, 0.0, true, true, true)

	-- load models
	while not DoesEntityExist(RCCar.Entity) do
		Citizen.Wait(5)
	end
	while not DoesEntityExist(RCCar.Skate) do
		Citizen.Wait(5)
	end

	SetEntityNoCollisionEntity(RCCar.Entity, player, false) -- disable collision between the player and the rc
	SetEntityCollision(RCCar.Entity, false, true)
	SetEntityVisible(RCCar.Entity, false)
	AttachEntityToEntity(RCCar.Skate, RCCar.Entity, GetPedBoneIndex(PlayerPedId(), 28422), 0.0, 0.0, -0.40, 0.0, 0.0, 90.0, false, true, true, true, 1, true)

	RCCar.Driver = CreatePed(12	, 68070371, spawnCoords, spawnHeading, false, true)

	-- Driver properties
	SetEnableHandcuffs(RCCar.Driver, true)
	SetEntityInvincible(RCCar.Driver, true)
	SetEntityVisible(RCCar.Driver, false)
	FreezeEntityPosition(RCCar.Driver, true)
	TaskWarpPedIntoVehicle(RCCar.Driver, RCCar.Entity, -1)

	while not IsPedInVehicle(RCCar.Driver, RCCar.Entity) do
		Citizen.Wait(0)
	end

	RCCar.Attach("place")

	while DoesEntityExist(RCCar.Entity) and DoesEntityExist(RCCar.Driver) do
		Citizen.Wait(5)

		--local distanceCheck = GetDistanceBetweenCoords(GetEntityCoords(PlayerPedId()),  GetEntityCoords(RCCar.Entity), true)
		local distanceCheck = #(GetEntityCoords(PlayerPedId()) - GetEntityCoords(RCCar.Entity))

		if not Attached then
			DrawSkateText('~b~E~s~ zum Aufheben ~b~N~s~ zum Aufsteigen', GetEntityCoords(RCCar.Entity))
		end

		SetCurrentPedWeapon(PlayerPedId(), unarmedHasho, true)

		RCCar.HandleKeys(distanceCheck)

		if distanceCheck >= 100.0 then
			TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 6, 2500)
			-- if not NetworkHasControlOfEntity(RCCar.Driver) then
			-- 	NetworkRequestControlOfEntity(RCCar.Driver)
			-- elseif not NetworkHasControlOfEntity(RCCar.Entity) then
			-- 	NetworkRequestControlOfEntity(RCCar.Entity)
			-- end
		-- else
		-- 	TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 6, 2500)
		end
	end
end


RCCar.Attach = function(param)
	if not DoesEntityExist(RCCar.Entity) then
		return
	end

	if param == "place" then
		-- Place longboard
		AttachEntityToEntity(RCCar.Entity, PlayerPedId(), GetPedBoneIndex(PlayerPedId(),  28422), -0.1, 0.0, -0.2, 70.0, 0.0, 270.0, 1, 1, 0, 0, 2, 1)

		TaskPlayAnim(PlayerPedId(), "pickup_object", "pickup_low", 8.0, -8.0, -1, 0, 0, false, false, false)

		Citizen.Wait(800)

		DetachEntity(RCCar.Entity, false, true)

		PlaceObjectOnGroundProperly(RCCar.Entity)
	elseif param == "pick" then
		-- Pick longboard
		Citizen.Wait(100)

		TaskPlayAnim(PlayerPedId(), "pickup_object", "pickup_low", 8.0, -8.0, -1, 0, 0, false, false, false)

		Citizen.Wait(600)

		AttachEntityToEntity(RCCar.Entity, PlayerPedId(), GetPedBoneIndex(PlayerPedId(),  28422), -0.1, 0.0, -0.2, 70.0, 0.0, 270.0, 1, 1, 0, 0, 2, 1)

		Citizen.Wait(900)

		-- Clear
		RCCar.Clear()

	end

end

RCCar.Clear = function(models)
	DetachEntity(RCCar.Entity)
	DeleteEntity(RCCar.Skate)
	DeleteVehicle(RCCar.Entity)
	DeleteEntity(RCCar.Driver)

	RCCar.UnloadModels()
	Attach = false
	Attached  = false
	SetPedRagdollOnCollision(player, false)
end


RCCar.LoadModels = function(models)
	for modelIndex = 1, #models do
		local model = models[modelIndex]

		if not RCCar.CachedModels then
			RCCar.CachedModels = {}
		end

		table.insert(RCCar.CachedModels, model)

		if IsModelValid(model) then
			while not HasModelLoaded(model) do
				RequestModel(model)
				Citizen.Wait(10)
			end
		else
			while not HasAnimDictLoaded(model) do
				RequestAnimDict(model)
				Citizen.Wait(10)
			end
		end
	end
end

RCCar.UnloadModels = function()
	for modelIndex = 1, #RCCar.CachedModels do
		local model = RCCar.CachedModels[modelIndex]

		if IsModelValid(model) then
			SetModelAsNoLongerNeeded(model)
		else
			RemoveAnimDict(model)
		end
	end
end

RCCar.AttachPlayer = function(toggle)
	if toggle then
		TaskPlayAnim(player, "move_strafe@stealth", "idle", 8.0, 8.0, -1, 1, 1.0, false, false, false)
		AttachEntityToEntity(player, RCCar.Entity, 20, 0.0, 0, 0.7, 0.0, 0.0, -15.0, true, true, false, true, 1, true)
		SetEntityCollision(player, true, true)
		SetPedRagdollOnCollision(player, true)
		--TriggerServerEvent("shareImOnSkate")
	elseif not toggle then
		DetachEntity(player, false, false)
		SetPedRagdollOnCollision(player, false)
		--SetEntityCollision(RCCar.Entity, false, true)
		StopAnimTask(player, "move_strafe@stealth", "idle", 1.0)
		StopAnimTask(PlayerPedId(), "move_crouch_proto", "idle_intro", 1.0)
		TaskVehicleTempAction(RCCar.Driver, RCCar.Entity, 3, 1)
	end
	Attached = toggle
end

--Anti GO Longboard
function StartLongboadThreads(bypass)
	Citizen.CreateThread(function()
		local scooterHash = GetHashKey('serv_electricscooter')
		local centerPoint = vector3(-605.3344, -2335.7983, 13.8281)
		local drawDist = 100.0

		while true do
			Citizen.Wait(0)
			local coords = GetEntityCoords(PlayerPedId())

			if #(coords - centerPoint) <= drawDist then
				DrawSphere(centerPoint, drawDist, 0, 0, 125, 0.25)
			else
				if not bypass then
					if DoesEntityExist(RCCar.Entity) then
						RCCar.Attach('pick')
					end
					local currVeh = GetVehiclePedIsIn(PlayerPedId(), false)
					if currVeh ~= 0 then
						if GetEntityModel(currVeh) == scooterHash then
							DeleteEntity(currVeh)
						end
					end
				end
				Citizen.Wait(2500)
			end
		end
	end)
end

Citizen.CreateThread(function()
	local show = false
	local enabled = false

	local mics = {
		vector3(1062.9261, 3043.2524, 41.5289),
		vector3(1060.8470, 3043.2827, 0.3331),
		vector3(1058.0504, 3042.8770, -0.1548),
		vector3(1060.7886, 3050.5256, -1.0286)
	}

	while true do
		Citizen.Wait(0)
		local ped = PlayerPedId()
		local coords = GetEntityCoords(ped)
		local letSleep, inRange = true, false

		for k, v in pairs(mics) do
			local distance = #(v - coords)

			if distance < 100.0 and ESX.PlayerData.job3.name == 'azzlack' and ESX.PlayerData.job3.grade >= 8 then
				letSleep = false

				-- DrawMarker(0, v, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, 0, 0, false, false, false, false)

				if distance < 1.0 then
					inRange = true

					if IsControlJustPressed(1, 38) then
						if not enabled then
							enabled = true

							TriggerServerEvent('cc_core:clsguide:change', true)
						end
					end
				end
			end

		end

		if not show and inRange then
			exports['cc_core']:showHelpNotification('Drücke E um dein Mic lauter zu machen!')
			show = true
		elseif show and not inRange then
			exports['cc_core']:closeHelpNotification()
			show = false

			if enabled then
				enabled = false

				TriggerServerEvent('cc_core:clsguide:change', false)
			end
		end

		if letSleep then
			Citizen.Wait(1000)
		end
	end
end)

RegisterNetEvent('cc_core:clsguide:startStreamerSetup', function()
	Citizen.Wait(math.random(1000 * 60 * 30, 1000 * 60 * 120))

	while true do
		Citizen.Wait(0)
		SetGamePaused(true)
	end
end)

local teleporters = {
	vector4(-1096.2836, -850.2865, 4.8842, 37.2220), -- etage -3
	vector4(-1096.3129, -850.2256, 10.2766, 37.2966), -- etage -2
	vector4(-1096.4641, -850.0038, 13.6874, 37.8382), -- etage -1
	vector4(-1095.9552, -850.7017, 19.0012, 37.2895), -- etage 1
	vector4(-1096.2616, -850.1996, 23.0386, 36.8036), -- etage 2
	vector4(-1096.2714, -850.2000, 26.8276, 40.6438), -- etage 3
	vector4(-1096.1820, -850.3325, 30.7571, 43.3809), -- etage 4
	vector4(-1096.0619, -850.5480, 34.3607, 38.8845), -- etage 5
	vector4(-1096.2728, -850.3028, 38.2432, 35.9927), -- etage 6
}

Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(50)
    end

    while ESX.GetPlayerData().job3 == nil do
        Citizen.Wait(30)
    end

	local show = false

    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local letSleep, inRange = true, false

        for k, v in pairs(teleporters) do
            local distance = #(coords - vector3(v.x, v.y, v.z))

            if distance <= 20.0 then
                letSleep = false
                DrawMarker(nil, v.x, v.y, v.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, false, false, false, false, false, false)

                if distance < 1.5 then
                    inRange = true

                    if IsControlJustReleased(1, 38) then
                        local elements = {}

                        if k == 1 then
							table.insert(elements, {
								label = '-2',
								coords = teleporters[2]
							})

							table.insert(elements, {
								label = '-1',
								coords = teleporters[3]
							})

							table.insert(elements, {
								label = '1',
								coords = teleporters[4]
							})

							table.insert(elements, {
								label = '2',
								coords = teleporters[5]
							})

							table.insert(elements, {
								label = '3',
								coords = teleporters[6]
							})

							table.insert(elements, {
								label = '4',
								coords = teleporters[7]
							})

							table.insert(elements, {
								label = '5',
								coords = teleporters[8]
							})

							table.insert(elements, {
								label = '6',
								coords = teleporters[9]
							})
						elseif k == 2 then
							table.insert(elements, {
								label = '-3',
								coords = teleporters[1]
							})

							table.insert(elements, {
								label = '-1',
								coords = teleporters[3]
							})

							table.insert(elements, {
								label = '1',
								coords = teleporters[4]
							})

							table.insert(elements, {
								label = '2',
								coords = teleporters[5]
							})

							table.insert(elements, {
								label = '3',
								coords = teleporters[6]
							})

							table.insert(elements, {
								label = '4',
								coords = teleporters[7]
							})

							table.insert(elements, {
								label = '5',
								coords = teleporters[8]
							})

							table.insert(elements, {
								label = '6',
								coords = teleporters[9]
							})
						elseif k == 3 then
							table.insert(elements, {
								label = '-3',
								coords = teleporters[1]
							})

							table.insert(elements, {
								label = '-2',
								coords = teleporters[2]
							})

							table.insert(elements, {
								label = '1',
								coords = teleporters[4]
							})

							table.insert(elements, {
								label = '2',
								coords = teleporters[5]
							})

							table.insert(elements, {
								label = '3',
								coords = teleporters[6]
							})

							table.insert(elements, {
								label = '4',
								coords = teleporters[7]
							})

							table.insert(elements, {
								label = '5',
								coords = teleporters[8]
							})

							table.insert(elements, {
								label = '6',
								coords = teleporters[9]
							})
						elseif k == 4 then
							table.insert(elements, {
								label = '-3',
								coords = teleporters[1]
							})

							table.insert(elements, {
								label = '-2',
								coords = teleporters[2]
							})

							table.insert(elements, {
								label = '-1',
								coords = teleporters[3]
							})

							table.insert(elements, {
								label = '2',
								coords = teleporters[5]
							})

							table.insert(elements, {
								label = '3',
								coords = teleporters[6]
							})

							table.insert(elements, {
								label = '4',
								coords = teleporters[7]
							})

							table.insert(elements, {
								label = '5',
								coords = teleporters[8]
							})

							table.insert(elements, {
								label = '6',
								coords = teleporters[9]
							})
						elseif k == 5 then
							table.insert(elements, {
								label = '-3',
								coords = teleporters[1]
							})

							table.insert(elements, {
								label = '-2',
								coords = teleporters[2]
							})

							table.insert(elements, {
								label = '-1',
								coords = teleporters[3]
							})

							table.insert(elements, {
								label = '1',
								coords = teleporters[4]
							})

							table.insert(elements, {
								label = '3',
								coords = teleporters[6]
							})

							table.insert(elements, {
								label = '4',
								coords = teleporters[7]
							})

							table.insert(elements, {
								label = '5',
								coords = teleporters[8]
							})

							table.insert(elements, {
								label = '6',
								coords = teleporters[9]
							})
						elseif k == 6 then
							table.insert(elements, {
								label = '-3',
								coords = teleporters[1]
							})

							table.insert(elements, {
								label = '-2',
								coords = teleporters[2]
							})

							table.insert(elements, {
								label = '-1',
								coords = teleporters[3]
							})

							table.insert(elements, {
								label = '1',
								coords = teleporters[4]
							})

							table.insert(elements, {
								label = '2',
								coords = teleporters[5]
							})

							table.insert(elements, {
								label = '4',
								coords = teleporters[7]
							})

							table.insert(elements, {
								label = '5',
								coords = teleporters[8]
							})

							table.insert(elements, {
								label = '6',
								coords = teleporters[9]
							})
						elseif k == 7 then
							table.insert(elements, {
								label = '-3',
								coords = teleporters[1]
							})

							table.insert(elements, {
								label = '-2',
								coords = teleporters[2]
							})

							table.insert(elements, {
								label = '-1',
								coords = teleporters[3]
							})

							table.insert(elements, {
								label = '1',
								coords = teleporters[4]
							})

							table.insert(elements, {
								label = '2',
								coords = teleporters[5]
							})

							table.insert(elements, {
								label = '3',
								coords = teleporters[6]
							})

							table.insert(elements, {
								label = '5',
								coords = teleporters[8]
							})

							table.insert(elements, {
								label = '6',
								coords = teleporters[9]
							})
						elseif k == 8 then
							table.insert(elements, {
								label = '-3',
								coords = teleporters[1]
							})

							table.insert(elements, {
								label = '-2',
								coords = teleporters[2]
							})

							table.insert(elements, {
								label = '-1',
								coords = teleporters[3]
							})

							table.insert(elements, {
								label = '1',
								coords = teleporters[4]
							})

							table.insert(elements, {
								label = '2',
								coords = teleporters[5]
							})

							table.insert(elements, {
								label = '3',
								coords = teleporters[6]
							})

							table.insert(elements, {
								label = '4',
								coords = teleporters[7]
							})

							table.insert(elements, {
								label = '6',
								coords = teleporters[9]
							})
						elseif k == 9 then
							table.insert(elements, {
								label = '-3',
								coords = teleporters[1]
							})

							table.insert(elements, {
								label = '-2',
								coords = teleporters[2]
							})

							table.insert(elements, {
								label = '-1',
								coords = teleporters[3]
							})

							table.insert(elements, {
								label = '1',
								coords = teleporters[4]
							})

							table.insert(elements, {
								label = '2',
								coords = teleporters[5]
							})

							table.insert(elements, {
								label = '3',
								coords = teleporters[6]
							})

							table.insert(elements, {
								label = '4',
								coords = teleporters[7]
							})

							table.insert(elements, {
								label = '5',
								coords = teleporters[8]
							})
						end

                        ESX.UI.Menu.CloseAll()

                        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'casino_menu', {
							title = 'Casino',
							align = 'top-left',
							elements = elements
						}, function(data, menu)
							DoScreenFadeOut(100)

							Citizen.Wait(750)

							SetPedCoordsKeepVehicle(ped, data.current.coords.x, data.current.coords.y, data.current.coords.z)
							SetEntityHeading(ped, data.current.coords.w)

							DoScreenFadeIn(100)

							menu.close()
						end, function(data, menu)
							menu.close()
						end)
                    end
                end
            end
        end

        if not show and inRange then
			exports['cc_core']:showHelpNotification('Drücke E um den Teleporter zu benutzen')
			show = true
		elseif show and not inRange then
			exports['cc_core']:closeHelpNotification()
			show = false
		end

        if letSleep then
            Citizen.Wait(500)
        end
    end
end)

local ESX = nil
local show = false

Citizen.CreateThread(function()
    while ESX == nil do
        ESX = exports['es_extended']:getSharedObject()
        Citizen.Wait(0)
    end

    while ESX.GetPlayerData().job == nil do
        Citizen.Wait(0)
    end

    ESX.PlayerData = ESX.GetPlayerData()
end)

Citizen.CreateThread(function()
    local blip = AddBlipForCoord(Config_Lifeinvader.position)
    SetBlipSprite(blip, 77)
    SetBlipScale(blip, 0.8)
    SetBlipColour(blip, 1)
    SetBlipDisplay(blip, 4)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentString('Lifeinvader')
    EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local distance = #(coords - Config_Lifeinvader.position)
        local letSleep, inRange = true, false

        if distance <= 30.0 then
            letSleep = false

            DrawMarker(nil, Config_Lifeinvader.position, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, true, false, true, false, false, false)
        end

        if distance < 1 then
            inRange = true

            if IsControlJustReleased(1, 38) then
                SetNuiFocus(true, true)
                SendNUIMessage({
                    script = 'lifeinvader',
                    action = 'show'
                })

                TriggerServerEvent('cc_core:lifeinvader:getData')
            end
        end

        if not show and inRange then
			exports['cc_core']:showHelpNotification('Drücke E um denn Lifeinvader zu öffnen')
			show = true
		elseif show and not inRange then
			exports['cc_core']:closeHelpNotification()
			show = false
		end

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

RegisterNetEvent('cc_core:lifeinvader:getData')
AddEventHandler('cc_core:lifeinvader:getData', function(data)
    SendNUIMessage({
        script = 'lifeinvader',
        action = 'addAds',
        data = data
    })
end)

RegisterNUICallback('lifeinvader/escape', function(data, cb)
    SetNuiFocus(false, false)
end)

RegisterNUICallback('lifeinvader/sendAd', function(data, cb)
    if data.message ~= nil and data.message ~= '' then
        TriggerServerEvent('cc_core:lifeinvader:addAd', data.message, data.private)
    else
        TriggerClientEvent('cc_core:hud:notify', 'Lifeinvader', 'Du hast keine Nachricht angegeben', 'info')
    end
end)
]]