Config_Banking = {}

Config_Banking.Banks = {
    vector3(150.266, -1040.203, 29.374),
    vector3(-1212.980, -330.841, 37.787),
    vector3(-2962.582, 482.627, 15.703),
    vector3(-112.202, 6469.295, 31.626),
    vector3(314.187, -278.621, 54.170),
    vector3(-351.534, -49.529, 49.042),
    vector3(241.727, 220.706, 106.286),
    vector3(-351.534, -49.529, 49.042),
    vector3(1175.064, 2706.643, 38.0940),
    vector3(-562.4059, -581.6586, 41.4304)
}

Config_Banking.ATMS = {
    vector3(89.75, 2.35, 68.31),
    vector3(1167.02, -456.32, 66.79),
    vector3(-386.733, 6045.953, 31.501),
    vector3(-284.037, 6224.385, 31.187),
    vector3(-284.037, 6224.385, 31.187),
    vector3(-135.165, 6365.738, 31.101),
    vector3(-110.753, 6467.703, 31.784),
    vector3(-94.9690, 6455.301, 31.784),
    vector3(155.4300, 6641.991, 31.784),
    vector3(1703.138, 6426.783, 32.730),
    vector3(1735.114, 6411.035, 35.164),
    vector3(1702.842, 4933.593, 42.051),
    vector3(1967.333, 3744.293, 32.272),
    vector3(1821.917, 3683.483, 34.244),
    vector3(1174.532, 2705.278, 38.027),
    vector3(166.1157, 6634.8408, 31.7106),
    vector3(540.0420, 2671.007, 42.177),
    vector3(2564.399, 2585.100, 38.016),
    vector3(2558.683, 349.6010, 108.050),
    vector3(2558.051, 389.4817, 108.660),
    vector3(1077.692, -775.796, 58.218),
    vector3(1139.018, -469.886, 66.789),
    vector3(1168.975, -457.241, 66.641),
    vector3(1153.884, -326.540, 69.245),
    vector3(381.2827, 323.2518, 103.270),
    vector3(236.4638, 217.4718, 106.840),
    vector3(265.0043, 212.1717, 106.780),
    vector3(285.2029, 143.5690, 104.970),
    vector3(-164.568, 233.5066, 94.919),
    vector3(-1827.04, 785.5159, 138.020),
    vector3(-1409.39, -99.2603, 52.473),
    vector3(-1205.35, -325.579, 37.870),
    vector3(-1215.64, -332.231, 37.881),
    vector3(-2072.41, -316.959, 13.345),
    vector3(-2975.72, 379.7737, 14.992),
    vector3(-2962.60, 482.1914, 15.762),
    vector3(-2955.70, 488.7218, 15.486),
    vector3(-3044.22, 595.2429, 7.595),
    vector3(-3144.13, 1127.415, 20.868),
    vector3(-3241.10, 996.6881, 12.500),
    vector3(-3241.11, 1009.152, 12.877),
    vector3(-1305.40, -706.240, 25.352),
    vector3(-538.225, -854.423, 29.234),
    vector3(-711.156, -818.958, 23.768),
    vector3(-717.614, -915.880, 19.268),
    vector3(-526.566, -1222.90, 18.434),
    vector3(-256.831, -719.646, 33.444),
    vector3(-203.548, -861.588, 30.205),
    vector3(119.9000, -883.826, 31.191),
    vector3(149.4551, -1038.95, 29.366),
    vector3(-846.304, -340.402, 38.687),
    vector3(-1204.35, -324.391, 37.877),
    vector3(-1216.27, -331.461, 37.773),
    vector3(-56.1935, -1752.53, 29.452),
    vector3(-261.692, -2012.64, 30.121),
    vector3(-273.001, -2025.60, 30.197),
    vector3(314.187, -278.621, 54.170),
    vector3(-351.534, -49.529, 49.042),
    vector3(24.589, -946.056, 29.357),
    vector3(-254.112, -692.483, 33.616),
    vector3(-1570.197, -546.651, 34.955),
    vector3(-1415.909, -211.825, 46.500),
    vector3(-1430.112, -211.014, 46.500),
    vector3(33.232, -1347.849, 29.497),
    vector3(287.645, -1282.646, 29.659),
    vector3(289.012, -1256.545, 29.440),
    vector3(295.839, -895.640, 29.217),
    vector3(1686.753, 4815.809, 42.008),
    vector3(-302.408, -829.945, 32.417),
    vector3(5.134, -919.949, 29.557),
    vector3(2682.9048, 3286.5984, 55.2411),
    vector3(-3016, 7.0, 179.3266),
    vector3(120.7111, -3019.7671, 7.0409),
    vector3(122.3426, -1293.5032, 29.2555),
    vector3(-1802.7200, -1196.1604, 13.0176),
    vector3(2762.5107, 1369.1263, 24.5294),
    vector3(-809.3117, -1238.2451, 7.3374),
    vector3(-1200.8198, -885.3911, 13.2605),
    vector3(1835.9835, 2578.5750, 46.0144),
    vector3(1117.4758, 216.6205, -50.4352),
    vector3(305.6193, -189.3305, 61.5757),
    vector3(103.7581, 207.0088, 108.3686),
    vector3(-619.5933, -303.3964, 35.3428),
    vector3(-1412.4668, -601.7825, 30.5201),
    vector3(-1412.4668, -601.7825, 30.5201),
    vector3(-1414.7183, -603.2210, 30.5495),
    vector3(-443.5821, -23.6501, 46.1957),
    vector3(472.3668, -1001.5579, 30.692),
    vector3(2710.0408, 3450.8450, 55.5477),
    vector3(-808.9165, -1350.6936, 5.1500),
    vector3(-4.4576, -1830.6582, 25.3364),
    vector3(953.3079, -2391.8730, 22.3220),
    vector3(-943.5706, -2071.1028, 9.5260),
    vector3(-552.4893, -205.9764, 38.2210),
    vector3(-539.8637, -198.4607, 38.2210),
    vector3(712.2125, 1267.0504, 359.4825),
    vector3(1124.7935, -650.6382, 56.7023),
    vector3(-1900.8491, 2065.4541, 139.8904),
    vector3(-1337.9930, -1255.7524, 5.9441),
    vector3(919.8786, 39.1003, 80.0960),
    vector3(236.8345, -1081.6208, 29.3246),
    vector3(-559.2758, -589.3956, 41.4304),
    vector3(-276.7017, -1051.2710, 27.2107),
    vector3(604.0, -9.2, 75.6),

    vector3(1151.6, -1533.0, 34.4),
    vector3(1152.4, -1549.4, 34.0),
    vector3(1150.4, -1537.7, 38.3),

    -- vector3(-576.9, -194.6, 38.2),
    -- vector3(-527.8, -166.1, 38.2),
    -- vector3(-537.5, -171.8, 38.2),
    -- vector3(-586.8, -143.5, 47.2),
    -- vector3(-587.7, -142.5, 47.2),
    -- vector3(-588.8, -141.4, 47.2),
    -- vector3(-567.5, -199.0, 33.4),

    vector3(-178.3003, -302.5749, 55.1507),

    vector3(2473.6824, -270.7017, -70.6942),

    --bennsys
    vector3(872.3885, -2109.3867, 30.4594),

    -- easy credit
    vector3(-236.2344, 6220.4780, 31.9445),

    vector3(-445.0133, 6015.6343, 31.2886),

    -- doj fib
    vector3(2522.1670, -422.7883, 94.5821),

    -- zeytunsbar

    vector3(72.3497, -804.8713, 30.5029),
    vector3(140.5107, -770.2848, 241.1519),

    --Slice Tacos
    vector3(-1175.8, -1266.6, 6.2),

    vector3(-1099.3, -823.6, 19.0),
    -- lifeinvadner 
    vector3(4050.81, 2348.79, 24.7),

    -- Würfelpark 
    vector3(187.5053, -899.8595, 30.7131),

    --Flamingo
    vector3(-1755.3, -722.5, 10.4)
}