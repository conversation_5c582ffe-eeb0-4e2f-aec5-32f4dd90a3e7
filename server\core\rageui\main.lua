ESX = exports['es_extended']:getSharedObject()

local Customer = {}

local function haveAnimal(identifier, type, cb)
    if type == 'Husky' then
        for k, v in pairs(Customer.Husky) do
            if identifier == v then
                cb(true)
            end
        end

        cb(false)
    elseif type == 'Mops' then
        for k, v in pairs(Customer.Mops) do
            if identifier == v then
                cb(true)
            end
        end

        cb(false)
    elseif type == 'Pudel' then
        for k, v in pairs(Customer.Pudel) do
            if identifier == v then
                cb(true)
            end
        end

        cb(false)
    elseif type == 'Retriever' then
        for k, v in pairs(Customer.Retriever) do
            if identifier == v then
                cb(true)
            end
        end

        cb(false)
    elseif type == 'Shepard' then
        for k, v in pairs(Customer.Shepard) do
            if identifier == v then
                cb(true)
            end
        end

        cb(false)
    elseif type == 'Rottweiler' then
        for k, v in pairs(Customer.Rottweiler) do
            if identifier == v then
                cb(true)
            end
        end

        cb(false)
    elseif type == 'WischmoppHund' then
        for k, v in pairs(Customer.WischmoppHund) do
            if identifier == v then
                cb(true)
            end
        end

        cb(false)
    elseif type == 'Katze' then
        for k, v in pairs(Customer.Katze) do
            if identifier == v then
                cb(true)
            end
        end

        cb(false)
    elseif type == 'Lion' then
        for k, v in pairs(Customer.Lion) do
            if identifier == v then
                cb(true)
            end
        end

        cb(false)
    elseif type == 'Coyote' then
        for k, v in pairs(Customer.Coyote) do
            if identifier == v then
                cb(true)
            end
        end

        cb(false)
    end
end

local function addAnimal(identifier, type, cb)
    haveAnimal(identifier, type, function(haveAnimal)
        if not haveAnimal then
            local state = SaveResourceFile(GetCurrentResourceName(), './configs/animal/backups/config_backup_' .. os.date('%d.%m.%Y-%I.%M.%p') .. '.json', json.encode(Customer))
    
            if state == 1 then
                local length = #Customer[type]
        
                Customer[type][length + 1] = tostring(identifier)
            
                local state = SaveResourceFile(GetCurrentResourceName(), './configs/animal/config.json', json.encode(Customer))
            
                if state == 1 then
                    cb(true)
                else
                    cb(false)
                end 
            else
                cb(false)
            end
        else
            cb(false)
        end
    end)
end

Citizen.CreateThread(function()
    Customer = json.decode(LoadResourceFile(GetCurrentResourceName(), './configs/animal/config.json'))
end)

exports('addAnimal', addAnimal)

function Notify(sendTo, title, message, type)
    TriggerClientEvent('cc_core:hud:notify', sendTo, type, title, message)
end