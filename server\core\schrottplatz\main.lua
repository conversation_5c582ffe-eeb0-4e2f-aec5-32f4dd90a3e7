RegisterServerEvent('cc_core:schrott:destroy')
AddEventHandler('cc_core:schrott:destroy', function(model, plate)
    local playerId = source

    MySQL.Async.fetchAll('SELECT vehicle FROM owned_vehicles WHERE owner = @owner AND plate = @plate', {
        ['@owner'] = ESX.GetPlayerIdentifier(playerId),
        ['@plate'] = plate
    }, function(result)
        if #result ~= 0 then
            local org = json.decode(result[1].vehicle)

            if org.model == model then
                Notify(playerId, 'Schrottplatz', 'Du hast das Fahrzeug erfolgreich verschrottet', 'success')
                ESX.AddPlayerInventoryItem(playerId, 'metall', 20, GetCurrentResourceName())
                log(playerId, 'Schorttplatz', 'Der Spieler ' .. GetPlayerName(playerId) .. ' verschrottet ein Fahrzeug mit dem Kennzeichzen **' .. plate .. '**', 'https://canary.discord.com/api/webhooks/1221940550660526231/8PUxGsTSteSOc9yJquFJqb8xyRCJK_GcO3bCpsuaymiMOc9ycoeegqRCtHv_XZBWNo0o')
                MySQL.Async.execute('DELETE FROM owned_vehicles WHERE plate = @plate', {
                    ['@plate'] = plate
                })
                TriggerClientEvent('cc_core:garage:removeVehicle', playerId, plate)
            end
        end
    end)
end)

--clientcode
schrottplatzCode = [[
local show = false

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(4)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        
        if IsPedInAnyVehicle(ped, false) then
            local distance = #(coords - vector3(1357.94,-2095.19, 52.0))
            if distance < 10.0 then
                letSleep = false
                inRange = true

                if IsControlJustPressed(0, 38) then
                    local props = ESX.Game.GetVehicleProperties(GetVehiclePedIsIn(ped, false))
                    local owned = exports['cc_core']:isVehicleOwned(ESX.Math.Trim(props.plate))
                    if owned then
                        TriggerServerEvent('cc_core:schrott:destroy', props.model, props.plate)
                        ESX.Game.DeleteVehicle(GetVehiclePedIsIn(ped, false))
                    else
                        Notify('Schrottplatz', "Dieses Fahrzeug gehört nicht dir!", "info")
                    end
                end
            end
        else
            Citizen.Wait(1000)
        end

        helpNotify(inRange, show, 'Drücke E um dein Auto beim Schrotthändler abzugeben', function(bool)
            show = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)
]]