<?xml version="1.0" encoding="UTF-8"?>

<CWeaponInfoBlob>
  <SlotNavigateOrder>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="353" />
          <Entry>SLOT_RAILGUN</Entry>
        </Item>
      </WeaponSlots>
    </Item>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="353" />
          <Entry>SLOT_RAILGUN</Entry>
        </Item>
      </WeaponSlots>
    </Item>
  </SlotNavigateOrder>
  <SlotBestOrder>
    <WeaponSlots>
      <Item>
        <OrderNumber value="257" />
        <Entry>SLOT_RAILGUN</Entry>
      </Item>
    </WeaponSlots>
  </SlotBestOrder>
  <TintSpecValues />
  <FiringPatternAliases />
  <UpperBodyFixupExpressionData />
  <AimingInfos>
    <Item type="CAimingInfo">
      <Name>RIFLE_LO_RAILGUN_STRAFE</Name>
      <HeadingLimit value="60.000000" />
      <SweepPitchMin value="-75.000000" />
      <SweepPitchMax value="55.000000" />
    </Item>
  </AimingInfos>
  <Infos>
    <Item>
      <Infos>
        <Item type="CAmmoInfo">
          <Name>AMMO_RAILGUN</Name>
          <Model />
          <Audio />
          <Slot />
          <AmmoMax value="20" />
          <AmmoMax50 value="20" />
          <AmmoMax100 value="20" />
          <AmmoMaxMP value="20" />
          <AmmoMax50MP value="20" />
          <AmmoMax100MP value="20" />
          <AmmoFlags />
        </Item>
      </Infos>
    </Item>
    <Item>
      <Infos>
        <Item type="CWeaponInfo">
          <Name>WEAPON_RAILGUN</Name>
          <Model>w_ar_railgun</Model>
          <Audio>AUDIO_ITEM_RAILGUN</Audio>
          <Slot>SLOT_RAILGUN</Slot>
          <DamageType>EXPLOSIVE</DamageType>
          <Explosion>
            <Default>EXP_TAG_RAILGUN</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>INSTANT_HIT</FireType>
          <WheelSlot>WHEEL_HEAVY</WheelSlot>
          <Group>GROUP_HEAVY</Group>
          <AmmoInfo ref="AMMO_RAILGUN" />
          <AimingInfo ref="RIFLE_LO_RAILGUN_STRAFE" />
          <ClipSize value="1" />
          <AccuracySpread value="1.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMinOverride value="-1.000000" />
          <RecoilAccuracyMax value="0.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="230000.000000" />
          <HeadShotDamageModifierPlayer value="1288860.000000" />
          <Damage value="30.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <VehicleDamageModifier value="1.000000" />
          <Force value="0.000000" />
          <ForceHitPed value="0.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="20.000000" />
              <ForceBack value="20.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="25.000000" />
              <ForceBack value="20.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CLAVICLE</BoneTag>
              <ForceFront value="20.000000" />
              <ForceBack value="20.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CLAVICLE</BoneTag>
              <ForceFront value="20.000000" />
              <ForceBack value="20.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="500.000000" />
          <Penetration value="0.010000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="2.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="2.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="0.200000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.375000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_RIFLE_ASSAULT</EffectGroup>
            <FlashFx>muz_railgun</FlashFx>
            <FlashFxAlt />
            <FlashFxFP />
            <FlashFxFPAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxFP />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <ShellFx />
            <ShellFxFP />
            <TracerFx>bullet_tracer_railgun</TracerFx>
            <PedDamageHash>BulletLarge</PedDamageHash>
            <TracerFxChanceSP value="0.150000" />
            <TracerFxChanceMP value="0.750000" />
            <FlashFxChanceSP value="1.000000" />
            <FlashFxChanceMP value="1.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="0.700000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.200000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="300" />
          <InitialRumbleIntensity value="0.300000" />
          <InitialRumbleIntensityTrigger value="0.950000" />
          <RumbleDuration value="200" />
          <RumbleIntensity value="1.000000" />
          <RumbleIntensityTrigger value="0.950000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="300" />
          <InitialRumbleIntensityFps value="1.000000" />
          <RumbleDurationFps value="150" />
          <RumbleIntensityFps value="1.000000" />
          <NetworkPlayerDamageModifier value="1.000000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.000000" />
          <LockOnRange value="40.000000" />
          <WeaponRange value="500.000000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="0.000000" />
          <DamageFallOffRangeMax value="0.000000" />
          <DamageFallOffModifier value="1.000000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_CAMERA</DefaultCameraHash>
          <AimCameraHash />
          <FireCameraHash />
          <CoverCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash>DEFAULT_THIRD_PERSON_PED_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
          <CinematicShootingCameraHash>DEFAULT_THIRD_PERSON_PED_CINEMATIC_SHOOTING_CAMERA</CinematicShootingCameraHash>
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="35.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="28.000000" />
          <FirstPersonScopeAttachmentFov value="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.000000" y="0.000000" z="-0.028000" />
          <FirstPersonScopeAttachmentOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeRotationOffset x="0.600000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="0.000000" y="0.000000" z="-0.025000" />
          <FirstPersonAsThirdPersonRNGOffset x="-0.025000" y="0.000000" z="-0.025000" />
          <FirstPersonAsThirdPersonLTOffset x="0.010000" y="0.050000" z="-0.075000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.055000" y="0.000000" z="-0.040000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="-0.050000" y="0.100000" z="-0.050000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <ZoomFactorForAccurateMode value="1.300000" />
          <RecoilShakeHash>SHOTGUN_RECOIL_SHAKE</RecoilShakeHash>
          <RecoilShakeHashFirstPerson>FPS_SHOTGUN_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="0" />
          <RecoilShakeAmplitude value="0.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.000000" />
          <IkRecoilDisplacementScope value="0.000000" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.265000" y="0.175000" z="0.550000" />
          <AimProbeLengthMin value="0.400000" />
          <AimOffsetMax x="0.150000" y="-0.175000" z="0.500000" />
          <AimProbeLengthMax value="0.350000" />
          <AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000" />
          <AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000" />
          <AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000" />
          <AimOffsetMinFPSLT x="0.120000" y="0.431000" z="0.469000" />
          <AimOffsetMaxFPSLT x="0.048000" y="-0.225000" z="0.609000" />
          <AimOffsetMinFPSRNG x="0.180000" y="0.475000" z="0.309000" />
          <AimOffsetMaxFPSRNG x="0.138000" y="-0.212000" z="0.618000" />
          <AimOffsetMinFPSScope x="0.039000" y="0.378000" z="0.431000" />
          <AimOffsetMaxFPSScope x="0.006000" y="-0.059000" z="0.694000" />
          <AimOffsetEndPosMinFPSIdle x="-0.225000" y="0.692000" z="-0.155000" />
          <AimOffsetEndPosMedFPSIdle x="-0.158000" y="0.639000" z="0.616000" />
          <AimOffsetEndPosMaxFPSIdle x="-0.217000" y="-0.006000" z="0.887000" />
          <AimOffsetEndPosMinFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMedFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMaxFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeRadiusOverrideFPSIdle value="0.000000" />
          <AimProbeRadiusOverrideFPSIdleStealth value="0.000000" />
          <AimProbeRadiusOverrideFPSLT value="0.000000" />
          <AimProbeRadiusOverrideFPSRNG value="0.000000" />
          <AimProbeRadiusOverrideFPSScope value="0.000000" />
          <TorsoAimOffset x="-1.200000" y="0.550000" />
          <TorsoCrouchedAimOffset x="0.100000" y="0.120000" />
          <LeftHandIkOffset x="0.200000" y="0.050000" z="-0.050000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="0.000000" />
          <ReticuleStyleHash>WEAPONTYPE_RIFLE</ReticuleStyleHash>
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_RAILGUN</PickupHash>
          <MPPickupHash>PICKUP_AMMO_BULLET_MP</MPPickupHash>
          <HumanNameHash>WT_RAILGUN</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>RAILGUN</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>Shotgun</NmShotTuningSet>
          <AttachPoints>
            <Item>
              <AttachBone>WAPClip</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_RAILGUN_CLIP_01</Name>
                  <Default value="true" />
                </Item>
              </Components>
            </Item>
          </AttachPoints>
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>CarriedInHand ApplyBulletForce Gun CanLockonOnFoot CanLockonInVehicle CanFreeAim TwoHanded AnimReload AnimCrouchFire UsableOnFoot UsableInCover OnlyFireOneShot NoLeftHandIKWhenBlocked AllowCloseQuarterKills HasLowCoverReloads HasLowCoverSwaps LongWeapon DriveByMPOnly UseFPSAimIK UseFPSSecondaryMotion PlayOutOfAmmoAnim BlockFirstPersonStateTransitionWhileFiring</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="DEFAULT" />
          <AmmoDiminishingRate value="3" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="1.000000" />
          <StealthFiringBreathingAdditiveWeight value="1.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="1.000000" />
          <StealthFiringLeanAdditiveWeight value="1.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="90" />
          <HudSpeed value="25" />
          <HudCapacity value="15" />
          <HudAccuracy value="20" />
          <HudRange value="70" />
          <VehicleAttackAngle value="25.000000" />
          <TorsoIKAngleLimit value="-1.000000" />
          <CamoDiffuseTexIdxs />
        </Item>
      </Infos>
    </Item>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos />
    </Item>
  </Infos>
  <VehicleWeaponInfos />
  <Name>DLC - Rail Gun</Name>
</CWeaponInfoBlob>