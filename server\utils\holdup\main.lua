local rob = false
local robbers = {}

local lastSuccess = false

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(5000)
		if lastSuccess then
			rob = true
			Citizen.Wait(30*60000)
			rob = false
			lastSuccess = false
		end
	end
end)

local alarmHacked = false

RegisterNetEvent('cc_terminal:hackBankAlert', function(state)
	print('cc_terminal:hackBankAlert')
	if state ~= nil then
		print('~= nil')
		if type(state) == 'boolean' then
			print('type boolean')
			alarmHacked = state
			print('Set Alarm to:', alarmHacked)
		end
	end
end)

RegisterServerEvent('cc_core:holdup:tooFar')
AddEventHandler('cc_core:holdup:tooFar', function(currentStore)
    local playerId = source
	local xPlayers = exports["cc_core"]:GetPlayersFix()
	rob = false
	print('tooFar', rob, currentStore)
	for k, v in pairs(xPlayers) do
		if v.job == 'police' or v.job == 'army' or v.job == 'fib' or v.job == 'sheriff' then
			if v.jobDienst then
                Notify(v.playerId, 'Raub', 'Der Raubüberfall in ' .. Config_Holdup.Stores[currentStore].nameOfStore .. ' wurde abgebrochen!', 'info')
				TriggerClientEvent('cc_core:holdup:killBlip', v.playerId, Config_Holdup.Stores[currentStore].nameOfStore)
			end
		end
	end

	if robbers[playerId] then
		TriggerClientEvent('cc_core:holdup:tooFar', playerId)
		robbers[playerId] = nil
        Notify(playerId, 'Raub', 'Der Raubüberfall in ' .. Config_Holdup.Stores[currentStore].nameOfStore .. ' wurde abgebrochen!', 'info')
	end
end)

RegisterServerEvent('cc_core:holdup:robberyStarted')
AddEventHandler('cc_core:holdup:robberyStarted', function(currentStore)
	print('robberyStarted:', source, currentStore)
    local playerId = source

	if GetPlayerRoutingBucket(playerId) ~= 0 then
		return
	end

	if Config_Holdup.Stores[currentStore] then
		print('Passed Check 1.')
		local store = Config_Holdup.Stores[currentStore]

		if (os.time() - store.lastRobbed) < store.timerBeforeNewRob and store.lastRobbed ~= 0 then
            Notify(playerId, 'Raub', 'Die ' .. store.nameOfStore .. ' wurde vor kurzem ausgeraubt. Bitte warte ' .. store.timerBeforeNewRob - (os.time() - store.lastRobbed)  .. ' Sekunden bist du die ' .. store.nameOfStore .. ' erneut ausrauben kannst', 'info')
			return
		end

		print('Passed Check 2.')

		local cops = 0
        local xPlayers = exports["cc_core"]:GetPlayersFix()

		for k, v in pairs(xPlayers) do
			if v.job == 'police' or v.job == 'army' or v.job == 'fib' or v.job == 'sheriff' then
				if v.jobDienst then
					cops = cops + 1
				end
			end
		end

		print('Passed Check 3. Total Cops:', cops)

		if not rob then
			print('Passed Check 4.')
			if cops >= store.policeNumberRequired then
				print('Passed Check 5.')
				rob = true

				exports['cc_core']:log(playerId, 'Raub - Logs', 'Der Spieler ' .. GetPlayerName(playerId) .. ' startet Raub: ' .. store.nameOfStore, 'https://canary.discord.com/api/webhooks/1220445439090753546/A2cQV1yrkXKShRJV1QJiFFdJW1AWg1GaYHdwQXdc4lKUDRd7DLm7Xq5OuzZq9Zf0ZfWo')

                if store.nameOfStore == 'Bank' then
					print('Robbed Store == Bank')
					if alarmHacked then
						print('Silent Alert Triggerd!')
						Notify(playerId, 'Raub', 'Stiller Raub Gestartet: ' .. store.nameOfStore, 'info')
					else
						print('Default Bank Altert!')
						for k, v in pairs(xPlayers) do
							if v.job == 'police' or v.job == 'army' or v.job == 'fib' or v.job == 'sheriff' then
								Notify(v.playerId, 'Raub', 'Raubüberfall im Gange bei ' .. store.nameOfStore, 'info')
								TriggerClientEvent('cc_core:holdup:setBlip', v.playerId,  currentStore, store.nameOfStore, store.position)
							end
						end
                        Notify(playerId, 'Raub', 'Der Alarm wurde ausgelöst. Die Staatliche Exekutive ist nun unterwegs.', 'info')
					end
				else
					print('Processing Default Rob Protocol')
					for k, v in pairs(xPlayers) do
						if v.job == 'police' or v.job == 'army' or v.job == 'fib' or v.job == 'sheriff' then
							Notify(v.playerId, 'Raub', 'Raubüberfall im Gange bei ' .. store.nameOfStore, 'info')
							TriggerClientEvent('cc_core:holdup:setBlip', v.playerId,  currentStore, store.nameOfStore, store.position)
						end
					end
                    Notify(playerId, 'Raub', 'Der Alarm wurde ausgelöst. Die Staatliche Exekutive ist nun unterwegs.', 'info')
				end

                Notify(playerId, 'Raub', 'Du hast angefangen ' .. store.nameOfStore .. ' auszurauben', 'info')
				
				TriggerClientEvent('cc_core:holdup:currentlyRobbing', playerId, currentStore)
				TriggerClientEvent('cc_core:holdup:startTimer', playerId)
				
				store.lastRobbed = os.time()
				robbers[playerId] = currentStore

				print('Starting Timeout!')

				SetTimeout(store.secondsRemaining * 1000, function()
					if robbers[playerId] then
						lastSuccess = true
						rob = false
						print('rob:', rob, 'robberyStarted')
						alarmHacked = false
						if GetPlayerName(playerId) ~= nil then
							for k, v in pairs(xPlayers) do
								if v.job == 'police' or v.job == 'army' or v.job == 'fib' or v.job == 'sheriff' then
									if v.jobDienst then
                                        Notify(playerId, 'Raub', 'Raubüberfall erfolgreich in ' .. store.nameOfStore, 'info')
									end

                                    TriggerClientEvent('cc_core:holdup:killBlip', v.playerId, currentStore, store.nameOfStore)
								end
							end

							Citizen.Wait(500)

							if store.type == 'money' then
								TriggerClientEvent('cc_core:holdup:robberyComplete', playerId, 'money', store.reward)

								if store.giveBlackMoney then
									ESX.AddPlayerAccountMoney(playerId, 'black_money', store.reward, GetCurrentResourceName())
									-- ESX.AddPlayerInventoryItem(playerId, 'unregistered_cash', store.reward * 0.9)
									exports['cc_core']:log(playerId, 'Raub - Logs', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat: ' .. store.nameOfStore..' Erfolgreich ausgeraubt und bekommt: '..store.reward..' Schwarzgeld!', 'https://canary.discord.com/api/webhooks/1220445439090753546/A2cQV1yrkXKShRJV1QJiFFdJW1AWg1GaYHdwQXdc4lKUDRd7DLm7Xq5OuzZq9Zf0ZfWo')
								else
									ESX.AddPlayerMoney(playerId, store.reward, GetCurrentResourceName())
									exports['cc_core']:log(playerId, 'Raub - Logs', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat: ' .. store.nameOfStore..' Erfolgreich ausgeraubt und bekommt: '..store.reward..' Cash!', 'https://canary.discord.com/api/webhooks/1220445439090753546/A2cQV1yrkXKShRJV1QJiFFdJW1AWg1GaYHdwQXdc4lKUDRd7DLm7Xq5OuzZq9Zf0ZfWo')
								end
							else
								local stats = 0
								local random = math.random(1, 100)
								if random < 16 then
									stats = 1
								elseif random >= 16 and random < 33 then
									stats = 2
								elseif random >= 33 and random < 49 then
									stats = 3
								elseif random >= 49 and random < 65 then
									stats = 2
								elseif random >= 65 and random < 81 then
									stats = 1
								elseif random >= 81 then
									stats = 3
								end
								for k, v in pairs(store.reward) do
									if stats == k then
										if not ESX.HasPlayerWeapon(playerId, v) then
											ESX.AddPlayerWeapon(playerId, v, 250, 100.0)
                                            Notify(playerId, 'Raub', 'Der Raubüberfall ist abgeschlossen, du bekommst eine ' .. v, 'info')
											TriggerClientEvent('cc_core:holdup:robberyComplete', playerId, 'weapon')
											exports['cc_core']:log(playerId, 'Raub - Logs', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat: ' .. store.nameOfStore..' Erfolgreich ausgeraubt und bekommt: '..v, 'https://canary.discord.com/api/webhooks/1220445439090753546/A2cQV1yrkXKShRJV1QJiFFdJW1AWg1GaYHdwQXdc4lKUDRd7DLm7Xq5OuzZq9Zf0ZfWo')
										else
											TriggerClientEvent('cc_core:holdup:robberyComplete', playerId, 'weapon')
                                            Notify(playerId, 'Raub', 'Du hast bereits eine ' .. v, 'info')
										end
									end
								end
							end
						end
					end
				end)
			else
                Notify(playerId, 'Raub', 'Es müssen mindestens ' .. store.policeNumberRequired.. ' LSPD Mitarbeiter in Dienst sein, um die ' .. store.nameOfStore .. ' auszurauben', 'info')
			end
		else
            Notify(playerId, 'Raub', 'Die ' .. store.nameOfStore .. ' wird bereits ausgeraubt', 'info')
		end
	end
end)

--clientcode
holdupCode = [[
local holdingUp = false
local store = ""
local show = false
local currentStore = ''

print('holdup loaded')

local function drawTxt(x, y, width, height, scale, text, r, g, b, a, outline)
	SetTextFont(0)
	SetTextScale(scale, scale)
	SetTextColour(r, g, b, a)
	SetTextDropshadow(0, 0, 0, 0,255)
	SetTextDropShadow()

	if outline then
        SetTextOutline()
    end

	BeginTextCommandDisplayText('STRING')
	AddTextComponentSubstringPlayerName(text)
	EndTextCommandDisplayText(x - width / 2, y - height / 2 + 0.005)
end

RegisterNetEvent('cc_core:holdup:currentlyRobbing')
AddEventHandler('cc_core:holdup:currentlyRobbing', function(currentStore)
	holdingUp, store = true, currentStore
end)

RegisterNetEvent('cc_core:holdup:killBlip')
AddEventHandler('cc_core:holdup:killBlip', function(currentStore, storeName)
	if Config_Holdup.Stores[currentStore] ~= nil then
		RemoveBlip(Config_Holdup.Stores[currentStore].blipRobbery)
	end
end)

RegisterNetEvent('cc_core:holdup:setBlip')
AddEventHandler('cc_core:holdup:setBlip', function(currentStore, storeName, position)
	Config_Holdup.Stores[currentStore].blipRobbery = AddBlipForCoord(position.x, position.y, position.z)

	SetBlipSprite(Config_Holdup.Stores[currentStore].blipRobbery, 161)
	SetBlipScale(Config_Holdup.Stores[currentStore].blipRobbery, 2.0)
	SetBlipColour(Config_Holdup.Stores[currentStore].blipRobbery, 3)
	PulseBlip(Config_Holdup.Stores[currentStore].blipRobbery)
end)

RegisterNetEvent('cc_core:holdup:tooFar')
AddEventHandler('cc_core:holdup:tooFar', function()
	holdingUp, store = false, ''
    Notify('Raub', 'Der Raubüberfall wurde abgebrochen', 'info')
end)

RegisterNetEvent('cc_core:holdup:robberyComplete')
AddEventHandler('cc_core:holdup:robberyComplete', function(type, award)
	holdingUp, store = false, ''

	if type == 'money' then
        Notify('Raub', 'Der Raubüberfall ist abgeschlossen, du hast ' .. award .. '$ gestolen', 'info')
	end
end)

RegisterNetEvent('cc_core:holdup:startTimer')
AddEventHandler('cc_core:holdup:startTimer', function()
	local timer = Config_Holdup.Stores[store].secondsRemaining

	Citizen.CreateThread(function()
		while timer > 0 and holdingUp do
			Citizen.Wait(1000)

			if timer > 0 then
				timer = timer - 1
			end
		end
	end)

	Citizen.CreateThread(function()
		while holdingUp do
			Citizen.Wait(0)
			if timer >= 1 then
				drawTxt(0.66, 1.44, 1.0, 1.0, 0.4, Config_Holdup.Stores[store].nameOfStore .. ' - Raubüberfall: ~r~' .. timer .. '~s~ verbleibende Sekunden', 255, 255, 255, 255)
			end
		end
	end)
end)

Citizen.CreateThread(function()
	for k, v in pairs(Config_Holdup.Stores) do
		if v.blip then
			local blip = AddBlipForCoord(v.position.x, v.position.y, v.position.z)
			SetBlipSprite(blip, v.position.sprite)
			SetBlipScale(blip, v.position.scale)
			SetBlipAsShortRange(blip, true)
			BeginTextCommandSetBlipName("STRING")
			AddTextComponentString(v.nameOfStore)
			EndTextCommandSetBlipName(blip)
		end
	end
end)

Citizen.CreateThread(function()
	while true do
        Citizen.Wait(1)
		local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)

		for k,v in pairs(Config_Holdup.Stores) do
			local storePos = v.position
			local distance = #(coords - vector3(storePos.x, storePos.y, storePos.z))

			if distance < 15.0 then
				letSleep = false
				if not holdingUp then
					DrawMarker(nil, storePos.x, storePos.y, storePos.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 255, 156, 35, 100, false, false, 2, false, false, false, false)

					if distance < 0.5 then
						inRange = true

						currentStore = v.nameOfStore

						if IsControlJustReleased(0, 38) then
							if IsPedArmed(PlayerPedId(), 4) then
								TriggerServerEvent('cc_core:holdup:robberyStarted', k)
							else
                                Notify('Raub', 'Du musst eine Waffe benutzen, um den Raubüberfall zu starten', 'info')
							end
						end
					end
				end
			end
		end

		if holdingUp then
			local storePos = Config_Holdup.Stores[store].position
			local distance2 = #(coords - vector3(storePos.x, storePos.y, storePos.z))

			if distance2 > Config_Holdup.Stores[store].MaxDistance then
				TriggerServerEvent('cc_core:holdup:tooFar', store)
			end
		end

        helpNotify(inRange, show, 'Drücke E um die ' .. currentStore .. ' auszurauben', function(bool)
            show = bool
        end)

		if letSleep then
			Citizen.Wait(1000)
		end
	end
end)
]]