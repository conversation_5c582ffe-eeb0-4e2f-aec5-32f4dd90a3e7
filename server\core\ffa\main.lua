local ESX = exports['es_extended']:getSharedObject()

local playerZone, playerZoneType, privateZones, playerCount, playerStats, data = {}, {}, {}, {}, {}, {}

local uniqueDim = 500

CreateThread(function()
    for k, v in pairs(Config_FFA.ffaZones) do
        playerCount[k] = 0
    end
end)

MySQL.ready(function()
    MySQL.Async.fetchAll('SELECT * FROM ffa ORDER BY kills DESC LIMIT 10', {}, function(result)
        if #result ~= 0 then
            local message, count = '', 1
            
            for k, v in pairs(result) do
                local kd = v.kills/v.deaths
                table.insert(playerStats, {
                    name = v.name,
                    kills = v.kills,
                    deaths = v.deaths
                })
                message = message .. '**#' .. count .. '** ' .. v.name .. ' - Kills: ' .. v.kills .. ' - Tode: ' .. v.deaths .. ' - KD: ' .. kd .. '\n'
                count = count + 1
            end

            sendToDiscord(message)
        end
    end)
end)

RegisterServerEvent('cc_core:ffa:joinZone')
AddEventHandler('cc_core:ffa:joinZone', function(zone, zoneType, password)
    local playerId = source

    if ESX.GetPlayerNeu(playerId) then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [FF-22]", false, "Afrika")
        return
    end

    if GetPlayerRoutingBucket(playerId) ~= 0 then
        return
    end

    if playerZone[playerId] == nil then
        playerZone[playerId] = false
    end

    if playerZoneType[playerId] == nil then
        playerZoneType[playerId] = false
    end

    if playerCount[zone] ~= nil then
        if playerCount[zone] <= Config_FFA.ffaZones[zone].maxPlayers then
            if not playerZone[playerId] then
                local identifier = ESX.GetPlayerIdentifier(playerId)
                local createNew = true
                local dimShit = 1100
                playerZone[playerId] = zone
                playerZoneType[playerId] = zoneType

                if zoneType == 'ffa' then
                    playerCount[zone] = playerCount[zone] + 1
                
                    dimShit = Config_FFA.ffaZones[zone].dimension
                elseif zoneType == 'private' then
                    for k, v in pairs(privateZones) do
                        if v.password == password then
                            createNew = false
                            dimShit = v.dimension
                        end
                    end

                    if createNew then
                        dimShit = dimShit + #privateZones
                    
                        table.insert(privateZones, {
                            dimension = dimShit,
                            password = password
                        })
                    end
                elseif zoneType == 'training' then
                    uniqueDim = uniqueDim + 1

                    dimShit = uniqueDim
                end

                SetPlayerRoutingBucket(playerId, dimShit)

                MySQL.Async.fetchAll('SELECT * FROM ffa WHERE identifier = @identifier', {
                    ['@identifier'] = identifier
                }, function(result)
                    if #result ~= 0 then
                        data[playerId] = {
                            identifier = identifier,
                            playerId = playerId,
                            zone = zone,
                            type = zoneType,
                            dimension = dimShit,
                            kills = result[1].kills,
                            deaths = result[1].deaths,
                            xp = result[1].xp
                        }

                        TriggerClientEvent('cc_core:ffa:updateStats', playerId, data[playerId])
                        TriggerClientEvent('cc_core:ffa:spawnPlayer', playerId, zone, name, zoneType)
                    else
                        MySQL.Sync.execute('INSERT INTO ffa (identifier, name, inZone) VALUES (@identifier, @name, @inZone)', {
                            ['@identifier'] = ESX.GetPlayerIdentifier(playerId),
                            ['@name'] = ESX.GetPlayerRPName(playerId),
                            ['@inZone'] = true
                        })

                        data[playerId] = {
                            identifier = identifier,
                            playerId = playerId,
                            zone = zone,
                            type = zoneType,
                            dimension = dimShit,
                            kills = 0,
                            deaths = 0,
                            xp = 0,
                        }

                        TriggerClientEvent('cc_core:ffa:updateStats', playerId, data[playerId])
                        TriggerClientEvent('cc_core:ffa:spawnPlayer', playerId, zone, name, zoneType)
                    end
                end)
            end
        else
            --print('max players')
        end
    end
end)

RegisterServerEvent('cc_core:ffa:quitffa')
AddEventHandler('cc_core:ffa:quitffa', function()
    local playerId = source

    if playerZone[playerId] == nil then 
        playerZone[playerId] = false
    end

    if playerZoneType[playerId] == nil then 
        playerZoneType[playerId] = false
    end

    if type(playerZone[playerId]) ~= 'boolean' and type(playerZoneType[playerId]) ~= 'boolean' then
        SetPlayerRoutingBucket(playerId, 0)

        if playerZoneType[playerId] == 'ffa' then
            playerCount[playerZone[playerId]] = playerCount[playerZone[playerId]] - 1 
        end

        TriggerClientEvent('cc_core:ffa:quitffa', playerId, playerZoneType[playerId])
        TriggerClientEvent('huren:enableControl', playerId)

        MySQL.Async.execute('UPDATE ffa SET kills = @kills, deaths = @deaths, xp = @xp, inZone = @inZone, name = @name WHERE identifier = @identifier', {
            ['@identifier'] = data[playerId].identifier,
            ['@kills'] = data[playerId].kills,
            ['@deaths'] = data[playerId].deaths,
            ['@xp'] = data[playerId].xp,
            ['@inZone'] = false,
            ['@name'] = ESX.GetPlayerRPName(playerId)
        }, function(result)
            playerZone[playerId] = false
            playerZoneType[playerId] = false
        end)
    end
end)

RegisterServerEvent('cc_core:ffa:selfKill')
AddEventHandler('cc_core:ffa:selfKill', function()
    local playerId = source

    if playerZone[playerId] == nil then 
        playerZone[playerId] = false
    end

    if playerZoneType[playerId] == nil then 
        playerZoneType[playerId] = false
    end

    if type(playerZone[playerId]) ~= 'boolean' and type(playerZoneType[playerId]) ~= 'boolean' then
        data[playerId].deaths = data[playerId].deaths + 1

        TriggerClientEvent('cc_core:ffa:updateStats', playerId, data[playerId])
        TriggerClientEvent('cc_core:ffa:spawnPlayer', playerId, playerZone[playerId], 'Selfkill', playerZoneType[playerId])
        
        for k, v in pairs(data) do
            if v.zone == data[playerId].zone and v.dimension == data[playerId].dimension then
                TriggerClientEvent('cc_core:ffa:addPlayerKill', v.playerId, ESX.GetPlayerRPName(playerId), 'Selfkill')
            end
        end 
    end
end)

RegisterServerEvent('cc_core:ffa:selfKill_T')
AddEventHandler('cc_core:ffa:selfKill_T', function()
    local playerId = source

    if playerZone[playerId] == nil then 
        playerZone[playerId] = false
    end

    if playerZoneType[playerId] == nil then 
        playerZoneType[playerId] = false
    end

    if type(playerZone[playerId]) ~= 'boolean' and type(playerZoneType[playerId]) ~= 'boolean' then
        TriggerClientEvent('cc_core:ffa:spawnPlayer', playerId, playerZone[playerId], 'PED', playerZoneType[playerId])
    end
end)

RegisterServerEvent('cc_core:ffa:addKill')
AddEventHandler('cc_core:ffa:addKill', function(killer, statement)
    local playerId = source

    if statement then
        if playerZone[playerId] == nil then 
            playerZone[playerId] = false
        end
    
        if playerZoneType[playerId] == nil then 
            playerZoneType[playerId] = false
        end
    
        if type(playerZone[playerId]) ~= 'boolean' and type(playerZoneType[playerId]) ~= 'boolean' then
            data[killer].kills = data[killer].kills + 1
            data[playerId].deaths = data[playerId].deaths + 1
    
            data[killer].xp = data[killer].xp + 5
    
            TriggerClientEvent('cc_core:ffa:spawnPlayer', playerId, playerZone[playerId], ESX.GetPlayerRPName(killer), playerZoneType[playerId])
    
            TriggerClientEvent('cc_core:ffa:refill', killer)
            TriggerClientEvent('cc_core:ffa:updateStats', killer, data[killer])
            TriggerClientEvent('cc_core:ffa:updateStats', playerId, data[playerId])
    
            
            for k, v in pairs(data) do
                if v.zone == data[playerId].zone and v.dimension == data[playerId].dimension then
                    TriggerClientEvent('cc_core:ffa:addPlayerKill', v.playerId, ESX.GetPlayerRPName(killer), ESX.GetPlayerRPName(playerId))
                end
            end
        end
    else
        if playerZone[playerId] == nil then 
            playerZone[playerId] = false
        end
    
        if playerZoneType[playerId] == nil then 
            playerZoneType[playerId] = false
        end
    
        if type(playerZone[playerId]) ~= 'boolean' and type(playerZoneType[playerId]) ~= 'boolean' then    
            TriggerClientEvent('cc_core:ffa:spawnPlayer', playerId, playerZone[playerId], ESX.GetPlayerRPName(killer), playerZoneType[playerId])
    
            TriggerClientEvent('cc_core:ffa:refill', killer)
            
            for k, v in pairs(data) do
                if v.zone == data[playerId].zone and v.dimension == data[playerId].dimension then
                    TriggerClientEvent('cc_core:ffa:addPlayerKill', v.playerId, ESX.GetPlayerRPName(killer), ESX.GetPlayerRPName(playerId))
                end
            end
        end
    end
end)

AddEventHandler('playerDropped', function()
    local playerId = source

    if playerZone[playerId] == nil then 
        playerZone[playerId] = false
    end

    if playerZoneType[playerId] == nil then 
        playerZoneType[playerId] = false
    end

    if type(playerZone[playerId]) ~= 'boolean' and type(playerZoneType[playerId]) ~= 'boolean' then
        MySQL.Async.fetchAll('UPDATE ffa SET kills = @kills, deaths = @deaths, xp = @xp, inZone = @inZone, name = @name WHERE identifier = @identifier', {
            ['@identifier'] = data[playerId].identifier,
            ['@kills'] = data[playerId].kills,
            ['@deaths'] = data[playerId].deaths,
            ['@xp'] = data[playerId].xp,
            ['@inZone'] = true,
            ['@name'] = ESX.GetPlayerRPName(playerId)
        }, function(result)
            if playerZoneType[playerId] == 'ffa' then
                playerCount[playerZone[playerId]] = playerCount[playerZone[playerId]] - 1 
            end
        end)
    end
end)

ESX.RegisterServerCallback('cc_core:ffa:getPlayerCount', function(source, cb)
    cb(playerCount)
end)

ESX.RegisterServerCallback('cc_core:ffa:getPlayerStats', function(source, cb)
    cb(playerStats)
end)

ESX.RegisterServerCallback('cc_core:ffa:getPlayerState', function(source, cb)
    MySQL.Async.fetchAll('SELECT inZone FROM ffa WHERE identifier = @identifier', {
        ['@identifier'] = ESX.GetPlayerIdentifier(source)
    }, function(result)
        if #result ~= 0 then
            if result[1].inZone then
                MySQL.Async.execute('UPDATE ffa SET inZone = @inZone WHERE identifier = @identifier', {
                    ['@inZone'] = false,
                    ['@identifier'] = ESX.GetPlayerIdentifier(source)
                }, function()
                    cb(true)
                end)
            else
                cb(false)
            end
        end
    end)
end)

function sendToDiscord(message)
    local embeds = {
        {
            ["title"] = "» FFA × Top 10",
            ["description"] = message,
            ["type"] = "rich",
            ["author"] = {
                ["name"] = "Final Allstars",
                ["icon_url"] = "https://cdn.discordapp.com/attachments/1206998391184433152/1225119762007462018/PrudaV-Animation2.gif?ex=661ff8b0&is=660d83b0&hm=a4cae64c419e80f6d5e8fcc785515a453380b093887bedd7f9a7c178cf74da1d&",
            },
            ["footer"] = {
                ["text"] = "FFA - " .. "Final Allstars" .. " - ".. os.date("%d.%m.%y") .. " - " .. os.date("%X") .. " Uhr",
                ["icon_url"] = "",
            },
            ["thumbnail"] = {
                ["url"] = "https://cdn.discordapp.com/attachments/1206998391184433152/1225119762007462018/PrudaV-Animation2.gif?ex=661ff8b0&is=660d83b0&hm=a4cae64c419e80f6d5e8fcc785515a453380b093887bedd7f9a7c178cf74da1d&"
            }
        }
    }
    PerformHttpRequest('https://discord.com/api/webhooks/1228343155959660574/BLJJ1701B0LenL7W7Kuagr2xjiHUy0R_cOp234taOdOOss7DYZUz5lsWL-fkircp2zL-', function(err, text, headers) end, 'POST', json.encode({embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

--clientcode
ffaCode = [[
local ESX, zonee, typee, zoneconfig = nil, nil, nil, nil

local canSpawn, show = true, false
local eltablowaffe = {}

AddRelationshipGroup("enimies")

local deathCounter, maxDeathCounter = 0, 0

CreateThread(function()
    while ESX == nil do
        ESX = exports['es_extended']:getSharedObject()
        Wait(0)
    end

    Wait(10000)

    ESX.TriggerServerCallback('cc_core:ffa:getPlayerState', function(isIn)
        if isIn then
            
            SetEntityCoords(PlayerPedId(), Config_FFA.position)
        end
    end)
end)

RegisterNUICallback('ffa/escape', function(data, cb)
    SetNuiFocus(false, false)
end)

RegisterNUICallback('ffa/join', function(data, cb)
    SetNuiFocus(false, false)
    TriggerServerEvent('cc_core:ffa:joinZone', data.zone, data.type, data.password)
end)

-- RegisterCommand('quitffa', function(source)
--     if canSpawn then
--         if isInZone() then
--             RemoveAllPedWeapons(PlayerPedId(), true)
--             RemoveAllPedWeapons(PlayerPedId(), false)
--             RemoveAllPedWeapons(PlayerPedId())
            
--             Wait(200)

--             for k, v in ipairs(zoneconfig[zonee].weapons) do
--                 if HasPedGotWeapon(PlayerPedId(), GetHashKey(v.name), false) then
--                     RemoveWeaponFromPed(PlayerPedId(), GetHashKey(v.name))
--                 end

--                 for k1, v1 in pairs(v.components) do 
--                     if HasPedGotWeaponComponent(PlayerPedId(), GetHashKey(v.name), v1) then
--                         RemoveWeaponComponentFromPed(PlayerPedId(), GetHashKey(v.name), v1)
--                     end
--                 end
--             end


--             Wait(200)

--             TriggerServerEvent('cc_core:ffa:quitffa')            
--         end
--     end
-- end)

RegisterNetEvent('cc_core:ffa:updateStats')
AddEventHandler('cc_core:ffa:updateStats', function(data)
    SendNUIMessage({
        script = 'ffa',
        action = 'setStats',
        kills = data.kills,
        deaths = data.deaths,
        xp = data.xp
    })
end)

RegisterNetEvent('cc_core:ffa:addPlayerKill')
AddEventHandler('cc_core:ffa:addPlayerKill', function(killer, player)
    SendNUIMessage({
        script = 'ffa',
        action = 'addPlayerKill',
        killer = killer,
        player = player
    })
end)

RegisterNetEvent('cc_core:ffa:refill')
AddEventHandler('cc_core:ffa:refill', function()
    if isInZone() then
        SetPedArmour(PlayerPedId(), 100)
        Wait(200)
        SetEntityHealth(PlayerPedId(), 200)
    end
end)

RegisterNetEvent('cc_core:ffa:spawnPlayer')
AddEventHandler('cc_core:ffa:spawnPlayer', function(zone, name, zoneType)
    canSpawn = false
    

    if zoneType == 'ffa' then
        zoneconfig = Config_FFA.ffaZones
    elseif zoneType == 'training' then
        zoneconfig = Config_FFA.trainingZones
    elseif zoneType == 'private' then
        zoneconfig = Config_FFA.privateZones
    end

    local coords = zoneconfig[zone].spawns[math.random(#zoneconfig[zone].spawns)]
    local ped = PlayerPedId()

    if zonee == nil then
        SendNUIMessage({
            script = 'ffa',
            action = 'hud',
            show = true
        })

        zonee = zone
        typee = zoneType
        startThreads()
        SetEntityCoords(ped, coords)
        Wait(200)
        sendNotify('FFA', 'Du bist der FFA Zone ' .. zoneconfig[zonee].name .. ' beigetreten', 'info')
        giveWeapons()
        Wait(200)
        SetCurrentPedWeapon(ped, GetHashKey('WEAPON_PISTOL_MK2'), true)
        TriggerEvent('cc_core:ffa:refill')
    else
        print('ffa spawn')
        Wait(200)
        giveWeapons()

        if name ~= 'outofzone' then
            SendNUIMessage({
                script = 'ffa',
                action = 'deathscreen',
                show = true
            })

            SendNUIMessage({
                script = 'ffa',
                action = 'setKillerName',
                name = name,
                time = 3
            })
        end

        if name ~= 'outofzone' then
            Wait(3 * 1000)
        end

        RespawnPed(ped, coords, 0.0)
        TriggerEvent("cc_core:jobpack:revive")

        SendNUIMessage({
            script = 'ffa',
            action = 'deathscreen',
            show = false
        })

        startSpawnProtection()
        NetworkSetFriendlyFireOption(false)
        SetCanAttackFriendly(PlayerPedId(), false, false)
        
        if eltablowaffe ~= nil then
            SetCurrentPedWeapon(ped, eltablowaffe.name, true)
            eltablowaffe = nil
        end 
        
        sendNotify('FFA', 'Spawnschutz aktiviert', 'info')

        TriggerEvent('cc_core:ffa:refill')
    end

    canSpawn = true
end)

RegisterNetEvent('cc_core:ffa:quitffa')
AddEventHandler('cc_core:ffa:quitffa', function()
    
    SendNUIMessage({
        script = 'ffa',
        action = 'hud',
        show = false
    })

    sendNotify('FFA', 'Du hast die FFA Zone ' .. zoneconfig[zonee].name .. ' verlassen', 'info')
    Wait(200)
    removeWeapons()
    Wait(200)

    if typee == 'training' then
        for k, v in pairs(zoneconfig[zonee].npcspawns) do
            if v.spawned then
                local ped = v.spawnedPed
                v.spawnedPed = nil
                DeleteEntity(ped)
                v.spawned = false
                v.death = false
            end
        end 
    end

    Wait(200)
    zonee = nil
    typee = nil
    SetEntityCoords(PlayerPedId(), Config_FFA.position)
    SetPedArmour(PlayerPedId(), 0)
    Wait(200)
    SetEntityHealth(PlayerPedId(), 200)
end)

-- CreateThread(function()
--     local blip = AddBlipForCoord(Config_FFA.position)
--     SetBlipSprite(blip, 84)
--     SetBlipDisplay(blip, 4)
--     SetBlipScale(blip, 0.8)
--     SetBlipColour(blip, 0)
--     SetBlipAsShortRange(blip, true)
--     BeginTextCommandSetBlipName('STRING')
--     AddTextComponentString("FFA - Paintball")
--     EndTextCommandSetBlipName(blip)
-- end)

-- CreateThread(function()
--     while true do
--         Wait(0)
--         local letSleep, inRange = true, false
--         local ped = PlayerPedId()
--         local coords = GetEntityCoords(ped)
--         local distance = #(coords - Config_FFA.position)

--         if distance <= 20.0 then
--             letSleep = false

--             DrawMarker(31, Config_FFA.position, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 255, 156, 35, 140, true, false, false, false, false, false, false)
--         end

--         if distance <= 1.0 then
--             inRange = true

--             if IsControlJustPressed(0, 38) then
--                 SetNuiFocus(true, true)
--                 SendNUIMessage({
--                     script = 'ffa',
--                     action = 'show'
--                 })

--                 SendNUIMessage({
--                     script = 'ffa',
--                     action = 'clearZones'
--                 })

--                 ESX.TriggerServerCallback('cc_core:ffa:getPlayerCount', function(playerCount)
--                     for k, v in pairs(Config_FFA.ffaZones) do
--                         SendNUIMessage({
--                             script = 'ffa',
--                             action = 'addFFAZone',
--                             zone = k,
--                             type = 'ffa',
--                             name = v.name,
--                             maxPlayers = v.maxPlayers,
--                             currentPlayers = playerCount[k],
--                             label = v.label,
--                             imgUrl = v.imgUrl
--                         })
--                     end
--                 end)

--                 for k, v in pairs(Config_FFA.privateZones) do
--                     SendNUIMessage({
--                         script = 'ffa',
--                         action = 'addPrivateZone',
--                         zone = k,
--                         type = 'private',
--                         name = v.name,
--                         label = v.label,
--                         imgUrl = v.imgUrl
--                     })
--                 end

--                 for k, v in pairs(Config_FFA.trainingZones) do
--                     SendNUIMessage({
--                         script = 'ffa',
--                         action = 'addTrainingZone',
--                         zone = k,
--                         type = 'training',
--                         name = v.name,
--                         label = v.label,
--                         imgUrl = v.imgUrl
--                     })
--                 end

--                 ESX.TriggerServerCallback('cc_core:ffa:getPlayerStats', function(playerStats)
--                     for k, v in pairs(playerStats) do
--                         SendNUIMessage({
--                             script = 'ffa',
--                             action = 'addPlayerToStats',
--                             id = k,
--                             name = v.name,
--                             kills = v.kills,
--                             deaths = v.deaths
--                         })
--                     end
--                 end)
--             end
--         end

-- 		if not show and inRange then
-- 			exports['cc_core']:showHelpNotification('Drücke E um eine Zone beizutreten', 'E')
-- 			show = true
-- 		elseif show and not inRange then
-- 			exports['cc_core']:closeHelpNotification()
-- 			show = false
-- 		end

--         if letSleep then
--             Wait(1000)
--         end
--     end
-- end)

function RespawnPed(ped, coords, heading)
    SetEntityCoordsNoOffset(ped, coords.x, coords.y, coords.z, false, false, false, true)
    NetworkResurrectLocalPlayer(coords.x, coords.y, coords.z, heading, true, false)
    SetPlayerInvincible(ped, false)
    TriggerEvent('playerSpawned', coords.x, coords.y, coords.z)
    ClearPedBloodDamage(ped)
end

function isInZone()
    if zonee ~= nil then
        return true
    else
        return false
    end
end

exports('isInZone', isInZone)

function removeWeapons()
    for k, v in pairs(zoneconfig[zonee].weapons) do
        SetPedInfiniteAmmo(PlayerPedId(), false, v.name)
    end

    Wait(200)
    TriggerEvent("esx:restoreLoadout")
end

function startSpawnProtection()
    CreateThread(function()
        nw = true
        Wait(3 * 1000)
        
        if nw then
            sendNotify('FFA', 'Spawnschutz deaktiviert', 'info')
        end
        
        nw = false
        NetworkSetFriendlyFireOption(true)
        SetCanAttackFriendly(PlayerPedId(), true, true)
    end)
end

function giveWeapons()
    local ped = PlayerPedId()
    SetPedArmour(ped, 100)
    SetEntityHealth(ped, 100)
    RemoveAllPedWeapons(ped, true)
    Wait(1000)
    if zonee ~= nil then
        for k, v in pairs(zoneconfig[zonee].weapons) do
            TriggerEvent('cc_anticheat:weapons:check')
            GiveWeaponToPed(ped, GetHashKey(v.name), 9999, true, false)
            SetAmmoInClip(ped, GetHashKey(v.name), 999)
            SetPedInfiniteAmmo(PlayerPedId(), true, v.name)

            for k1, v1 in pairs(v.components) do 
                GiveWeaponComponentToPed(ped, GetHashKey(v.name), v1)
            end
        end
    end
end

function startThreads()
    CreateThread(function()
        while zonee ~= nil do
            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            local distance = #(coords - zoneconfig[zonee].position)

            DrawSphere(zoneconfig[zonee].position.x, zoneconfig[zonee].position.y, zoneconfig[zonee].position.z, zoneconfig[zonee].size, 0, 111, 255, 0.5)
        
            if distance >= zoneconfig[zonee].size then
                TriggerEvent('cc_core:ffa:spawnPlayer', zonee, 'outofzone')
                Wait(3 * 1000 * 1.1)
            end
            
            Wait(0)
        end
    end)

    CreateThread(function()
        local isDead = false

        while zonee ~= nil do
            Wait(0)
            local player = PlayerId()

            if NetworkIsPlayerActive(player) then
                local ped = PlayerPedId()
                
                if IsPedFatallyInjured(ped) and not isDead then
                    isDead = true
                
                    local killerEntity, deathCause = GetPedSourceOfDeath(ped), GetPedCauseOfDeath(ped)
                    local killerClientId = NetworkGetPlayerIndexFromPed(killerEntity)

                    if GetSelectedPedWeapon(ped) ~= GetHashKey('WEAPON_UNARMED') then
                        local _, weaponHash = GetCurrentPedWeapon(ped, true)
                        local weapon = ESX.GetWeaponFromHash(weaponHash)
                        eltablowaffe = weapon
                    end

                    if killerEntity ~= ped and killerClientId and NetworkIsPlayerActive(killerClientId) then
                        if typee ~= 'training' then
                            if typee == 'ffa' then
                                TriggerServerEvent("cc_core:ffa:addKill", GetPlayerServerId(killerClientId), true)
                                Wait(3 * 1000 * 1.1) 
                            else
                                TriggerServerEvent("cc_core:ffa:addKill", GetPlayerServerId(killerClientId), false)
                                Wait(3 * 1000 * 1.1)  
                            end
                        end
                    else
                        if typee ~= 'training' then
                            TriggerServerEvent("cc_core:ffa:selfKill")
                            Wait(3 * 1000 * 1.1)
                        else
                            TriggerServerEvent('cc_core:ffa:selfKill_T')

                            for k, v in pairs(zoneconfig[zonee].npcspawns) do
                                if v.spawned then
                                    local ped = v.spawnedPed
                                    v.spawnedPed = nil
                                    DeleteEntity(ped)
                                end
                            end

                            Wait(3 * 1000 * 1.1)

                            Wait(1500)

                            if zonee ~= nil then
                                for k, v in pairs(zoneconfig[zonee].npcspawns) do
                                    if v.spawnedPed == nil then
                                        v.spawned = false
                                        v.death = false
                                    end
                                end
                            end
                        end
                    end
                elseif not IsPedFatallyInjured(ped) then
                    isDead = false
                else
                    Wait(200)
                end
            end
        end
    end)

    CreateThread(function()
        while zonee ~= nil do 
            Wait(0)
            
            if nw then 
                DisableControlAction(0, 69) -- INPUT_VEH_ATTACK
                DisableControlAction(0, 70) -- INPUT_VEH_ATTACK2
                DisableControlAction(0, 92) -- INPUT_VEH_PASSENGER_ATTACK
                DisableControlAction(0, 114) -- INPUT_VEH_FLY_ATTACK
                DisableControlAction(0, 257) -- INPUT_ATTACK2
                DisableControlAction(0, 331) -- INPUT_VEH_FLY_ATTACK2

                if IsControlJustPressed(0, 24) then
                    nw = false
                    NetworkSetFriendlyFireOption(true)
                    SetCanAttackFriendly(PlayerPedId(), true, true)
                    sendNotify('FFA', 'Spawnschutz deaktiviert', 'info')
                end
            else 
                Wait(500)
            end
        end 
    end)

    CreateThread(function()
        while zonee ~= nil do
            Wait(0)

            if typee == 'training' then
                if maxDeathCounter == 0 then
                    maxDeathCounter = #zoneconfig[zonee].npcspawns
                end
                for k, v in pairs(zoneconfig[zonee].npcspawns) do
                    if not v.spawned then
                        v.spawned = true
    
                        while not HasModelLoaded(GetHashKey(v.ped)) do
                            RequestModel(GetHashKey(v.ped))
                            Wait(1)
                        end

                        local ped = CreatePed(4, GetHashKey(v.ped), v.coords.x, v.coords.y, v.coords.z - 1.0, v.coords.w, false, true)
                        
                        v.spawnedPed = ped
                        SetPedCombatAttributes(ped, 5, true)
                        SetPedCombatAttributes(ped, 46, true)
                        SetPedFleeAttributes(ped, 0, true)
        
                        SetPedRelationshipGroupHash(ped, GetHashKey("enimies"))
                        SetRelationshipBetweenGroups(5, GetHashKey("enimies"), GetHashKey("PLAYER"))
                        SetPedAccuracy(ped, v.accuracy)
                        SetPedShootRate(ped, v.shootRate)
                        TriggerEvent('cc_anticheat:weapons:check')
                        GiveWeaponToPed(ped, GetHashKey(v.weapon), 2000, false, true)
                        SetCurrentPedWeapon(ped, GetHashKey(v.weapon), true)
                        SetPedArmour(ped, 100)
                        SetPedMaxHealth(ped, 100)
                        SetPedInfiniteAmmoClip(ped, true)
                        SetPedCanRagdoll(ped, false)
                        SetPedMoveRateOverride(ped, 10.0)
                        SetPedSuffersCriticalHits(ped, v.oneshot)
                        FreezeEntityPosition(ped, v.freeze)

                        if k == maxDeathCounter then
                            deathCounter = 0
                        end
                    else
                        if not v.death then
                            if v.spawnedPed then
                                if GetEntityHealth(v.spawnedPed) <= 0 then
                                    v.death = true
                                    deathCounter = deathCounter + 1
                                end 
                            end
                        end

                        if v.death then
                            if deathCounter >= maxDeathCounter then
                                v.death = false
                                v.spawned = false
                                DeleteEntity(v.spawnedPed)
                                TriggerEvent('cc_core:ffa:refill')
                            end 
                        end
                    end
                end 
            else
                Wait(500)
            end
        end
    end)
end
]]