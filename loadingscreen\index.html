<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://kit.fontawesome.com/96a2c6323b.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <title>by cscripts</title>
</head>
<body>
<style>
    @import url("https://db.onlinewebfonts.com/c/5dc6fc5a874b27ff1c287c19dc30bf1b?family=Arame-Mono");
    @import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        user-select: none;
        -webkit-user-select: none;
    }

    body {
        font-size: 2vh;
        color: var(--clr-white);
        font-weight: 600;
        font-family: Rajdhani;
        overflow: hidden;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .main__loadingscreen-container {
        display: flex;
        align-items: center;
        justify-content: center;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.541);
        transition: opacity .5s;
    }

    .main__loadingscreen-grid {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }

    .main__loadingscreen-grid-logo-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        flex-basis: 20%;
        margin: 0 5vw;
    }

    .main__loadingscreen-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        filter: blur(.5vw);
    }
    
    .main__loadingscreen-bg.unblur {
        filter: none;
    }

    .main__loadingscreen-top {
        display: flex;
        position: absolute;
        top: 3%;
        user-select: none;
    }

    .main__loadingscreen-top p {
        line-height: 0.5vh;
        color: rgba(255, 255, 255, .7);
        font-size: .6vw;
        font-family: Arame-Mono;
    }

    .main__loadingscreen-teampage {
        position: absolute;
        background: 0 0;
        width: 155.2vh;
        height: 46vh;
        top: 30%;
        font-size: 4.4vh;
        color: #fff;
        overflow: hidden;
        text-align: center;
        text-shadow: 0 0 1.2vh rgba(255, 255, 255, 0.308);
        white-space: nowrap;
    }
    
    .main__loadingscreen-teampage-scroll-buttons {
        position: relative;
        width: 4vh;
        height: 4vh;
        top: 4vh;
        font-size: 1.2vh;
        margin-left: 0.4vh;
        border-radius: .25vh;
        background: none;
        border: .1vh solid rgba(255,255,255,.15);
        box-shadow: 0 0 3.7vh #ffffff26 inset,0 .4vh 5.6vh #ffffff26;
        padding: 1vh;
        cursor: pointer;
        transition: border .2s ease-in,box-shadow .2s ease-in,background-color .2s ease-in;
        color: white;
    }
    
    .main__loadingscreen-teampage-scroll-buttons:hover {
        box-shadow: 0 0 3vh #fff inset,0 0 1.7vh #c11717;
        background: #9c1414;
        border: .1vh solid transparent
    }
    
    .main__loadingscreen-teampage-scroll-item,
    .main__loadingscreen-teampage-title-line,
    input[type="range"] {
        position: relative;
    }
    
    .disabled {
        opacity: 0.6;
        pointer-events: none;
    }
    
    .main__loadingscreen-teampage-title-line {
        display: inline-block;
        width: 59vh;
        height: 0.16vh;
        top: -1vh;
        left: -2vh;
        margin-left: 4vh;
        background: linear-gradient(to right, #fcfcfc00, #adadad);
    }
    
    .main__loadingscreen-teampage-line-dot {
        position: absolute;
        width: 0.74vh;
        height: 0.74vh;
        top: -0.24vh;
        border-radius: 50%;
        background: #fff;
    }
    
    .main__loadingscreen-teampage-scroll-item {
        display: inline-block;
        width: 24vh;
        height: 30vh;
        top: 2.4vh;
        margin-left: 1.8vh;
        border-radius: 1.2vh;
        line-height: 2vh;
        font-size: 2.4vh;
        padding-top: 3.4vh;
        text-shadow: none;
        border-radius: .4vh;
        border: .1vh solid rgba(255, 255, 255, .15);
        box-shadow: 0vh 0vh 2.7vh rgba(255, 255, 255, .15) inset;
        transition: border .2s ease-in,box-shadow .2s ease-in,background-color .2s ease-in
    }

    .main__loadingscreen-teampage-scroll-item:hover {
        background-color: rgba(0, 0, 0, .5);
        border: .1vh solid #ff0000;
        color: rgba(255, 255, 255);
        box-shadow: 0vh 0vh 1.7vh #ff0000 inset;
    }
    
    .main__loadingscreen-teampage-scroll-item-img {
        position: absolute;
        width: 14vh;
        height: 14vh;
        left: 50%;
        top: 18vh;
        background-image: url("img/background2.png");
        transform: translate(-50%, -50%);
        border-radius: 50%;
        background-size: cover;
        background-repeat: no-repeat;
        background-position-x: 50%;
        filter: drop-shadow(0vh 0vh 1.44vh #fe1313);
    }

    .page {
        position: absolute;
        width: 100%;
        height: 100%;
        visibility: hidden;
        transform: translateX(100%);
        opacity: 0;
        transition: transform 0.6s ease, opacity 0.6s ease;
        z-index: 0;
    }
    
    .page.active {
        visibility: visible;
        transform: translateX(0);
        opacity: 1;
        z-index: 2;
    }
    
    .page.slide-in-from-left {
        transform: translateX(-65%);
    }
    
    .page.slide-in-from-right {
        transform: translateX(65%);
    }
    
    .page.slide-out-to-left {
        transform: translateX(-70%);
        opacity: 0;
        visibility: hidden;
    }
    
    .page.slide-out-to-right {
        transform: translateX(65%);
        opacity: 0;
        visibility: hidden;
    }

    .main__loadingscreen-bottom-information-container {
        position: absolute;
        bottom: 4vh;
        left: 4vh;
        width: 30vh;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2vh;
    }
    
    .main__loadingscreen-bottom-information-item {
        display: flex;
        align-items: center;
        gap: 1vh;
    }
    
    .main__loadingscreen-bottom-information-item-icon {
        font-size: 2vh;
        color: white;
    }
    
    .main__loadingscreen-bottom-information-item-text {
        color: white;
        font-size: 1.4vh;
    }
    
    .main__loadingscreen-bottom-information-item-text p:nth-last-child(1) {
        color: rgba(255, 255, 255, .5);
        font-size: 1.2vh;
    }

    .main__loadingscreen-teampage-scroll-container {
        display: flex;
        justify-content: center;
        flex-wrap: nowrap;
        margin-top: 2vh;
        gap: 1.5vh;
    }

    .main__loadingscreen-gallery-container {
        position: absolute;
        margin-top: -45%;
        margin-left: -65%;
        transform: translate(-50%, -50%);
        width: 65vw;
        height: auto;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .main__loadingscreen-gallery-container.active {
        visibility: visible;
        opacity: 1;
        transform: translateX(0);
        z-index: 2;
    }

    .main__loadingscreen-gallery-item {
        background-size: cover;
        background-repeat: no-repeat;
        background-position-y: 50%;
        border-radius: 0.8vh;
    }

    section {
        width: 70%;
        margin: 0 auto;
        line-height: 0;
    }

    section article {
        position: absolute;
        top: 10vh;
        width: 100%;
        background: #ffffff2c;
        display: grid;
        grid-template-columns: 10fr 5.4fr 5.4fr;
        grid-column-gap: 1.2vh;
        grid-row-gap: 1.2vh;
        padding: 1.4vh 1.4vh 0;
        border-radius: 1.4vh;
    }

    section div {
        background: #000;
        height: 25.3vw;
    }

    section div:first-of-type,
    section div:nth-of-type(2),
    section div:nth-of-type(6) {
        height: 18vh;
    }

    section div:nth-of-type(3) {
        height: 26vh;
    }

    section div:nth-of-type(4) {
        margin-top: -8vh;
        height: 40vh;
    }

    section div:nth-of-type(5) {
        margin-top: -8vh;
        height: 26vh;
    }

    section div:nth-of-type(7) {
        margin-top: -14vh;
        grid-column: 2/4;
        height: 12.6vh;
    }
</style>
<main>
    <video class="main__loadingscreen-bg" id="background-video" autoplay loop>
        <source src="img/loading.mp4" type="video/mp4">
    </video>

    <div class="main__loadingscreen-container">
        <!-- FINALCITY STANDARTPAGE 100% -->
        <div class="main__loadingscreen-grid" id="standardPage">
            <div class="main__loadingscreen-grid-logo-container">
                <img src="img/staticlogo.png" alt="">
            </div>
        </div>
        <div class="main__loadingscreen-top">
            <p>DRÜCKE SPACEBAR UM DIE MUSIK ZU STUMMEN</p>
        </div>
        <!-- FINALCITY STANDARTPAGE 100% -->

        <div class="main__loadingscreen-bottom-information-container">
            <div class="main__loadingscreen-bottom-information-item">
                <div class="main__loadingscreen-bottom-information-item-text">
                    <p>Page wechseln</p>
                    <p>Linke/Rechte Pfeil Taste</p>
                </div>
            </div>
        </div>

        <!-- FINAL CITY TEAMPAGE 100% -->
        <div class="main__loadingscreen-teampage" id="teamPage">
            <div class="main__loadingscreen-teampage-title-line">
                <div class="main__loadingscreen-teampage-line-dot" style="right:0vh;"></div>
            </div>
            <span class="main__loadingscreen-teampage-title">SERVER TEAM</span>
            <div class="main__loadingscreen-teampage-title-line" style="background: linear-gradient(to right, #adadad, #adadad00)">
                <div class="main__loadingscreen-teampage-line-dot"></div>
            </div>
            <p id="currentRole" style="font-size: 2vh; margin-top: 1vh; color: #ccc;"></p>
            <div class="main__loadingscreen-teampage-scroll-container" id="teamContainer"></div>
            <button class="main__loadingscreen-teampage-scroll-buttons disabled" id="left-arrow" onclick="previous()">
                <i class="fa-regular fa-chevron-left"></i>
            </button>
            <button class="main__loadingscreen-teampage-scroll-buttons" id="right-arrow" onclick="next()">
                <i class="fa-regular fa-chevron-right"></i>
            </button>
        </div>
        <!-- FINAL CITY TEAMPAGE 100% -->

        <!-- FINAL CITY GALARIE 100% -->
        <div class="main__loadingscreen-gallery-container page" id="galleryPage">
            <div class="main__loadingscreen-gallery-item-container">
            <section>
                <article>
                    <div class="main__loadingscreen-gallery-item" style="background-image:url(img/background2.png)"></div>
                    <div class="main__loadingscreen-gallery-item" style="background-image:url(img/2.png)"></div>
                    <div class="main__loadingscreen-gallery-item" style="background-image:url(img/3.png)"></div>
                    <div class="main__loadingscreen-gallery-item" style="background-image:url(img/4.png)"></div>
                    <div class="main__loadingscreen-gallery-item" style="background-image:url(img/6.png)"></div>
                    <div class="main__loadingscreen-gallery-item" style="background-image:url(img/7.png)"></div>
                    <div class="main__loadingscreen-gallery-item" style="background-image:url(img/background.png)"></div>
                </article>
            </section>
            </div>
        </div>
        <!-- FINAL CITY GALARIE 100% -->
    </div>
</main>
<script>
    const teamMembers = [
        {
            name: "089tiziano",
            role: "Projektleitung",
            image: "img/logo.gif",
            color: "red"
        },
        {
            name: "jamal.toss",
            role: "Projektleitung",
            image: "img/logo.gif",
            color: "red"
        },
        {
            name: "katana.cc",
            role: "Stv.Projektleitung",
            image: "img/logo.gif",
            color: "red"
        },
        {
            name: "khanda.cc",
            role: "Stv.Projektleitung",
            image: "img/logo.gif",
            color: "red"
        },
        {
            name: "b4raa0",
            role: "Projektverwaltung",
            image: "img/logo.gif",
            color: "red"
        },
        {
            name: ".bekasabi",
            role: "Projektverwaltung",
            image: "img/logo.gif",
            color: "red"
        },
        {
            name: "ya7u",
            role: "Projektverwaltung",
            image: "img/logo.gif",
            color: "red"
        },
        {
            name: "nerv.nicht",
            role: "Stv.Projektverwaltung",
            image: "img/logo.gif",
            color: "red"
        },
        {
            name: "xmani777",
            role: "Discord-Entwickler",
            image: "img/logo.gif",
            color: "red"
        },
    ];

    
    const itemsPerPage = 4;
    let currentIndex = 0;
    let currentRoleIndex = 0;

    const groupedMembers = groupByRole(teamMembers);
    const roles = Object.keys(groupedMembers);

    const standardPage = document.getElementById('standardPage');
    const teamPage = document.getElementById('teamPage');
    const galleryPage = document.querySelector('.main__loadingscreen-gallery-container');

    standardPage.classList.add('page', 'active');
    teamPage.classList.add('page', 'hidden');

    let currentPage = 'standard';

    const pages = ['gallery', 'standard', 'team'];
    let currentPageIndex = 1;
    
    function updatePages() {
        pages.forEach((name, index) => {
            const pageEl = document.getElementById(`${name}Page`);
            if (index === currentPageIndex) {
                pageEl.classList.add('active');
                pageEl.classList.remove('slide-out-to-left', 'slide-out-to-right', 'slide-in-from-left', 'slide-in-from-right');
            } else {
                pageEl.classList.remove('active');
            }
        });
    }
    
    function slideToPage(newIndex) {
        if (newIndex < 0 || newIndex >= pages.length || newIndex === currentPageIndex) return;
    
        const currentPageId = `${pages[currentPageIndex]}Page`;
        const newPageId = `${pages[newIndex]}Page`;
        const currentEl = document.getElementById(currentPageId);
        const newEl = document.getElementById(newPageId);
    
        const direction = newIndex > currentPageIndex ? 'right' : 'left';
        currentEl.classList.add(`slide-out-to-${direction}`);
        newEl.classList.add(`slide-in-from-${direction === 'right' ? 'left' : 'right'}`);
        newEl.classList.add('active');
    
        setTimeout(() => {
            currentEl.classList.remove('active', `slide-out-to-${direction}`);
            newEl.classList.remove(`slide-in-from-${direction === 'right' ? 'left' : 'right'}`);
            currentPageIndex = newIndex;
        }, 150);
    }

    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
            slideToPage(currentPageIndex - 1);
        } else if (e.key === 'ArrowRight') {
            slideToPage(currentPageIndex + 1);
        }
    });

    function createTeamCard(member) {
        const card = document.createElement("div");
        card.className = "main__loadingscreen-teampage-scroll-item";
        card.innerHTML = `
            <span class="main__loadingscreen-teampage-scroll-item-team-name" style="color: ${member.color}">${member.name}</span>
            <br>
            <span style="font-size:1.6vh;color:#929292;">${member.role}</span>
            <div class="main__loadingscreen-teampage-scroll-item-img" style="background-image:url('${member.image}')"></div>
        `;
        return card;
    }

    function renderTeamMembers() {
        const container = document.getElementById("teamContainer");
        container.innerHTML = "";
    
        const currentRole = roles[currentRoleIndex];
        const members = groupedMembers[currentRole];
    
        const titleElement = document.querySelector("#currentRole");
        titleElement.innerText = `${currentRole.toUpperCase()}`;
    
        const currentMembers = members.slice(currentIndex, currentIndex + itemsPerPage);
        currentMembers.forEach(member => {
            container.appendChild(createTeamCard(member));
        });
    
        document.getElementById("left-arrow").classList.toggle("disabled", currentIndex === 0 && currentRoleIndex === 0);
        document.getElementById("right-arrow").classList.toggle("disabled", 
            currentIndex + itemsPerPage >= members.length && currentRoleIndex === roles.length - 1
        );
    }

    function next() {
        const currentRole = roles[currentRoleIndex];
        const members = groupedMembers[currentRole];
    
        if (currentIndex + itemsPerPage < members.length) {
            currentIndex += itemsPerPage;
        } else if (currentRoleIndex + 1 < roles.length) {
            currentRoleIndex++;
            currentIndex = 0;
        }
        renderTeamMembers();
    }
    
    function previous() {
        if (currentIndex - itemsPerPage >= 0) {
            currentIndex -= itemsPerPage;
        } else if (currentRoleIndex > 0) {
            currentRoleIndex--;
            const prevRole = roles[currentRoleIndex];
            const prevMembers = groupedMembers[prevRole];
            currentIndex = Math.max(0, prevMembers.length - itemsPerPage);
        }
        renderTeamMembers();
    }

    function groupByRole(members) {
        const grouped = {};
        members.forEach(member => {
            if (!grouped[member.role]) {
                grouped[member.role] = [];
            }
            grouped[member.role].push(member);
        });
        return grouped;
    }

    renderTeamMembers();
    updatePages();
</script>
    <script src="js/script.js"></script>
</body>
</html>