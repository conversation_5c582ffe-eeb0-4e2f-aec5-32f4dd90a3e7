ESX = exports["es_extended"]:getSharedObject()

RegisterNetEvent('cc_core:multichar:getCharData', function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)

    if not xPlayer then return end

    local result = MySQL.single.await('SELECT visa, firstname, lastname, timePlay FROM users WHERE identifier = ?', {
        xPlayer.identifier
    })

    local job = xPlayer.getJob()
    local jobLabel = job and job.label or "Arbeitslos"
    local jobGradeLabel = (job and job.grade_label) and job.grade_label or tostring(job and job.grade or 0)
    local fullName = result and (result.firstname .. " " .. result.lastname) or "Unbekannt"

    local charData = {
        money = xPlayer.getMoney(),
        bank = xPlayer.getAccount('bank').money,
        name = fullName,
        visum = result and result.visa or "Kein Visum",
        joblabel = jobLabel,
        jobgrade = jobGradeLabel,
        playtime = result and result.playtime or 0
    }

    TriggerClientEvent('cc_core:multichar:sendCharData', src, charData)
end)

--clientcode
multicharCode = [[
local lastPosition = nil
local isInMultichar = false
local cam = nil
local currentBtn2State = false

RegisterNetEvent('cc_core:multichar:sendCharData')
AddEventHandler('cc_core:multichar:sendCharData', function(data)
    SendNUIMessage({
        script = "multichar",
        action = "show",
        money = ("%s$"):format(data.money),
        bank = ("%s$"):format(data.bank),
        name = data.name,
        visum = data.visum,
        joblabel = data.joblabel,
        jobgrade = data.jobgrade,
        btn2 = currentBtn2State,
        playtime = data.playtime
    })
end)

RegisterNetEvent('cc_core:multichar:showmultichar')
AddEventHandler('cc_core:multichar:showmultichar', function(btn2State)
    if exports['cc_core']:isInCharCreator() then
        return
    end

    currentBtn2State = btn2State

    local ped = PlayerPedId()

    if lastPosition == nil then
        lastPosition = GetEntityCoords(ped)
    end

    DoScreenFadeOut(0)

    if btn2State then
        exports.spawnmanager:spawnPlayer({
            x = -605.7002,
            y = 46.1824,
            z = 97.4991,
            heading = 90.0,
            model = "mp_m_freemode_01",
            skipFade = true
        }, function()
            SetEntityVisible(PlayerPedId(), true, 0)
            SetPedDefaultComponentVariation(PlayerPedId())
            FinishMulticharSetup()
        end)
    else
        SetEntityCoords(ped, -605.7002, 46.1824, 97.4991, false, false, false, true)
        SetEntityHeading(ped, 190.6434)
        FinishMulticharSetup()
    end
end)

function FinishMulticharSetup()
    Wait(200)
    LoadPlayerAnimation()
    DoScreenFadeIn(4000)

    local playerPed = PlayerPedId()
    cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
    local offset = GetOffsetFromEntityInWorldCoords(playerPed, 0, 1.7, 0.4)
    SetCamActive(cam, true)
    RenderScriptCams(true, false, 1, true, true)
    SetCamCoord(cam, offset.x, offset.y, offset.z)
    PointCamAtCoord(cam, -605.7002, 46.1824, 97.4991)

    SetNuiFocus(true, true)
    hideHud(true)

    TriggerServerEvent('cc_core:multichar:getCharData')
end

RegisterNUICallback('multichar/selectchar', function(data, cb)
    local ped = PlayerPedId()

    RemovePlayerAnimation()
    Wait(250)

    while IsPlayerSwitchInProgress() do
        Citizen.Wait(100)
    end

    hideHud(false)
    SetNuiFocus(false, false)
    RenderScriptCams(false, true, 2000, false, false)
    SetCamActive(cam, false)
    DestroyCam(cam, true)
    DisplayRadar(true)
    FreezeEntityPosition(ped, false)

    if currentBtn2State then
        local einreiseCoords = vector3(-3702.9390, -3622.0706, 7.6143)
        local einreiseHeading = 267.5453
        SetEntityCoords(ped, einreiseCoords.x, einreiseCoords.y, einreiseCoords.z, false, false, false, true)
        SetEntityHeading(ped, einreiseHeading)
        TriggerServerEvent('cc_core:tiziano:dupic')
    elseif lastPosition then
        SetEntityCoords(ped, lastPosition.x, lastPosition.y, lastPosition.z, false, false, false, true)
        lastPosition = nil
        TriggerServerEvent('cc_core:tiziano:dupic')
    end

    cb('ok')
end)

RegisterNUICallback('multichar/createchar', function(data, cb)
    RemovePlayerAnimation()
    TriggerEvent('cc_core:identity:showIdentity')
end)

-- AddEventHandler('playerSpawned', function()
--     if exports['cc_core']:isInCharCreator() then
--         return
--     end

--     local ped = PlayerPedId()

--     if lastPosition == nil then
--         lastPosition = GetEntityCoords(ped)
--     end
    
--     if not isInMultichar then
--         isInMultichar = true
--         TriggerEvent("cc_core:multichar:showmultichar", false)
--     end
-- end)

function LoadPlayerAnimation()
    local dict, anim = "timetable@ron@ig_5_p3", "ig_5_p3_base"
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
    end
    TaskPlayAnim(PlayerPedId(), dict, anim, 8.0, 8.0, -1, 1, 0.0, false, false, false)
end

function RemovePlayerAnimation()
    local dict = "timetable@ron@ig_5_p3"
    RemoveAnimDict(dict)
end
]]