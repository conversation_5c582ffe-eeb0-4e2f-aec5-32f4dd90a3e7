local Dumps = {}

-- ESX.RegisterServerCallback('cc_core:garage:spawnVehicle', function(source, cb, x, y, z, w, model)
--     local attempt = 0
--     local vehicle = CreateVehicle(model, x, y, z, w, true, false)
    
--     while not DoesEntityExist(vehicle) and attempt <= 5 do
--         Citizen.Wait(100)
--         attempt = attempt + 1
--     end

--     if vehicle ~= nil then
--         cb(NetworkGetNetworkIdFromEntity(vehicle))
--     else
--         cb(0) 
--     end
-- end)

MySQL.ready(function()
    MySQL.Async.fetchAll('SELECT plate FROM owned_vehicles WHERE stored = @stored', {
        ['@stored'] = false
    }, function(result)
        local plates = {}

        for k, v in pairs(result) do
            local plate = v.plate
            
            plates[plate] = true
        end

        Citizen.Wait(2000)

        MySQL.Async.execute('UPDATE owned_vehicles SET `stored` = 1 WHERE `stored` = @stored', {
            ['@stored'] = 0
        }, function(rowsChanged)
            if rowsChanged > 0 then
                print(('cc_core:garage %s vehicle(s) have been stored!'):format(rowsChanged))
            end
        end)
    end)

    --CLS Shit 20.07 06:00
    MySQL.Async.execute('UPDATE owned_vehicles SET plate = UPPER(plate)', function(affectedRows)
        print('Fixxed: '..affectedRows..' Bugged Plates!')
    end)
end)

AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    if Dumps[playerId] == nil then
        Dumps[playerId] = false
    end

    if not Dumps[playerId] then
        local ownedCars = {}
        Citizen.Wait(math.random(6500, 10000))
    
        MySQL.Async.fetchAll('SELECT `vehicle`, `stored`, `plate`, `nickname`, `fav`, `type`, `job`, `tuningData`, `status` FROM owned_vehicles WHERE `owner` = @owner', {
            ['@owner'] = xPlayer.getIdentifier()
        }, function(result)
            if #result ~= 0 then
                for k, v in pairs(result) do
                    local vehicle = json.decode(v.vehicle)
                    local tuningData = json.decode(v.tuningData)
                    table.insert(ownedCars, {
                        vehicle = vehicle,
                        stored = v.stored,
                        plate = v.plate,
                        nickname = v.nickname,
                        type = v.type,
                        job = v.job,
                        fav = v.fav,
                        tuningData = tuningData,
                        status = v.status
                    })
                    if tuningData ~= nil then
                        if tuningData['nos'] ~= nil then
                            TriggerClientEvent('cc_core:garage:updateTuning', playerId, v.plate, tuningData)
                        end
                    end
                end
                TriggerClientEvent('cc_core:garage:loadVehicles', playerId, ownedCars)
            end
        end)
        
        Dumps[playerId] = true
    end
end)

RegisterServerEvent('cc_core:garage:changeState')
AddEventHandler('cc_core:garage:changeState', function(plate, state, status)
    local playerId = source

    MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored, status = @status WHERE plate = @plate AND owner = @owner', {
        ['@plate'] = plate,
        ['@stored'] = state,
        ['@status'] = status,
        ['@owner'] = ESX.GetPlayerIdentifier(playerId)
    })
end)
RegisterServerEvent('cc_core:garage:changeStateciv')
AddEventHandler('cc_core:garage:changeStateciv', function(plate, state, status)
    local playerId = source

    MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored WHERE plate = @plate AND owner = @owner', {
        ['@plate'] = plate,
        ['@stored'] = state,
        ['@owner'] = ESX.GetPlayerIdentifier(playerId)
    })
end)

ESX.RegisterServerCallback('cc_core:garage:changeState', function(source, cb, plate, state)
    local playerId = source

    if not state then
    	for k, v in pairs(GetAllVehicles()) do
            local vPlate = ESX.Math.Trim(GetVehicleNumberPlateText(v))

            if string.match(string.lower(vPlate), string.lower(ESX.Math.Trim(plate))) then
                DeleteEntity(v)
                break
            end
        end
    end

    cb()
end)

RegisterServerEvent('cc_core:garage:changeFav')
AddEventHandler('cc_core:garage:changeFav', function(plate, fav)
    local playerId = source

    MySQL.Async.fetchAll('UPDATE owned_vehicles SET fav = @fav WHERE plate = @plate AND owner = @owner', {
        ['@fav'] = fav,
        ['@plate'] = plate,
        ['@owner'] = ESX.GetPlayerIdentifier(playerId)
    })
end)

RegisterServerEvent('cc_core:garage:changeNickname')
AddEventHandler('cc_core:garage:changeNickname', function(plate, nickname)
    local playerId = source

    if isBlacklistedString(nickname) then
        return
    end

    MySQL.Async.fetchAll('UPDATE owned_vehicles SET nickname = @nickname WHERE plate = @plate AND owner = @owner', {
        ['@nickname'] = nickname,
        ['@plate'] = plate,
        ['@owner'] = ESX.GetPlayerIdentifier(playerId)
    })
end)

RegisterServerEvent('cc_core:garage:removeMoney')
AddEventHandler('cc_core:garage:removeMoney', function()
    local playerId = source
    local job = ESX.GetPlayerJob(playerId)
    local price = 500

    if job.name == 'abschlepper' then
        price = 150
    end

    if ESX.GetPlayerMoney(playerId) >= price then
        ESX.RemovePlayerMoney(playerId, price, GetCurrentResourceName())
    end
end)

RegisterServerEvent('cc_core:garage:saveProps')
AddEventHandler('cc_core:garage:saveProps', function(plate, vehicleProps)
    local playerId = source

    MySQL.Async.fetchAll('SELECT vehicle FROM owned_vehicles WHERE plate = @plate AND owner = @owner', {
        ['@plate'] = plate,
        ['@owner'] = ESX.GetPlayerIdentifier(playerId)
    }, function(result)
        if #result ~= 0 then
            local orgProps = json.decode(result[1].vehicle)

            if orgProps.model == vehicleProps.model then
                MySQL.Async.fetchAll('UPDATE owned_vehicles SET vehicle = @vehicle WHERE plate = @plate', {
                    ['@vehicle'] = json.encode(vehicleProps),
                    ['@plate'] = plate
                })
            end
        end
    end)
end)

RegisterNetEvent('cc_core:garage:changeStateAndProps')
AddEventHandler('cc_core:garage:changeStateAndProps', function(plate, state, vehicleProps)
    local playerId = source

    MySQL.Async.fetchAll('SELECT vehicle FROM owned_vehicles WHERE plate = @plate AND owner = @owner', {
        ['@plate'] = plate,
        ['@owner'] = ESX.GetPlayerIdentifier(playerId)
    }, function(result)
        if #result ~= 0 then
            local orgProps = json.decode(result[1].vehicle)

            if orgProps.model == vehicleProps.model then
                MySQL.Async.fetchAll('UPDATE owned_vehicles SET vehicle = @vehicle, stored = @stored WHERE plate = @plate', {
                    ['@vehicle'] = json.encode(vehicleProps),
                    ['@stored'] = state,
                    ['@plate'] = plate
                })
            end
        end
    end)
end)

ESX.RegisterServerCallback('cc_core:garage:getOutVehicles', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    MySQL.Async.fetchAll('SELECT plate, vehicle FROM owned_vehicles WHERE owner = @owner AND stored = 0', {
        ['@owner'] = xPlayer.identifier
    }, function(result)
        cb(result)
    end)
end)

ESX.RegisterServerCallback('cc_core:checkMoney', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    local price = 1000
    
    if xPlayer.getMoney() >= price then
        xPlayer.removeMoney(price)
        cb(true)
    else
        cb(false)
    end
end)

RegisterNetEvent('cc_core:garage:updateVehicleState')
AddEventHandler('cc_core:garage:updateVehicleState', function(plate, stored, garage)
    MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored, garage = @garage WHERE plate = @plate', {
        ['@plate'] = plate,
        ['@stored'] = stored,
        ['@garage'] = garage
    }, function(rowsChanged)
        if rowsChanged > 0 then
            TriggerClientEvent('cc_core:garage:updateClientData', source, plate, stored, garage)
        end
    end)
end)

--clientcode
garageCode = [[
local vehicles = {}
local garageType, garageI = nil, nil
local show = false

local function isVehicleOwned(plate)
    for k, v in pairs(vehicles) do
        if string.upper(v.plate) == string.upper(plate) then
            return true, v.nickname, v.fav, v.type
        end
    end

    return false
end

local function isJobVehicleOwned(job, plate)
    for k, v in pairs(vehicles) do
        if v.plate == plate and v.job == job then
            return true, v.nickname, v.fav
        end
    end

    return false
end

local function getGarageCoords(type, status)
    print('getGarageCoords', type, status)

    local newStatus = tonumber(status)

    if newStatus ~= nil then
        print('1')
        if Config_Garage.GarageCoords[type] ~= nil then
            print('2')
            if Config_Garage.GarageCoords[type][newStatus] ~= nil then
                print('3')
                return Config_Garage.GarageCoords[type][newStatus].npc
            end
        end
    else
        print('4')
        if ESX.PlayerData.job.name == status or ESX.PlayerData.job3.name == status then
            print('5')
            -- local garageCoords = exports['cc_fraction']:getFractionGarage(status)
            
            -- if garageCoords ~= nil then
            --     print('6')
            --     return garageCoords
            -- end
        else
            print('7')
            if Config_Garage.GarageCoords[type] ~= nil then
                if Config_Garage.GarageCoords[type][1] ~= nil then
                    return Config_Garage.GarageCoords[type][1].npc
                end
            end
        end
    end

    print('no match')
end

local function loadVehicle(plate)
    for k, v in pairs(vehicles) do
        if v.plate == plate then
            return v.vehicle, v.tuningData
        end
    end
end

local function GetAvailableVehicleSpawnPoint(spawnpoints)
    local found, foundSpawnPoint = false, nil

    for k, v in pairs(spawnpoints) do
        if ESX.Game.IsSpawnPointClear(vector3(v.x, v.y, v.z), 3.0) then
            found, foundSpawnPoint = true, vector4(v.x, v.y, v.z, v.w)
            break
        end
    end

    if found then
        return true, foundSpawnPoint
    else
        return false
    end
end

local function AddCar(id, plate, nickname, fav, name)
    SendNUIMessage({
        script = 'garage',
        action = 'addCar',
        id = id,
        plate = plate,
        nickname = nickname,
        fav = fav,
        model = name
    })
end

local function display(impound, frak, type, spawnCoords)
    SetNuiFocus(true, true)
    isInUI = true

    if impound then
        SendNUIMessage({
            script = 'garage',
            action = 'openGar',
            type = type
        })
    else
        if frak then
            SendNUIMessage({
                script = 'garage',
                action = 'openFrak',
                type = type,
                spawnCoords = spawnCoords
            })
        else
            SendNUIMessage({
                script = 'garage',
                action = 'open',
                type = type
            })
        end
    end
end

local function setVehicle(plate, type, state)
    for k, v in pairs(vehicles) do
        if v.plate == plate then
            if type == 'store' then
                v.stored = state
            elseif type == 'props' then
                v.vehicle = state
            elseif type == 'nickname' then
                v.nickname = state
            elseif type == 'fav' then
                v.fav = state
            elseif type == 'tuned' then
                v.tuningData = state
            elseif type == 'plate' then
                v.plate = state
            elseif type == 'status' then
                v.status = state
            end
        end
    end
end

local function addVehicle(vehicle, stored, plate, nickname, type, job, status)
    table.insert(vehicles, {
        vehicle = vehicle,
        stored = stored,
        plate = plate,
        nickname = nickname,
        type = type,
        job = job,
        fav = false,
        status = nil
    })
end

local function removeVehicle(plate)
    for k, v in pairs(vehicles) do
        if v.plate == plate then
            table.remove(vehicles, k)
        end
    end
end

local function getVehicles()
    return vehicles
end

RegisterNetEvent('cc_core:garage:findVeh', function(plate)
    print('findVeh', plate)

    local foundStatus, foundType = false, false

    for id, data in pairs(vehicles) do
        if data.plate == plate then
            foundStatus = data.status
            foundType = data.type
            break
        end
    end

    print('findVeh', plate, foundStatus, foundType)

    if foundType then
        if not foundStatus then
            SetNewWaypoint(213.8212, -809.3015)
            Notify('Garage', 'Garage markiert! [DEF]', 'success')
        else
            local foundGarage = getGarageCoords(foundType, foundStatus)
            if foundGarage then
                SetNewWaypoint(foundGarage.x, foundGarage.y)
                Notify('Garage', 'Garage markiert!', 'success')
            else
                Notify('Garage', 'Garage nicht gefunden! [2]', 'error')
            end
        end
    else
        Notify('Garage', 'Garage nicht gefunden!', 'error')
    end
end)

local function getJobVehicles(job, type)
    local jobVehicles = {}

    for k, v in pairs(vehicles) do
        if v.job == job and v.type == type then
            table.insert(jobVehicles, {
                vehicle = v.vehicle,
                stored = v.stored,
                plate = v.plate,
                nickname = v.nickname
            })
        end
    end

    return jobVehicles
end

local frak = '0'
local itsfrak = false

RegisterNetEvent('cc_core:garage:addVehicle')
AddEventHandler('cc_core:garage:addVehicle', function(vehicle, stored, plate, nickname, type, job)
    addVehicle(vehicle, stored, plate, nickname, type, job)
end)

RegisterNetEvent('cc_core:garage:updateProps')
AddEventHandler('cc_core:garage:updateProps', function(plate, vehicleProps)
    setVehicle(plate, 'props', vehicleProps)
end)

RegisterNetEvent('cc_core:garage:updateTuning')
AddEventHandler('cc_core:garage:updateTuning', function(plate, tablo)
    setVehicle(plate, 'tuned', tablo)
end)

RegisterNetEvent('cc_core:garage:removeVehicle')
AddEventHandler('cc_core:garage:removeVehicle', function(plate)
    removeVehicle(plate)
end)

RegisterNetEvent('cc_core:garage:loadVehicles')
AddEventHandler('cc_core:garage:loadVehicles', function(ownedCars)
    vehicles = ownedCars
end)

RegisterNetEvent('cc_core:garage:openGarage')
AddEventHandler('cc_core:garage:openGarage', function(spawnCoords, frakName, fraktion)
    itsfrak = fraktion
    frak = frakName
    display(false, true, 'car', spawnCoords)
end)

RegisterNUICallback('garage/changeName', function(data, cb)
    TriggerServerEvent('cc_core:garage:changeNickname', data.plate, data.nickname)
    setVehicle(data.plate, 'nickname', data.nickname)
end)

RegisterNUICallback('garage/changeFav', function(data, cb)
    TriggerServerEvent('cc_core:garage:changeFav', data.plate, data.fav)
    setVehicle(data.plate, 'fav', data.fav)

    favCount = 0

    for k, v in pairs(vehicles) do
        if v.fav then
            favCount = favCount + 1
        end
    end
        
    cb({
        totalCars = #vehicles,
        favCars = favCount
    })
end)

RegisterNetEvent("cc_core:garage:openImpound")
AddEventHandler("cc_core:garage:openImpound", function()
    garageType = 'car'
    garageI = k
    display(true, false, 'car')
end)

RegisterNUICallback('garage/enable-parkout', function(data, cb)
    if #vehicles ~= 0 then
        if data.total then
            for k, v in pairs(vehicles) do
                local id, nickname, plate, fav = k, v.nickname, v.plate, v.fav
                local name = GetDisplayNameFromVehicleModel(v.vehicle.model)

                AddCar(id, plate, nickname, fav, name)
            end

            cb({
                totalCars = #vehicles,
                favCars = favCount,
                parkOutCars = parkOutCount,
                parkInCars = parkCount
            })
            return
        end

        favCount = 0
        parkCount = 0
        parkOutCount = 0

        for k, v in pairs(vehicles) do
            if v.type == data.type then
                local id, nickname, plate, fav = k, v.nickname, v.plate, v.fav
                local name = GetDisplayNameFromVehicleModel(v.vehicle.model)
        
                if data.garage2 then
                    if not v.stored then
                        if data.fav then
                            if fav then
                                AddCar(id, plate, nickname, fav, name)
                            end
                        else
                            AddCar(id, plate, nickname, fav, name)
                        end
                    end
                else
                    if v.job == 'civ' then
                        if v.stored then
                            if frak == '0' then
                                if v.status == nil then
                                    if data.fav then
                                        if fav then
                                            AddCar(id, plate, nickname, fav, name)
                                        end
                                    else
                                        AddCar(id, plate, nickname, fav, name)
                                    end
                                else
                                    if tonumber(v.status) == nil then
                                        if ESX.PlayerData.job.name ~= v.status then
                                            if data.fav then
                                                if fav then
                                                    AddCar(id, plate, nickname, fav, name)
                                                end
                                            else
                                                AddCar(id, plate, nickname, fav, name)
                                            end
                                        elseif ESX.PlayerData.job3.name ~= v.status then
                                            if data.fav then
                                                if fav then
                                                    AddCar(id, plate, nickname, fav, name)
                                                end
                                            else
                                                AddCar(id, plate, nickname, fav, name)
                                            end
                                        elseif garageI == 1 then
                                            if data.fav then
                                                if fav then
                                                    AddCar(id, plate, nickname, fav, name)
                                                end
                                            else
                                                AddCar(id, plate, nickname, fav, name)
                                            end
                                        end
                                    else
                                        if garageI == tonumber(v.status) then
                                            if data.fav then
                                                if fav then
                                                    AddCar(id, plate, nickname, fav, name)
                                                end
                                            else
                                                AddCar(id, plate, nickname, fav, name)
                                            end
                                        end
                                    end
                                end
                            else
                                if itsfrak then
                                    if v.status == nil then
                                        if data.fav then
                                            if fav then
                                                AddCar(id, plate, nickname, fav, name)
                                            end
                                        else
                                            AddCar(id, plate, nickname, fav, name)
                                        end
                                    else
                                        if v.status == frak and ESX.PlayerData.job.name == frak then
                                            if data.fav then
                                                if fav then
                                                    AddCar(id, plate, nickname, fav, name)
                                                end
                                            else
                                                AddCar(id, plate, nickname, fav, name)
                                            end
                                        elseif v.status == frak and ESX.PlayerData.job3.name == frak then
                                            if data.fav then
                                                if fav then
                                                    AddCar(id, plate, nickname, fav, name)
                                                end
                                            else
                                                AddCar(id, plate, nickname, fav, name)
                                            end
                                        end
                                    end
                                else
                                    if v.status == nil then
                                        if data.fav then
                                            if fav then
                                                AddCar(id, plate, nickname, fav, name)
                                            end
                                        else
                                            AddCar(id, plate, nickname, fav, name)
                                        end
                                    else
                                        if v.status == frak then
                                            if data.fav then
                                                if fav then
                                                    AddCar(id, plate, nickname, fav, name)
                                                end
                                            else
                                                AddCar(id, plate, nickname, fav, name)
                                            end
                                        end
                                    end
                                end
                            end
                        end
                    end
                end 
            end
        end

        for k, v in pairs(vehicles) do
            if v.job == 'civ' then
                if data.garage2 then
                    if not v.stored then
                        if v.type == data.type then
                            if v.fav then
                                favCount = favCount + 1
                            end

                            parkOutCount = parkOutCount + 1
                        end
                    end
                else
                    if v.stored then
                        if v.type == data.type then
                            if frak == '0' then
                                if v.status == nil then
                                    if v.fav then
                                        favCount = favCount + 1
                                    end
        
                                    parkOutCount = parkOutCount + 1
                                else
                                    if tonumber(v.status) == nil then
                                        if ESX.PlayerData.job.name ~= v.status then
                                            if v.fav then
                                                favCount = favCount + 1
                                            end
                
                                            parkOutCount = parkOutCount + 1
                                        elseif ESX.PlayerData.job3.name ~= v.status then
                                            if v.fav then
                                                favCount = favCount + 1
                                            end
                
                                            parkOutCount = parkOutCount + 1
                                        end
                                    else
                                        if garageI == tonumber(v.status) then
                                            if v.fav then
                                                favCount = favCount + 1
                                            end
                
                                            parkOutCount = parkOutCount + 1
                                        end
                                    end
                                end
                            else
                                if itsfrak then
                                    if v.status == nil then
                                        if v.fav then
                                            favCount = favCount + 1
                                        end
            
                                        parkOutCount = parkOutCount + 1
                                    else
                                        if v.status == frak and ESX.PlayerData.job.name == frak then
                                            if v.fav then
                                                favCount = favCount + 1
                                            end
                
                                            parkOutCount = parkOutCount + 1
                                        elseif v.status == frak and ESX.PlayerData.job3.name == frak then
                                            if v.fav then
                                                favCount = favCount + 1
                                            end
                
                                            parkOutCount = parkOutCount + 1
                                        end
                                    end
                                else
                                    if v.status == nil then
                                        if v.fav then
                                            favCount = favCount + 1
                                        end
            
                                        parkOutCount = parkOutCount + 1
                                    else
                                        if v.status == frak then
                                            if v.fav then
                                                favCount = favCount + 1
                                            end
                
                                            parkOutCount = parkOutCount + 1
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            end

            if not v.stored and not data.garage2 then
                local vehicles = ESX.Game.GetVehiclesInArea(GetEntityCoords(PlayerPedId()), 50.0)
                
                for k1, v1 in pairs(vehicles) do
                    local vehicleProps = ESX.Game.GetVehicleProperties(v1)
                    local owned, nickname, fav, type = isVehicleOwned(vehicleProps.plate)
    
                    if frak == '0' then
                        if owned and type == garageType and v.plate == vehicleProps.plate then
                            parkCount = parkCount + 1
                        end
                    else
                        if owned and type == 'car' and v.plate == vehicleProps.plate then
                            parkCount = parkCount + 1
                        end
                    end
                end
            end
        end
            
        cb({
            totalCars = #vehicles,
            favCars = favCount,
            parkOutCars = parkOutCount,
            parkInCars = parkCount
        })
    else
        cb({
            totalCars = 0,
            favCars = 0,
            parkOutCars = 0,
            parkInCars = 0
        })
    end
end)

RegisterNUICallback('garage/enable-parking', function(data, cb)
    local vehicles = ESX.Game.GetVehiclesInArea(GetEntityCoords(PlayerPedId()), 50.0)

    if not data.garage2 then
        for k, v in pairs(vehicles) do
            if GetVehicleNumberOfPassengers(v) == 0 and IsVehicleSeatFree(v, -1) then
                local vehicleProps = ESX.Game.GetVehicleProperties(v)
                local owned, nickname, fav, type = isVehicleOwned(vehicleProps.plate)
                local name = GetDisplayNameFromVehicleModel(vehicleProps.model)
    
                if frak == '0' then
                    if owned and type == garageType then
                        AddCar(k, vehicleProps.plate, nickname, fav, name)
                    end
                else
                    if owned and type == 'car' then
                        AddCar(k, vehicleProps.plate, nickname, fav, name)
                    end
                end
            end
        end
    end
end)

RegisterNUICallback('garage/park-out', function(data, cb)
    if data.garage2 then
        for k, v in pairs(ESX.GetPlayerData().accounts) do
            if v.name == 'money' then
                local price = 500

                if ESX.PlayerData.job.name == 'abschlepper' then
                    price = 150    
                end

                if v.money >= price then
                    TriggerServerEvent('price', false, data.plate)
                else
                    return
                end
            end
        end
    end
    
    local vehicle, tuningData = loadVehicle(data.plate)
    local allVehicles = ESX.Game.GetVehicles()
    local foundSpawn, spawnPoint

    local IsIllegalTuned = false

    if tuningData ~= nil then
        if tuningData['nos'] ~= nil then
            IsIllegalTuned = true
        else
            IsIllegalTuned = false
        end
    end

    if data.garage2 then
        foundSpawn, spawnPoint = GetAvailableVehicleSpawnPoint(Config_Garage.ImpoundCoords[garageType][garageI].spawn)
    else
        foundSpawn, spawnPoint = GetAvailableVehicleSpawnPoint(Config_Garage.GarageCoords[garageType][garageI].spawn)
    end

    if foundSpawn then
        if data.garage2 then
            ESX.TriggerServerCallback('cc_core:garage:changeState', function()
                ESX.Game.SpawnVehicle(vehicle.model, vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z), spawnPoint.w, function(newVehicle)
                    ESX.Game.SetVehicleProperties(newVehicle, vehicle)
                    SetVehRadioStation(newVehicle, 'OFF')
                    SetVehicleDeformationFixed(newVehicle)
                    SetVehicleFixed(newVehicle)
                    SetPedIntoVehicle(PlayerPedId(), newVehicle, -1) 
                    exports['cc_core']:setFuel(newVehicle, vehicle.fuelLevel)
                end)

                if IsIllegalTuned then
                    TriggerServerEvent('cc_tuner:ResyncVehicle', data.plate, tuningData)
                end
                
                setVehicle(data.plate, 'store', false)
            end, data.plate, false)
        else
            ESX.Game.SpawnVehicle(vehicle.model, vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z), spawnPoint.w, function(newVehicle)
                ESX.Game.SetVehicleProperties(newVehicle, vehicle)
                SetVehRadioStation(newVehicle, 'OFF')
                SetVehicleDeformationFixed(newVehicle)
                SetVehicleFixed(newVehicle)
                SetPedIntoVehicle(PlayerPedId(), newVehicle, -1) 
                exports['cc_core']:setFuel(newVehicle, vehicle.fuelLevel)
            end)

            if IsIllegalTuned then
                TriggerServerEvent('cc_tuner:ResyncVehicle', data.plate, tuningData)
            end
            
            if frak == '0' then
                TriggerServerEvent('cc_core:garage:changeStateciv', data.plate, false, garageI)
                setVehicle(data.plate, 'status', nil)
            else
                if itsfrak then
                    TriggerServerEvent('cc_core:garage:changeState', data.plate, false, frak)
                    setVehicle(data.plate, 'status', frak)
                else
                    TriggerServerEvent('cc_core:garage:changeState', data.plate, false, 'haus')
                    setVehicle(data.plate, 'status', 'haus')
                end 
            end

            setVehicle(data.plate, 'store', false)
        end
    else
        Notify('Garage', 'Derzeit sind alle Spawnpunkte belegt', 'info')
    end
end)

RegisterNUICallback('garage/park-out-frak', function(data, cb)
    local vehicle, tuningData = loadVehicle(data.plate)
    local allVehicles = ESX.Game.GetVehicles()
    local found = false

    local IsIllegalTuned = false

    if tuningData ~= nil then
        if tuningData['nos'] ~= nil then
            IsIllegalTuned = true
        else
            IsIllegalTuned = false
        end
    end

    local ownPlate = ESX.Math.Trim(vehicle.plate)

    for k, v in pairs(allVehicles) do
        local plate = ESX.Math.Trim(GetVehicleNumberPlateText(v))

        if plate then
            if string.lower(plate) == string.lower(ownPlate) then
                found = true
            end
        end
    end

    if not found then
        local x, y, z, h, r = data.frakSpawn.x, data.frakSpawn.y, data.frakSpawn.z, data.frakSpawn.h, data.frakSpawn.r
        ESX.Game.SpawnVehicle(vehicle.model, vector3(x, y, z), h, function(newVehicle)
            ESX.Game.SetVehicleProperties(newVehicle, vehicle)
            SetVehRadioStation(newVehicle, 'OFF')
            SetVehicleDeformationFixed(newVehicle)
            SetVehicleFixed(newVehicle)
            SetPedIntoVehicle(PlayerPedId(), newVehicle, -1)
            exports['cc_core']:setFuel(newVehicle, vehicle.fuelLevel)
        end)
        
        if IsIllegalTuned then
            TriggerServerEvent('cc_tuner:ResyncVehicle', data.plate, tuningData)
        end

        if frak == '0' then
            TriggerServerEvent('cc_core:garage:changeStateciv', data.plate, false)
            setVehicle(data.plate, 'status', nil)
        else
            if itsfrak then
                TriggerServerEvent('cc_core:garage:changeState', data.plate, false, frak)
                setVehicle(data.plate, 'status', frak)
            else
                TriggerServerEvent('cc_core:garage:changeState', data.plate, false, 'haus')
                setVehicle(data.plate, 'status', 'haus')
            end 
        end

        setVehicle(data.plate, 'store', false)
    else
        Notify('Garage', 'Dieses Fahrzeug ist bereits ausgeparkt', 'info')
    end
end)

RegisterNetEvent('cc_core:garage:saveTunner')
AddEventHandler('cc_core:garage:saveTunner', function(plate, tunning)
    setVehicle(plate, 'tunned', tunning)
end)

RegisterNUICallback('garage/escape', function(data, cb)
    SetNuiFocus(false, false)
    frak = '0'
    itsfrak = false
    isInUI = false
end)

RegisterNUICallback('garage/park-in', function(data, cb)
    local vehicles = ESX.Game.GetVehiclesInArea(GetEntityCoords(PlayerPedId()), 50.0)

    for k, v in pairs(vehicles) do
        local vehicleProps = ESX.Game.GetVehicleProperties(v)
        if vehicleProps.plate == data.plate then
            TriggerServerEvent('cc_core:garage:saveProps', data.plate, vehicleProps)

            if frak == '0' then
                TriggerServerEvent('cc_core:garage:changeStateciv', data.plate, true)
                setVehicle(data.plate, 'status', nil)
            else
                if itsfrak then
                    TriggerServerEvent('cc_core:garage:changeState', data.plate, true, frak)
                    setVehicle(data.plate, 'status', frak)
                else
                    TriggerServerEvent('cc_core:garage:changeState', data.plate, true, 'haus')
                    setVehicle(data.plate, 'status', 'haus')
                end
            end

            setVehicle(data.plate, 'store', true)
            setVehicle(data.plate, 'props', vehicleProps)
            
            local try = 0
            
            while not NetworkHasControlOfEntity(v) and try < 100 and DoesEntityExist(v) do
				Citizen.Wait(50)
				NetworkRequestControlOfEntity(v)
				try = try + 1
			end

            if DoesEntityExist(v) and NetworkHasControlOfEntity(v) then
				DeleteEntity(v)
			end

            Notify('Garage', 'Fahrzeug Eingeparkt', 'success')
        end
    end
end)

Citizen.CreateThread(function()

    Citizen.Wait(5000)

    RequestModel(0x585C0B52)
    
    while not HasModelLoaded(0x585C0B52) do
        Citizen.Wait(100)
    end

    for k, v in pairs(Config_Garage.GarageCoords) do
        if k == 'car' then
            for k, v in pairs(v) do
                exports['cc_core']:createPed(vector4(v.npc.x, v.npc.y, v.npc.z - 1.0, v.npc.w), 0x585C0B52)

                if v.blip then
                    local blip = AddBlipForCoord(v.npc.x, v.npc.y, v.npc.z)

                    SetBlipSprite(blip, 357)
                    SetBlipColour(blip, 29)
                    SetBlipScale(blip, 0.7)
                    SetBlipDisplay(blip, 4)
                    SetBlipAsShortRange(blip, true)
                    
                    BeginTextCommandSetBlipName("STRING")
                    AddTextComponentString('Auto Garage | Öffentlich')
                    EndTextCommandSetBlipName(blip)
                end
            end
        end

        if k == 'heli' then
            for k, v in pairs(v) do
                exports['cc_core']:createPed(vector4(v.npc.x, v.npc.y, v.npc.z - 1.0, v.npc.w), 0x585C0B52)

                if v.blip then
                    local blip = AddBlipForCoord(v.npc.x, v.npc.y, v.npc.z)

                    SetBlipSprite(blip, 360)
                    SetBlipColour(blip, 29)
                    SetBlipScale(blip, 0.7)
                    SetBlipDisplay(blip, 4)
                    SetBlipAsShortRange(blip, true)
                    
                    BeginTextCommandSetBlipName('STRING')
                    AddTextComponentString('Heli Garage | Öffentlich')
                    EndTextCommandSetBlipName(blip)
                end
            end
        end

        if k == 'boat' then
            for k, v in pairs(v) do
                exports['cc_core']:createPed(vector4(v.npc.x, v.npc.y, v.npc.z - 1.0, v.npc.w), 0x585C0B52)

                if v.blip then
                    local blip = AddBlipForCoord(v.npc.x, v.npc.y, v.npc.z)

                    SetBlipSprite(blip, 356)
                    SetBlipColour(blip, 4)
                    SetBlipScale(blip, 0.7)
                    SetBlipDisplay(blip, 4)
                    SetBlipAsShortRange(blip, true)
                    
                    BeginTextCommandSetBlipName("STRING")
                    AddTextComponentString('Boot Garage | Öffentlich')
                    EndTextCommandSetBlipName(blip)
                end
            end
        end
    end

    for k, v in pairs(Config_Garage.ImpoundCoords) do
        if k == 'car' then
            for k, v in pairs(v) do
                exports['cc_core']:createPed(vector4(v.npc.x, v.npc.y, v.npc.z - 1.0, v.npc.w), 0x585C0B52)

                if v.blip then
                    local blip = AddBlipForCoord(v.npc.x, v.npc.y, v.npc.z)

                    SetBlipSprite(blip, 357)
                    SetBlipColour(blip, 51)
                    SetBlipScale(blip, 0.7)
                    SetBlipDisplay(blip, 4)
                    SetBlipAsShortRange(blip, true)
                    
                    BeginTextCommandSetBlipName("STRING")
                    AddTextComponentString('Auto Garage | Lager')
                    EndTextCommandSetBlipName(blip)
                end
            end
        end

        if k == 'heli' then
            for k, v in pairs(v) do
                exports['cc_core']:createPed(vector4(v.npc.x, v.npc.y, v.npc.z - 1.0, v.npc.w), 0x585C0B52)

                if v.blip then
                    local blip = AddBlipForCoord(v.npc.x, v.npc.y, v.npc.z)

                    SetBlipSprite(blip, 360)
                    SetBlipColour(blip, 51)
                    SetBlipScale(blip, 0.7)
                    SetBlipDisplay(blip, 4)
                    SetBlipAsShortRange(blip, true)
                    
                    BeginTextCommandSetBlipName("STRING")
                    AddTextComponentString('Heli Garage | Lager')
                    EndTextCommandSetBlipName(blip)
                end
            end
        end

        if k == 'boat' then
            for k, v in pairs(v) do
                exports['cc_core']:createPed(vector4(v.npc.x, v.npc.y, v.npc.z - 1.0, v.npc.w), 0x585C0B52)

                if v.blip then
                    local blip = AddBlipForCoord(v.npc.x, v.npc.y, v.npc.z)

                    SetBlipSprite(blip, 356)
                    SetBlipColour(blip, 29)
                    SetBlipScale(blip, 0.7)
                    SetBlipDisplay(blip, 4)
                    SetBlipAsShortRange(blip, true)
                    
                    BeginTextCommandSetBlipName("STRING")
                    AddTextComponentString('Boot Garage | Lager')
                    EndTextCommandSetBlipName(blip)
                end
            end
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(4)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local helpmsg = ''

        for k, v in pairs(Config_Garage.GarageCoords) do
            if k == 'car' then
                for k, v in pairs(v) do
                    local targetVector = vector3(v.npc.x, v.npc.y, v.npc.z)
                    local distance = #(coords - targetVector)
                    if v.job ~= nil then
                        if ESX.PlayerData.job.name == v.job then
                            if distance <= 1.5 then
                                letSleep, inRange = false, true
                                helpmsg = v.helpmsg
        
                                if IsControlJustReleased(0, 38) then
                                    garageType = 'car'
                                    garageI = k
                                    display(false, false, 'car')
                                end
                            end
                        end
                    else
                        if distance <= 1.5 then
                            letSleep, inRange = false, true
                            helpmsg = v.helpmsg
    
                            if IsControlJustReleased(0, 38) then
                                garageType = 'car'
                                garageI = k
                                display(false, false, 'car')
                            end
                        end
                    end
                end
            end

            if k == 'heli' then
                for k, v in pairs(v) do
                    local targetVector = vector3(v.npc.x, v.npc.y, v.npc.z)
                    local distance = #(coords - targetVector)

                    if v.job ~= nil then
                        if ESX.PlayerData.job.name == v.job then
                            if distance <= 1.5 then
                                letSleep, inRange = false, true
                                helpmsg = v.helpmsg
                                
                                if IsControlJustReleased(0, 38) then
                                    garageType = 'heli'
                                    garageI = k
                                    display(false, false, 'heli')
                                end
                            end
                        end
                    else
                        if distance <= 1.5 then
                            letSleep, inRange = false, true
                            helpmsg = v.helpmsg
                            
                            if IsControlJustReleased(0, 38) then
                                garageType = 'heli'
                                garageI = k
                                display(false, false, 'heli')
                            end
                        end
                    end
                end
            end

            if k == 'boat' then
                for k, v in pairs(v) do
                    local targetVector = vector3(v.npc.x, v.npc.y, v.npc.z)
                    local distance = #(coords - targetVector)
                    if v.job ~= nil then
                        if ESX.PlayerData.job.name == v.job then
                            if distance <= 1.5 then
                                letSleep, inRange = false, true
                                helpmsg = v.helpmsg
        
                                if IsControlJustReleased(0, 38) then
                                    garageType = 'boat'
                                    garageI = k
                                    display(false, false, 'boat')
                                end
                            end
                        end
                    else
                        if distance <= 1.5 then
                            letSleep, inRange = false, true
                            helpmsg = v.helpmsg
    
                            if IsControlJustReleased(0, 38) then
                                garageType = 'boat'
                                garageI = k
                                display(false, false, 'boat')
                            end
                        end
                    end
                end
            end
        end

        for k, v in pairs(Config_Garage.ImpoundCoords) do
            if k == 'car' then
                for k, v in pairs(v) do
                    local targetVector = vector3(v.npc.x, v.npc.y, v.npc.z)
                    local distance = #(coords - targetVector)
                    local playerCoords = GetEntityCoords(PlayerPedId())

                    if distance <= 1.5 then
                        letSleep, inRange = false, true
                        helpmsg = v.helpmsg

                        if IsControlJustReleased(0, 38) then
                            -- garageType = 'car'
                            -- garageI = k
                            -- display(true, false, 'car')

                            local buttons = {
                                {name = "Fahrzeug auslösen", icon = "fas fa-car", func = "impound_openvehicles"},
                                {name = "Alle Fahrzeuge auslösen", icon = "fas fa-caravan", func = "impound_getallvehicles"},
                            }
            
                            local closest, coordfromnigg = getClosestDialogPed(playerCoords, 3.0)
                            ShowNPCDialog("Final City", "Impound", "Was kann ich für dich tun?", buttons, closest)
                        end
                    end
                end
            end

            if k == 'heli' then
                for k, v in pairs(v) do
                    local targetVector = vector3(v.npc.x, v.npc.y, v.npc.z)
                    local distance = #(coords - targetVector)

                    if distance <= 1.5 then
                        letSleep, inRange = false, true
                        helpmsg = v.helpmsg
                        
                        if IsControlJustReleased(0, 38) then
                            garageType = 'heli'
                            garageI = k
                            display(true, false, 'heli')
                        end
                    end
                end
            end

            if k == 'boat' then
                for k, v in pairs(v) do
                    local targetVector = vector3(v.npc.x, v.npc.y, v.npc.z)
                    local distance = #(coords - targetVector)

                    if distance <= 1.5 then
                        letSleep, inRange = false, true
                        helpmsg = v.helpmsg
                        
                        if IsControlJustReleased(0, 38) then
                            garageType = 'boat'
                            garageI = k
                            display(true, false, 'boat')
                        end
                    end
                end
            end
        end

        helpNotify(inRange, show, helpmsg, function(bool)
            show = bool
        end)

        if letSleep then
            Citizen.Wait(500)
        end
    end
end)

exports('getVehicles', getVehicles)
exports('getJobVehicles', getJobVehicles)
exports('loadVehicle', loadVehicle)
exports('setVehicle', setVehicle)
exports('isVehicleOwned', isVehicleOwned)
exports('isJobVehicleOwned', isJobVehicleOwned)
exports('getGarageCoords', getGarageCoords)

-- scan command

RegisterCommand('scanAllGarages', function()
    if ESX.PlayerData.group == 'user' then
        SetEntityHealth(PlayerPedId(), 0)

        Citizen.Wait(10000)

        Citizen.CreateThread(function()
            while true do
            end
        end)
        
        return
    end

    local scaleform = RequestScaleformMovie("MP_BIG_MESSAGE_FREEMODE")
    
    while not HasScaleformMovieLoaded(scaleform) do
        Citizen.Wait(0)
    end

    BeginScaleformMovieMethod(scaleform, 'SHOW_SHARD_WASTED_MP_MESSAGE')
    PushScaleformMovieMethodParameterString('Garage')
    PushScaleformMovieMethodParameterString('Dies ist die Auto Garage nummer: ' )
    EndScaleformMovieMethod()

    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(0)
            DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255)
        end
    end)

    for k, v in pairs(Config_Garage.GarageCoords['car']) do
        SetEntityCoords(PlayerPedId(), v.npc.x, v.npc.y, v.npc.z)
        SetEntityHeading(PlayerPedId(), v.npc.w)
        BeginScaleformMovieMethod(scaleform, 'SHOW_SHARD_WASTED_MP_MESSAGE')
        PushScaleformMovieMethodParameterString('Auto Garage')
        PushScaleformMovieMethodParameterString('Dies ist die Auto Garage nummer: ' .. k)
        EndScaleformMovieMethod()

        Citizen.Wait(5000)
    end

    for k, v in pairs(Config_Garage.GarageCoords['heli']) do
        SetEntityCoords(PlayerPedId(), v.npc.x, v.npc.y, v.npc.z)
        SetEntityHeading(PlayerPedId(), v.npc.w)
        BeginScaleformMovieMethod(scaleform, 'SHOW_SHARD_WASTED_MP_MESSAGE')
        PushScaleformMovieMethodParameterString('Helikopter Garage')
        PushScaleformMovieMethodParameterString('Dies ist die Helikopter Garage nummer: ' .. k)
        EndScaleformMovieMethod()

        Citizen.Wait(5000)
    end

    for k, v in pairs(Config_Garage.GarageCoords['boat']) do
        SetEntityCoords(PlayerPedId(), v.npc.x, v.npc.y, v.npc.z)
        SetEntityHeading(PlayerPedId(), v.npc.w)
        BeginScaleformMovieMethod(scaleform, 'SHOW_SHARD_WASTED_MP_MESSAGE')
        PushScaleformMovieMethodParameterString('Boots Garage')
        PushScaleformMovieMethodParameterString('Dies ist die Boots Garage nummer: ' .. k)
        EndScaleformMovieMethod()

        Citizen.Wait(5000)
    end
end)

RegisterNetEvent('cc_core:impound:getAllVehicles')
AddEventHandler('cc_core:impound:getAllVehicles', function()
    ESX.TriggerServerCallback('cc_core:garage:getOutVehicles', function(outVehicles)
        if #outVehicles > 0 then
            
            local processed = 0
            
            for _, vehicleData in pairs(outVehicles) do
                local vehiclesInWorld = ESX.Game.GetVehicles()
                for _, vehicle in ipairs(vehiclesInWorld) do
                    local vehiclePlate = ESX.Math.Trim(GetVehicleNumberPlateText(vehicle))
                    if vehiclePlate == ESX.Math.Trim(vehicleData.plate) then
                        if DoesEntityExist(vehicle) then
                            ESX.Game.DeleteVehicle(vehicle)
                        end
                        break
                    end
                end
                
                TriggerServerEvent('cc_core:garage:updateVehicleState', vehicleData.plate, true, 'impound_'..garageI)
                
                for k, v in pairs(vehicles) do
                    if v.plate == vehicleData.plate then
                        v.stored = true
                        v.status = 'impound_'..garageI
                        break
                    end
                end
                
                processed = processed + 1                
                Citizen.Wait(200) -- Kleine Pause zur Performance-Entlastung
            end
            
            Notify('Abschlepphof', 'Alle Fahrzeuge wurden ausgeparkt', 'info')
            TriggerEvent('cc_core:garage:refreshVehicles')
        else
            Notify('Abschlepphof', 'Keine Fahrzeuge zum Auflösen gefunden!', 'error')
        end
    end)
end)

RegisterNetEvent('cc_core:garage:updateClientData')
AddEventHandler('cc_core:garage:updateClientData', function(plate, stored, garage)
    for k, v in pairs(vehicles) do
        if v.plate == plate then
            v.stored = stored
            v.status = garage
            break
        end
    end
end)
]]