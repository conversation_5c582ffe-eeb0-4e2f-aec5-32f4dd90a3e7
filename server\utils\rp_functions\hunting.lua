local uniqueId = 100
local playerIds = {}

RegisterServerEvent('cc_core:hunting:enter')
AddEventHandler('cc_core:hunting:enter', function()
    local playerId = source
    
    if ESX.GetPlayerNeu(playerId) then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [FH-72]", false, "Afrika")
        return
    end

    if playerIds[playerId] == nil then
        playerIds[playerId] = 0
    end

    if playerIds[playerId] == 0 then
        uniqueId = uniqueId + 1
        playerIds[playerId] = uniqueId
        SetPlayerRoutingBucket(playerId, uniqueId)
        TriggerClientEvent('cc_core:hunting:enter', playerId)
    else
        playerIds[playerId] = 0
        SetPlayerRoutingBucket(playerId, 0)
        TriggerClientEvent('cc_core:hunting:leave', playerId)
    end
end)

RegisterServerEvent('cc_core:hunting:getItem')
AddEventHandler('cc_core:hunting:getItem', function(index)
    local playerId = source
    local base = Config_Hunt.Entitys[index]

    if ESX.PlayerCanCarryItem(playerId, base.reward, base.count) then
        ESX.AddPlayerInventoryItem(playerId, base.reward, base.count, GetCurrentResourceName())
    else
        Notify(playerId, 'Information', 'Dein Inventar ist voll', 'info')
    end
end)

--clientcode
huntingCode = [[
local show, showHunt, isHunting = false, false, false
local currentVehicle = nil
local hearRange = 55.0

local function spawnAnimals()
    for k, v in pairs(Config_Hunt.Entitys) do
        while not HasModelLoaded(GetHashKey(v.model)) do        
            RequestModel(GetHashKey(v.model))
            Citizen.Wait(10)
        end

        if v.entity == nil then
            v.entity = CreatePed(4, GetHashKey(v.model), v.coords.x, v.coords.y, v.coords.z, 0.0, false, false)
            print('CREATEPED', GetCurrentResourceName())
            TaskWanderStandard(v.entity, 10.0, 10)
            v.flee = false
        end
    end
end

local function deleteAnimals()
    for k, v in pairs(Config_Hunt.Entitys) do
        if v.entity ~= nil then
            DeleteEntity(v.entity)
            v.entity = nil
            v.flee = false
        end
    end
end

Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(100)
    end

    while ESX.GetPlayerData().job == nil do
        Citizen.Wait(100)
    end

    -- local checkHunting = GetResourceKvpInt('cc_core:hunting:inHunting')

    -- if CheckIfWasHunting ~= 0 then
    --     SetEntityCoords(PlayerPedId(), Config_Hunt.HuntingIn, 0.0, 0.0, 0.0, false)
    --     SetResourceKvpInt('cc_core:hunting:inHunting', 0)
    -- end
end)

local function startThread()
    Citizen.CreateThread(function()
        while isHunting do
            Citizen.Wait(0)
            local ped = PlayerPedId()
            local inRange = false
    
            DrawSphere(Config_Hunt.HuntingCentral, 275.0, 0, 111, 245, 0.5)
    
            if IsPedDucking(ped) then
                HearRange = 0.0
            elseif IsPedSprinting(ped) then
                HearRange = 40.0
            else
                HearRange = 25.0
            end
    
            for k, v in pairs(Config_Hunt.Entitys) do
                if DoesEntityExist(v.entity) then
                    local distance = #(GetEntityCoords(v.entity) - Config_Hunt.HuntingCentral)

                    if distance >= 275.0 then
                        SetEntityCoords(v.entity, v.coords)
                    end

                    local pedDistance = #(GetEntityCoords(v.entity) - GetEntityCoords(ped))
    
                    if pedDistance <= HearRange and not v.flee then
                        v.flee = true
                        TaskSmartFleePed(v.entity, ped, 275.0, -1, false, false)
                    end
    
                    if pedDistance <= 2.5 and GetEntityHealth(v.entity) <= 0 then
                        inRange = true

                        if IsControlJustReleased(1, 38) then
                            while not HasAnimDictLoaded('anim@heists@narcotics@funding@gang_idle') do
                                RequestAnimDict('anim@heists@narcotics@funding@gang_idle')
                                Citizen.Wait(50)
                            end
    
                            TaskPlayAnim(ped, 'anim@heists@narcotics@funding@gang_idle', 'gang_chatting_idle01', 8.0, -8.0, 8.0, 0, 0, false, false, false)

                            startProgressbar(11.5)

                            Citizen.Wait(250)
    
                            while IsEntityPlayingAnim(ped, 'anim@heists@narcotics@funding@gang_idle', 'gang_chatting_idle01', 3) do
                                Citizen.Wait(50)
                            end
    
                            TriggerServerEvent('cc_core:hunting:getItem', k)
                            DeleteEntity(v.entity)
                            v.entity = nil
                        end
                    end
                end
            end
    
            if not showHunt and inRange then
                exports['cc_core']:showHelpNotification('Drücke E um das Tier zu häuten')
                showHunt = true
            elseif showHunt and not inRange then
                exports['cc_core']:closeHelpNotification()
                showHunt = false
            end
        end
    end)
end

Citizen.CreateThread(function()
    local blip = AddBlipForCoord(Config_Hunt.HuntingIn)

    SetBlipSprite(blip, 303)
    SetBlipScale(blip, 0.5)
    SetBlipColour(blip, 76)
    SetBlipAsShortRange(blip, true)

    BeginTextCommandSetBlipName('STRING')
    AddTextComponentSubstringPlayerName('Jagd Hütte')
    EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
    while not HasModelLoaded(GetHashKey('ig_hunter')) do
        RequestModel(GetHashKey('ig_hunter'))
        Citizen.Wait(100)
    end

    local ped = CreatePed(4, GetHashKey('ig_hunter'), -1490.3151, 4981.5410, 62.3586, 84.4013, false, true)
    print('CREATEPED', GetCurrentResourceName())
    FreezeEntityPosition(ped, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)

    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local distance = #(Config_Hunt.HuntingIn - coords)
        local letSleep, inRange = true, false

        if distance <= 1.5 then
            letSleep, inRange = false, true

            if IsControlJustPressed(1, 38) then
                local elements = {}

                if not isHunting then
                    elements = {
                        { label = 'Hunting Starten', value = 'enter' }
                    }
                else
                    elements = {
                        { label = 'Hunting Verlassen', value = 'leave' }
                    }
                end

                ESX.UI.Menu.Open("default", GetCurrentResourceName(), "hunting_enter_leave", {
                    title = "Enter",
                    align = "top-left",
                    elements = elements
                }, function(data, menu)
                    menu.close()

                    if data.current.value == 'enter' then
                        TriggerEvent('skinchanger:getSkin', function(skin)
                            if skin.sex == 0 then
                                TriggerEvent('skinchanger:loadClothes', skin, Config_Hunt.WorkCloth["male"])
                            else
                                TriggerEvent('skinchanger:loadClothes', skin, Config_Hunt.WorkCloth["female"])
                            end
                        end)

                        isHunting = true

                        spawnAnimals()
                        
                        TriggerServerEvent('cc_core:hunting:enter')

                        while not HasModelLoaded(GetHashKey("bodhi2")) do
                            RequestModel(GetHashKey("bodhi2"))
                            Citizen.Wait(50)
                        end
                
                        currentVehicle = CreateVehicle(GetHashKey("bodhi2"), -1499.4445, 4969.4775, 63.3838, 179.1386, false, false)
                        SetPedIntoVehicle(PlayerPedId(), currentVehicle, -1)

                        RemoveAllPedWeapons(PlayerPedId(), true)

                        Citizen.Wait(500)

                        TriggerEvent('cc_anticheat:weapons:check')
                        TriggerEvent('cc_anticheat:weapons:check')
                        GiveWeaponToPed(PlayerPedId(), GetHashKey("WEAPON_MUSKET"), 9999, false, false)
                        GiveWeaponToPed(PlayerPedId(), GetHashKey("WEAPON_MARKSMANPISTOL"), 9999, false, false)

                        startThread()
                    elseif data.current.value == 'leave' then
                        ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin, jobSkin)
                            TriggerEvent('skinchanger:loadSkin', skin)
                        end)

                        isHunting = false

                        SetEntityCoords(PlayerPedId(), Config_Hunt.HuntingIn, 0.0, 0.0, 0.0, false)
                        SetResourceKvpInt('cc_huntig:inHunting', 0)
                        deleteAnimals()
                        TriggerServerEvent('cc_core:hunting:enter')

                        DeleteEntity(currentVehicle)
                        currentVehicle = nil

                        
                        Citizen.Wait(200)
                        TriggerEvent("esx:restoreLoadout")
                    end
                end, function(data,menu)
                    menu.close()
                end)
            end
        end

        if not show and inRange then
            exports['cc_core']:showHelpNotification('Drücke E um Hunting zu starten')
            show = true
        elseif show and not inRange then
            exports['cc_core']:closeHelpNotification()
            show = false
        end

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)
]]