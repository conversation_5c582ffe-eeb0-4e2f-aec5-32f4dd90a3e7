Config_Supermarket = {}

Config_Supermarket.Positions = {
    { -- 1
        coords = vector4(1728.6326, 6416.7114, 35.0372, 244.8624),
        menu = vector4(1733.8705, 6421.5381, 35.0372, 53.5264),
        delivery = vector3(1739.2605, 6399.1362, 35.2699),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },


    { -- 3
        coords = vector4(1698.6565, 4922.5366, 42.0637, 326.4052),
        menu = vector4(1705.7577, 4921.9600, 42.0636, 359.8225),
        delivery = vector3(1707.8901, 4944.4888, 42.1883),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 4
        coords = vector4(2676.5847, 3280.2571, 55.2411, 333.0546),
        menu = vector4(2672.1643, 3285.5342, 55.2411, 259.8209),
        delivery = vector3(2686.9260, 3297.5503, 55.4206),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 5
        coords = vector4(2555.5928, 380.9144, 108.6229, 352.7198),
        menu = vector4(2549.2014, 383.6706, 108.6229, 84.0476),
        delivery = vector3(2565.5635, 396.8084, 108.4634),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 6
        coords = vector4(-46.3005, -1757.3809, 29.4210, 51.3330),
        menu = vector4(-45.2914, -1750.4908, 29.4210, 66.6157),
        delivery = vector3(-39.3680, -1744.4601, 29.1262),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 7
        coords = vector4(24.4547, -1345.6638, 29.4970, 264.8128),
        menu = vector4(26.9869, -1339.2946, 29.4970, 0.7028),
        delivery = vector3(15.9118, -1340.8209, 29.2849),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 8
        coords = vector4(-1820.6359, 794.7505, 138.0917, 134.0458),
        menu = vector4(-1827.2900, 796.9246, 138.1791, 154.3074),
        delivery = vector3(-1823.6051, 779.2728, 137.7832),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 9
        coords = vector4(549.3104, 2669.6465, 42.1565, 97.5150),
        menu = vector4(547.6390, 2663.0823, 42.1565, 188.9846),
        delivery = vector3(537.7867, 2657.8198, 42.3746),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 10
        coords = vector4(-1485.8329, -378.5176, 40.1634, 134.8095),
        menu = vector4(-1479.7000, -372.8107, 39.1634, 21.8697),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 11
        coords = vector4(-1222.6115, -908.6647, 12.3264, 30.8209),
        menu = vector4(-1217.9652, -915.6396, 11.3263, 251.7331),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 12
        coords = vector4(-706.1667, -912.8978, 19.2156, 89.2901),
        menu = vector4(-709.5592, -906.8517, 19.2156, 122.3153),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 13
        coords = vector4(372.9942, 328.0888, 103.5665, 253.1298),
        menu = vector4(373.6776, 325.3798, 103.5664, 82.9201),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 14
        coords = vector4(-2966.4724, 390.0821, 15.0433, 87.6375),
        menu = vector4(-2967.7505, 392.8026, 15.0433, 273.4961),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 15
        coords = vector4(1959.3127, 3741.4270, 32.3437, 299.7394),
        menu = vector4(1961.5081, 3740.1028, 32.3438, 123.6010),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 16
        coords = vector4(-3040.5410, 584.0583, 7.9089, 15.1609),
        menu = vector4(-3038.7004, 585.9492, 7.9089, 197.7382),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 17
        coords = vector4(270.99, -978.85, 29.37, 162.58),
        menu = vector4(267.8, -979.08, 29.37, 171.31),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 18
        coords = vector4(1164.5747, -321.9695, 69.2052, 99.3813),
        menu = vector4(1163.5762, -324.0580, 69.2051, 280.2363),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 19
        coords = vector4(1134.0752, -981.7684, 46.4158, 281.1325),
        menu = vector4(1135.6493, -982.9195, 46.4159, 97.3552),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

--    { -- 20
--        coords = vector4(22.9914, -1105.5984, 29.7970, 158.8670),
--        menu = vector4(18.4728, -1109.6238, 29.7970, 251.2816),
--        deliveryCoords = vector4(20.3298, -1104.6304, 29.7970, 156.4165),
--        type = 'gunshop',
--
--        blip = {
--            enable = true,
--            sprite = 110,
--            scale = 0.8,
--            color = 0,
--            display = 2,
--            shortRange = true,
--            text = 'Waffenladen'
--        },
--
--        npc = {
--            enable = true
--        }
--    },

    -- { -- 21
    --     coords = vector4(-661.7230, -933.5272, 21.8292, 178.0793),
    --     menu = vector4(-664.7456, -938.8309, 21.8292, 268.2987),
    --     deliveryCoords = vector4(-664.5890, -933.4690, 21.8292, 178.9679),
    --     type = 'gunshop',

    --     blip = {
    --         enable = true,
    --         sprite = 110,
    --         scale = 0.8,
    --         color = 0,
    --         display = 2,
    --         shortRange = true,
    --         text = 'Waffenladen'
    --     },

    --     npc = {
    --         enable = true
    --     }
    -- },

    -- { -- 22
    --     coords = vector4(809.7928, -2159.0850, 29.6190, 2.7066),
    --     menu = vector4(812.6052, -2153.7383, 29.6190, 90.8941),
    --     deliveryCoords = vector4(812.5121, -2158.9912, 29.6190, 4.3470),
    --     type = 'gunshop',

    --     blip = {
    --         enable = true,
    --         sprite = 110,
    --         scale = 0.8,
    --         color = 0,
    --         display = 2,
    --         shortRange = true,
    --         text = 'Waffenladen'
    --     },

    --     npc = {
    --         enable = true
    --     }
    -- },

    -- { -- 23
    --     coords = vector4(1692.5511, 3761.3167, 34.7053, 227.8833),
    --     menu = vector4(1694.4967, 3755.6155, 34.7054, 317.0382),
    --     deliveryCoords = vector4(1690.5386, 3759.0977, 34.7053, 227.5700),
    --     type = 'gunshop',

    --     blip = {
    --         enable = true,
    --         sprite = 110,
    --         scale = 0.8,
    --         color = 0,
    --         display = 2,
    --         shortRange = true,
    --         text = 'Waffenladen'
    --     },

    --     npc = {
    --         enable = true
    --     }
    -- },

    -- { -- 24
    --     coords = vector4(-331.4027, 6085.3081, 31.4548, 227.4752),
    --     menu = vector4(-329.6377, 6079.5234, 31.4548, 317.7876),
    --     deliveryCoords = vector4(-333.3794, 6083.2642, 31.4548, 226.5322),
    --     type = 'gunshop',

    --     blip = {
    --         enable = false,
    --         sprite = 110,
    --         scale = 0.8,
    --         color = 0,
    --         display = 2,
    --         shortRange = true,
    --         text = 'Waffenladen'
    --     },

    --     npc = {
    --         enable = true
    --     }
    -- },

    -- { -- 25
    --     coords = vector4(253.8337, -51.0164, 69.9410, 73.3914),
    --     menu = vector4(249.6878, -46.4133, 69.9412, 165.1075),
    --     deliveryCoords = vector4(254.7405, -48.2988, 69.9411, 74.8354),
    --     type = 'gunshop',

    --     blip = {
    --         enable = false,
    --         sprite = 110,
    --         scale = 0.8,
    --         color = 0,
    --         display = 2,
    --         shortRange = true,
    --         text = 'Waffenladen'
    --     },

    --     npc = {
    --         enable = true
    --     }
    -- },

    -- { -- 26
    --     coords = vector4(2567.5559, 292.4731, 108.7348, 358.7544),
    --     menu = vector4(2570.3562, 297.8053, 108.7350, 92.1413),
    --     deliveryCoords = vector4(2570.3774, 292.4994, 108.7348, 357.8241),
    --     type = 'gunshop',

    --     blip = {
    --         enable = false,
    --         sprite = 110,
    --         scale = 0.8,
    --         color = 0,
    --         display = 2,
    --         shortRange = true,
    --         text = 'Waffenladen'
    --     },

    --     npc = {
    --         enable = true
    --     }
    -- },


    -- { -- 27
    --     coords = vector4(-1118.6295, 2700.0168, 18.5541, 220.4919),
    --     menu = vector4(-1117.3231, 2694.2505, 18.5542, 315.6191),
    --     deliveryCoords = vector4(-1120.7407, 2698.4480, 18.5541, 223.5931),
    --     type = 'gunshop',

    --     blip = {
    --         enable = false,
    --         sprite = 110,
    --         scale = 0.8,
    --         color = 0,
    --         display = 2,
    --         shortRange = true,
    --         text = 'Waffenladen'
    --     },

    --     npc = {
    --         enable = true
    --     }
    -- },

    -- { -- 28
    --     coords = vector4(841.8532, -1035.2898, 28.1948, 355.3928),
    --     menu = vector4(844.8454, -1029.9778, 28.1949, 91.8706),
    --     deliveryCoords = vector4(844.6583, -1035.3165, 28.1948, 355.8862),
    --     type = 'gunshop',

    --     blip = {
    --         enable = true,
    --         sprite = 110,
    --         scale = 0.8,
    --         color = 0,
    --         display = 2,
    --         shortRange = true,
    --         text = 'Waffenladen'
    --     },

    --     npc = {
    --         enable = true
    --     }
    -- },

    -- { -- 29
    --     coords = vector4(-1304.2588, -395.0209, 36.6958, 71.3149),
    --     menu = vector4(-1308.7203, -390.9347, 36.6958, 170.5852),
    --     deliveryCoords = vector4(-1303.5034, -392.3857, 36.6958, 80.4469),
    --     type = 'gunshop',

    --     blip = {
    --         enable = false,
    --         sprite = 110,
    --         scale = 0.8,
    --         color = 0,
    --         display = 2,
    --         shortRange = true,
    --         text = 'Waffenladen'
    --     },

    --     npc = {
    --         enable = true
    --     }
    -- },

    { -- 30
        coords = vector4(-2538.98, 2312.48, 33.41, 93.74),
        menu = vector4(-2542.12, 2309.41, 33.41, 353.3),
        delivery = vector3(-2524.73, 2344.64, 33.06),
        type = 'supermarket',

        blip = {
            enable = false,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },

    { -- 31
        coords = vector4(81.8919, -224.2501, 54.6493, 66.7286),
        menu = vector4(80.7163, -223.8658, 54.6493, 253.7434),
        delivery = vector3(81.5560, -221.6733, 54.6493),
        type = 'supermarket',

        blip = {
            enable = true,
            sprite = 52,
            scale = 0.8,
            color = 2,
            display = 2,
            shortRange = true,
            text = 'Supermarket'
        },

        npc = {
            enable = true
        }
    },
}

Config_Supermarket.Items = {
    {
        itemName = 'phone',
        itemLabel = 'Handy',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 175,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'fixkit',
        itemLabel = 'Reperaturkasten',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 500,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'deff',
        itemLabel = 'Defibrillator',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 10000,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'gps',
        itemLabel = 'GPS',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 60,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'headbag',
        itemLabel = 'Sack',
        itemType = 'item',
        shopType = 'gunshop',
        itemPrice = 50,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'fernglas',
        itemLabel = 'Fernglas',
        itemType = 'item',
        shopType = 'gunshop',
        itemPrice = 50,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'kabelbinder',
        itemLabel = 'Seile',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 30,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'weapon_petrolcan',
        itemLabel = 'Benzinkanister',
        itemType = 'weapon',
        shopType = 'supermarket',
        itemPrice = 70,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'waschschlappen',
        itemLabel = 'Waschschlappen',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 30,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'bagpack',
        itemLabel = 'Tasche',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 200,
        count = 1000,
        maxCount = 1000
    },
    {
        itemName = 'angel',
        itemLabel = 'Angel',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 100,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'switch',
        itemLabel = 'Switch',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 100,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'water',
        itemLabel = 'Wasser',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 50,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'toast',
        itemLabel = 'Sucuk Toast',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 50,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'giesskanne',
        itemLabel = 'Gießkanne',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 10,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'fertilizer',
        itemLabel = 'Dünger',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 25,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'weed_lemonhaze_seed',
        itemLabel = 'Samen',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 20,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'schweisgeraet',
        itemLabel = 'Schweißgerät',
        itemType = 'item',
        shopType = 'supermarket',
        itemPrice = 1000,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'WEAPON_POOLCUE',
        itemLabel = 'Billiard-Kö',
        itemType = 'weapon',
        shopType = 'gunshop',
        itemPrice = 60,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'WEAPON_BAT',
        itemLabel = 'Baseball Schläger',
        itemType = 'weapon',
        shopType = 'gunshop',
        itemPrice = 80,
        count = 1000,
        maxCount = 1000
    },

    {
        itemName = 'GADGET_PARACHUTE',
        itemLabel = 'Fallschirm',
        itemType = 'item',
        shopType = 'gunshop',
        itemPrice = 1000,
        count = 1000,
        maxCount = 1000
    }
}