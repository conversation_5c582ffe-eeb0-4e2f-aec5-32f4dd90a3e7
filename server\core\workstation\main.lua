local ESX = exports['es_extended']:getSharedObject()

local PerformanceMode = false

RegisterCommand('WorkstationGoesBrrr', function(source, args)
    if source == 0 then
        if args ~= nil then
            if args[1] == 'on' then
                PerformanceMode = true
            else
                PerformanceMode = false
            end
        end
    end
end)

local function LeihArbeiterPrint(what, typo)
    if what ~= nil and typo ~= nil then
        if type(what) == 'string' and type(typo) == 'string' then
            if typo == 'error' then
                print('[^1Workstation^0] '..what)
            else
                print('[^3Workstation^0] '..what)
            end
        end
    end
end

local Workstations = {}
--[[
El Tablo Structure Men
Workstations[identifier] = {
    stationName = confName,
    startCount = itemCount,
    currItem = itemName,
    currCount = itemCount,
    currOutput = confData.output,
    currOutputCount = 0,
    threadTick = 60,
    finished = false,
    legal = false
}
]]
--Database Stuff
MySQL.ready(function()
    MySQL.Async.fetchAll('SELECT * FROM workstations', {}, function(result)
        if result then
            for k, v in pairs(result) do
                local currentStationData = json.decode(v.stationData)
                Workstations[v.owner] = {
                    stationName = currentStationData.stationName,
                    startCount = currentStationData.startCount,
                    currItem = currentStationData.currItem,
                    currCount = currentStationData.currCount,
                    currOutput = currentStationData.currOutput,
                    currOutputCount = currentStationData.currOutputCount,
                    threadTick = currentStationData.threadTick,
                    finished = currentStationData.finished,
                    legal = currentStationData.legal
                }
            end
            LeihArbeiterPrint('Created Workstations Table!', 'LeonHasstDiesenTrick')
        end
    end)
end)
function CreateWorkstaionInDatabase(ownerIdentifier, stationTable)
    if ownerIdentifier ~= nil and stationTable ~= nil then
        MySQL.Async.insert('INSERT INTO workstations (owner, stationData) VALUES(@owner, @stationData)', { ['@owner'] = ownerIdentifier, ['@stationData'] = json.encode(stationTable) }, function(affectedRows)
            LeihArbeiterPrint('Workstation: '..ownerIdentifier..' Created: '..affectedRows, 'LeonHasstDiesenTrick')
        end)
    end
end
function SaveWorkstationToDatabase(ownerIdentifier, stationTable)
    if ownerIdentifier ~= nil and stationTable ~= nil then
        MySQL.Async.execute('UPDATE workstations SET stationData = @LuaData WHERE owner = @LuaOwner', { ['@LuaData'] = json.encode(stationTable), ['@LuaOwner'] = ownerIdentifier }, function(rowsChanged)
            if rowsChanged > 0 then
                LeihArbeiterPrint('Workstation: '..ownerIdentifier..' Updated Table!', 'LeonHasstDiesenTrick')
            end
        end)
    end
end
function DeleteWorkstationFromDatabase(ownerIdentifier)
    if ownerIdentifier ~= nil then
        MySQL.Async.execute('DELETE FROM workstations WHERE owner = @LuaOwner', { ['@LuaOwner'] = ownerIdentifier }, function(rowsChanged)
            if rowsChanged > 0 then
                LeihArbeiterPrint('Workstation Deleted: '..ownerIdentifier, 'LeonHasstDiesenTrick')
            end
        end)
    end
end

--Load Onjoin
local CheckedJoin = {}
RegisterNetEvent('cc_workstation:joinCheck', function()
    if not CheckedJoin[source] then
        CheckedJoin[source] = true
        local PlayerIdentifier = ESX.GetPlayerIdentifier(source)
        if PlayerIdentifier ~= nil then
            for workstationId, workstationData in pairs(Workstations) do
                if PlayerIdentifier == workstationId then
                    if not Workstations[PlayerIdentifier].finished then
                        StartWorkstationThread(source, PlayerIdentifier, false)
                        TriggerClientEvent('cc_workstation:upldateClientUi', source, Workstations[PlayerIdentifier])
                        -- print('Player: ^3'..PlayerIdentifier..'^0 Joined and has a Running Workstation: '..Workstations[PlayerIdentifier].stationName..' Items Remaining: '..Workstations[PlayerIdentifier].currItem..' x' ..Workstations[PlayerIdentifier].currCount)
                    else
                        Wait(math.random(2500, 10000))
                        TriggerClientEvent('cc_core:hud:notify', source, 'success', 'Workstation', Workstations[PlayerIdentifier].stationName..' Wartet auf Abholung!')
                    end
                end
            end
        end
    end
end)

AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    CheckedJoin[source] = true
    if xPlayer.identifier ~= nil then
        for workstationId, workstationData in pairs(Workstations) do
            if xPlayer.identifier == workstationId then
                if not Workstations[xPlayer.identifier].finished then
                    StartWorkstationThread(playerId, xPlayer.identifier, false)
                    TriggerClientEvent('cc_workstation:upldateClientUi', playerId, Workstations[xPlayer.identifier])
                    -- print('Player: ^3'..xPlayer.identifier..'^0 Joined and has a Running Workstation: '..Workstations[xPlayer.identifier].stationName..' Items Remaining: '..Workstations[xPlayer.identifier].currItem..' x' ..Workstations[xPlayer.identifier].currCount)
                else
                    Wait(math.random(2500, 10000))
                    TriggerClientEvent('cc_core:hud:notify', playerId, 'success', 'Workstation', Workstations[xPlayer.identifier].stationName..' Wartet auf Abholung!')
                end
            end
        end
    end
end)

--Server Functions
function StartWorkstation(source, name, identifier, itemName, itemCount)
    if source ~= nil and name ~= nil and identifier ~= nil and itemName ~= nil and itemCount ~= nil then
        for confName, confData in pairs(Config_Workstation.Workstations) do
            if name == confName then
                if itemCount > confData.stationLimit then
                    TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'Menge ist über dem Limit!')
                    break
                end
                Workstations[identifier] = {
                    stationName = confName,
                    startCount = itemCount,
                    currItem = itemName,
                    currCount = itemCount,
                    currOutput = confData.output,
                    currOutputCount = 0,
                    threadTick = tonumber(confData.time * 1000),
                    finished = false,
                    legal = confData.legal
                }
                StartWorkstationThread(source, identifier, true)
                TriggerClientEvent('cc_workstation:upldateClientUi', source, Workstations[identifier])
            end
        end
    end
end

function StartWorkstationThread(threadOwner, threadIdentifier, firstTime)
    if threadOwner ~= nil and threadIdentifier ~= nil and firstTime ~= nil then
        local ThreadSource = threadOwner
        local ThreadId = threadIdentifier
        LeihArbeiterPrint('Player: '..ThreadSource..' '..ThreadId..' Started Station: '..Workstations[ThreadId].stationName..' Items Remaining: '..Workstations[ThreadId].currItem..' x' ..Workstations[ThreadId].currCount, 'LeonHasstDiesenTrick')
        TriggerClientEvent('cc_workstation:closeUi', ThreadSource)
        TriggerClientEvent('cc_workstation:toggleState', ThreadSource, Workstations[ThreadId].stationName)
        TriggerClientEvent('cc_core:hud:notify', ThreadSource, 'success', 'Workstation', Workstations[ThreadId].stationName..' Startet!')
        if firstTime then
            ESX.RemovePlayerInventoryItem(ThreadSource, Workstations[ThreadId].currItem, Workstations[ThreadId].currCount, GetCurrentResourceName())
            CreateWorkstaionInDatabase(ThreadId, Workstations[ThreadId])
        end
        CreateThread(function()
            while true do
                Citizen.Wait(Workstations[ThreadId].threadTick)
                if ThreadSource ~= nil and ThreadId ~= nil and Workstations[ThreadId] ~= nil then
                    if GetPlayerName(ThreadSource) ~= nil then
                        if Workstations[ThreadId].currCount >= 1 then
                            if PerformanceMode then
                                local SaveChance = math.random(1, 15)
                                if SaveChance == 3 then
                                    SaveWorkstationToDatabase(ThreadId, Workstations[ThreadId])
                                end
                            else
                                SaveWorkstationToDatabase(ThreadId, Workstations[ThreadId])
                            end
                            Workstations[ThreadId].currCount = Workstations[ThreadId].currCount -1
                            Workstations[ThreadId].currOutputCount = Workstations[ThreadId].currOutputCount +1
                            TriggerClientEvent('cc_workstation:upldateClientUi', ThreadSource, Workstations[ThreadId])
                        else
                            Workstations[ThreadId].finished = true
                            TriggerClientEvent('cc_workstation:upldateClientUi', ThreadSource, Workstations[ThreadId])
                            TriggerClientEvent('cc_core:hud:notify', ThreadSource, 'success', 'Workstation', 'Warte auf Abholung!')
                            break
                        end
                    else
                        LeihArbeiterPrint('Player: '..ThreadId..' Quitted! Saving Workstation And Stopping Thread!', 'LeonHasstDiesenTrick')
                        SaveWorkstationToDatabase(ThreadId, Workstations[ThreadId])
                        break
                    end
                end
            end
        end)
    end
end

function IsValidTime()
    local currentHour = tonumber(os.date('%H'))
    if currentHour >= 2 and currentHour <= 9 then
        return false
    else
        return true
    end
end

--Server Events
RegisterNetEvent('cc_workstation:startWorkstation', function(currPos, item, count)
    if GetPlayerRoutingBucket(source) == 0 then
        if currPos ~= nil and item ~= nil and count ~= nil then
            if count ~= 0 then
                if #(ESX.GetPlayerCoords(source, true) - Config_Workstation.Workstations[currPos].coords) <= 25.0 then
                    if ESX.GetPlayerInventoryItem(source, item).count >= count then
                        local PlayerIdentifier = ESX.GetPlayerIdentifier(source)
                        if PlayerIdentifier ~= nil then
                            local canGo = true
                            if not Config_Workstation.Workstations[currPos].legal then
                                if IsValidTime() then
                                    print('startWorkstation Illegal canGo = true')
                                    canGo = true
                                else
                                    print('startWorkstation Illegal canGo = false')
                                    canGo = false
                                end
                            end
                            if canGo then
                                if not Workstations[PlayerIdentifier] then
                                    StartWorkstation(source, currPos, PlayerIdentifier, item, count)
                                else
                                    TriggerClientEvent('cc_workstation:gotClientRequest', source, item, count)
                                end
                            else
                                TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'Geht um diese Uhrzeit nicht!')
                            end
                        else
                            TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'FEHLER - ID [Im Support Melden mit @CLS]!')
                        end
                    else
                        TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'So viel Hast du nicht!')
                    end
                end
            else
                TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'Wähle Erst eine Richtige Menge aus!')
            end
        end
    else
        TriggerEvent("EasyAdmin:banPlayer", source, "[Workstation Abuse] No No Square.", false, "Afrika")
    end
end)

local TaschenStations = {
    'Heroin Workstation',
    'Ecstasy Workstation',
    'Speed Workstation'
}

local function NeedTasche(currName)
    for _, name in pairs(TaschenStations) do
        if currName == name then
            return true
        end
    end
    return false
end

RegisterNetEvent('cc_workstation:takeItems', function(currentPos)
    if currentPos ~= nil then
        if GetPlayerRoutingBucket(source) == 0 then
            local PlayerIdentifier = ESX.GetPlayerIdentifier(source)
            if PlayerIdentifier ~= nil then
                if Workstations[PlayerIdentifier] ~= nil then
                    -- print(source, Workstations[PlayerIdentifier].stationName, currentPos, (Workstations[PlayerIdentifier].stationName == currentPos))
                    if Workstations[PlayerIdentifier].stationName == currentPos then
                        local go = false
                        if NeedTasche(Workstations[PlayerIdentifier].stationName) then
                            if ESX.GetPlayerInventoryItem(source, 'bigbag').count > 0 then
                                go = true
                            end
                        else
                            go = true
                        end
                        if go then
                            local canTake = true
                            if not Config_Workstation.Workstations[currentPos].legal then
                                if IsValidTime() then
                                    canTake = true
                                else
                                    canTake = false
                                end
                            end
                            if canTake then
                                if Workstations[PlayerIdentifier].finished then
                                    if Workstations[PlayerIdentifier].currOutputCount > 0 then
                                        ESX.AddPlayerInventoryItem(source, Workstations[PlayerIdentifier].currOutput, Workstations[PlayerIdentifier].currOutputCount, GetCurrentResourceName())
                                        TriggerClientEvent('cc_workstation:toggleState', source, nil)
                                        DeleteWorkstationFromDatabase(PlayerIdentifier)
                                        Workstations[PlayerIdentifier] = nil
                                        TriggerClientEvent('cc_core:hud:notify', source, 'success', 'Workstation', 'Erfolgreich Items Entzogen!')
                                    else
                                        TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'Du kannst noch nichts Entnehmen!')
                                    end
                                else
                                    if Workstations[PlayerIdentifier].currOutputCount > 0 then
                                        local OldTake = Workstations[PlayerIdentifier].currOutputCount
                                        ESX.AddPlayerInventoryItem(source, Workstations[PlayerIdentifier].currOutput, Workstations[PlayerIdentifier].currOutputCount, GetCurrentResourceName())
                                        Workstations[PlayerIdentifier].currOutputCount = 0
                                        Workstations[PlayerIdentifier].startCount = Workstations[PlayerIdentifier].startCount - OldTake
                                        TriggerClientEvent('cc_core:hud:notify', source, 'success', 'Workstation', 'Erfolgreich: '..OldTake..' Items Entzogen!')
                                        TriggerClientEvent('cc_workstation:upldateClientUi', source, Workstations[PlayerIdentifier])
                                        SaveWorkstationToDatabase(PlayerIdentifier, Workstations[PlayerIdentifier])
                                        if Workstations[PlayerIdentifier].currOutputCount == Workstations[PlayerIdentifier].startCount then
                                            TriggerClientEvent('cc_workstation:toggleState', source, nil)
                                            DeleteWorkstationFromDatabase(PlayerIdentifier)
                                            local EndTable = {
                                                stationName = Workstations[PlayerIdentifier].stationName,
                                                startCount = 0,
                                                currItem = Workstations[PlayerIdentifier].currItem,
                                                currCount = 0,
                                                currOutput = 0,
                                                currOutputCount = 0,
                                                threadTick = 0,
                                                finished = false,
                                                legal = false
                                            }
                                            Workstations[PlayerIdentifier] = nil
                                            TriggerClientEvent('cc_workstation:upldateClientUi', source, EndTable)
                                            TriggerClientEvent('cc_core:hud:notify', source, 'success', 'Workstation', 'Erfolgreich Items Entzogen!')
                                        end
                                    else
                                        TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'Du kannst noch nichts Entnehmen!')
                                    end 
                                end
                            else
                                TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'Geht um diese Uhrzeit nicht!')
                            end
                        else
                            TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'Du brauchst eine Feldtasche!')
                        end
                    else
                        TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'Du kannst das hier nicht Rausholen!')
                    end
                else
                    TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'Es läuft keine Workstation!')
                end
            end
        else
            TriggerEvent("EasyAdmin:banPlayer", source, "[Workstation Abuse] No No Square.", false, "Afrika")
        end
    end
end)

RegisterNetEvent('cc_workstation:putNewItems', function(item, count)
    if item ~= nil and count ~= nil then
        local PlayerIdentifier = ESX.GetPlayerIdentifier(source)
        local Workstation = Workstations[PlayerIdentifier]
        if Workstation ~= nil then
            for stationName, stationData in pairs(Config_Workstation.Workstations) do
                if Workstation.stationName == stationName then
                    if not Workstation.finished then
                        if (Workstation.currCount + count) <= stationData.stationLimit then
                            if ESX.GetPlayerInventoryItem(source, item).count >= count then
                                ESX.RemovePlayerInventoryItem(source, item, count, GetCurrentResourceName())
                                Workstation.startCount = Workstation.startCount + count
                                Workstation.currCount = Workstation.currCount + count
                                SaveWorkstationToDatabase(PlayerIdentifier, Workstation)
                                TriggerClientEvent('cc_workstation:upldateClientUi', source, Workstation)
                                TriggerClientEvent('cc_core:hud:notify', source, 'success', 'Workstation', count..' '..stationData.outputLabel..' Nachgelegt!')
                            else
                                TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'So viel hast du nicht!')
                            end
                        else
                            TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Workstation', 'So viel Passt hier nicht rein!')
                        end
                    end
                end
            end
        end
    end
end)

ESX.RegisterServerCallback('cc_workstation:requestCurrentData', function(source, cb, currName)
    if currName ~= nil then
        local PlayerIdentifier = ESX.GetPlayerIdentifier(source)
        if PlayerIdentifier ~= nil then
            if Workstations[PlayerIdentifier] then
                cb(Workstations[PlayerIdentifier])
            else
                for stationName, stationData in pairs(Config_Workstation.Workstations) do
                    if stationName == currName then
                        local ReturnTable = {
                            stationName = currName,
                            startCount = 0,
                            currItem = stationData.input,
                            currCount = 0,
                            currOutput = stationData.output,
                            currOutputCount = 0,
                            threadTick = stationData.time,
                            finished = false,
                            legal = false
                        }
                        cb(ReturnTable)
                    end
                end
            end
        end
    end
end)

--PlayerDropped
AddEventHandler('playerDropped', function()
    CheckedJoin[source] = nil
end)

--[[
{"currOutputCount":464,"startCount":902,"currItem":"tilidin","legal":false,"finished":false,"currOutput":"tilidin2","threadTick":60000,"currCount":438,"stationName":"Tilidin Station"}
]]

--clientcode
workstationCode = [[
ESX = nil
PlayerData = {}

Citizen.CreateThread(function()
    while ESX == nil do
        ESX = exports['es_extended']:getSharedObject()
        Citizen.Wait(10)
    end

    while ESX.GetPlayerData().job == nil do
		Citizen.Wait(10)
	end

	PlayerData = ESX.GetPlayerData()

    StartClientThread()
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
    PlayerData.job = job
end)

PlayerPed = PlayerPedId() 
PlayerCoords = GetEntityCoords(PlayerPed)
CurrentVehicle = GetVehiclePedIsIn(PlayerPed, false)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(2500)
        if PlayerPed ~= PlayerPedId() then
            PlayerPed = PlayerPedId()
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(500)
        if ESX ~= nil then
            PlayerCoords = GetEntityCoords(PlayerPed)
            CurrentVehicle = GetVehiclePedIsIn(PlayerPed, false)
        end
    end
end)

Citizen.CreateThread(function()
    for k, v in pairs(Config_Workstation.Workstations) do
        if v.blip.enabled then
            local blip = AddBlipForCoord(v.coords)
            SetBlipSprite(blip, v.blip.id)
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, v.blip.scale)
            SetBlipColour(blip, v.blip.color)
            SetBlipAsShortRange(blip, v.blip.shortrange)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(v.blip.name)
            EndTextCommandSetBlipName(blip)
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if GetEntityModel(PlayerPedId()) ~= GetHashKey('mp_m_freemode_01') and GetEntityModel(PlayerPedId()) ~= GetHashKey('mp_f_freemode_01') then
            return
        else
            Citizen.Wait(math.random(2500, 10000))
            TriggerServerEvent('cc_workstation:joinCheck')
            break
        end
    end
end)

local CurrentStationName = 'Workstation'
local CurrentStationData = {}
local CurrentAmount = 1
local CurrentStation = nil

--Client Events
RegisterNetEvent('cc_workstation:toggleState', function(stationName)
    CurrentStation = stationName
    print('state is ' .. CurrentStation)
end)

function StartClientThread()
    Citizen.CreateThread(function()
        local show = true
        local msg = ''
        local ThreadSleep = 1000
        while true do
            Citizen.Wait(ThreadSleep)
            if ESX ~= nil then
                local inRange = false
                local NearStation = false
                for stationName, stationData in pairs(Config_Workstation.Workstations) do
                    local haveAccess = true
    
                    if stationData.job ~= 'none' then
                        if PlayerData.job.name ~= stationData.job then
                            haveAccess = false
                        end
                    end
    
                    if haveAccess then
                        if CurrentStation == nil then
                            if #(PlayerCoords - stationData.coords) <= 5.75 then
                                inRange = true
                                NearStation = true
                                msg = 'Drücke E um auf die ' .. stationData.blip.name .. ' zuzugreifen!'
                                CurrentStationName = stationName
                                CurrentStationData = stationData
                                if IsControlJustReleased(1, 38) then
                                    OpenUi(CurrentStationData)
                                end
                            end
                        else
                            if CurrentStation == stationName then
                                if #(PlayerCoords - stationData.coords) <= 5.75 then
                                    inRange = true
                                    NearStation = true
                                    msg = 'Drücke E um auf die ' .. stationData.blip.name .. ' zuzugreifen!'
                                    CurrentStationName = stationName
                                    CurrentStationData = stationData
                                    if IsControlJustReleased(1, 38) then
                                        OpenUi(CurrentStationData)
                                    end
                                end
                            end
                        end 
                    end
                end
                if not show and inRange then
                    exports['cc_core']:showHelpNotification(msg, 'E')
                    show = true
                elseif show and not inRange then
                    exports['cc_core']:closeHelpNotification()
                    show = false
                end
                if NearStation then
                    ThreadSleep = 0
                else
                    CurrentStationName = 'Workstation'
                    CurrentStationData = {}
                    ThreadSleep = 1000
                end
            end
        end
    end)
end

local Buildstation = {}

RegisterNetEvent('cc_workstation:updateBuild', function(buildTable)
    if buildTable ~= nil then
        Buildstation = buildTable
    end
end)

function OpenUi(currentData)
    if currentData ~= nil then
        ESX.TriggerServerCallback('cc_workstation:requestCurrentData', function(buildTable)
            if buildTable ~= nil then
                Buildstation = buildTable
                SetNuiFocus(true, true)
                print(CurrentStationData.time)
                if Buildstation.finished then
                    SendNUIMessage({
                        script = 'workstation',
                        action = 'show',
                        finished = true,
                        updateTime = CurrentStationData.time,
                        itemLimit = currentData.stationLimit,
                        itemLabel = currentData.inputLabel,
                        startCount = Buildstation.startCount,
                        outputLabel = currentData.outputLabel,
                        outputCount = Buildstation.currOutputCount,
                        output = currentData.output,
                        input = currentData.input,
                        currAmount = CurrentAmount
                    })
                else
                    SendNUIMessage({
                        script = 'workstation',
                        action = 'show',
                        finished = false,
                        updateTime = CurrentStationData.time,
                        itemLimit = currentData.stationLimit,
                        itemLabel = currentData.inputLabel,
                        startCount = Buildstation.startCount,
                        outputLabel = currentData.outputLabel,
                        outputCount = Buildstation.currOutputCount,
                        output = currentData.output,
                        input = currentData.input,
                        currAmount = CurrentAmount
                    })
                end
            end
        end, CurrentStationName)
    end
end

RegisterNetEvent('cc_workstation:upldateClientUi', function(uiData)
    if uiData ~= nil then
        Buildstation = uiData
        SendNUIMessage({
            script = 'workstation',
            action = 'updateUI',
            finished = Buildstation.finished,
            updateTime = CurrentStationData.time,
            itemLimit = CurrentStationData.stationLimit,
            itemLabel = CurrentStationData.inputLabel,
            startCount = Buildstation.startCount,
            outputLabel = CurrentStationData.outputLabel,
            outputCount = Buildstation.currOutputCount,
            output = CurrentStationData.output,
            input = CurrentStationData.input,
            currAmount = CurrentAmount
        })
    end
end)

RegisterNUICallback('updateUi', function(countData)
    if countData ~= nil then
        if CurrentStationData ~= nil then
            for _, intCount in pairs(countData) do
                CurrentAmount = tonumber(intCount)
                SendNUIMessage({
                    script = 'workstation',
                    action = 'updateUI',
                    finished = false,
                    updateTime = CurrentStationData.time,
                    itemLimit = CurrentStationData.stationLimit,
                    itemLabel = CurrentStationData.inputLabel,
                    startCount = Buildstation.startCount,
                    outputLabel = CurrentStationData.outputLabel,
                    outputCount = Buildstation.currOutputCount,
                    output = CurrentStationData.output,
                    input = CurrentStationData.input,
                    currAmount = CurrentAmount
                })
            end
        end
    end
end)

RegisterNUICallback('workstation/startButton', function()
    if CurrentStationData ~= nil then
        print('Start:', CurrentStationName)
        TriggerServerEvent('cc_workstation:startWorkstation', CurrentStationName, CurrentStationData.input, CurrentAmount)
    end
end)

RegisterNUICallback('workstation/takeButton', function()
    if CurrentStationData ~= nil then
        print('Take:', CurrentStationName)
        TriggerServerEvent('cc_workstation:takeItems', CurrentStationName)
    end
end)

RegisterNetEvent('cc_workstation:closeUi', function()
    SendNUIMessage({
        action = 'closeUI'
    })
end)

RegisterNetEvent('cc_workstation:gotClientRequest', function(item, count)
    if item ~= nil and count ~= nil then
        TriggerServerEvent('cc_workstation:putNewItems', item, count)
    end
end)

RegisterNUICallback('workstation/escape', function()
    SetNuiFocus(false, false)
end)
]]