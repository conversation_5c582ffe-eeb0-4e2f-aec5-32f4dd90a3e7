-- local DISCORD_TOKEN = "MTA1NzM0MTgyNjAxMDQ1MjA5MQ.GDTJoa.PKr_cMKuhF1W2c2uZHr8dfcRYMq7tYDwNrL5gE"
-- local IMAGE_CHANNEL_ID = "1353590136440623186"
-- local ANNOUNCEMENT_CHANNEL_ID = "1342157315347841105"
-- local playerCount = 0
-- ESX = exports["es_extended"]:getSharedObject()

-- local lastUpdated = 0  -- Zeit des letzten Updates
-- local imagesCache = {}  -- C<PERSON> für die Bilder, um sie nicht jedes Mal neu zu holen

-- function getDiscordImages(cb)
--     -- Prü<PERSON>, ob 24 Stunden vergangen sind
--     local currentTime = os.time()
--     if currentTime - lastUpdated < 86400 then  -- 86400 Sekunden = 24 Stunden
--         return cb(imagesCache)  -- Gibt die gecachten Bilder zurück, wenn weniger als 24 Stunden vergangen sind
--     end

--     -- Wenn 24 Stunden vergangen sind, rufe neue Bilder ab
--     PerformHttpRequest("https://discord.com/api/v10/channels/" .. IMAGE_CHANNEL_ID .. "/messages?limit=10", function(err, text, headers)
--         if err ~= 200 then
--             print("Fehler beim Abrufen der Bilder:", err)
--             return cb({})
--         end

--         local messages = json.decode(text)
--         local images = {}

--         -- Füge alle Bilder in eine Liste ein
--         for _, message in pairs(messages) do
--             if message.attachments and #message.attachments > 0 then
--                 for _, attachment in pairs(message.attachments) do
--                     if attachment.url:match(".png") or attachment.url:match(".jpg") or attachment.url:match(".jpeg") then
--                         table.insert(images, {url = attachment.url, reactions = message.reactions or {}})
--                     end
--                 end
--             end
--         end

--         -- Sortiere die Bilder nach der Anzahl der Reaktionen
--         table.sort(images, function(a, b)
--             local aReactions = #a.reactions
--             local bReactions = #b.reactions
--             return aReactions > bReactions  -- Sortiere absteigend
--         end)

--         -- Nimm nur die 3 Bilder mit den meisten Reaktionen
--         imagesCache = {}
--         for i = 1, math.min(3, #images) do
--             table.insert(imagesCache, images[i].url)
--         end

--         lastUpdated = currentTime  -- Setze die Zeit des letzten Updates
--         cb(imagesCache)
--     end, "GET", "", { ["Authorization"] = "Bot " .. DISCORD_TOKEN })
-- end

-- function getLatestAnnouncements(cb)
--     PerformHttpRequest("https://discord.com/api/v10/channels/" .. ANNOUNCEMENT_CHANNEL_ID .. "/messages?limit=5", function(err, text, headers)
--         if err ~= 200 then
--             print("Fehler beim Abrufen der Ankündigungen:", err)
--             return cb({})
--         end

--         local messages = json.decode(text)
--         local announcements = {}

--         for _, message in pairs(messages) do
--             if message.author and message.content then
--                 local timestamp = os.date("%d.%m.%Y %H:%M", os.time())
--                 local avatarUrl = message.author.avatar and "https://cdn.discordapp.com/avatars/" .. message.author.id .. "/" .. message.author.avatar .. ".png" or "https://cdn.discordapp.com/embed/avatars/0.png"

--                 table.insert(announcements, {
--                     username = message.author.username,
--                     avatar = avatarUrl,
--                     content = message.content,
--                     timestamp = timestamp
--                 })
--             end
--         end

--         cb(announcements)
--     end, "GET", "", { ["Authorization"] = "Bot " .. DISCORD_TOKEN })
-- end

-- function getPlayerInfo(src, cb)
--     local xPlayer = ESX.GetPlayerFromId(src)
--     if not xPlayer then
--         return cb(nil)
--     end

--     local identifier = xPlayer.getIdentifier()

--     MySQL.Async.fetchScalar('SELECT dateofbirth FROM users WHERE identifier = @identifier', {
--         ['@identifier'] = identifier
--     }, function(birthdate)
--         if not birthdate then birthdate = "Unbekannt" end

--         MySQL.Async.fetchScalar('SELECT timePlay FROM users WHERE identifier = @identifier', {
--             ['@identifier'] = identifier
--         }, function(playtime)
--             if not playtime then
--                 playtime = "Unbekannt"
--             end

--             MySQL.Async.fetchScalar('SELECT married FROM users WHERE identifier = @identifier', {
--                 ['@identifier'] = identifier
--             }, function(married)
--                 if not married then
--                     married = "Nicht verheiratet"
--                 end

--                 MySQL.Async.fetchScalar('SELECT COUNT(*) FROM hex_properties WHERE owner = @owner', {
--                     ['@owner'] = identifier
--                 }, function(houseCount)
--                     local houseStatus = (houseCount > 0) and "Im Besitz" or "Nicht im Besitz"

--                     MySQL.Async.fetchScalar('SELECT COUNT(*) FROM owned_vehicles WHERE owner = @steamID', {
--                         ['@steamID'] = identifier
--                     }, function(vehicleCount)
--                         if not vehicleCount then vehicleCount = 0 end
        
--                         local playerInfo = {
--                             name = xPlayer.getName(),
--                             date = birthdate,
--                             height = xPlayer.get("height") or "180cm",
--                             gender = 'Männlich',
--                             jobLabel = xPlayer.getJob().label,
--                             jobGrade = xPlayer.getJob().grade_label,
--                             playtime = playtime,
--                             married = married,
--                             vehicleCount = vehicleCount,
--                             houseStatus = houseStatus
--                         }
        
--                         cb(playerInfo)
--                     end)
--                 end)
--             end)
--         end)
--     end)
-- end

-- function getJobCounts(cb)
--     local jobCounts = {
--         mechanic = 0,
--         ambulance = 0,
--         taxi = 0,
--         abschlepper = 0,
--         police = 0,
--         doj = 0,
--         unemployed = 0
--     }

--     for _, playerId in ipairs(GetPlayers()) do
--         local xPlayer = ESX.GetPlayerFromId(playerId)
--         if xPlayer then
--             local job = xPlayer.getJob().name
--             if jobCounts[job] then
--                 jobCounts[job] = jobCounts[job] + 1
--             else
--                 jobCounts.unemployed = jobCounts.unemployed + 1
--             end
--         end
--     end

--     cb(jobCounts)
-- end

-- RegisterCommand('openPauseMenu', function(source)
--     getDiscordImages(function(images)
--         getLatestAnnouncements(function(announcements)
--             local rightImages = {images[1] or "", images[2] or ""}
--             local middleImage = images[3] or ""

--             TriggerClientEvent("showPauseMenu", source, rightImages, middleImage, announcements)
--         end)
--     end)
--     getPlayerInfo(source, function(playerInfo)
--         TriggerClientEvent("cc_core:pause:updateprofile", source, playerInfo.name, playerInfo.date, playerInfo.height, playerInfo.gender, playerInfo.jobLabel, playerInfo.jobGrade, playerInfo.playtime, playerInfo.married, playerInfo.vehicleCount, playerInfo.houseStatus)
--     end)
--     getJobCounts(function(jobCounts)
--         TriggerClientEvent("cc_core:pause:updateFraktionPage", source, jobCounts)
--     end)
-- end)

-- -- **Spieleranzahl aktualisieren**
-- function updatePlayerCount()
--     playerCount = #GetPlayers()
-- end

-- AddEventHandler('playerConnecting', function()
--     Citizen.Wait(2000)
--     updatePlayerCount()
--     TriggerClientEvent('cc_core:pause:updatePlayerCount', -1, playerCount)
-- end)

-- AddEventHandler('playerDropped', function()
--     updatePlayerCount()
--     TriggerClientEvent('cc_core:pause:updatePlayerCount', -1, playerCount)
-- end)

-- RegisterServerEvent('cc_core:pause:requestPlayerCount')
-- AddEventHandler('cc_core:pause:requestPlayerCount', function()
--     local src = source
--     TriggerClientEvent('cc_core:pause:updatePlayerCount', src, playerCount) 
-- end)

-- AddEventHandler('onResourceStart', function(resourceName)
--     if resourceName == GetCurrentResourceName() then
--         updatePlayerCount()
--         TriggerClientEvent('cc_core:pause:updatePlayerCount', -1, playerCount)
--     end
-- end)

-- --clientcode
-- pausemenuCode = [[
-- Citizen.CreateThread(function()
--     Citizen.Wait(2000)
--     TriggerServerEvent('cc_core:pause:requestPlayerCount')
-- end)

-- RegisterKeyMapping('openPauseMenu2', 'Öffne das Pause Menü', 'keyboard', 'F11')

-- RegisterCommand("openPauseMenu2", function()
--     ExecuteCommand("openPauseMenu")
-- end, false)

-- RegisterNetEvent("showPauseMenu")
-- AddEventHandler("showPauseMenu", function(rightImages, middleImage, announcements)
--     SetNuiFocus(true, true)

--     SendNUIMessage({
--         script = 'pausemenu',
--         action = 'show',
--         rightImages = rightImages,
--         middleImage = middleImage,
--         announcements = announcements
--     })
-- end)

-- RegisterNetEvent("cc_core:pause:updateprofile")
-- AddEventHandler("cc_core:pause:updateprofile", function(name, date, height, gender, jobLabel, jobGrade, playtime, married, vehicleCount, houseStatus)
--     if vehicleCount == nil then
--         vehicleCount = 0
--     end
--     SetNuiFocus(true, true)

--     SendNUIMessage({
--         script = 'pausemenu',
--         action = 'show',
--         name = name,
--         date = date,
--         height = height,
--         gender = gender,
--         jobLabel = jobLabel,
--         jobGrade = jobGrade,
--         playtime = playtime,
--         married = married,
--         vehicleCount = vehicleCount,
--         houseStatus = houseStatus
--     })
-- end)


-- RegisterNetEvent('cc_core:pause:updatePlayerCount')
-- AddEventHandler('cc_core:pause:updatePlayerCount', function(count)
--     Citizen.Wait(2000)
--     SendNUIMessage({
--         script = 'pausemenu',
--         action = 'playercount',
--         playercount = count
--     })
-- end)

-- RegisterNetEvent("cc_core:pause:updateFraktionPage")
-- AddEventHandler("cc_core:pause:updateFraktionPage", function(jobCounts)
--     SetNuiFocus(true, true)
    
--     SendNUIMessage({
--         script = 'pausemenu',
--         action = 'fraktionpage',
--         mechanic = jobCounts.mechanic,
--         ambulance = jobCounts.ambulance,
--         taxi = jobCounts.taxi,
--         abschlepper = jobCounts.abschlepper,
--         lspd = jobCounts.police,
--         doj = jobCounts.doj,
--         unemployed = jobCounts.unemployed
--     })
-- end)

-- RegisterNUICallback('pause/escape', function(data, cb)
--   SetNuiFocus(false, false)
-- end)
-- ]]