local players = {}

AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    if not players[playerId] then
        players[playerId] = {}
        players[playerId].playerId = playerId
        players[playerId].identifier = xPlayer.getIdentifier()
        players[playerId].job = xPlayer.getJob().name
        players[playerId].jobLabel = xPlayer.getJob().label
        players[playerId].jobGrade = xPlayer.getJob().grade
        players[playerId].job3 = xPlayer.getJob3().name
        players[playerId].job3Label = xPlayer.getJob3().label
        players[playerId].job3Grade = xPlayer.getJob3().grade
        players[playerId].group = xPlayer.getGroup()
        players[playerId].jobDienst = xPlayer.getJob().dienst
        players[playerId].rpName = xPlayer.getRPName()
    end
end)

AddEventHandler('esx:setJob', function(playerId, job, lastJob)
    if players[playerId] then
        players[playerId].job = job.name
        players[playerId].jobLabel = job.label
        players[playerId].jobGrade = job.grade
    end
end)

AddEventHandler('esx:setJob3', function(playerId, job, lastJob)
    if players[playerId] then
        players[playerId].job3 = job.name
        players[playerId].job3Label = job.label
        players[playerId].job3Grade = job.grade
    end
end)

AddEventHandler('esx:setJobDienst', function(playerId, job, state)
    if players[playerId] then
        players[playerId].jobDienst = state
    end
end)

AddEventHandler('playerDropped', function(reason)
    local playerId = source
    if players[playerId] then
        players[playerId] = nil
    end
end)

local function isJobLegal(jobName)
    if jobName == 'mechanic' or jobName == 'police' or jobName == 'ambulance' or jobName == 'fib' or jobName == 'army' then
        return true
    end
    
    return false
end

local function GetPlayersFix()
    return players
end

exports('GetPlayersFix', GetPlayersFix)
exports('isJobLegal', isJobLegal)