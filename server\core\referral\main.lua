RegisterServerEvent("cc_core:referral:getData")
AddEventHandler("cc_core:referral:getData", function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    local identifier = ESX.GetPlayerIdentifier(src)

    MySQL.Async.fetchAll("SELECT referral_code, referral_bonus FROM users WHERE identifier = @identifier", {
        ['@identifier'] = identifier
    }, function(result)
        local referralCode = result[1].referral_code
        local bonusCount = result[1].referral_bonus or 0

        local fakePlayers = {}
        for playerName, _ in pairs(Config_Referral.ReferralPlayers) do
            table.insert(fakePlayers, {name = playerName, progress = 20})
        end

        TriggerClientEvent("cc_core:referral:showUI", src, {
            referralCode = referralCode,
            invites = bonusCount,
            achievements = Config_Referral.Rewards,
            players = fakePlayers
        })
    end)
end)

RegisterServerEvent("cc_core:referral:claimReward")
AddEventHandler("cc_core:referral:claimReward", function(rewardId)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)

    local reward = Config_Referral.Rewards[rewardId]

    if reward then
        if reward.item == "bank" then
            xPlayer.addAccountMoney('bank', reward.amount)
        elseif reward.item == "coin" then
            xPlayer.addInventoryItem("coin", reward.amount)
        end
        TriggerClientEvent("esx:showNotification", src, "Belohnung erfolgreich abgeholt!")
    end
end)

--clientcode
referralCode = [[
RegisterCommand("openreferral", function()
    SetNuiFocus(true, true)
    TriggerServerEvent("cc_core:referral:getData")
end)

RegisterNetEvent("cc_core:referral:showUI")
AddEventHandler("cc_core:referral:showUI", function(referralData)
    SendNUIMessage({
        script = "referral",
        action = "show",
        referralcode = referralData.referralCode,
        invites = referralData.invites,
        achievements = referralData.achievements,
        players = referralData.players
    })
end)

RegisterNUICallback("referral/escape", function(data, cb)
    SetNuiFocus(false, false)
    cb({})
end)

RegisterNUICallback("referral/copy", function(data, cb)
    TriggerEvent("chat:addMessage", {
        color = {0, 255, 0},
        multiline = true,
        args = {"System", "Dein Referral-Code wurde kopiert: " .. data.code}
    })
    cb({})
end)

RegisterNUICallback("referral/claimReward", function(data, cb)
    TriggerServerEvent("cc_core:referral:claimReward", data.rewardId)
    cb({})
end)

RegisterNetEvent('cc_core:referral:copyToClipboard')
AddEventHandler('cc_core:referral:copyToClipboard', function(referralCode)
    SendNUIMessage({
        script = "referral",
        action = "copyToClipboard",
        text = referralCode
    })
end)

RegisterKeyMapping('openreferral', 'Öffne das ReferralRedeem Menu', 'keyboard', 'F10')
]]