ESX = exports['es_extended']:getSharedObject()

AddEventHandler("txAdmin:events:announcement", function(data)
    ccTxAdmin.ServerAnnounce(-1, data.author..": "..data.message)
end)

AddEventHandler('txAdmin:events:scheduledRestart', function(eventData)
    if eventData.secondsRemaining == 1800 then
        ccTxAdmin.ServerAnnounce(-1, 'Der Server wird in 30 Minuten neugestartet')
    elseif eventData.secondsRemaining == 900 then
        ccTxAdmin.ServerAnnounce(-1, 'Der Server wird in 15 Minuten neugestartet')
    elseif eventData.secondsRemaining == 600 then
        ccTxAdmin.ServerAnnounce(-1, 'Der Server wird in 10 Minuten neugestartet')
    elseif eventData.secondsRemaining == 300 then
        ccTxAdmin.ServerAnnounce(-1, 'Der Server wird in 5 Minuten neugestartet')
     elseif eventData.secondsRemaining == 180 then
        ccTxAdmin.ServerAnnounce(-1, 'Der Server wird in 3 Minuten neugestartet')
    elseif eventData.secondsRemaining == 60 then
        ccTxAdmin.ServerAnnounce(-1, 'Der Server wird in 1 Minuten neugestartet')
    end
end)

--AddEventHandler("txAdmin:events:playerKicked", function(eventData)
    --TriggerClientEvent("fs_base:hud:announce", -1, 'Der Spieler ' .. GetPlayerName(eventData.target) .. ' wurde von ' .. eventData.author .. ' gekickt. Grund: ' .. eventData.reason, 4000, "Final Story - txAdmin")
--end)

--AddEventHandler("txAdmin:events:playerBanned", function(eventData)
    --TriggerClientEvent("fs_base:hud:announce", -1, 'Der Spieler ' .. eventData.targetName .. ' wurde von ' .. eventData.author .. ' gebannt. Grund: ' .. eventData.reason, 5500, "Final Story - txAdmin")
--end)

--AddEventHandler("txAdmin:events:playerWarned", function(eventData)
    --TriggerClientEvent("fs_base:hud:announce", -1, 'Der Spieler ' .. GetPlayerName(eventData.target) .. ' wurde von ' .. eventData.author .. ' verwarnt. Grund: ' .. eventData.reason, 3500, "Final Story - txAdmin")
--end)