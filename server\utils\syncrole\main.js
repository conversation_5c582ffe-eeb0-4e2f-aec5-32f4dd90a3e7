const https = require("https");

exports("getUserDiscordRoles", async (user, guild, token) => {
    const options = {
        hostname: "discord.com",
        path: `/api/v8/guilds/${guild}/members/${user}`,
        method: "GET",
        headers: {
            Authorization: `Bot ${token}`,
            "Content-Type": "application/json",
        },
    };

    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let body = "";

            res.on("data", (chunk) => {
                body += chunk;
            });

            res.on("end", () => {
                if (res.statusCode === 200) {
                    try {
                        const data = JSON.parse(body);
                        resolve(data.roles);
                    } catch (e) {
                        reject(`Failed to parse response body: ${e.message}`);
                    }
                }
            });
        });

        req.on("error", (err) => {
            reject(`Request to Discord API failed: ${err}`);
        });

        req.end();
    });
});
