RegisterServerEvent('cc_core:jail:jailPlayer')
AddEventHandler('cc_core:jail:jailPlayer', function(target, time, reason)
    local playerId = source

    if GetPlayerName(target) ~= nil then
        local job = ESX.GetPlayerJob(playerId).name

        if job == 'doj' or job == 'marshal' or job == 'police' or job == 'army' or job == 'fib' or job == 'sheriff' then
            TriggerClientEvent('cc_core:jail:jailPlayer', target, time)
    
            ESX.SetPlayerJail(target, time)
    
            Notify(target, 'Information', 'Du must noch ' .. time .. ' Hafteinheiten absitzen!', 'info')
        end 
    end
end)

RegisterServerEvent('cc_core:jail:unJailPlayer')
AddEventHandler('cc_core:jail:unJailPlayer', function(identifier)
    local playerId = source
    local xPlayer = ESX.GetPlayerFromIdentifier(identifier)

    if xPlayer ~= nil then
        TriggerClientEvent('cc_core:jail:unJailPlayer', xPlayer.source)

        ESX.SetPlayerJail(xPlayer.source, 0)
    else
        MySQL.Async.execute('UPDATE users SET jail = @jail WHERE identifier = @identifier', {
            ['@jail'] = 0,
            ['@identifier'] = identifier
        })
    end

    Notify(playerId, 'Information', 'Du wurdest entlassen, viel Erfolg noch im Leben', 'info')
end)

RegisterServerEvent('cc_core:jail:updateJailTime')
AddEventHandler('cc_core:jail:updateJailTime', function(time)
    local playerId = source

    ESX.SetPlayerJail(playerId, time)
end)

ESX.RegisterServerCallback('cc_jail:retrieveJailedPlayers', function(source, cb)
    local job = ESX.GetPlayerJob(source).name

    if job == 'police' or job == 'army' or job == 'fib' or job == 'sheriff' then
        local persons = {}

        MySQL.Async.fetchAll('SELECT firstname, lastname, jail, identifier FROM users WHERE jail > 1', {}, function(result)
            if result ~= 0 then
                for k, v in pairs(result) do
                    table.insert(persons, {
                        name = v.firstname .. ' ' .. v.lastname,
                        time = v.jail,
                        identifier = v.identifier
                    })
                end

                cb(persons)
            end
        end)
    else
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [JAIL-1]", false, "Afrika")
    end
end)

--clientcode
jailCode = [[
print('jail module loaded')
local show = false
local jailTime = 0

local function isInJail()
    if jailTime >= 1 then
        return true
    else
        return false
    end
end

exports('isInJail', isInJail)

local function unJail()
    ClearPedTasksImmediately(PlayerPedId())
    
    Citizen.Wait(50)

    SetEntityCoords(PlayerPedId(), Config_Jail.Teleports.home)

    ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin)
		TriggerEvent('skinchanger:loadSkin', skin)
	end)

    Notify('Knast', 'Sie wurden entlassen, viel Erfolg noch im Leben', 'info')
end

local function inJail()
    Citizen.CreateThread(function()
        while jailTime > 0 do
            jailTime = jailTime - 1

            Notify('Knast', 'Du must noch ' .. jailTime .. ' Hafteinheiten absitzen', 'info')

            TriggerServerEvent('cc_core:jail:updateJailTime', jailTime)

            if jailTime == 0 then
                unJail()

                TriggerServerEvent('cc_core:jail:updateJailTime', 0)
            end

            Citizen.Wait(60000)
        end
    end)
end

local function jailLogin()
    SetEntityCoords(PlayerPedId(), Config_Jail.JailPositions.cell)

    Notify('Knast', 'Als du schlafen gingst warst du im Knast, daher bist du es jetzt wieder', 'info')

    inJail()
end

local function cam()
	local camOptions = Config_Jail.Cutscene["CameraPosition"]

	camOptions.cameraId = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
    local camCoords, camRotation = camOptions["pos"], camOptions["rotation"]

    SetCamCoord(camOptions.cameraId, camCoords.x, camCoords.y, camCoords.z)
	SetCamRot(camOptions.cameraId, camRotation.x, camRotation.y, camRotation.z)

	RenderScriptCams(true, false, 0, true, true)
end

RegisterNetEvent('cc_core:jail:jailPlayer')
AddEventHandler('cc_core:jail:jailPlayer', function(time)
    jailTime = time

	DoScreenFadeOut(100)
	Citizen.Wait(250)

    TriggerEvent('skinchanger:getSkin', function(skin)
		if GetHashKey(GetEntityModel(PlayerPedId())) == GetHashKey("mp_m_freemode_01") then
			local clothesSkin = {
				['tshirt_1'] = 20, ['tshirt_2'] = 15,
				['torso_1'] = 33, ['torso_2'] = 0,
				['arms'] = 0,
				['pants_1'] = 7, ['pants_2'] = 0,
				['shoes_1'] = 34, ['shoes_2'] = 0,
			}

			--TriggerEvent('skinchanger:loadClothes', skin, clothesSkin)
		else
			local clothesSkin = {
				['tshirt_1'] = 15, ['tshirt_2'] = 0,
				['torso_1'] = 2, ['torso_2'] = 6,
				['arms'] = 2,
				['pants_1'] = 2, ['pants_2'] = 0,
				['shoes_1'] = 35, ['shoes_2'] = 0,
			}

			--TriggerEvent('skinchanger:loadClothes', skin, clothesSkin)
		end
	end)

    while not HasModelLoaded(-1320879687) do
        RequestModel(-1320879687)
        Citizen.Wait(10)
    end
    
    local coords = Config_Jail.Cutscene.policePosition
    local cop = CreatePed(5, -1320879687, coords.x, coords.y, coords.z, coords.w, false, false)
	print('CREATEPED', GetCurrentResourceName())
    TaskStartScenarioInPlace(ped, "WORLD_HUMAN_PAPARAZZI", 0, false)

    local coords = Config_Jail.Cutscene.photoPosition
    local ped = PlayerPedId()
    SetEntityCoords(ped, coords.x, coords.y, coords.z - 1)
    SetEntityHeading(ped, coords.w)
    FreezeEntityPosition(ped, true)

    cam()

    Citizen.Wait(1000)
    DoScreenFadeIn(100)
    Citizen.Wait(10000)
    DoScreenFadeOut(250)

    local coords = Config_Jail.JailPositions.cell
    SetEntityCoords(ped, coords.x, coords.y, coords.z)
    DeleteEntity(cop)
    SetModelAsNoLongerNeeded(-1320879687)

    Citizen.Wait(1000)
    DoScreenFadeIn(250)

    RenderScriptCams(false, false, 0, true, true)
	FreezeEntityPosition(ped, false)
	DestroyCam(Config_Jail.Cutscene["CameraPosition"].cameraId)

    inJail()
end)

local function openJailMenu()
	ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'jail_prison_menu', {
		title = "Gefängnis Menu",
		align = 'top-left',
		elements = {
			{ label = "Spieler Inhaftieren", value = "jail_closest_player" },
			{ label = "Spieler freilassen", value = "unjail_player" }
		}
	}, function(data, menu)
		if data.current.value == "jail_closest_player" then
			menu.close()

			ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'jail_choose_time_menu', {
				title = "Haftzeit in (Minuten)"
			}, function(data2, menu2)
				local jailTime2 = tonumber(data2.value)
				
				if jailTime2 ~= nil then
                    if jailTime2 < 120 then
                        menu2.close()

                        local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()

						if closestPlayer ~= -1 and closestDistance < 3.0 then
							ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'jail_choose_reason_menu', {
								title = "Begründung der Haft"
							}, function(data3, menu3)
								local reason = data3.value
								
								if reason ~= nil then
                                    menu3.close()
			
									local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
			
									if closestPlayer ~= -1 and closestDistance < 3.0 then
                                        TriggerServerEvent("cc_core:jail:jailPlayer", GetPlayerServerId(closestPlayer), jailTime2, reason)
                                    else
										Notify('Knast', 'Kein Spieler in deiner Nähe', 'info')
									end
								else
                                    Notify('Knast', 'du musst die Haft schon begründen', 'info')
								end
							end, function(data3, menu3)
								menu3.close()
							end)
                        else
                            Notify('Knast', 'Kein Spieler in deiner Nähe', 'info')
						end
                    else
                        Notify('Knast', 'Du kannst nur maximal 120 Hafteinheiten geben', 'info')
                    end
                else
                    Notify('Knast', 'gebe die Zeit bitte in Minuten ein', 'info')
				end
			  end, function(data2, menu2)
				menu2.close()
			end)
		elseif data.current.value == "unjail_player" then
			local elements = {}

			ESX.TriggerServerCallback("cc_jail:retrieveJailedPlayers", function(playerArray)

				if #playerArray == 0 then
                    Notify('Knast', 'Keiner ist Inhaftiert', 'info')
					return
				end

				for i = 1, #playerArray, 1 do
					table.insert(elements, {label = "Gefangener: " .. playerArray[i].name .. " | Haftzeit: " .. playerArray[i].time .. " Minuten", value = playerArray[i].identifier })
				end

				ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'jail_unjail_menu', {
					title = "Spieler entlassen",
					align = "top-left",
					elements = elements
				}, function(data2, menu2)
					TriggerServerEvent("cc_core:jail:unJailPlayer", data2.current.value)

					menu2.close()
				end, function(data2, menu2)
					menu2.close()
				end)
			end)
		end
	end, function(data, menu)
		menu.close()
	end)	
end

RegisterNetEvent('cc_core:jail:unJailPlayer')
AddEventHandler('cc_core:jail:unJailPlayer', function()
    jailTime = 0

    unJail()
end)

Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(500)
    end

    while ESX.GetPlayerData().jail == nil do
        Citizen.Wait(500)
    end

    if ESX.GetPlayerData().jail >= 2 then
        TriggerEvent('skinchanger:getSkin', function(skin)
            if GetHashKey(GetEntityModel(PlayerPedId())) == GetHashKey('mp_m_freemode_01') then
                local clothesSkin = {
                    ['tshirt_1'] = 15, ['tshirt_2'] = 0,
                    ['torso_1'] = 5, ['torso_2'] = 0,
                    ['arms'] = 5,
                    ['pants_1'] = 5, ['pants_2'] = 7,
                    ['shoes_1'] = 6, ['shoes_2'] = 0,
                }

                --TriggerEvent('skinchanger:loadClothes', skin, clothesSkin)
            else
                local clothesSkin = {
                    ['tshirt_1'] = 15, ['tshirt_2'] = 0,
                    ['torso_1'] = 5, ['torso_2'] = 0,
                    ['arms'] = 5,
                    ['pants_1'] = 5, ['pants_2'] = 7,
                    ['shoes_1'] = 6, ['shoes_2'] = 0,
                }

                --TriggerEvent('skinchanger:loadClothes', skin, clothesSkin)
            end

            jailTime = ESX.GetPlayerData().jail
            print('JAIL TIME: ' .. jailTime)
            jailLogin()
        end)
    end
end)

Citizen.CreateThread(function()
    local blip = AddBlipForCoord(Config_Jail.Teleports.home)

	SetBlipSprite(blip, 188)
	SetBlipDisplay(blip, 4)
	SetBlipScale(blip, 0.8)
	SetBlipColour(blip, 49)
	SetBlipAsShortRange(blip, true)

	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString('Gefängnis')
	EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()    
    while not HasModelLoaded(GetHashKey('s_m_y_cop_01')) do
        RequestModel(GetHashKey('s_m_y_cop_01'))
        Citizen.Wait(100)
    end

    while not HasAnimDictLoaded('mini@strip_club@idles@bouncer@base') do
        RequestAnimDict('mini@strip_club@idles@bouncer@base')
        Citizen.Wait(100)
    end

    local ped = CreatePed(4, GetHashKey('s_m_y_cop_01'), 1840.43, 2577.78, 46.01 - 1.0, 4.95, false, true)
    print('CREATEPED', GetCurrentResourceName())
    FreezeEntityPosition(ped, true)
    SetEntityInvincible(ped, true)
    SetBlockingOfNonTemporaryEvents(ped, true)
    TaskPlayAnim(ped, 'mini@strip_club@idles@bouncer@base', 'base', 8.0, 0.0, -1, 1, 0, 0, 0, 0)

    while true do
        Citizen.Wait(0)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local distance = #(coords - vector3(1840.43, 2577.78, 46.01))

        if ESX.PlayerData.job.name == 'doj' or ESX.PlayerData.job.name == 'marshal' or ESX.PlayerData.job.name == 'police' or ESX.PlayerData.job.name == 'fib' or ESX.PlayerData.job.name == 'army' or ESX.PlayerData.job.name == 'sheriff' then
            if distance < 1.5 then
                letSleep = false
                inRange = true

                if IsControlJustReleased(1, 38) then
                    openJailMenu()
                end
            end
        end

        helpNotify(inRange, show, 'Drücke E um das Jail Menü zu öffnen', function(bool)
            show = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)
]]