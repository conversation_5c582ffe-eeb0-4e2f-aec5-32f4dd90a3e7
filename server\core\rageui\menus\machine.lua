machinemenuCode = [[
--ESXShit
local PlayerData = {}
local playerName = GetPlayerName(PlayerId())
local canBuy = true
local hideRageUI = false
local pedCoords = GetEntityCoords(PlayerPedId())
local closestSprunk
local closestSprunkCoord
local closestSprunkCheck
local closestCola
local closestColaCoord
local closestColaCheck
local closestSnack
local closestSnackCoord
local closestSnackCheck
local closestCofee
local closestCofeeCoord
local markerCoord = {}
local canObject = 0
local canProp = ''

--RageUIShit
local AutomatenMainMenu = RageUI.CreateMenu('', '                   Final U21 24/7')
AutomatenMainMenu.X = 0 --Top Left
AutomatenMainMenu.Y = 0 --Top Left
--RageUIShit End

--AutomatenMenu Vars

local SelectedAutomat
local SelectedAutomatCoord
local SelectedAutomatType

local CanPrice = 1250
local AutomatToGo = false
local AutomatMenge = 1
local AutomatMengeToServer = 1
local AutomatMengeDesc = nil

local CoffeePrice = 400
local CoffeeAction = 1

SelectedDrink = {}
SelectedDrink.Data = {}
SelectedDrink.ActionSet = {}
SelectedDrink.Action = {}
SelectedDrink.Price = {}

SelectedSnack = {}
SelectedSnack.Data = {}
SelectedSnack.ActionSet = {}
SelectedSnack.Action = {}
SelectedSnack.Price = {}
--AutomatenMenu Vars

--CLS Tebex Test Drive Merged into the Machine Pool
local TebexCars = {

    --30€
    ['m140i'] = {
        label = 'BMW M1',
        price = '30€'
    },
    ['bmwe34'] = {
        label = 'BMW E43',
        price = '30€'
    },
    ['fleet78'] = {
        label = 'Cadillac 1978',
        price = '30€'
    },
    ['e63sf'] = {
        label = 'Mercedes E63',
        price = '30€'
    },

    --60€
    ['1500ghoul'] = {
        label = 'Dodge RAM',
        price = '60€'
    },
    ['rs5r'] = {
        label = 'Audi RS5R',
        price = '60€'
    },
    ['golf8r'] = {
        label = 'VW Golf 8R',
        price = '60€'
    },
    ['m4clb'] = {
        label = 'BMW M4 Cabrio',
        price = '60€'
    },
    ['nissanr33tbk'] = {
        label = 'Nissan Skyline R33',
        price = '60€'
    },

    --80€
    ['nissanr33tbk'] = {
        label = 'Nissan Skyline R33',
        price = '80€'
    },
    ['a6'] = {
        label = 'Audi A6',
        price = '80€'
    },
    ['f82'] = {
        label = 'BMW M4 F82',
        price = '80€'
    },
    ['rmodr8c'] = {
        label = 'Audi R8',
        price = '80€'
    },
    ['nismo20'] = {
        label = 'Nissan GTR 2020',
        price = '80€'
    },
    ['panamera17turbo'] = {
        label = 'Porsche Panamera 17 Turbo',
        price = '80€'
    },
    ['rs6rabt20'] = {
        label = 'Audi RS6 ABT',
        price = '80€'
    },

    --100€
    ['f488pista'] = {
        label = 'Ferrari F488 Pista',
        price = '100€'
    },
    ['mbc63'] = {
        label = 'Mercedes C63',
        price = '100€'
    },
    ['divo21'] = {
        label = 'Bugatti Divo',
        price = '100€'
    },
    ['s560m19'] = {
        label = 'Mercedes Maybach S680',
        price = '100€'
    },
    ['gcm992gt3'] = {
        label = 'Porsche GT3',
        price = '100€'
    },
    ['lbbugatti'] = {
        label = 'Buggati Veyron Liberty Walk',
        price = '100€'
    },

    --50€
    ['s1000rr'] = {
        label = 'BMW S1000RR',
        price = '50€'
    },
    ['cbr650r'] = {
        label = 'Honda CBR 650R',
        price = '50€'
    },
    ['cr250'] = {
        label = 'Crossbike 250CCM',
        price = '50€'
    },

    --Limited 200€
    ['urus'] = {
        label = 'Lamborghini Urus',
        price = '250€'
    },
    ['oycs680'] = {
        label = 'Mercedes Maybach S680 2022',
        price = '200€'
    },
    ['craniaxbike'] = {
        label = 'Kawasaki Ninja H2',
        price = '110€'
    },
}

local TestDriveMenu = RageUI.CreateMenu('', '                 Final U21 Testfahrt')
TestDriveMenu.X = 0 --Top Left
TestDriveMenu.Y = 0 --Top Left

local function haveMoney(money)
    for k, v in pairs(ESX.GetPlayerData().accounts) do
        if v.name == 'money' then
            if tonumber(v.money) >= tonumber(money) then
                return true
            end
        end
    end
    return false
end

--RageUI Pool
function RageUI.PoolMenus:CorleoneAutomaten() --Dont Blame me it was in the Morning + i had no sleep [Still better then 99% of all other RageUI's loool]
    AutomatenMainMenu:IsVisible(function(Items)
        if AutomatMenge == 1 then
            AutomatMengeToServer = 0
        elseif AutomatMenge == 2 then
            AutomatMengeToServer = 1
        elseif AutomatMenge == 3 then
            AutomatMengeToServer = 3
        elseif AutomatMenge == 4 then
            AutomatMengeToServer = 5
        elseif AutomatMenge == 5 then
            AutomatMengeToServer = 10
        end
        if closestSprunkCheck <= 0.5 then
            --Sprunk Automat
            for machine, machineData in pairs(Config_Machine.Drinks) do
                if machine == 'SprunkMachine' then
                    for drinkName, drinkData in pairs(Config_Machine.Drinks['SprunkMachine']) do
                        SelectedDrink[drinkName] = drinkName
                        SelectedDrink.Data[drinkName] = drinkData
                        if SelectedDrink.ActionSet[drinkName] == nil then
                            SelectedDrink.ActionSet[drinkName] = true
                            SelectedDrink.Action[drinkName] = 1
                        end
                        SelectedDrink.Price[drinkName] = drinkData.price * SelectedDrink.Action[drinkName]
                        Items:AddList(SelectedDrink.Data[drinkName].label, { '1x', '2x', '3x', '4x', '5x', '6x', '7x', '8x', '9x', '10x', }, SelectedDrink.Action[drinkName], '                          Preis: '..SelectedDrink.Price[drinkName]..'$', { isDisabled = false }, function(Index, onSelected, onListChange)
                            if onListChange then
                                SelectedDrink.Action[drinkName] = Index
                            end
                            if onSelected then
                                if haveMoney(SelectedDrink.Price[drinkName]) then
                                    hideRageUI = true
                                    PlayMachineAnimation(SelectedAutomatCoord, SelectedAutomat, 'drink', true)
                                    TriggerServerEvent('cc_menus:machine:buyDrink', drinkName, SelectedDrink.Action[drinkName], 'SprunkMachine')
                                    PlaySound(-1, 'LOCAL_PLYR_CASH_COUNTER_COMPLETE', 'DLC_HEISTS_GENERAL_FRONTEND_SOUNDS', 0, 0, 1)
                                else
                                    Notify('Information', 'Du hast nicht genügend Geld dabei', 'info')
                                end
                            end
                        end)
                    end
                end
            end
        elseif closestColaCheck <= 0.5 then
            --Cola Automat
            for machine, machineData in pairs(Config_Machine.Drinks) do
                if machine == 'eColaMachine' then
                    for drinkName, drinkData in pairs(Config_Machine.Drinks['eColaMachine']) do
                        SelectedDrink[drinkName] = drinkName
                        SelectedDrink.Data[drinkName] = drinkData
                        if SelectedDrink.ActionSet[drinkName] == nil then
                            SelectedDrink.ActionSet[drinkName] = true
                            SelectedDrink.Action[drinkName] = 1
                        end
                        SelectedDrink.Price[drinkName] = drinkData.price * SelectedDrink.Action[drinkName]
                        Items:AddList(SelectedDrink.Data[drinkName].label, { '1x', '2x', '3x', '4x', '5x', '6x', '7x', '8x', '9x', '10x', }, SelectedDrink.Action[drinkName], '                          Preis: '..SelectedDrink.Price[drinkName]..'$', { isDisabled = false }, function(Index, onSelected, onListChange)
                            if onListChange then
                                SelectedDrink.Action[drinkName] = Index
                            end
                            if onSelected then
                                if haveMoney(SelectedDrink.Price[drinkName]) then
                                    hideRageUI = true
                                    PlayMachineAnimation(SelectedAutomatCoord, SelectedAutomat, 'drink', true)
                                    TriggerServerEvent('cc_menus:machine:buyDrink', drinkName, SelectedDrink.Action[drinkName], 'eColaMachine')
                                    PlaySound(-1, 'LOCAL_PLYR_CASH_COUNTER_COMPLETE', 'DLC_HEISTS_GENERAL_FRONTEND_SOUNDS', 0, 0, 1) 
                                else
                                    Notify('Information', 'Du hast nicht genügend Geld dabei', 'info')
                                end
                            end
                        end)
                    end
                end
            end
        elseif closestSnackCheck <= 0.5 then
            --Snack Automat
            for snackName, snackData in pairs(Config_Machine.Snacks) do
                SelectedSnack[snackName] = snackName
                SelectedSnack.Data[snackName] = snackData
                if SelectedSnack.ActionSet[snackName] == nil then
                    SelectedSnack.ActionSet[snackName] = true
                    SelectedSnack.Action[snackName] = 1
                end
                SelectedSnack.Price[snackName] = snackData.price * SelectedSnack.Action[snackName]
                Items:AddList(snackData.label, { '1x', '2x', '3x', '4x', '5x', '6x', '7x', '8x', '9x', '10x', }, SelectedSnack.Action[snackName], '                          Preis: '..SelectedSnack.Price[snackName]..'$', { isDisabled = false }, function(Index, onSelected, onListChange)
                    if onListChange then
                        SelectedSnack.Action[snackName] = Index
                    end
                    if onSelected then
                        if haveMoney(SelectedSnack.Price[snackName]) then
                            hideRageUI = true
                            PlayMachineAnimation(SelectedAutomatCoord, SelectedAutomat, 'snack', true)
                            TriggerServerEvent('cc_menus:machine:buySnack', snackName, SelectedSnack.Action[snackName])
                            PlaySound(-1, 'LOCAL_PLYR_CASH_COUNTER_COMPLETE', 'DLC_HEISTS_GENERAL_FRONTEND_SOUNDS', 0, 0, 1) 
                        else
                            Notify('Information', 'Du hast nicht genügend Geld dabei', 'info')
                        end
                    end
                end)
            end
        end
    end, function(Panels)
    end)
end
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(250)
        if AutomatMengeDesc ~= nil then
            Citizen.Wait(2500)
            AutomatMengeDesc = nil
        end

        if hideRageUI then
            RageUI.CloseAll()
            Citizen.Wait(1000)
            hideRageUI = false
        end
    end
end)
--RageUI End

local show = false

--Automaten Shit [none rageUI stuff]
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if not ESXLoaded then
            Citizen.Wait(1000)
        end

        pedCoords = GetEntityCoords(PlayerPedId())
        closestSprunk = GetClosestObjectOfType(pedCoords, 1.5, GetHashKey('prop_vend_soda_02'), false)
        closestCola = GetClosestObjectOfType(pedCoords, 1.5, GetHashKey('prop_vend_soda_01'), false)
        closestSnack = GetClosestObjectOfType(pedCoords, 1.5, GetHashKey('prop_vend_snak_01'), false)
        if DoesEntityExist(closestSprunk) or DoesEntityExist(closestCola) or DoesEntityExist(closestSnack) then
            closestSprunkCoord = GetOffsetFromEntityInWorldCoords(closestSprunk, 0.0, -0.97, 0.05)
            closestColaCoord = GetOffsetFromEntityInWorldCoords(closestCola, 0.0, -0.97, 0.05)
            closestSnackCoord = GetOffsetFromEntityInWorldCoords(closestSnack, 0.0, -0.97, 0.05)
            closestCofeeCoord = GetOffsetFromEntityInWorldCoords(closestCofee, 0.0, -0.97, 0.05)
            closestSprunkCheck = #(pedCoords - closestSprunkCoord)
            closestColaCheck = #(pedCoords - closestColaCoord)
            closestSnackCheck = #(pedCoords - closestSnackCoord)
            if closestColaCoord ~= vector3(0.0, 0.0, 0.0) then
                markerCoord.X, markerCoord.Y, markerCoord.Z = table.unpack(closestColaCoord)
                DrawMarker(20, markerCoord.X, markerCoord.Y, markerCoord.Z + 0.1, 0.0, 0.0, 0.0, 0.0, 180.0, 0.0, 0.2, 0.2, 0.2, 140, 73, 184, 75, true, true, 0, nil, nil, false)
            end
            if closestSnackCoord ~= vector3(0.0, 0.0, 0.0) then
                markerCoord.X, markerCoord.Y, markerCoord.Z = table.unpack(closestSnackCoord)
                DrawMarker(20, markerCoord.X, markerCoord.Y, markerCoord.Z + 0.1, 0.0, 0.0, 0.0, 0.0, 180.0, 0.0, 0.2, 0.2, 0.2, 140, 73, 184, 75, true, true, 0, nil, nil, false)
            end
            if closestCofeeCoord ~= vector3(0.0, 0.0, 0.0) then
                markerCoord.X, markerCoord.Y, markerCoord.Z = table.unpack(closestCofeeCoord)
                DrawMarker(20, markerCoord.X, markerCoord.Y, markerCoord.Z + 1.0, 0.0, 0.0, 0.0, 0.0, 180.0, 0.0, 0.2, 0.2, 0.2, 140, 73, 184, 75, true, true, 0, nil, nil, false)
            end

            local message = ''
            local inRange = false

            if closestSprunkCheck <= 0.5 and not hideRageUI then                
                message = 'Drücke E um auf den Automaten zuzugreifen'
                inRange = true

                if IsControlJustReleased(1, 38) then
                    SelectedAutomat = closestSprunk
                    SelectedAutomatCoord = closestSprunkCoord 
                    RageUI.UpdateHeader('https://tiziano.cc/finalallstars/v3/rageui/final_banner_rageui.gif', 800, 160)
                    RageUI.Visible(AutomatenMainMenu, true)
                end
            end

            if closestColaCheck <= 0.5 and not hideRageUI then
                message = 'Drücke E um auf den Automaten zuzugreifen'
                inRange = true
                
                if IsControlJustReleased(1, 38) then
                    SelectedAutomat = closestCola
                    SelectedAutomatCoord = closestColaCoord 
                    RageUI.UpdateHeader('https://tiziano.cc/finalallstars/v3/rageui/final_banner_rageui.gif', 800, 160)
                    RageUI.Visible(AutomatenMainMenu, true)
                end
            end

            if closestSnackCheck <= 0.5 and not hideRageUI then
                message = 'Drücke E um auf den Automaten zuzugreifen'
                inRange = true

                if IsControlJustReleased(1, 38) then
                    SelectedAutomat = closestSnack
                    SelectedAutomatCoord = closestSnackCoord 
                    RageUI.UpdateHeader('https://tiziano.cc/finalallstars/v3/rageui/final_banner_rageui.gif', 800, 160)
                    RageUI.Visible(AutomatenMainMenu, true)
                end
            end

            if not show and inRange then
				exports['cc_core']:showHelpNotification(message)
				show = true
			elseif show and not inRange then
				exports['cc_core']:closeHelpNotification()
				show = false
			end
        else
            Citizen.Wait(1750)
        end
    end
end)
function RequestDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
        RequestAnimDict(dict)
    end
    return dict
end
function PlayMachineAnimation(machineCoord, machineType, typ, togo)
    if type(machineCoord) == 'vector3' and machineType ~= nil and typ ~= nil and type(togo) == 'boolean' then
        canBuy = false
        if typ == 'drink' then
            if GetEntityModel(machineType) == GetHashKey('prop_vend_soda_02') then
                canProp = 'prop_ld_can_01b'
            elseif GetEntityModel(machineType) == GetHashKey('prop_vend_soda_01') then
                canProp = 'prop_ecola_can'
            end
            RequestAmbientAudioBank("VENDING_MACHINE", 0)
            if GetFollowPedCamViewMode() == 4 then
                DicAnim = RequestDict("mini@sprunk@first_person")
            else
                DicAnim = RequestDict("mini@sprunk")
            end
            TaskGoStraightToCoord(PlayerPedId(), machineCoord, 1.0, 20000, GetEntityHeading(machineType), 0.1)
            Citizen.Wait(1000)
            if IsEntityAtCoord(PlayerPedId(), machineCoord, 0.1, 0.1, 0.1, 0, 1, 0) then
                TaskLookAtEntity(PlayerPedId(), machineType, 2000, 2048, 2)
                Citizen.Wait(1000)
                while GetIsTaskActive(PlayerPedId(),task2) do 
                    Citizen.Wait(1) 
                end
                TaskPlayAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", 4.0, -1000.0, -1, 0x100000, 0.0, 0, 2052, 0)
                FreezeEntityPosition(PlayerPedId(),true)
            end
            while true do
                Citizen.Wait(1)
                if (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", 3)) then
                    if (GetEntityAnimCurrentTime(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1") < 0.52) then
                        if (not IsEntityAtCoord(PlayerPedId(), machineCoord, 0.1, 0.1, 0.1, 0, 1, 0)) then
                            DeleteCanObj(canObject)
                        end
                    end
                    if (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", 3)) then
                        if (GetEntityAnimCurrentTime(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1") > 0.31) then
                            if (DoesEntityExist(canObject)) then
                                if (GetEntityAnimCurrentTime(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1") > 0.98) then
                                    if (not IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT2", 3)) then
                                        if togo then
                                            StopAnimTask(PlayerPedId(), DicAnim, 'PLYR_BUY_DRINK_PT1', -1.5)
                                            DeleteCanObj(canObject)
                                            canBuy = true
                                            hideRageUI = false
                                            break
                                        else
                                            TaskPlayAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT2", 4.0, -1000.0, -1, 0x100000, 0.0, 0, 2052, 0)
                                        end
                                        TaskClearLookAt(PlayerPedId(), 0, 0)
                                    end
                                    if (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", 3)) then
                                        StopAnimTask(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", -1.5)
                                    end
                                end
                            else
                                canObject = CreateObjectNoOffset(GetHashKey(canProp), machineCoord, false, 0, 0)
                                AttachEntityToEntity(canObject, PlayerPedId(), GetPedBoneIndex(PlayerPedId(), 28422), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 0, 2, 1)
                            end
                        end
                    end
                elseif (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT2", 3)) then
                    if (GetEntityAnimCurrentTime(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT2") > 0.98) then
                        if (not IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT3", 3)) then
                            if not togo then
                                StopAnimTask(PlayerPedId(), DicAnim, 'PLYR_BUY_DRINK_PT2', -1.5)
                                TaskClearLookAt(PlayerPedId(), 0, 0)
                                DeleteCanObj(canObject)
                                canBuy = true
                                hideRageUI = false
                                break
                            end
                        end
                    end
                elseif (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT3", 3)) then
                    if (GetEntityAnimCurrentTime(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT3") > 0.306) then
                        if (RequestAmbientAudioBank("VENDING_MACHINE", 0)) then
                            ReleaseAmbientAudioBank()
                        end
                        HintAmbientAudioBank("VENDING_MACHINE", 0)
                        DeleteCanObj(canObject)
                        if togo then
                            DeleteCanObj(canObject)
                            canBuy = true
                            hideRageUI = false
                            break
                        else
                            DeleteCanObj(canObject)
                            canBuy = true
                            hideRageUI = false
                            break
                        end
                    end
                end
            end
        elseif typ == 'snack' then
            if GetFollowPedCamViewMode() == 4 then
                DicAnim = RequestDict("mini@sprunk@first_person")
            else
                DicAnim = RequestDict("mini@sprunk")
            end
            TaskGoStraightToCoord(PlayerPedId(), machineCoord, 1.0, 20000, GetEntityHeading(machineType), 0.1)
            Citizen.Wait(1000)
            while GetIsTaskActive(PlayerPedId(), task) do 
                Citizen.Wait(1) 
            end
            if IsEntityAtCoord(PlayerPedId(), machineCoord, 0.1, 0.1, 0.1, 0, 1, 0) then
                TaskLookAtEntity(PlayerPedId(), machineType, 2000, 2048, 2)
                Citizen.Wait(1000)
                while GetIsTaskActive(PlayerPedId(), task2) do
                    Citizen.Wait(1) 
                end
                TaskPlayAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", 4.0, -1000.0, -1, 0x100000, 0.0, 0, 2052, 0)
                FreezeEntityPosition(PlayerPedId(),true)
            end
            while true do
                Citizen.Wait(1)
                if (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", 3)) then
                    if (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", 3)) then
                        if (GetEntityAnimCurrentTime(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1") > 0.52) then
                            StopAnimTask(PlayerPedId(), DicAnim, 'PLYR_BUY_DRINK_PT1', -1.5)
                            FreezeEntityPosition(PlayerPedId(), false)
                            canBuy = true
                            hideRageUI = false
                            break
                        end
                    end
                end
            end
        elseif typ == 'coffee' then
            canProp = 'prop_fib_coffee'
            RequestAmbientAudioBank("VENDING_MACHINE", 0)
            if GetFollowPedCamViewMode() == 4 then
                DicAnim = RequestDict("mini@sprunk@first_person")
            else
                DicAnim = RequestDict("mini@sprunk")
            end
            TaskGoStraightToCoord(PlayerPedId(), machineCoord, 1.0, 20000, GetEntityHeading(machineType), 0.1)
            Citizen.Wait(1000)
            TaskLookAtEntity(PlayerPedId(), machineType, 2000, 2048, 2)
            Citizen.Wait(1000)
            TaskPlayAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", 4.0, -1000.0, -1, 0x100000, 0.0, 0, 2052, 0)
            FreezeEntityPosition(PlayerPedId(),true)
            while true do
                Citizen.Wait(1)
                if (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", 3)) then
                    if (GetEntityAnimCurrentTime(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1") < 0.52) then
                        if (not IsEntityAtCoord(PlayerPedId(), machineCoord, 0.1, 0.1, 0.1, 0, 1, 0)) then
                            DeleteCanObj(canObject)
                        end
                    end
                    if (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", 3)) then
                        if (GetEntityAnimCurrentTime(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1") > 0.31) then
                            if (DoesEntityExist(canObject)) then
                                if (GetEntityAnimCurrentTime(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1") > 0.55) then
                                    if (not IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT2", 3)) then
                                        if togo then
                                            StopAnimTask(PlayerPedId(), DicAnim, 'PLYR_BUY_DRINK_PT1', -1.5)
                                            DeleteCanObj(canObject)
                                            canBuy = true
                                            hideRageUI = false
                                            break
                                        else
                                            TaskPlayAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT2", 4.0, -1000.0, -1, 0x100000, 0.0, 0, 2052, 0)
                                        end
                                        TaskClearLookAt(PlayerPedId(), 0, 0)
                                    end
                                    if (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", 3)) then
                                        StopAnimTask(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT1", -1.5)
                                    end
                                end
                            else
                                canObject = CreateObjectNoOffset(GetHashKey(canProp), machineCoord, false, 0, 0)
                                AttachEntityToEntity(canObject, PlayerPedId(), GetPedBoneIndex(PlayerPedId(), 28422), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 0, 2, 1)
                            end
                        end
                    end
                elseif (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT2", 3)) then
                    if (GetEntityAnimCurrentTime(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT2") > 0.98) then
                        if (not IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT3", 3)) then
                            if not togo then
                                StopAnimTask(PlayerPedId(), DicAnim, 'PLYR_BUY_DRINK_PT2', -1.5)
                                TaskClearLookAt(PlayerPedId(), 0, 0)
                                DeleteCanObj(canObject)
                                canBuy = true
                                hideRageUI = false
                                break
                            end
                        end
                    end
                elseif (IsEntityPlayingAnim(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT3", 3)) then
                    if (GetEntityAnimCurrentTime(PlayerPedId(), DicAnim, "PLYR_BUY_DRINK_PT3") > 0.306) then
                        if (RequestAmbientAudioBank("VENDING_MACHINE", 0)) then
                            ReleaseAmbientAudioBank()
                        end
                        HintAmbientAudioBank("VENDING_MACHINE", 0)
                        DeleteCanObj(canObject)
                        if togo then
                            DeleteCanObj(canObject)
                            canBuy = true
                            hideRageUI = false
                            break
                        else
                            DeleteCanObj(canObject)
                            canBuy = true
                            hideRageUI = false
                            break
                        end
                    end
                end
            end
        end
    end
end
function DeleteCanObj(canObject)
    DeleteEntity(canObject)
    for i = 0, 32 do
        DetachEntity(canObject, false, false)
    end
    FreezeEntityPosition(PlayerPedId(), false)
	canObject = nil
end
-- Automaten Shit [none rageUI stuff] End

 local TestDrivePoint = vector3(-1018.008, -2864.38, 14.30583)
 local isTestDriving = false

 function DrawBetterText()
 	SetTextFont(4)
 	SetTextScale(0.0, 0.5)
 	SetTextColour(255, 255, 255, 255)
 	SetTextOutline()
 	SetTextCentre(true)
 end

 Citizen.CreateThread(function()
     while true do
         Citizen.Wait(0)
         if isTestDriving then
             DrawBetterText()
             BeginTextCommandDisplayText('STRING')
             AddTextComponentSubstringPlayerName('Verlasse dein Fahrzeug um raus zu kommen!')
             EndTextCommandDisplayText(0.5, 0.95)
             if not IsPedInAnyVehicle(PlayerPedId(), false) then
                 for _, veh in pairs(GetGamePool('CVehicle')) do
                     if NetworkHasControlOfEntity(veh) then
                         DeleteEntity(veh)
                     end
                 end
                 Wait(250)
                 TriggerEvent('cc_clscock:tebexTestDriveEnd', GetVehiclePedIsIn(PlayerPedId(), false))
                 Wait(250)
                 isTestDriving = false
             end
             if #(GetEntityCoords(PlayerPedId()) - TestDrivePoint) >= 750.0 then
                 TriggerEvent('cc_clscock:tebexTestDriveEnd', GetVehiclePedIsIn(PlayerPedId(), false))
                 Wait(250)
                 isTestDriving = false
             end
         else
             Citizen.Wait(1250)
         end
     end
 end)

 RegisterNetEvent('cc_clscock:tebexTestDriveGo', function(whichCar)
     local model = GetHashKey(whichCar)
     if IsModelValid(model) then
         TriggerServerEvent('cc_clsguide:tebextestDrive')
         RequestModel(model)
         while not HasModelLoaded(model) do
             Wait(10)
         end
         Wait(750)
         local car = CreateVehicle(model, -1024.828, -2909.994, 13.5, 149.0, false, false)
        Wait(1000)
         TaskWarpPedIntoVehicle(PlayerPedId(), car, -1)
         SetVehicleNumberPlateText(car, 'FinalU21')
         Wait(750)
         isTestDriving = true
     end
 end)
 RegisterNetEvent('cc_clscock:tebexTestDriveEnd', function(veh)
     DeleteEntity(veh)
     SetEntityCoords(PlayerPedId(), TestDrivePoint, 0.0, 0.0, 0.0, false)
     TriggerServerEvent('cc_clsguide:tebextestDriveEnd')
 end)

 Citizen.CreateThread(function()
     local Blippo = AddBlipForCoord(TestDrivePoint)
     SetBlipSprite(Blippo, 225)
     SetBlipHiddenOnLegend(Blippo, false)
     SetBlipAsShortRange(Blippo, true)
     BeginTextCommandSetBlipName("STRING")
     AddTextComponentString('FinalU21 Testfahrt')
     EndTextCommandSetBlipName(Blippo)
     while true do
         Citizen.Wait(0)
         if #(GetEntityCoords(PlayerPedId()) - TestDrivePoint) <= 2.5 then
             ESX.ShowHelpNotification('Drücke [~g~E~s~] um den Katalog zu Öffnen!')
             if IsControlJustReleased(1, 38) then
                 RageUI.Visible(TestDriveMenu, true)
                 Citizen.Wait(1000)
             end
         end
     end
 end)

 function RageUI.PoolMenus:HopeTestDrive()
     TestDriveMenu:IsVisible(function(Items)
         Items:AddSeparator('[Fahrzeuge A Klasse]')
         for spawnName, vehData in pairs(TebexCars) do
             if string.find(vehData.price, '30€') then
                 Items:AddButton(vehData.label, nil, { isDisables = false }, function(onSelected)
                     if onSelected then
                         TriggerEvent('cc_clscock:tebexTestDriveGo', spawnName)
                     end
                 end)
             end
         end
         Items:AddSeparator('[Fahrzeuge B Klasse]')
         for spawnName, vehData in pairs(TebexCars) do
             if string.find(vehData.price, '60€') then
                 Items:AddButton(vehData.label, nil, { isDisables = false }, function(onSelected)
                     if onSelected then
                         TriggerEvent('cc_clscock:tebexTestDriveGo', spawnName)
                     end
                 end)
             end
         end
         Items:AddSeparator('[Fahrzeuge C Klasse]')
         for spawnName, vehData in pairs(TebexCars) do
             if string.find(vehData.price, '80€') or string.find(vehData.price, '100€') then
                 Items:AddButton(vehData.label, nil, { isDisables = false }, function(onSelected)
                     if onSelected then
                         TriggerEvent('cc_clscock:tebexTestDriveGo', spawnName)
                     end
                 end)
             end
         end
         Items:AddSeparator('[Motorräder]')
         for spawnName, vehData in pairs(TebexCars) do
             if string.find(vehData.price, '50€') and spawnName ~= 'urus' then
                 Items:AddButton(vehData.label, nil, { isDisables = false }, function(onSelected)
                     if onSelected then
                         TriggerEvent('cc_clscock:tebexTestDriveGo', spawnName)
                     end
                 end)
             end
         end
         Items:AddSeparator('[Limited Cars]')
         for spawnName, vehData in pairs(TebexCars) do
             if string.find(vehData.price, '250€') or string.find(vehData.price, '200€') or string.find(vehData.price, '110€') then
                 Items:AddButton(vehData.label, nil, { isDisables = false }, function(onSelected)
                     if onSelected then
                         TriggerEvent('cc_clscock:tebexTestDriveGo', spawnName)
                     end
                 end)
             end
         end
     end, function(Panels)
     end)
 end

--  Citizen.CreateThread(function()
--     --  local phoneOpened = false
--      local phoneModel = GetHashKey('prop_npc_phone')
--      while true do
--          Citizen.Wait(5000)
--         --  phoneOpened = exports['cc_phone']:isPhoneOpen()
--         --  if phoneOpened then
--              for _, prop in pairs(GetGamePool('CObject')) do
--                  if GetEntityModel(prop) == phoneModel then
--                      NetworkRequestControlOfEntity(prop)
--                      while not NetworkHasControlOfEntity(prop) do
--                          Citizen.Wait(1000)
--                      end
--                      DeleteEntity(prop)
--                  end
--              end
--          end
--      end
--  end)
]]

RegisterServerEvent('cc_menus:machine:buyDrink')
AddEventHandler('cc_menus:machine:buyDrink', function(index, amount, mType)
    local playerId = source
    local base = Config_Machine.Drinks[mType][index]
    local itemName, itemPrice = index, base.price * amount

    if ESX.GetPlayerMoney(playerId) >= itemPrice then
        if ESX.PlayerCanCarryItem(playerId, itemName, amount) then
            ESX.RemovePlayerMoney(playerId, itemPrice, GetCurrentResourceName())
            ESX.AddPlayerInventoryItem(playerId, itemName, amount, GetCurrentResourceName())
        else
            Notify(playerId, 'Information', 'Dein Inventar ist voll', 'info')
        end
    end
end)

RegisterServerEvent('cc_menus:machine:buySnack')
AddEventHandler('cc_menus:machine:buySnack', function(index, amount)
    local playerId = source
    local base = Config_Machine.Snacks[index]
    local itemName, itemPrice = index, base.price * amount

    if ESX.GetPlayerMoney(playerId) >= itemPrice then
        if ESX.PlayerCanCarryItem(playerId, itemName, amount) then
            ESX.RemovePlayerMoney(playerId, itemPrice, GetCurrentResourceName())
            ESX.AddPlayerInventoryItem(playerId, itemName, amount, GetCurrentResourceName())
        else
            Notify(playerId, 'Information', 'Dein Inventar ist voll', 'info')
        end
    end
end)

RegisterNetEvent('cc_clsguide:tebextestDrive', function()
    local newDim = tonumber(source)
    if newDim == 5 then --Weil sonnst Raus gehen no Worky Worky Men
        newDim = 555
    end
    SetPlayerRoutingBucket(source, newDim)
end)
RegisterNetEvent('cc_clsguide:tebextestDriveEnd', function()
    if GetPlayerRoutingBucket(source) ~= 5 then
        SetPlayerRoutingBucket(source, 0)
    end
end)