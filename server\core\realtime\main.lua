local customTime = false

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(30000)
        if not customTime then
            local h, m, s = tonumber(os.date("%H")), tonumber(os.date("%M")), tonumber(os.date("%S"))
            TriggerClientEvent('cc_core:realtime:changetime', -1, h, m, s)
        end
    end
end)

RegisterCommand('time', function(source, args)
    if source == 0 then
        if args[1] then
            local h = tonumber(args[1])
            if h >= 0 and h <= 24 then
                TriggerClientEvent('cc_core:realtime:changetime', -1, h, 30, 30)
                customTime = true
                
                print('^2Activated^0 Custom Time! Hour:', h)
            end
        else
            customTime = false

            print('^1Deactivated^0 Custom Time')
        end
    end
end)

local CurrWeather = 'extrasunny'

RegisterCommand('weather', function(source, args)
    if source == 0 then
        CurrWeather = args[1]
        TriggerClientEvent('cc_core:realtime:changeWeather', -1, Curr<PERSON>eat<PERSON>)
        print('Weather Set To: '..CurrWeather)
    end
end)

AddEventHandler('cc_core:esx:playerLoaded', function(playerId)
    TriggerClientEvent('cc_core:realtime:changeWeather', playerId, CurrWeather)
end)