-- ====================================
-- IMPOUND SYSTEM - CLIENT SIDE
-- ====================================

local impoundBlips = {}
local impoundPeds = {}
local isInImpoundMenu = false

-- ====================================
-- IMPOUND MENU FUNCTIONS
-- ====================================
local function OpenImpoundMenu(impoundLot)
    isInImpoundMenu = true
    
    ESX.TriggerServerCallback('garage:getPlayerVehicles', function(vehicles)
        local elements = {}
        local impoundedVehicles = {}
        
        -- Abgeschleppte Fahrzeuge anzeigen
        for k, v in pairs(vehicles) do
            if not v.stored then -- Fahrzeuge die draußen sind (potentiell abgeschleppt)
                table.insert(impoundedVehicles, v)
                local vehicleName = GetVehicleDisplayName(v.vehicle.model)
                table.insert(elements, {
                    label = v.nickname .. ' (' .. v.plate .. ') - ' .. vehicleName .. ' - $' .. impoundLot.price,
                    value = 'release_vehicle',
                    vehicleData = v,
                    price = impoundLot.price
                })
            end
        end
        
        -- Alle Fahrzeuge auslösen Option
        if #impoundedVehicles > 0 then
            local totalPrice = #impoundedVehicles * impoundLot.price
            table.insert(elements, 1, {
                label = '~g~Alle Fahrzeuge auslösen - $' .. totalPrice,
                value = 'release_all',
                vehicles = impoundedVehicles,
                totalPrice = totalPrice
            })
        end
        
        if #elements == 0 then
            table.insert(elements, {
                label = '~r~Keine abgeschleppten Fahrzeuge',
                value = 'nothing'
            })
        end
        
        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'impound_menu', {
            title = impoundLot.label,
            align = 'top-left',
            elements = elements
        }, function(data, menu)
            if data.current.value == 'release_vehicle' then
                ESX.TriggerServerCallback('esx:getPlayerData', function(playerData)
                    if playerData.money >= data.current.price then
                        TriggerServerEvent('garage:releaseVehicle', data.current.vehicleData.plate, data.current.price)
                        menu.close()
                        isInImpoundMenu = false
                    else
                        ShowNotification('~r~Nicht genug Geld! Du brauchst $' .. data.current.price)
                    end
                end)
                
            elseif data.current.value == 'release_all' then
                ESX.TriggerServerCallback('esx:getPlayerData', function(playerData)
                    if playerData.money >= data.current.totalPrice then
                        TriggerServerEvent('garage:releaseAllVehicles', data.current.totalPrice)
                        menu.close()
                        isInImpoundMenu = false
                    else
                        ShowNotification('~r~Nicht genug Geld! Du brauchst $' .. data.current.totalPrice)
                    end
                end)
            end
        end, function(data, menu)
            menu.close()
            isInImpoundMenu = false
        end)
    end)
end

-- ====================================
-- IMPOUND INTERACTION
-- ====================================
local function CreateImpoundBlip(impoundLot)
    local blip = AddBlipForCoord(impoundLot.npc.x, impoundLot.npc.y, impoundLot.npc.z)
    
    SetBlipSprite(blip, impoundLot.blipSprite or 67)
    SetBlipColour(blip, impoundLot.blipColor or 1)
    SetBlipScale(blip, 0.7)
    SetBlipDisplay(blip, 4)
    SetBlipAsShortRange(blip, true)
    
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(impoundLot.blipLabel or 'Abschlepphof')
    EndTextCommandSetBlipName(blip)
    
    return blip
end

local function CreateImpoundPed(impoundLot)
    local pedModel = impoundLot.pedModel or `s_m_y_dealer_01`
    
    RequestModel(pedModel)
    while not HasModelLoaded(pedModel) do
        Wait(1)
    end
    
    local ped = CreatePed(4, pedModel, impoundLot.npc.x, impoundLot.npc.y, impoundLot.npc.z - 1.0, impoundLot.npc.w, false, true)
    
    SetEntityCanBeDamaged(ped, false)
    SetPedCanRagdollFromPlayerImpact(ped, false)
    SetBlockingOfNonTemporaryEvents(ped, true)
    SetPedFleeAttributes(ped, 0, 0)
    SetPedCombatAttributes(ped, 17, 1)
    FreezeEntityPosition(ped, true)
    
    return ped
end

-- ====================================
-- MAIN THREAD FOR IMPOUND
-- ====================================
Citizen.CreateThread(function()
    while true do
        local sleep = 1000
        local playerCoords = GetEntityCoords(PlayerPedId())
        
        for k, impoundLot in pairs(Config.ImpoundLots) do
            local distance = #(playerCoords - vector3(impoundLot.npc.x, impoundLot.npc.y, impoundLot.npc.z))
            
            if distance < 10.0 then
                sleep = 0
                
                if distance < 2.0 then
                    ESX.ShowHelpNotification(impoundLot.helpText or 'Drücke ~INPUT_CONTEXT~ um den Abschlepphof zu öffnen')
                    
                    if IsControlJustReleased(0, 38) and not isInImpoundMenu then -- E Key
                        OpenImpoundMenu(impoundLot)
                    end
                end
            end
        end
        
        Wait(sleep)
    end
end)

-- ====================================
-- EVENTS
-- ====================================
RegisterNetEvent('garage:vehicleReleased')
AddEventHandler('garage:vehicleReleased', function(plate)
    ShowNotification('Fahrzeug ~g~' .. plate .. '~s~ wurde freigegeben!')
end)

RegisterNetEvent('garage:allVehiclesReleased')
AddEventHandler('garage:allVehiclesReleased', function(count)
    ShowNotification('~g~' .. count .. '~s~ Fahrzeuge wurden freigegeben!')
end)

-- ====================================
-- ADMIN COMMANDS
-- ====================================
RegisterCommand('impoundvehicle', function(source, args)
    if args[1] then
        local plate = string.upper(args[1])
        TriggerServerEvent('garage:impoundVehicle', plate)
    else
        ShowNotification('~r~Verwendung: /impoundvehicle [Kennzeichen]')
    end
end, false)

RegisterCommand('impoundnear', function(source, args)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local vehicle = ESX.Game.GetClosestVehicle(coords)
    
    if DoesEntityExist(vehicle) then
        local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)
        TriggerServerEvent('garage:impoundVehicle', vehicleProps.plate)
        ESX.Game.DeleteVehicle(vehicle)
        ShowNotification('Fahrzeug ~r~abgeschleppt~s~!')
    else
        ShowNotification('~r~Kein Fahrzeug in der Nähe!')
    end
end, false)

-- ====================================
-- INITIALIZATION FOR IMPOUND
-- ====================================
Citizen.CreateThread(function()
    -- Warten bis ESX geladen ist
    while not ESX.IsPlayerLoaded() do
        Wait(100)
    end
    
    -- Abschlepphof-Blips und NPCs erstellen
    for k, impoundLot in pairs(Config.ImpoundLots) do
        if impoundLot.showBlip then
            impoundBlips[k] = CreateImpoundBlip(impoundLot)
        end
        
        if impoundLot.showPed then
            impoundPeds[k] = CreateImpoundPed(impoundLot)
        end
    end
    
    print('[GARAGE] Impound-System geladen!')
end)

-- ====================================
-- CLEANUP FOR IMPOUND
-- ====================================
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- Impound Blips entfernen
        for k, blip in pairs(impoundBlips) do
            if DoesBlipExist(blip) then
                RemoveBlip(blip)
            end
        end
        
        -- Impound NPCs entfernen
        for k, ped in pairs(impoundPeds) do
            if DoesEntityExist(ped) then
                DeleteEntity(ped)
            end
        end
    end
end)
