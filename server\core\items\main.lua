ESX.RegisterUsableItem('fixkit', function(playerId)
    TriggerClientEvent('cc_core:items:repair', playerId)
end)

ESX.RegisterUsableItem('blitzkit', function(playerId)
    TriggerClientEvent('cc_core:items:repair', playerId, true)
end)

ESX.RegisterUsableItem('clip', function(playerId)
    TriggerClientEvent('cc_core:items:clip', playerId, false)
end)

ESX.RegisterUsableItem('cph4', function(playerId)
    TriggerClientEvent('cc_clsguide:ohjavisky', playerId, 'speed')
    TriggerClientEvent('cc_core:items:cph4', playerId)
end)

local BlacklistedHashes = {
    GetHashKey('WEAPON_ASSAULTRIFLE'),
}

local WeaponDurability = {
    --Pistolen
    ['WEAPON_PISTOL'] = 1.0,
    ['WEAPON_PISTOL_MK2'] = 1.0,
    ['WEAPON_COMBATPISTOL'] = 1.0,
    ['WEAPON_APPISTOL'] = 1.0,
    ['WEAPON_STUNGUN'] = 1.0,
    ['WEAPON_PISTOL50'] = 1.0,
    ['WEAPON_SNSPISTOL'] = 1.0,
    ['WEAPON_SNSPISTOL_MK2'] = 1.0,
    ['WEAPON_HEAVYPISTOL'] = 1.0,
    ['WEAPON_VINTAGEPISTOL'] = 1.0,
    ['WEAPON_FLAREGUN'] = 1.0,
    ['WEAPON_MARKSMANPISTOL'] = 1.0,
    ['WEAPON_REVOLVER'] = 1.0,
    ['WEAPON_REVOLVER_MK2'] = 1.0,
    ['WEAPON_DOUBLEACTION'] = 1.0,
    ['WEAPON_RAYPISTOL'] = 1.0,
    ['WEAPON_CERAMICPISTOL'] = 1.0,
    ['WEAPON_NAVYREVOLVER'] = 1.0,
    ['WEAPON_GADGETPISTOL'] = 1.0,
    ['WEAPON_STUNGUN_MP'] = 1.0,
    --SMGs
    ['WEAPON_MICROSMG'] = 1.0,
    ['WEAPON_SMG'] = 1.0,
    ['WEAPON_SMG_MK2'] = 1.0,
    ['WEAPON_ASSAULTSMG'] = 1.0,
    ['WEAPON_COMBATPDW'] = 1.0,
    ['WEAPON_MACHINEPISTOL'] = 1.0,
    ['WEAPON_MINISMG'] = 1.0,
    ['WEAPON_RAYCABINE'] = 1.0,
    --Shotguns
    ['WEAPON_PUMPSHOTGUN'] = 0.5,
    ['WEAPON_PUMPSHOTGUN_MK2'] = 0.5,
    ['WEAPON_SAWNOFFSHOTGUN'] = 0.5,
    ['WEAPON_ASSAULTSHOTGUN'] = 0.5,
    ['WEAPON_BULLPUPSHOTGUN'] = 0.5,
    ['WEAPON_MUSKET'] = 0.5,
    ['WEAPON_HEAVYSHOTGUN'] = 0.5,
    ['WEAPON_DBSHOTGUN'] = 0.5,
    ['WEAPON_AUTOSHOTGUN'] = 0.5,
    ['WEAPON_COMBATSHOTGUN'] = 0.5,
    --Sturmgewehre
    ['WEAPON_ASSAULTRIFLE'] = 1.0,
    ['WEAPON_ASSAULTRIFLE_MK2'] = 1.0,
    ['WEAPON_CARBINERIFLE'] = 1.0,
    ['WEAPON_CARBINERIFLE_MK2'] = 1.0,
    ['WEAPON_ADVANCEDRIFLE'] = 1.0,
    ['WEAPON_SPECIALCARBINE'] = 1.0,
    ['WEAPON_SPECIALCARBINE_MK2'] = 1.0,
    ['WEAPON_BULLPUPRIFLE'] = 1.0,
    ['WEAPON_BULLPUPRIFLE_MK2'] = 1.0,
    ['WEAPON_COMPACTRIFLE'] = 1.0,
    ['WEAPON_MILITARYRIFLE'] = 1.0,
    ['WEAPON_HEAVYRIFLE'] = 1.0,
    ['WEAPON_TACTICALRIFLE'] = 1.0,
    --LMGs
    ['WEAPON_MG'] = 1.0,
    ['WEAPON_COMBATMG'] = 1.0,
    ['WEAPON_COMBATMG_MK2'] = 1.0,
    ['WEAPON_GUSENBERG'] = 5.0,
    --Sniper Rifles
    ['WEAPON_SNIPERRIFLE'] = 10.0,
    ['WEAPON_HEAVYSNIPER'] = 10.0,
    ['WEAPON_HEAVYSNIPER_MK2'] = 10.0,
    ['WEAPON_MARKSMANRIFLE'] = 10.0,
    ['WEAPON_MARKSMANRIFLE_MK2'] = 10.0,
    ['WEAPON_PRECISIONRIFLE'] = 10.0,
    --Schwere Waffen
    ['WEAPON_RPG'] = 10.0,
    ['WEAPON_GRENADELAUNCHER'] = 10.0,
    ['WEAPON_GRENADELAUNCHER_SMOKE'] = 10.0,
    ['WEAPON_MINIGUN'] = 10.0,
    ['WEAPON_FIREWORK'] = 10.0,
    ['WEAPON_RAILGUN'] = 10.0,
    ['WEAPON_HOMINGLAUNCHER'] = 10.0,
    ['WEAPON_COMPACTLAUNCHER'] = 10.0,
    ['WEAPON_RAYMINIGUN'] = 10.0,
    ['WEAPON_EMPLAUNCHER'] = 10.0,
}

local function GetDamagePerUse(weapon)
    for wName, wDamage in pairs(WeaponDurability) do
        if weapon == wName then
            return wDamage
        end
    end
    return nil
end

local BlacklistedHashes = {
    GetHashKey('WEAPON_ASSAULTRIFLE')
}

RegisterServerEvent('cc_core:items:clip')
AddEventHandler('cc_core:items:clip', function(hash, cop)
    local playerId = source
    --[[for _, wHash in pairs(BlacklistedHashes) do
        if hash == wHash then
            TriggerClientEvent('cc_core:hud:notify', playerId, 'error', 'Munition', 'Die Waffe braucht Spezial Munition!')
            return
        end
    end]]
    local itemName = 'clip'
    if cop then
        itemName = 'clip_cop'
    end
    local weaponName = ESX.GetWeaponFromHash(hash)
    local item = ESX.GetPlayerInventoryItem(playerId, itemName)
    if item then
        if item.count >= 1 then
            --local damageTick = GetDamagePerUse(string.upper(weaponName.name))
            --if damageTick ~= nil then
                ESX.RemovePlayerInventoryItem(playerId, itemName, 1, GetCurrentResourceName())
                --local oldWeaponHealth = ESX.GetPlayerWeaponHealth(playerId, weaponName.name)
                ESX.AddPlayerWeaponAmmo(playerId, weaponName.name, 30)
                --ESX.SetPlayerWeaponHealth(playerId, weaponName.name, oldWeaponHealth - damageTick)
                --Citizen.Wait(255)
                --local weaponHealth = ESX.GetPlayerWeaponHealth(playerId, weaponName.name)
                --print('Player:', playerId, 'Used Magazine! New Weapon Health:', weaponHealth)
                --if weaponHealth <= 0.0 then
                --    ESX.RemovePlayerWeapon(playerId, weaponName.name)
                --    TriggerClientEvent('cc_core:hud:notify', playerId, 'error', 'Munition', 'Deine Waffe ist Kaputt gegangen!')
                --    if weaponName.name ~= 'WEAPON_STUNGUN' and weaponName.name ~= 'WEAPON_COMBATPDW' and weaponName.name ~= 'WEAPON_SMG' and weaponName.name ~= 'WEAPON_PUMPSHOTGUN' and weaponName.name ~= 'WEAPON_SNIPERRIFLE' and weaponName.name ~= 'WEAPON_CARBINERIFLE' and weaponName.name ~= 'WEAPON_REVOLVER' and weaponName.name ~= 'WEAPON_COMBATPISTOL' then
                --        ESX.AddPlayerInventoryItem(playerId, string.upper(weaponName.name)..'_BROKY', 1, GetCurrentResourceName())
                --    end
                --end
            --end
        end
    end
end)

ESX.RegisterUsableItem('50cal_ammo', function(source)
    if ESX.HasPlayerWeapon(source, 'WEAPON_SNIPERRIFLE') then
        TriggerClientEvent('cc_core:hud:notify', source, 'success', '50.CAL', 'Magazin Nachgeladen!')
        ESX.RemovePlayerInventoryItem(source, '50cal_ammo', 1, GetCurrentResourceName())
        ESX.AddPlayerWeaponAmmo(source, 'WEAPON_SNIPERRIFLE', 5)
    elseif ESX.HasPlayerWeapon(source, 'WEAPON_REVOLVER') then
        TriggerClientEvent('cc_core:hud:notify', source, 'success', '50.CAL', 'Magazin Nachgeladen!')
        ESX.RemovePlayerInventoryItem(source, '50cal_ammo', 1, GetCurrentResourceName())
        ESX.AddPlayerWeaponAmmo(source, 'WEAPON_REVOLVER', 6)
    else
        TriggerClientEvent('cc_core:hud:notify', source, 'error', '50.CAL', 'Diese Munition passt nur in 50 Kaliber Waffen!')
    end
end)

ESX.RegisterUsableItem('762_ammo', function(source)
    if ESX.HasPlayerWeapon(source, 'WEAPON_ASSAULTRIFLE') then
        TriggerClientEvent('cc_core:hud:notify', source, 'success', '7.62 Munition', 'Sturmgewehr Nachgeladen!')
        ESX.RemovePlayerInventoryItem(source, '762_ammo', 1, GetCurrentResourceName())
        ESX.AddPlayerWeaponAmmo(source, 'WEAPON_ASSAULTRIFLE', 30)
    else
        TriggerClientEvent('cc_core:hud:notify', source, 'error', '7.62 Munition', 'Diese Munition passt nur in ein Sturmgewehre!')
    end
end)

ESX.RegisterServerCallback('cc_core:items:getWeaponHealth', function(source, cb, wHash)
    if wHash ~= nil then
        local weapon = ESX.GetWeaponFromHash(wHash)
        if weapon then
            weapon.name = string.upper(weapon.name)
            local weaponHealth = ESX.GetPlayerWeaponHealth(source, weapon.name)
            cb(weaponHealth)
        end
    end
end)

local jobArray = {
    'police',
    'sheriff',
    'fib',
    'army',
    'ambulance'
}

local function legalJob(jobName)
    for k, v in pairs(jobArray) do
        if v == jobName then
            return true
        end
    end

    return false
end

ESX.RegisterUsableItem('clip_cop', function(playerId)
    local jobName = ESX.GetPlayerJob(playerId).name

    if legalJob(jobName) then
        TriggerClientEvent('cc_core:items:clip', playerId, true)
    end
end)

ESX.RegisterUsableItem('bulletproof', function(playerId)
    ESX.RemovePlayerInventoryItem(playerId, 'bulletproof', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:items:bulletproof', playerId)
end)

ESX.RegisterUsableItem('kevlarweste', function(playerId)
    ESX.RemovePlayerInventoryItem(playerId, 'kevlarweste', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:items:kevlarweste', playerId)
end)

ESX.RegisterUsableItem('verbandskasten', function(playerId)
    ESX.RemovePlayerInventoryItem(playerId, 'verbandskasten', 1, GetCurrentResourceName())
    TriggerClientEvent('cc_core:items:verbandskasten', playerId)
end)

ESX.RegisterUsableItem('bulletproof_cop', function(playerId)
    local jobName = ESX.GetPlayerJob(playerId).name

    if legalJob(jobName) then
        ESX.RemovePlayerInventoryItem(playerId, 'bulletproof_cop', 1, GetCurrentResourceName())
        TriggerClientEvent('cc_core:items:bulletproof', playerId)
    end
end)

ESX.RegisterUsableItem('cop_repairkit', function(playerId)
    local jobName = ESX.GetPlayerJob(playerId).name

    if legalJob(jobName) then
        TriggerClientEvent('cc_core:items:repair', playerId, false, true)
    end
end)

ESX.RegisterUsableItem('newlockpick', function(source)
    TriggerClientEvent('cc_items:checkLockpick', source)
end)

local dices = {}

ESX.RegisterUsableItem('dice', function(source)
    local coords = GetEntityCoords(GetPlayerPed(source))
    local distance = #(coords - vector3(52.1924, -806.7092, 31.5384))

    if distance > 25.0 then
        Notify(source, 'Information', 'Du kannst nur in der Nähe von Zeytunsbar Würfeln!', 'info')
        return
    end

    if dices[source] == nil then
        dices[source] = {}
        dices[source].use = false
        dices[source].random = math.random(1, 6)
    end

    if not dices[source].use then
        dices[source].use = true
        TriggerClientEvent('cc_core:items:diceResult', source)
    end
end)

RegisterCommand('diceR', function(source, args)
    if ESX.GetPlayerGroup(source) == 'projektleitung' then
        if dices[source] == nil then
            dices[source] = {}
            dices[source].use = false
            dices[source].random = tonumber(args[1])
        end
    
        if not dices[source].use then
            dices[source].use = true
            TriggerClientEvent('cc_core:items:diceResult', source)
        end
    end
end)

RegisterServerEvent('cc_core:items:diceResult')
AddEventHandler('cc_core:items:diceResult', function()
    local playerId = source
    
    if dices[playerId] ~= nil then
        if dices[playerId].use then
            local xPlayers = exports['cc_core']:GetPlayersFix()
            local sourcePed = GetPlayerPed(playerId)
            local sourceCoords = GetEntityCoords(sourcePed)
        
            for k, v in pairs(xPlayers) do
                local ped = GetPlayerPed(v.playerId)
                local coords = GetEntityCoords(ped)
                local distance = #(coords - sourceCoords)
        
                if distance < 5.0 then
                    Notify(v.playerId, 'Würfel', ESX.GetPlayerRPName(playerId) .. ' hat eine ' .. dices[playerId].random .. ' gewürfelt.', 'info')
                end
            end

            dices[playerId] = nil
        end 
    end
end)

RegisterServerEvent('cc_core:items:removeItem')
AddEventHandler('cc_core:items:removeItem', function(itemName)
    local playerId = source
    local item = ESX.GetPlayerInventoryItem(playerId, itemName)

    if ESX.IsBlacklistedItem(itemName) then
        return
    end

    if item then
        if item.count >= 1 then
            ESX.RemovePlayerInventoryItem(playerId, itemName, 1, GetCurrentResourceName())
        end
    end
end)

--clientcode
itemsCode = [[
local cooldown, cooldown2 = false, false

RegisterNetEvent('cc_core:items:repair')
AddEventHandler('cc_core:items:repair', function(super, cop)
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)

    if IsAnyVehicleNearPoint(coords.x, coords.y, coords.z, 5.0) then
        local vehicle = nil

        if IsPedInAnyVehicle(ped, false) then
            vehicle = GetVehiclePedIsIn(ped, false)
        else
            vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 5.0, 0, 71)
        end

        
        if DoesEntityExist(vehicle) then
            isUsingRepair = true
    
            Citizen.CreateThread(function()
                while isUsingRepair do
                    Citizen.Wait(0)
                    TriggerEvent('inventory:closeInventory')
                end
            end)

            TaskStartScenarioInPlace(ped, 'PROP_HUMAN_BUM_BIN', 0, true)
            if not super then
                Citizen.Wait(20000)
            else
                Citizen.Wait(3000)
            end

            SetVehicleFixed(vehicle)
            SetVehicleDeformationFixed(vehicle)
            SetVehicleUndriveable(vehicle, false)
            ClearPedTasksImmediately(ped)
            if cop == nil then
                if not super then
                    TriggerServerEvent('cc_core:items:removeItem', 'fixkit')
                else
                    TriggerServerEvent('cc_core:items:removeItem', 'blitzkit')
                end
            else
                TriggerServerEvent('cc_core:items:removeItem', 'cop_repairkit')
            end
            Notify('Repairkit', 'Fahrzeug repariert!', 'info')
            isUsingRepair = false
        end
    end
end)

RegisterNetEvent('cc_core:items:clip')
AddEventHandler('cc_core:items:clip', function(cop)
    local ped = PlayerPedId()
    
    if IsPedArmed(ped, 4) then
        local hash = GetSelectedPedWeapon(ped)
		
		if hash ~= nil then
            TriggerServerEvent('cc_core:items:clip', hash, cop)
            Notify('Clip', 'Du hast erfolgreich deine Munition benutzt!', 'sucess')
		else
            Notify('Clip', 'Du hast keine Waffe in der Hand!', 'info')
		end
    else
        Notify('Clip', 'Diese Munition passt nicht zu deiner Waffe!', 'info')
    end
end)

RegisterNetEvent('cc_core:items:cph4')
AddEventHandler('cc_core:items:cph4', function()
    local ped = PlayerPedId()
    
    SetPedArmour(ped, 100)
    SetEntityHealth(ped, 200)
    TriggerServerEvent('cc_core:items:removeItem', 'cph4')
end)

RegisterNetEvent('cc_core:items:diceResult')
AddEventHandler('cc_core:items:diceResult', function()
    loadAnimDict("anim@mp_player_intcelebrationmale@wank", function()
        TaskPlayAnim(PlayerPedId(), "anim@mp_player_intcelebrationmale@wank", "wank", 8.0, 1.0, -1, 49, 0, 0, 0, 0)
        Citizen.Wait(1500)
        TriggerServerEvent('cc_core:items:diceResult')
        Citizen.Wait(1500)
        ClearPedTasks(PlayerPedId())
    end)
end)

--CLS SHIT ITEM
RegisterNetEvent('cc_items:checkLockpick', function()
    local reqTry = 0
	local closestVehicle, distance = ESX.Game.GetClosestVehicle()
    if DoesEntityExist(closestVehicle) and distance < 5.0 then
        if IsVehicleSeatFree(closestVehicle, -1) then
            NetworkRequestControlOfEntity(closestVehicle)
            while not NetworkHasControlOfEntity(closestVehicle) do
                NetworkRequestControlOfEntity(closestVehicle)
                Wait(255)
                reqTry = reqTry + 1
                if reqTry >= 10 then
                    TriggerEvent('cc_core:hud:notify', 'error', 'Information', 'Dietrich Abgebrochen!')
                    TriggerServerEvent('cc_core:items:removeItem', 'newlockpick', 1)
                    break
                end
            end
            SetVehicleDoorsLocked(closestVehicle, 1)
            TriggerEvent('cc_core:hud:notify', 'info', 'Information', 'Fahrzeug Aufgebrochen!')
            TriggerServerEvent('cc_core:items:removeItem', 'newlockpick', 1)
        else
            TriggerEvent('cc_core:hud:notify', 'error', 'Information', 'Es darf niemand im Fahrzeug sitzen!')
        end
    else
        TriggerEvent('cc_core:hud:notify', 'error', 'Information', 'Kein Fahrzeug in der Nähe!')
    end
end)

RegisterNetEvent('cc_core:items:bulletproof')
AddEventHandler('cc_core:items:bulletproof', function()
    local ped = PlayerPedId()
    
    loadAnimDict('anim@heists@narcotics@funding@gang_idle', function()
        startProgressbar(3)
        TaskPlayAnim(ped, 'anim@heists@narcotics@funding@gang_idle' ,'gang_chatting_idle01', 8.0, -8.0, 5000, 0, 0, false, false, false)

        Citizen.Wait(500)

        while IsEntityPlayingAnim(ped, 'anim@heists@narcotics@funding@gang_idle', 'gang_chatting_idle01', 3) do
			Citizen.Wait(0)
			DisableAllControlActions(0)
			EnableControlAction(0, 1, true)
			EnableControlAction(0, 2, true)
			EnableControlAction(0, 38, true)
		end

        ClearPedTasks(ped)
        AddArmourToPed(ped, 100)
        SetPedArmour(ped, 100)
        Notify('Information', 'Du hast 1x Schutzweste benutzt', 'info')
    end)
end)

RegisterNetEvent('cc_core:items:kevlarweste')
AddEventHandler('cc_core:items:kevlarweste', function()
    local ped = PlayerPedId()
    
    loadAnimDict('anim@heists@narcotics@funding@gang_idle', function()
        startProgressbar(5)
        TaskPlayAnim(ped, 'anim@heists@narcotics@funding@gang_idle' ,'gang_chatting_idle01', 8.0, -8.0, 5000, 0, 0, false, false, false)

        Citizen.Wait(500)

        while IsEntityPlayingAnim(ped, 'anim@heists@narcotics@funding@gang_idle', 'gang_chatting_idle01', 3) do
			Citizen.Wait(0)
			DisableAllControlActions(0)
			EnableControlAction(0, 1, true)
			EnableControlAction(0, 2, true)
			EnableControlAction(0, 38, true)
		end

        ClearPedTasks(ped)
        AddArmourToPed(ped, GetPedArmour(ped)+25)
        SetPedArmour(ped, GetPedArmour(ped)+25)
        Notify('Information', 'Du hast 1x Schutzweste benutzt', 'info')
    end)
end)

RegisterNetEvent('cc_core:items:verbandskasten')
AddEventHandler('cc_core:items:verbandskasten', function()
    local ped = PlayerPedId()
    
    loadAnimDict('anim@heists@narcotics@funding@gang_idle', function()
        startProgressbar(5)
        TaskPlayAnim(ped, 'anim@heists@narcotics@funding@gang_idle' ,'gang_chatting_idle01', 8.0, -8.0, 5000, 0, 0, false, false, false)

        Citizen.Wait(500)

        while IsEntityPlayingAnim(ped, 'anim@heists@narcotics@funding@gang_idle', 'gang_chatting_idle01', 3) do
			Citizen.Wait(0)
			DisableAllControlActions(0)
			EnableControlAction(0, 1, true)
			EnableControlAction(0, 2, true)
			EnableControlAction(0, 38, true)
		end

        ClearPedTasks(ped)
        SetEntityHealth(ped, GetEntityHealth(ped)+50)
        Notify('Information', 'Du hast 1x Verbandskasten benutzt', 'info')
    end)
end)

RegisterNetEvent('cc_core:items:medikit')
AddEventHandler('cc_core:items:medikit', function()
    local ped = PlayerPedId()
    
    loadAnimDict('anim@heists@narcotics@funding@gang_idle', function()
        startProgressbar(3)
        TaskPlayAnim(ped, 'anim@heists@narcotics@funding@gang_idle' ,'gang_chatting_idle01', 8.0, -8.0, 5000, 0, 0, false, false, false)

        Citizen.Wait(500)

        while IsEntityPlayingAnim(ped, 'anim@heists@narcotics@funding@gang_idle', 'gang_chatting_idle01', 3) do
			Citizen.Wait(0)
			DisableAllControlActions(0)
			EnableControlAction(0, 1, true)
			EnableControlAction(0, 2, true)
			EnableControlAction(0, 38, true)
		end

        ClearPedTasks(ped)
        SetEntityHealth(ped, GetEntityMaxHealth(ped))
        Notify('Information', 'Du hast 1x Verbandskasten benutzt', 'info')
    end)
end)

RegisterCommand('bulletproofm', function()
    if not cooldown then
        cooldown, cooldown2 = true, true

        if haveItem('bulletproof', 1) then
            TriggerServerEvent('cc_core:items:removeItem', 'bulletproof')
            TriggerEvent('cc_core:items:bulletproof')
        end

        Citizen.Wait(10000)
        cooldown, cooldown2 = false, false
    end
end)

local jobArray = {
    'police',
    'sheriff',
    'fib',
    'army',
    'ambulance',
    'Elite'
}

local function legalJob(jobName)
    for k, v in pairs(jobArray) do
        if v == jobName then
            return true
        end
    end

    return false
end

RegisterCommand('medikitm', function()
    if not cooldown2 then
        cooldown, cooldown2 = true, true

        if haveItem('medikit', 1) then
            if legalJob(ESX.PlayerData.job.name) then
                TriggerServerEvent('cc_core:items:removeItem', 'medikit')
                TriggerEvent('cc_core:items:medikit') 
            end
        end

        Citizen.Wait(10000)
        cooldown, cooldown2 = false, false
    end
end)

RegisterKey('bulletproofm', 'Schutzweste ziehen', 'PAGEDOWN')
RegisterKey('medikitm', 'Medikit ziehen', 'PAGEUP')
]]