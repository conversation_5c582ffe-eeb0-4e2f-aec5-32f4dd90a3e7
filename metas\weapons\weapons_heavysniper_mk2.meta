<?xml version="1.0" encoding="UTF-8"?>

<CWeaponInfoBlob>
  <SlotNavigateOrder>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="311" />
          <Entry>SLOT_HEAVYSNIPER_MK2</Entry>
        </Item>
      </WeaponSlots>
    </Item>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="311" />
          <Entry>SLOT_HEAVYSNIPER_MK2</Entry>
        </Item>
      </WeaponSlots>
    </Item>
  </SlotNavigateOrder>
  <SlotBestOrder>
    <WeaponSlots>
      <Item>
        <OrderNumber value="229" />
        <Entry>SLOT_HEAVYSNIPER_MK2</Entry>
      </Item>
    </WeaponSlots>
  </SlotBestOrder>
  <TintSpecValues />
  <FiringPatternAliases />
  <UpperBodyFixupExpressionData />
  <AimingInfos />
  <Infos>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos>
        <Item type="CWeaponInfo">
          <Name>WEAPON_HEAVYSNIPER_MK2</Name>
          <Model>w_sr_heavysnipermk2</Model>
          <Audio>AUDIO_ITEM_HEAVYSNIPER_MK2</Audio>
          <Slot>SLOT_HEAVYSNIPER_MK2</Slot>
          <DamageType>BULLET</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>DELAYED_HIT</FireType>
          <WheelSlot>WHEEL_SNIPER</WheelSlot>
          <Group>GROUP_SNIPER</Group>
          <AmmoInfo ref="AMMO_SNIPER" />
          <AimingInfo ref="RIFLE_HI_BASE_STRAFE" />
          <ClipSize value="6" />
          <AccuracySpread value="5.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMinOverride value="0.500000" />
          <RecoilAccuracyMax value="0.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="230000.000000" />
          <HeadShotDamageModifierPlayer value="1288860.000000" />
          <Damage value="230.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.250000" />
          <NetworkHitLimbsDamageModifier value="0.250000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <VehicleDamageModifier value="1.000000" />
          <Force value="500.000000" />
          <ForceHitPed value="230.000000" />
          <ForceHitVehicle value="2000.000000" />
          <ForceHitFlyingHeli value="5000.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="100.000000" />
              <ForceBack value="50.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="75.000000" />
              <ForceBack value="120.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="750.000000" />
          <Penetration value="1.000000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="5000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="3.300000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="1.200000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_RIFLE_SNIPER</EffectGroup>
            <FlashFx>muz_alternate_star</FlashFx>
            <FlashFxAlt>muz_alternate_star</FlashFxAlt>
            <FlashFxFP />
            <FlashFxFPAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxFP />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <MuzzleOverrideOffset x="0.744600" y="0.000000" z="0.042000" />
            <ShellFx>eject_sniper_heavy</ShellFx>
            <ShellFxFP />
            <TracerFx>bullet_tracer</TracerFx>
            <PedDamageHash>BulletLarge</PedDamageHash>
            <TracerFxChanceSP value="0.150000" />
            <TracerFxChanceMP value="0.750000" />
            <FlashFxChanceSP value="1.000000" />
            <FlashFxChanceMP value="1.000000" />
            <FlashFxAltChance value="0.200000" />
            <FlashFxScale value="0.500000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="true" />
            <FlashFxLightOffsetDist value="0.000000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="200" />
          <InitialRumbleIntensity value="0.850000" />
          <InitialRumbleIntensityTrigger value="0.950000" />
          <RumbleDuration value="200" />
          <RumbleIntensity value="0.850000" />
          <RumbleIntensityTrigger value="0.950000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="200" />
          <InitialRumbleIntensityFps value="1.000000" />
          <RumbleDurationFps value="200" />
          <RumbleIntensityFps value="1.000000" />
          <NetworkPlayerDamageModifier value="1.390000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.000000" />
          <LockOnRange value="50.000000" />
          <WeaponRange value="500.000000"  />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <BulletDirectionPitchOffset value="0.000000" />
          <BulletDirectionPitchHomingOffset value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="1500.000000" />
          <DamageFallOffRangeMax value="1500.000000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>SNIPER_AIM_CAMERA</DefaultCameraHash>
          <AimCameraHash />
          <FireCameraHash />
          <CoverCameraHash>MELEE_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash>SNIPER_AIM_CAMERA</CoverReadyToFireCameraHash>
          <RunAndGunCameraHash>DEFAULT_THIRD_PERSON_PED_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <PovTurretCameraHash />
          <CameraFov value="45.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="0.000000" />
          <FirstPersonScopeAttachmentFov value="0.000000" />
          <FirstPersonDrivebyIKOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentOffset x="-0.004000" y="0.110000" z="-0.043000" />
          <FirstPersonScopeRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="-0.050000" y="0.000000" z="-0.025000" />
          <FirstPersonAsThirdPersonRNGOffset x="-0.050000" y="0.000000" z="-0.025000" />
          <FirstPersonAsThirdPersonLTOffset x="-0.050000" y="0.000000" z="-0.025000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.100000" y="0.050000" z="-0.005000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="-0.050000" y="0.100000" z="-0.050000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <FirstPersonScopeAttachmentData />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash>DEFAULT_FIRST_PERSON_RECOIL_SHAKE</RecoilShakeHash>
          <RecoilShakeHashFirstPerson>DEFAULT_FIRST_PERSON_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="0.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.000000" />
          <IkRecoilDisplacementScope value="0.000000" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <ReticuleHudPositionOffsetForPOVTurret x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.100000" y="0.225000" z="0.600000" />
          <AimProbeLengthMin value="0.575000" />
          <AimOffsetMax x="0.150000" y="-0.225000" z="0.485000" />
          <AimProbeLengthMax value="0.550000" />
          <AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000" />
          <AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000" />
          <AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000" />
          <AimOffsetMinFPSLT x="0.180000" y="0.231000" z="0.669000" />
          <AimOffsetMaxFPSLT x="0.048000" y="-0.225000" z="0.409000" />
          <AimOffsetMinFPSRNG x="0.120000" y="0.275000" z="0.509000" />
          <AimOffsetMaxFPSRNG x="0.138000" y="-0.212000" z="0.518000" />
          <AimOffsetMinFPSScope x="0.090000" y="0.078000" z="0.531000" />
          <AimOffsetMaxFPSScope x="0.006000" y="-0.059000" z="0.694000" />
          <AimOffsetEndPosMinFPSIdle x="-0.474000" y="0.787000" z="-0.316000" />
          <AimOffsetEndPosMedFPSIdle x="-0.334000" y="0.831000" z="0.750000" />
          <AimOffsetEndPosMaxFPSIdle x="-0.376000" y="0.114000" z="1.118000" />
          <AimOffsetEndPosMinFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMedFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMaxFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeRadiusOverrideFPSIdle value="0.000000" />
          <AimProbeRadiusOverrideFPSIdleStealth value="0.000000" />
          <AimProbeRadiusOverrideFPSLT value="0.000000" />
          <AimProbeRadiusOverrideFPSRNG value="0.000000" />
          <AimProbeRadiusOverrideFPSScope value="0.000000" />
          <TorsoAimOffset x="-0.340000" y="0.550000" />
          <TorsoCrouchedAimOffset x="0.160000" y="0.120000" />
          <LeftHandIkOffset x="0.100000" y="0.050000" z="0.000000" />
          <ReticuleMinSizeStanding value="0.600000" />
          <ReticuleMinSizeCrouched value="0.500000" />
          <ReticuleScale value="0.050000" />
          <ReticuleStyleHash>WEAPONTYPE_RIFLE</ReticuleStyleHash>
          <FirstPersonReticuleStyleHash>SNIPER_MAX</FirstPersonReticuleStyleHash>
          <PickupHash>PICKUP_WEAPON_HEAVYSNIPER_MK2</PickupHash>
          <MPPickupHash>PICKUP_AMMO_BULLET_MP</MPPickupHash>
          <HumanNameHash>WT_SNIP_HVY2</HumanNameHash>
          <MovementModeConditionalIdle>MMI_2Handed</MovementModeConditionalIdle>
          <StatName>HVYSNIPER</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>Sniper</NmShotTuningSet>
          <AttachPoints>
            <Item>
              <AttachBone>WAPClip</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CLIP_01</Name>
                  <Default value="true" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CLIP_02</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CLIP_ARMORPIERCING</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CLIP_EXPLOSIVE</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CLIP_FMJ</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CLIP_INCENDIARY</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPScop</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_SCOPE_LARGE_MK2</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_SCOPE_MAX</Name>
                  <Default value="true" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_SCOPE_NV</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_SCOPE_THERMAL</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPSupp</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_SR_SUPP_03</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_MUZZLE_08</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_MUZZLE_09</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPBarrel</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_SR_BARREL_01</Name>
                  <Default value="true" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_SR_BARREL_02</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
			<Item>
              <AttachBone>gun_root</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CAMO</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CAMO_02</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CAMO_03</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CAMO_04</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CAMO_05</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CAMO_06</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CAMO_07</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CAMO_08</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CAMO_09</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CAMO_10</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_HEAVYSNIPER_MK2_CAMO_IND_01</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
          </AttachPoints>
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>CarriedInHand FirstPersonScope Gun CanFreeAim TwoHanded AnimReload AnimCrouchFire UsableOnFoot UsableInCover HasLowCoverReloads HasLowCoverSwaps IgnoreHelmets DriveByMPOnly UseFPSAimIK DisableFPSAimForScope UseFPSSecondaryMotion IncendiaryGuaranteedChance</WeaponFlags>
          <TintSpecValues ref="TINT_GUNRUNNING" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="default" />
          <AmmoDiminishingRate value="3" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="1.000000" />
          <StealthFiringBreathingAdditiveWeight value="1.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="1.000000" />
          <StealthFiringLeanAdditiveWeight value="1.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="100" />
          <HudSpeed value="20" />
          <HudCapacity value="5" />
          <HudAccuracy value="90" />
          <HudRange value="100" />
          <VehicleAttackAngle value="25.000000" />
          <TorsoIKAngleLimit value="-1.000000" />
          <MeleeRightFistTargetHealthDamageScaler value="-1.000000" />
          <AirborneAircraftLockOnMultiplier value="1.000000" />
          <ArmouredVehicleGlassDamageOverride value="-1.000000" />
          <CamoDiffuseTexIdxs />
          <RotateBarrelBone />
          <RotateBarrelBone2 />
          <FrontClearTestParams>
            <ShouldPerformFrontClearTest value="false" />
            <ForwardOffset value="0.000000" />
            <VerticalOffset value="0.000000" />
            <HorizontalOffset value="0.000000" />
            <CapsuleRadius value="0.000000" />
            <CapsuleLength value="0.000000" />
          </FrontClearTestParams>
        </Item>
      </Infos>
    </Item>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos />
    </Item>
  </Infos>
  <VehicleWeaponInfos />
  <WeaponGroupDamageForArmouredVehicleGlass />
  <Name>DLC - Mk II - Heavy Sniper</Name>
</CWeaponInfoBlob>