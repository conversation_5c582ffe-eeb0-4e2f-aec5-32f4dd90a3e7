<?xml version="1.0" encoding="UTF-8"?>

<CWeaponInfoBlob>
  <SlotNavigateOrder>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="203" />
          <Entry>SLOT_SPECIALCARBINE_MK2</Entry>
        </Item>
      </WeaponSlots>
    </Item>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="213" />
          <Entry>SLOT_SPECIALCARBINE_MK2</Entry>
        </Item>
      </WeaponSlots>
    </Item>
  </SlotNavigateOrder>
  <SlotBestOrder>
    <WeaponSlots>
      <Item>
        <OrderNumber value="49" />
        <Entry>SLOT_SPECIALCARBINE_MK2</Entry>
      </Item>
    </WeaponSlots>
  </SlotBestOrder>
  <TintSpecValues />
  <FiringPatternAliases />
  <UpperBodyFixupExpressionData />
  <AimingInfos />
  <Infos>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos>
        <Item type="CWeaponInfo">
          <Name>WEAPON_SPECIALCARBINE_MK2</Name>
          <Model>w_ar_specialcarbinemk2</Model>
          <Audio>AUDIO_ITEM_CARBINE_SPECIAL_MK2</Audio>
          <Slot>SLOT_SPECIALCARBINE_MK2</Slot>
          <DamageType>BULLET</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>INSTANT_HIT</FireType>
          <WheelSlot>WHEEL_RIFLE</WheelSlot>
          <Group>GROUP_RIFLE</Group>
          <AmmoInfo ref="AMMO_RIFLE" />
          <AimingInfo ref="RIFLE_HI_BASE_STRAFE" />
          <ClipSize value="30" />
          <AccuracySpread value="3.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMinOverride value="-1.000000" />
          <RecoilAccuracyMax value="0.000000" />
          <RecoilErrorTime value="3.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="230000.000000" />
          <HeadShotDamageModifierPlayer value="1288860.000000" />
          <Damage value="32.500000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <VehicleDamageModifier value="1.000000" />
          <Force value="75.000000" />
          <ForceHitPed value="140.000000" />
          <ForceHitVehicle value="1200.000000" />
          <ForceHitFlyingHeli value="1250.000000" />
          <OverrideForces>
            <Item>
              <BoneTag>BONETAG_HEAD</BoneTag>
              <ForceFront value="80.000000" />
              <ForceBack value="50.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_NECK</BoneTag>
              <ForceFront value="60.000000" />
              <ForceBack value="90.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_THIGH</BoneTag>
              <ForceFront value="40.000000" />
              <ForceBack value="1.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_THIGH</BoneTag>
              <ForceFront value="40.000000" />
              <ForceBack value="1.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_L_CALF</BoneTag>
              <ForceFront value="70.000000" />
              <ForceBack value="80.000000" />
            </Item>
            <Item>
              <BoneTag>BONETAG_R_CALF</BoneTag>
              <ForceFront value="60.000000" />
              <ForceBack value="100.000000" />
            </Item>
          </OverrideForces>
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="600.000000" />
          <Penetration value="0.100000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.135000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.750000" />
          <BulletBendingZoomedRadius value="0.375000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_RIFLE_ASSAULT</EffectGroup>
            <FlashFx>muz_assault_rifle</FlashFx>
            <FlashFxAlt>muz_alternate_star</FlashFxAlt>
            <FlashFxFP>muz_assault_rifle_fp</FlashFxFP>
            <FlashFxFPAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxFP />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <MuzzleOverrideOffset x="0.000000" y="0.000000" z="0.000000" />
            <ShellFx>eject_auto</ShellFx>
            <ShellFxFP>eject_auto_fp</ShellFxFP>
            <TracerFx>bullet_tracer</TracerFx>
            <PedDamageHash>BulletLarge</PedDamageHash>
            <TracerFxChanceSP value="0.150000" />
            <TracerFxChanceMP value="0.750000" />
            <FlashFxChanceSP value="1.000000" />
            <FlashFxChanceMP value="1.000000" />
            <FlashFxAltChance value="0.200000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="false" />
            <FlashFxLightOffsetDist value="0.200000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="90" />
          <InitialRumbleIntensity value="0.700000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="90" />
          <RumbleIntensity value="0.100000" />
          <RumbleIntensityTrigger value="0.800000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="120" />
          <InitialRumbleIntensityFps value="0.900000" />
          <RumbleDurationFps value="90" />
          <RumbleIntensityFps value="0.200000" />
          <NetworkPlayerDamageModifier value="1.000000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.700000" />
          <LockOnRange value="65.000000" />
          <WeaponRange value="500.000000"  />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <BulletDirectionPitchOffset value="0.000000" />
          <BulletDirectionPitchHomingOffset value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="47.500000" />
          <DamageFallOffRangeMax value="120.000000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_CAMERA</DefaultCameraHash>
          <AimCameraHash />
          <FireCameraHash />
          <CoverCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash>DEFAULT_THIRD_PERSON_PED_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
          <CinematicShootingCameraHash>DEFAULT_THIRD_PERSON_PED_CINEMATIC_SHOOTING_CAMERA</CinematicShootingCameraHash>
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <PovTurretCameraHash />
          <CameraFov value="35.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="30.000000" />
          <FirstPersonScopeAttachmentFov value="30.000000" />
          <FirstPersonDrivebyIKOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonScopeOffset x="0.000000" y="0.025000" z="-0.040000" />
          <FirstPersonScopeAttachmentOffset x="0.000000" y="0.075000" z="-0.059000" />
          <FirstPersonScopeRotationOffset x="-0.550000" y="0.000000" z="0.000000" />
          <FirstPersonScopeAttachmentRotationOffset x="-0.400000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="-0.100000" y="0.050000" z="0.000000" />
          <FirstPersonAsThirdPersonRNGOffset x="-0.050000" y="-0.050000" z="-0.075000" />
          <FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.050000" z="-0.100000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.070000" y="-0.018000" z="-0.050000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="-0.000000" y="0.100000" z="-0.050000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.050000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <FirstPersonScopeAttachmentData>
            <Item>
              <Name>COMPONENT_AT_SCOPE_MACRO_MK2</Name>
              <FirstPersonScopeAttachmentFov value="27.500000" />
              <FirstPersonScopeAttachmentOffset x="0.000000" y="0.200000" z="-0.055000" />
              <FirstPersonScopeAttachmentRotationOffset x="-0.300000" y="0.000000" z="0.000000" />
            </Item>
            <Item>
              <Name>COMPONENT_AT_SCOPE_MEDIUM_MK2</Name>
              <FirstPersonScopeAttachmentFov value="25.000000" />
              <FirstPersonScopeAttachmentOffset x="0.000000" y="0.100000" z="-0.062000" />
              <FirstPersonScopeAttachmentRotationOffset x="-0.300000" y="0.000000" z="0.000000" />
            </Item>
          </FirstPersonScopeAttachmentData>
          <ZoomFactorForAccurateMode value="1.300000" />
          <RecoilShakeHash>ASSAULT_RIFLE_RECOIL_SHAKE</RecoilShakeHash>
          <RecoilShakeHashFirstPerson>FPS_ASSAULT_RIFLE_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
          <AccuracyOffsetShakeHash>DEFAULT_THIRD_PERSON_ACCURACY_OFFSET_SHAKE</AccuracyOffsetShakeHash>
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="0.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.000000" />
          <IkRecoilDisplacementScope value="0.000000" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <ReticuleHudPositionOffsetForPOVTurret x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.250000" y="0.200000" z="0.600000" />
          <AimProbeLengthMin value="0.430000" />
          <AimOffsetMax x="0.165000" y="-0.180000" z="0.470000" />
          <AimProbeLengthMax value="0.340000" />
          <AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000" />
          <AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000" />
          <AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000" />
          <AimOffsetMinFPSLT x="0.180000" y="0.231000" z="0.669000" />
          <AimOffsetMaxFPSLT x="0.048000" y="-0.225000" z="0.409000" />
          <AimOffsetMinFPSRNG x="0.120000" y="0.275000" z="0.509000" />
          <AimOffsetMaxFPSRNG x="0.138000" y="-0.212000" z="0.518000" />
          <AimOffsetMinFPSScope x="0.090000" y="0.078000" z="0.531000" />
          <AimOffsetMaxFPSScope x="0.006000" y="-0.059000" z="0.694000" />
          <AimOffsetEndPosMinFPSIdle x="-0.284000" y="0.612000" z="-0.205000" />
          <AimOffsetEndPosMedFPSIdle x="-0.178000" y="0.639000" z="0.616000" />
          <AimOffsetEndPosMaxFPSIdle x="-0.217000" y="-0.096000" z="0.887000" />
          <AimOffsetEndPosMinFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMedFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMaxFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeRadiusOverrideFPSIdle value="0.000000" />
          <AimProbeRadiusOverrideFPSIdleStealth value="0.000000" />
          <AimProbeRadiusOverrideFPSLT value="0.000000" />
          <AimProbeRadiusOverrideFPSRNG value="0.000000" />
          <AimProbeRadiusOverrideFPSScope value="0.000000" />
          <TorsoAimOffset x="-1.000000" y="0.550000" />
          <TorsoCrouchedAimOffset x="0.100000" y="0.120000" />
          <LeftHandIkOffset x="0.015000" y="0.095000" z="-0.008000" />
          <ReticuleMinSizeStanding value="0.600000" />
          <ReticuleMinSizeCrouched value="0.500000" />
          <ReticuleScale value="0.300000" />
          <ReticuleStyleHash>WEAPONTYPE_RIFLE</ReticuleStyleHash>
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_SPECIALCARBINE_MK2</PickupHash>
          <MPPickupHash>PICKUP_AMMO_BULLET_MP</MPPickupHash>
          <HumanNameHash>WT_SPCARBINE2</HumanNameHash>
          <MovementModeConditionalIdle>MMI_2Handed</MovementModeConditionalIdle>
          <StatName>SPCARB_MK2</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>Automatic</NmShotTuningSet>
          <AttachPoints>
            <Item>
              <AttachBone>WAPClip</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CLIP_01</Name>
                  <Default value="true" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CLIP_02</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CLIP_TRACER</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CLIP_INCENDIARY</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CLIP_ARMORPIERCING</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CLIP_FMJ</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPFlshLasr_2</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_AR_FLSH</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPScop_2</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_SIGHTS</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_SCOPE_MACRO_MK2</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_SCOPE_MEDIUM_MK2</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPSupp_2</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_AR_SUPP_02</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_MUZZLE_01</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_MUZZLE_02</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_MUZZLE_03</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_MUZZLE_04</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_MUZZLE_05</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_MUZZLE_06</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_MUZZLE_07</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPGrip_2</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_AR_AFGRIP_02</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>WAPBarrel</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_AT_SC_BARREL_01</Name>
                  <Default value="true" />
                </Item>
                <Item>
                  <Name>COMPONENT_AT_SC_BARREL_02</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
            <Item>
              <AttachBone>gun_root</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CAMO</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CAMO_02</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CAMO_03</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CAMO_04</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CAMO_05</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CAMO_06</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CAMO_07</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CAMO_08</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CAMO_09</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CAMO_10</Name>
                  <Default value="false" />
                </Item>
                <Item>
                  <Name>COMPONENT_SPECIALCARBINE_MK2_CAMO_IND_01</Name>
                  <Default value="false" />
                </Item>
              </Components>
            </Item>
          </AttachPoints>
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>CarriedInHand Automatic Gun CanLockonOnFoot CanLockonInVehicle CanFreeAim TwoHanded AnimReload AnimCrouchFire UsableOnFoot UsableInCover AllowEarlyExitFromFireAnimAfterBulletFired NoLeftHandIKWhenBlocked AllowCloseQuarterKills HasLowCoverReloads HasLowCoverSwaps LongWeapon UseFPSAimIK UseFPSSecondaryMotion FPSOnlyExitFireAnimAfterRecoilEnds</WeaponFlags>
          <TintSpecValues ref="TINT_GUNRUNNING" />
          <FiringPatternAliases ref="FIRING_PATTERN_RIFLE" />
          <ReloadUpperBodyFixupExpressionData ref="default" />
          <AmmoDiminishingRate value="3" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="1.000000" />
          <StealthFiringBreathingAdditiveWeight value="1.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="1.000000" />
          <StealthFiringLeanAdditiveWeight value="1.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="36" />
          <HudSpeed value="65" />
          <HudCapacity value="40" />
          <HudAccuracy value="55" />
          <HudRange value="40" />
          <VehicleAttackAngle value="25.000000" />
          <TorsoIKAngleLimit value="-1.000000" />
          <MeleeRightFistTargetHealthDamageScaler value="-1.000000" />
          <AirborneAircraftLockOnMultiplier value="1.000000" />
          <ArmouredVehicleGlassDamageOverride value="-1.000000" />
          <CamoDiffuseTexIdxs />
          <RotateBarrelBone />
          <RotateBarrelBone2 />
          <FrontClearTestParams>
            <ShouldPerformFrontClearTest value="false" />
            <ForwardOffset value="0.000000" />
            <VerticalOffset value="0.000000" />
            <HorizontalOffset value="0.000000" />
            <CapsuleRadius value="0.000000" />
            <CapsuleLength value="0.000000" />
          </FrontClearTestParams>
        </Item>
      </Infos>
    </Item>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos />
    </Item>
  </Infos>
  <VehicleWeaponInfos />
  <WeaponGroupDamageForArmouredVehicleGlass />
  <Name>DLC - Mk II - Special Carbine</Name>
</CWeaponInfoBlob>