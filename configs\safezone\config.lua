MINERA_SAFEZONE = {}

MINERA_SAFEZONE.Notification = function(src, type, xPlayer, message)
    if (type == 'client') then
        TriggerEvent('cc_core:hud:notify', 'info', 'SafeZone', message)
    end
end

MINERA_SAFEZONE.NotifyTranslations = {
	Entered = "Du hast die SafeZone Betreten.",
	Leaved = "Du hast die SafeZone Verlassen."
}

MINERA_SAFEZONE.SafeZones = {
    {
        Zone = { x = 159.2401, y = -897.978, z = 61.0787 },
        ZoneSize = 70.0,
        Blip = false,
        BlipColor = 2,
        BlipAlpha = 128,
        SpeedLimit = 50.0,
        ShowSafeZoneText = true,
        ShowNotify = true,
        AllowWeaponsInHands = false,
        DisablePlayerFiring = true,
        Marker = {
            Enabled = false,
            Type = 28,
            RenderDistance = 0.0,
            Color = {
                R = 63,
                G = 132,
                B = 11,
                Alpha = 100,
            },
        },
    },
    {
        Zone = { x = -3705.4219, y = -3619.4077, z = 12.8106 },
        ZoneSize = 110.0,
        Blip = false,
        BlipColor = 2,
        BlipAlpha = 128,
        SpeedLimit = 20.0,
        ShowSafeZoneText = true,
        ShowNotify = true,
        AllowWeaponsInHands = false,
        DisablePlayerFiring = true,
        Marker = {
            Enabled = false,
            Type = 28,
            RenderDistance = 0.0,
            Color = {
                R = 63,
                G = 132,
                B = 11,
                Alpha = 100,
            },
        },
    },
    {
        Zone = { x = 890.3819, y = -2112.1824, z = 30.4607 },
        ZoneSize = 45.0,
        Blip = false,
        BlipColor = 2,
        BlipAlpha = 128,
        SpeedLimit = 20.0,
        ShowSafeZoneText = true,
        ShowNotify = true,
        AllowWeaponsInHands = false,
        DisablePlayerFiring = true,
        Marker = {
            Enabled = false,
            Type = 28,
            RenderDistance = 0.0,
            Color = {
                R = 63,
                G = 132,
                B = 11,
                Alpha = 100,
            },
        },
    },
}