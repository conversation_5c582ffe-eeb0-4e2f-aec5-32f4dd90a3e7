personalmenuCode = [[
--RageUIShit
local MainMenu = RageUI.CreateMenu('', '                         Final Allstars')
MainMenu.X = 0 --Top Left
MainMenu.Y = 65 --Top Left
local PersonalMenu = RageUI.CreateSubMenu(MainMenu, 'Personalien')
PersonalMenu.X = 0 --Top Left
PersonalMenu.Y = 65 --Top Left
local BillingMenu = RageUI.CreateSubMenu(MainMenu, 'Rechnungen')
BillingMenu.X = 0 --Top Left
BillingMenu.Y = 65 --Top Left
local JobMenu = RageUI.CreateSubMenu(MainMenu, 'Job Managment')
JobMenu.X = 0 --Top Left
JobMenu.Y = 65 --Top Left
local JobMenu2 = RageUI.CreateSubMenu(MainMenu, 'Job Managment')
JobMenu2.X = 0 --Top Left
JobMenu2.Y = 65 --Top Left
local JobMiscMenu = RageUI.CreateSubMenu(MainMenu, 'Job Optionen')
JobMiscMenu.X = 0 --Top Left
JobMiscMenu.Y = 65 --Top Left
local FIBNETWORKMENU = RageUI.CreateSubMenu(JobMiscMenu, 'FIB NETWORK')
FIBNETWORKMENU.X = 0 --Top Left
FIBNETWORKMENU.Y = 65 --Top Left
local FIBMessagesMenu = RageUI.CreateSubMenu(JobMiscMenu, 'FIB NETWORK')
FIBMessagesMenu.X = 0 --Top Left
FIBMessagesMenu.Y = 65 --Top Left
local FIBOwnerMenu = RageUI.CreateSubMenu(JobMiscMenu, 'FIB NETWORK')
FIBOwnerMenu.X = 0 --Top Left
FIBOwnerMenu.Y = 65 --Top Left
local FIBBillingsMenu = RageUI.CreateSubMenu(JobMiscMenu, 'FIB Network')
FIBBillingsMenu.X = 0 --Top Left
FIBBillingsMenu.Y = 65 --Top Left
local VehicleMenu = RageUI.CreateSubMenu(MainMenu, 'Fahrzeug Menu')
VehicleMenu.X = 0 --Top Left
VehicleMenu.Y = 65 --Top Left
local SettingsMenu = RageUI.CreateSubMenu(MainMenu, 'Einstellungen')
SettingsMenu.X = 0 --Top Left
SettingsMenu.Y = 65 --Top Left
local GameSettingsMenu = RageUI.CreateSubMenu(SettingsMenu, 'Spiel Einstellungen')
GameSettingsMenu.X = 0 --Top Left
GameSettingsMenu.Y = 65 --Top Left
local CrosshairSettingsMenu = RageUI.CreateSubMenu(SettingsMenu, 'Crosshair Einstellungen')
local BlipSettingsMenu = RageUI.CreateSubMenu(SettingsMenu, 'Blip Einstellungen')
local CustomBlipMenu = RageUI.CreateSubMenu(BlipSettingsMenu, 'Custom Einstellungen')
--RageUIShit End

--PersonalMenu
local MoneyAction = 1
local MoneyDesc = nil
local BlackAction = 1
local BlackDesc = nil
local AusweisAction = 1
local AusweisDesc = nil
local DriveLicenseAction = 1
local DriveLicDesc = nil
local DriveShowLicenseAction = 1
local DriveShowLicDesc = nil
local WaffenLiceenseAction = 1
local WaffenLicDesc = nil
local WorkLiceenseAction = 1
local WorkLicDesc = nil
local WorkLiceenseAction2 = 1
local WorkLicDesc2 = nil
local currWeaHash = nil
local currWeaName = nil
local currWeaHealthDescr = 'Warte auf ~r~Inspizierung~s~!'
local unarmedHash = GetHashKey('WEAPON_UNARMED')
local inspecting = false
--PersonalMenu End

--JobMenu
local BossAction = 1
local BossDec = '~r~Kein~s~ Spieler in der Nähe'
local closestCock
local closestPlayerName

local Boss2Action = 1
local Boss2Dec = '~r~Kein~s~ Spieler in der Nähe'
local closest2Cock
local closest2PlayerName
--JobMenu End

--JobMiscMenu
local menuOpen = false
local currNumber = ''
local currFilter = ''
allPhoneData = {}
allBillingData = {}
allOwnerData = {}

--JobMiscMenu End

--VehicleMenu
local Engine_Checked = true
local EngineDesc = nil
VehicleDoors = {}
VehicleDoors.Driver = false
VehicleDoors.CoDriver = false
VehicleDoors.BackL = false
VehicleDoors.BackR = false
VehicleDoors.Coffre = false
VehicleDoors.Motor = false
local SelectedDoor = 1
--VehicleMenu End

--GameSettings
local Drift_Checked = false
local Kino_Checked = false

--RegisterKeyMapping('kino1', 'Kino Modus', 'keyboard', 'M')

RegisterCommand('kino1', function()
    --if exports['cc_phone']:isPhoneOpen() then
        --return
    --end
    SetCurrentPedWeapon(PlayerPedId(), GetHashKey('WEAPON_UNARMED'), true)
    if not Kino_Checked then
        Kino_Checked = true
        DisplayRadar(false)
    else
        Kino_Checked = false
        DisplayRadar(true)
    end
end)

local function isInKino()
    return Kino_Checked
end

exports('isInKino', isInKino)

function StartWeaThread()
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(1000)
            currWeaHash = GetSelectedPedWeapon(PlayerPedId())
            if currWeaHash ~= nil and currWeaHash ~= unarmedHash then
                currWeaName = ESX.GetWeaponFromHash(currWeaHash).label
            end
        end
    end)
end

local dienstNumber = 0

-- Citizen.CreateThread(function()
--     while true do
--         Citizen.Wait(0)
--         if inspecting then
--             DisableControlAction(1, 73, true)
--             DisableControlAction(1, 105, true)
--             DisableControlAction(1, 120, true)
--             DisableControlAction(1, 154, true)
--             DisableControlAction(1, 186, true)
--             DisableControlAction(1, 252, true)
--             DisableControlAction(1, 323, true)
--             DisableControlAction(1, 337, true)
--             DisableControlAction(1, 345, true)
--             DisableControlAction(1, 354, true)
--             DisableControlAction(1, 357, true)
--         else
--             Citizen.Wait(500)
--         end
--     end
-- end)

local darkness = 0 --Kino Shit
local filter = 0 --Kino Shit
--GameSettings End

--CrosshairSettingsMenu
local Crosshair_Style = 1 --1 Cross 2 Dot
local Crosshair_Color = 1
local Crosshair_R = 0
local Crosshair_G = 255
local Crosshair_B = 0
local ratio = GetAspectRatio()
--CrosshairSettingsMenu End

--BlipMenu
SelectedBlip = {}
LoadBlips = {}
LoadBlips.LoadSize = {}
LoadBlips.LoadState = {}
local blips = {
    ['vehicleshop'] = {
        coord = vector3(1004.0, -2.0, 82.0),
        blipId = 0,
        sprite = 40,
        label = 'Auto Shop',
        scale = 0.8
    },
    ['store'] = {
        coord = vector3(1046.0, 64.0, 82.0),
        blipId = 0,
        sprite = 66,
        label = 'Laden East Side',
        scale = 0.5
    },
    ['store_1'] = {
        coord = vector3(1093.0, 141.0, 82.0),
        blipId = 0,
        sprite = 66,
        label = 'Laden West Side',
        scale = 0.5
    },
    ['store_2'] = {
        coord = vector3(1130.0, 200.0, 82.0),
        blipId = 0,
        sprite = 66,
        label = 'Laden Noth Side',
        scale = 0.5
    },
    ['store_3'] = {
        coord = vector3(1186.0, 293.0, 82.0),
        blipId = 0,
        sprite = 66,
        label = 'Laden Sout Side',
        scale = 0.5
    }
}
--BlipMenu End

--ESXEvents&KVP'S
Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(500)
    end
    while ESX.GetPlayerData().job3 == nil do
        Citizen.Wait(500)
    end
    StartWeaThread()
    local Check_Drift = GetResourceKvpInt('cc_f2new-drift')
    Wait(100)
    local Check_Kino = GetResourceKvpInt('cc_f2new-kino')
    Wait(100)
    if Check_Drift ~= 0 then
        Drift_Checked = true
    else
        Drift_Checked = false
    end
    if Check_Kino ~= 0 then
        Kino_Checked = true
    else
        Kino_Checked = false
    end

    local Check_dienst = GetResourceKvpInt('cc_f2-new_dienstNumber')

    dienstNumber = Check_dienst
end)

--ESXEvents&KVP'S End

--RageUI Pool
function RageUI.PoolMenus:CorleoneF2()
    MainMenu:IsVisible(function(Items)
        Items:AddButton('Personalien', 'Ausweiß und co ist hier', { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
            if onSelected then
                return
            end
        end, PersonalMenu)
        Items:AddButton('Rechnungen', 'Ausstehende Rechnungen', { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
            if onSelected then
                TriggerEvent("billing_ui:viewInvoices")
                RageUI.CloseAll()
                return
            end
        end)
        -- if PlayerData.job.grade_name == 'boss' then
        --     Items:AddButton('Jobmanagment', 'Verwalte '..PlayerData.job.label, { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
        --         if onSelected then
        --             closestCock = ESX.Game.GetClosestPlayer()
        --             if GetPlayerServerId(closestCock) ~= 0 then
        --                 BossDec = 'Spieler : '..GetPlayerName(GetPlayerFromServerId(GetPlayerServerId(closestCock)))
        --             else
        --                 BossDec = '~r~Kein~s~ Spieler in der Nähe'
        --             end
        --             return
        --         end
        --     end, JobMenu)
        -- end
            if PlayerData.job.grade >= 10 then
                Items:AddButton('Jobmanagment', 'Verwalte '..PlayerData.job.label, { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
                if onSelected then
                    closestCock = ESX.Game.GetClosestPlayer()
                    if GetPlayerServerId(closestCock) ~= 0 then
                        BossDec = 'Spieler : '..GetPlayerName(GetPlayerFromServerId(GetPlayerServerId(closestCock)))
                    else
                        BossDec = '~r~Kein~s~ Spieler in der Nähe'
                    end
                    return
                end
            end, JobMenu)
        end


        if PlayerData.job3.grade_name == 'boss' then
            Items:AddButton('Jobmanagment', 'Verwalte '..PlayerData.job3.label, { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
                if onSelected then
                    closest2Cock = ESX.Game.GetClosestPlayer()
                    if GetPlayerServerId(closestCock) ~= 0 then
                        Boss2Dec = 'Spieler : '..GetPlayerName(GetPlayerFromServerId(GetPlayerServerId(closest2Cock)))
                    else
                        Boss2Dec = '~r~Kein~s~ Spieler in der Nähe'
                    end
                    return
                end
            end, JobMenu2)
        end
        if (PlayerData.job.name == 'Elite' and PlayerData.job.grade >= 8) or (PlayerData.job.name == 'doj' and PlayerData.job.grade >= 9) then
            Items:AddButton('Job Optionen', 'Neue Features', { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
                if onSelected then  --https://cdn.discordapp.com/attachments/989998202818297896/990007952599556146/FIB.png
                    RageUI.UpdateHeader('https://tiziano.cc/finalallstars/v3/rageui/final_personal_rageui.gif', 374, 102)
                    return
                end
            end, JobMiscMenu)
        end
        if IsPedInAnyVehicle(PlayerPedId(), false) then
            Items:AddButton('Fahrzeug Menu', 'Fahrzeug : '..GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)) or '<:', { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
                if onSelected then
                    return
                end
            end, VehicleMenu)
        end
        Items:AddButton('Einstellungen', 'Server Optionen', { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
            if onSelected then
                return
            end
        end, SettingsMenu)
        -- if currWeaHash ~= unarmedHash then
        --     if currWeaName ~= nil then
        --         Items:AddSeparator(currWeaName or '')
        --         Items:AddButton('                        Inspizieren', currWeaHealthDescr, { isDisabled = false }, function(onSelected)
        --             if onSelected then
        --                 inspecting = true
        --                 if IsPistol(currWeaHash) then
        --                     local animLib = 'anim@weapons@pistol@machine_str'
        --                     RequestAnimDict(animLib)
        --                     while not HasAnimDictLoaded(animLib) do
        --                         Wait(10)
        --                     end
        --                     TaskPlayAnim(PlayerPedId(), animLib, 'reload_aim', 8.0, 8.0, 9999999, 1, 0.0, false, false, false)
        --                     Citizen.Wait(1500)
        --                     ClearPedTasks(PlayerPedId())
        --                 else
        --                     local animLib = 'anim@amb@machinery@weapon_test@'
        --                     RequestAnimDict(animLib)
        --                     while not HasAnimDictLoaded(animLib) do
        --                         Wait(10)
        --                     end
        --                     TaskPlayAnim(PlayerPedId(), animLib, 'base_amy_skater_01', 8.0, 8.0, 9999999, 1, 0.0, false, false, false)
        --                     Citizen.Wait(7500)
        --                     ClearPedTasks(PlayerPedId())
        --                 end
        --                 ESX.TriggerServerCallback('cc_core:items:getWeaponHealth', function(res)
        --                     if res then
        --                         local full = 100.0
        --                         local myUsage = full - res
        --                         currWeaHealthDescr = 'Waffe zu: '..myUsage..'% Abgenutzt.'
        --                     end
        --                 end, currWeaHash)
        --                 inspecting = false
        --             end
        --         end)
        --     end
        -- end
    end, function(Panels)
    end)
    PersonalMenu:IsVisible(function(Items)
        while PlayerData.job == nil do
            Wait(10)
        end
        Items:AddSeparator(PlayerData.job.label..' - '..PlayerData.job.grade_label)
        Items:AddSeparator(PlayerData.job3.label..' - '..PlayerData.job3.grade_label)
        Items:AddSeparator('Bankkonto: '..ESX.Math.GroupDigits(Bank..'~s~$'));
        Items:AddSeparator('Visumstufe: ' .. PlayerData.level) --@Leon Visum Shit here
        if Money > 0 then
            Items:AddList('Geld: '..ESX.Math.GroupDigits(Money..'~s~$'), { 'Geben~s~', '~r~Wegwerfen~s~' }, MoneyAction, MoneyDesc, { isDisabled = false }, function(Index, onSelected, onListChange)
                if onListChange then
                    MoneyAction = Index
                end
                if onSelected then
                    if MoneyAction == 1 then
                        local amount, check = CheckQuantity(KeyboardInput('COCK_BOX', 'Wie viel?', '', 12))
                        if amount then
                            local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
                            if closestDistance ~= -1 and closestDistance <= 3 then
                                local closestPed = GetPlayerPed(closestPlayer)
                                if not IsPedSittingInAnyVehicle(closestPed) then
                                    TriggerServerEvent('esx:giveInventoryItem', GetPlayerServerId(closestPlayer), 'item_account', 'money', check)
                                else
                                    RageUI:ErrorSound()
                                    MoneyDesc = 'Du darfst in ~r~keinem~s~ Fahrzeug sein'
                                end
                            else
                                RageUI:ErrorSound()
                                MoneyDesc = '~r~Keine~s~ Person in der Nähe'
                            end
                        else
                            RageUI:ErrorSound()
                            MoneyDesc = '~r~Ungültige~s~ Menge'
                        end
                    end
                    if MoneyAction == 2 then
                        local amount, check = CheckQuantity(KeyboardInput('COCK_BOX', 'Wie viel?', '', 12))
                        if amount then
                            if not IsPedSittingInAnyVehicle(PlayerPedId()) then
                                TriggerServerEvent('esx:removeInventoryItem', 'item_account', 'money', check)
                            else
                                RageUI:ErrorSound()
                                MoneyDesc = 'Du darfst in ~r~keinem~s~ Fahrzeug sein'
                            end
                        else
                            RageUI:ErrorSound()
                            MoneyDesc = '~r~Ungültige~s~ Menge'
                        end
                    end
                end
            end)
        end
        if BlackMoney > 0 then
            Items:AddList('Schwarzgeld: ~r~'..ESX.Math.GroupDigits(BlackMoney..'~s~$'), { 'Geben~s~', '~r~Wegwerfen~s~' }, BlackAction, BlackDesc, { isDisabled = false }, function(Index, onSelected, onListChange)
                if onListChange then
                    BlackAction = Index
                end
                if onSelected then
                    if BlackAction == 1 then
                        local amount, check = CheckQuantity(KeyboardInput('COCK_BOX', 'Wie viel?', '', 12))
                        if amount then
                            local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
                            if closestDistance ~= -1 and closestDistance <= 3 then
                                local closestPed = GetPlayerPed(closestPlayer)
                                if not IsPedSittingInAnyVehicle(closestPed) then
                                    TriggerServerEvent('esx:giveInventoryItem', GetPlayerServerId(closestPlayer), 'item_account', 'black_money', check)
                                else
                                    RageUI:ErrorSound()
                                    BlackDesc = 'Du darfst in ~r~keinem~s~ Fahrzeug sein'
                                end
                            else
                                RageUI:ErrorSound()
                                BlackDesc = '~r~Keine~s~ Person in der Nähe'
                            end
                        else
                            RageUI:ErrorSound()
                            BlackDesc = '~r~Ungültige~s~ Menge'
                        end
                    end
                    if BlackAction == 2 then
                        local amount, check = CheckQuantity(KeyboardInput('COCK_BOX', 'Wie viel?', '', 12))
                        if amount then
                            if not IsPedSittingInAnyVehicle(PlayerPedId()) then
                                TriggerServerEvent('esx:removeInventoryItem', 'item_account', 'black_money', check)
                            else
                                RageUI:ErrorSound()
                                BlackDesc = 'Du darfst in ~r~keinem~s~ Fahrzeug sein'
                            end
                        else
                            RageUI:ErrorSound()
                            BlackDesc = '~r~Ungültige~s~ Menge'
                        end
                    end
                end
            end)
        end
        Items:AddSeparator('Dokumente')
        Items:AddList('Personalausweis', { 'Zeigen~s~', '~s~Anschauen~s~' }, AusweisAction, AusweisDesc, { isDisabled = false }, function(Index, onSelected, onListChange)
            if onListChange then
                AusweisAction = Index
            end
            if onSelected then
                if AusweisAction == 1 then
                    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
                    if closestDistance ~= -1 and closestDistance <= 3.0 then
                        TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(closestPlayer), 'personal')
                    else
                        RageUI:ErrorSound()
                        AusweisDesc = '~r~Keine~s~ Person in der Nähe'
                    end
                elseif AusweisAction == 2 then
                    TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(PlayerId()), 'personal')
                end
            end
        end)
        Items:AddList('Führerschein Anschauen', { 'Auto', 'Bike', 'Helikopter', 'Flugzeug', 'Boot' }, DriveLicenseAction, DriveLicDesc, { isDisabled = false }, function(Index, onSelected, onListChange)
            if onListChange then
                DriveLicenseAction = Index
            end
            if onSelected then
                if DriveLicenseAction == 1 then
                    TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(PlayerId()), 'car')
                elseif DriveLicenseAction == 2 then
                    TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(PlayerId()), 'bike')
                elseif DriveLicenseAction == 3 then
                    TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(PlayerId()), 'helicopter')
                elseif DriveLicenseAction == 4 then
                    TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(PlayerId()), 'airplane')
                elseif DriveLicenseAction == 5 then
                    TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(PlayerId()), 'boat')
                end
            end
        end)
        Items:AddList('Führerschein Zeigen', { 'Auto', 'Bike', 'Helikopter', 'Flugzeug', 'Boot' }, DriveShowLicenseAction, DriveShowLicDesc, { isDisabled = false }, function(Index, onSelected, onListChange)
            if onListChange then
                DriveShowLicenseAction = Index
            end
            if onSelected then
                local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
                if closestDistance ~= -1 and closestDistance <= 3.0 then
                    if DriveShowLicenseAction == 1 then
                        TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(closestPlayer), 'car')
                    elseif DriveShowLicenseAction == 2 then
                        TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(closestPlayer), 'bike')
                    elseif DriveShowLicenseAction == 3 then
                        TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(closestPlayer), 'helicopter')
                    elseif DriveShowLicenseAction == 4 then
                        TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(closestPlayer), 'airplane')
                    elseif DriveShowLicenseAction == 5 then
                        TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(closestPlayer), 'boat')
                    end
                else
                    RageUI:ErrorSound()
                    DriveShowLicDesc = '~r~Keine~s~ Person in der Nähe'
                end
            end
        end)
        Items:AddList('Waffenschein', { 'Zeigen~s~', '~s~Anschauen~s~' }, WaffenLiceenseAction, WaffenLicDesc, { isDisabled = false }, function(Index, onSelected, onListChange)
            if onListChange then
                WaffenLiceenseAction = Index
            end
            if onSelected then
                if WaffenLiceenseAction == 1 then
                    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
                    if closestDistance ~= -1 and closestDistance <= 3.0 then
                        TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(closestPlayer), 'weapon')
                    else
                        RageUI:ErrorSound()
                        WaffenLicDesc = '~r~Keine~s~ Person in der Nähe'
                    end
                elseif WaffenLiceenseAction == 2 then
                    TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(PlayerId()), 'weapon')
                end
            end
        end)

        if ESX.GetPlayerData().job.name == 'ambulance' or ESX.GetPlayerData().job.name == 'police' or ESX.GetPlayerData().job.name == 'doj' or ESX.GetPlayerData().job.name == 'fib' or ESX.GetPlayerData().job.name == 'sheriff' or ESX.GetPlayerData().job.name == 'mechanic' or ESX.GetPlayerData().job.name == 'taxi' or ESX.GetPlayerData().job.name == 'driver' then
            Items:AddList('Arbeitsausweis', { 'Zeigen~s~', '~s~Anschauen~s~' }, WorkLiceenseAction, WorkLicDesc, { isDisabled = false }, function(Index, onSelected, onListChange)
                if onListChange then
                    WorkLiceenseAction = Index
                end
                if onSelected then
                    if WorkLiceenseAction == 1 then
                        local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
                        if closestDistance ~= -1 and closestDistance <= 3.0 then
                            TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(closestPlayer), 'work', false)
                        else
                            RageUI:ErrorSound()
                            WorkLicDesc = '~r~Keine~s~ Person in der Nähe'
                        end
                    elseif WorkLiceenseAction == 2 then
                        TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(PlayerId()), 'work', false)
                    end
                end
            end)
        end

        if ESX.GetPlayerData().job3.name == 'weazel' then
            Items:AddList('Arbeitsausweis', { 'Zeigen~s~', '~s~Anschauen~s~' }, WorkLiceenseAction2, WorkLicDesc2, { isDisabled = false }, function(Index, onSelected, onListChange)
                if onListChange then
                    WorkLiceenseAction2 = Index
                end
                if onSelected then
                    if WorkLiceenseAction2 == 1 then
                        local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
                        if closestDistance ~= -1 and closestDistance <= 3.0 then
                            TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(closestPlayer), 'work', true)
                        else
                            RageUI:ErrorSound()
                            WorkLicDesc2 = '~r~Keine~s~ Person in der Nähe'
                        end
                    elseif WorkLiceenseAction2 == 2 then
                        TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(PlayerId()), 'work', true)
                    end
                end
            end)
        end
    end, function(Panels)
    end)
    BillingMenu:IsVisible(function(Items)
        TriggerEvent("billing_ui:viewInvoices")
        RageUI.Visible(MainMenu, not RageUI.Visible(MainMenu))
    end, function(Panels)
    end)
    JobMenu:IsVisible(function(Items)
        Items:AddList('Boss Aktion', { 'Einstellen', 'Befördern', 'Degradieren', '~r~Entlassen~s~' }, BossAction, BossDec, { isDisabled = false }, function(Index, onSelected, onListChange)
            if onListChange then
                BossAction = Index
            end
            if onSelected then
                local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
                if closestPlayer == -1 or closestDistance > 3.0 then
                    TriggerEvent('cc_core:hud:notify', 'info', 'Information', 'Kein Spieler in der nähe!')
                else
                    if BossAction == 1 then
                        TriggerServerEvent("cc_fraktion:jobEinladen", GetPlayerServerId(closestPlayer), false)
                    elseif BossAction == 2 then
                        TriggerServerEvent("cc_fraktion:jobPromote", GetPlayerServerId(closestPlayer), false)
                    elseif BossAction == 3 then
                        TriggerServerEvent("cc_fraktion:jobDegradieren", GetPlayerServerId(closestPlayer), false)
                    elseif BossAction == 4 then
                        TriggerServerEvent("cc_fraktion:jobFeuern", GetPlayerServerId(closestPlayer), false)
                    end
                end
            end
        end)
    end, function(Panels)
    end)
    JobMenu2:IsVisible(function(Items)
        Items:AddList('Boss Aktion', { 'Einstellen', 'Befördern', 'Degradieren', '~r~Entlassen~s~' }, Boss2Action, Boss2Dec, { isDisabled = false }, function(Index, onSelected, onListChange)
            if onListChange then
                Boss2Action = Index
            end
            if onSelected then
                local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
                if closestPlayer == -1 or closestDistance > 3.0 then
                    TriggerEvent('cc_core:hud:notify', 'info', 'Information', 'Kein Spieler in der nähe!')
                else
                    if Boss2Action == 1 then
                        TriggerServerEvent("cc_fraktion:jobEinladen", GetPlayerServerId(closestPlayer), true)
                    elseif Boss2Action == 2 then
                        TriggerServerEvent("cc_fraktion:jobPromote", GetPlayerServerId(closestPlayer), true)
                    elseif Boss2Action == 3 then
                        TriggerServerEvent("cc_fraktion:jobDegradieren", GetPlayerServerId(closestPlayer), true)
                    elseif Boss2Action == 4 then
                        TriggerServerEvent("cc_fraktion:jobFeuern", GetPlayerServerId(closestPlayer), true)
                    end
                end
            end
        end)
    end, function(Panels)
    end)
    JobMiscMenu:IsVisible(function(Items)
        Items:AddButton('                        FIB NETWORK', '                 Willkommen Director!', { isDisables = false, RightLabel = '→→→' }, function(onSelected)
            if onSelected then
                return
            end
        end, FIBNETWORKMENU)
    end, function(Panels)
    end)
    FIBNETWORKMENU:IsVisible(function(Items)
        Items:AddButton('                 Telefonnummer Orten', nil, { isDisabled = false }, function(onSelected)
            if onSelected then
                local check, number = CheckQuantity(KeyboardInput('COCK_BOX', 'Nummer?', '', 16))
                if check then
                    TriggerServerEvent('cc_menus:searchPhone', number)
                else
                    RageUI:ErrorSound()
                end
            end
        end)
        if (PlayerData.job.name == 'Elite' and PlayerData.job.grade >= 8) or (PlayerData.job.name == 'doj' and PlayerData.job.grade >= 9) then
            Items:AddButton('                 Rechnungen einsehen', nil, { isDisabled = false }, function(onSelected)
                if onSelected then
                    TriggerServerEvent('cc_menus:buildRechnungen')
                end
            end)
            Items:AddButton('                 Kennzeichen Orten', nil, { isDisabled = false }, function(onSelected)
                if onSelected then
                    local plate = KeyboardInput('COCK_BOX', 'Kennzeichen?', '', 8)

                    if plate and plate ~= '' then
                        TriggerServerEvent('cc_menus:searchPlate', plate)
                    else
                        RageUI:ErrorSound()
                    end
                end
            end)
        end
        if PlayerData.job.name == 'doj' then
            Items:AddList('Arbeitsausweis', { 'Zeigen~s~', '~s~Anschauen~s~' }, WorkLiceenseAction, WorkLicDesc, { isDisabled = false }, function(Index, onSelected, onListChange)
                if onListChange then
                    WorkLiceenseAction = Index
                end
                if onSelected then
                    if dienstNumber == 0 then
                        local check, number = CheckQuantity(KeyboardInput('COCK_BOX', 'Nummer?', '', 16))

                        if check then
                            dienstNumber = number
                            SetResourceKvpInt('cc_f2-new_dienstNumber', number)
                        else
                            RageUI:ErrorSound()
                        end
                    end
                    if dienstNumber ~= 0 then
                        if WorkLiceenseAction == 1 then
                            local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()

                            if closestDistance ~= -1 and closestDistance <= 3.0 then
                                TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(closestPlayer), 'work', false, 'fib', dienstNumber)
                            else
                                RageUI:ErrorSound()
                                WorkLicDesc = '~r~Keine~s~ Person in der Nähe'
                            end
                        elseif WorkLiceenseAction == 2 then
                            TriggerServerEvent('cc_utils:openDocument', GetPlayerServerId(PlayerId()), GetPlayerServerId(PlayerId()), 'work', false, 'fib', dienstNumber)
                        end
                    end
                end
            end)
        end
        if PlayerData.job.name == 'doj' and PlayerData.job.grade >= 11 then
            Items:AddButton('                 Telefonnummer Scannen', nil, { isDisabled = true }, function(onSelected)
                if onSelected then
                    local check, number = CheckQuantity(KeyboardInput('COCK_BOX', 'Nummer?', '', 16))
                    if check then
                        TriggerServerEvent('cc_menus:searchPhoneMesssages', number)
                    else
                        RageUI:ErrorSound()
                    end
                end
            end)
        end
        if PlayerData.job.name == 'doj' and PlayerData.job.grade >= 12 then
            Items:AddButton('                 Telefonbuch Check', nil, { isDisabled = true }, function(onSelected)
                if onSelected then
                    local check, number = CheckQuantity(KeyboardInput('COCK_BOX', 'Nummer?', '', 16))
                    if check then
                        TriggerServerEvent('cc_menus:searchOwnerOfNumber', number)
                    else
                        RageUI:ErrorSound()
                    end
                end
            end)
        end
    end, function(Panels)
    end)
    VehicleMenu:IsVisible(function(Items)
        if IsPedInAnyVehicle(PlayerPedId(), false) then
            Items:CheckBox('Motor', EngineDesc, Engine_Checked, { Style = 1, isDisabled = false }, function(onSelected, IsChecked)
                if onSelected then
                    if GetEntitySpeed(GetVehiclePedIsIn(PlayerPedId())) <= 1.0 then
                        Engine_Checked = IsChecked
                        if IsChecked then
                            SetVehicleEngineOn(GetVehiclePedIsIn(PlayerPedId()), true, false, true)
                        else
                            SetVehicleEngineOn(GetVehiclePedIsIn(PlayerPedId()), false, false, true)
                        end
                    else
                        EngineDesc = 'Geht ~r~nur~s~ im Stillstand~s~'
                    end
                end
            end)
            Items:AddList('Tür : ', { 'Fahrertür', 'Beifahrertür', 'Hinten Links', 'Hinten Rechts', 'Kofferraum', 'Motorhaube' }, SelectedDoor, 'Tür öffnen/schließen', { isDisabled = false }, function(Index, onSelected, onListChange)
                if onListChange then
                    SelectedDoor = Index
                end
                if onSelected then
                    if SelectedDoor == 1 then
                        if not VehicleDoors.Driver then
                            VehicleDoors.Driver = true
                            OpenVehicleDoor(0)
                        else
                            VehicleDoors.Driver = false
                            CloseVehicleDoor(0)
                        end
                    elseif SelectedDoor == 2 then
                        if not VehicleDoors.CoDriver then
                            VehicleDoors.CoDriver = true
                            OpenVehicleDoor(1)
                        else
                            VehicleDoors.CoDriver = false
                            CloseVehicleDoor(1)
                        end
                    elseif SelectedDoor == 3 then
                        if not VehicleDoors.BackL then
                            VehicleDoors.BackL = true
                            OpenVehicleDoor(2)
                        else
                            VehicleDoors.BackL = false
                            CloseVehicleDoor(2)
                        end
                    elseif SelectedDoor == 4 then
                        if not VehicleDoors.BackR then
                            VehicleDoors.BackR = true
                            OpenVehicleDoor(3)
                        else
                            VehicleDoors.BackR = false
                            CloseVehicleDoor(3)
                        end
                    elseif SelectedDoor == 5 then
                        if not VehicleDoors.Coffre then
                            VehicleDoors.Coffre = true
                            OpenVehicleDoor(5)
                        else
                            VehicleDoors.Coffre = false
                            CloseVehicleDoor(5)
                        end
                    elseif SelectedDoor == 6 then
                        if not VehicleDoors.Motor then
                            VehicleDoors.Motor = true
                            OpenVehicleDoor(4)
                        else
                            VehicleDoors.Motor = false
                            CloseVehicleDoor(4)
                        end
                    end
                end
            end)
            Items:AddSeparator('Fahrzeug Verwaltung')
            Items:AddButton('Fahrzeugpapiere Übergeben', nil, { isDisabled = false }, function(onSelected)
                if onSelected then
                    giveCarKeys()
                    Wait(500)
                end
            end)
        else
            RageUI.GoBack()
        end
    end, function(Panels)
    end)
    SettingsMenu:IsVisible(function(Items)
        Items:AddButton('Spiel Einstellungen', nil, { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
            if onSelected then
                return
            end
        end, GameSettingsMenu)
        --Items:AddButton('Blip Einstellungen', nil, { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
        --	if onSelected then
        --		return
        --	end
        --end, BlipSettingsMenu)
    end, function(Panels)
    end)
    GameSettingsMenu:IsVisible(function(Items)
        Items:AddButton('Mapfehler Reparieren', 'Rendert die Map neu [Manchmal Buggy]', { isDisabled = false }, function(onSelected)
            if onSelected then
                RefreshInterior(GetInteriorAtCoords(GetEntityCoords(PlayerPedId())))
            end
        end)
        Items:CheckBox('Drift Mode', 'Shift halten in einem Auto', Drift_Checked, { Style = 1, isDisabled = false }, function(onSelected, IsChecked)
            if onSelected then
                Drift_Checked = IsChecked
                if IsChecked then
                    SetResourceKvpInt('cc_f2new-drift', 1)
                else
                    SetResourceKvpInt('cc_f2new-drift', 0)
                end
            end
        end)
        Items:CheckBox('Kino Modus', 'Deaktiviert die HUD', Kino_Checked, { Style = 1, isDisabled = false }, function(onSelected, IsChecked)
            if onSelected then
                Kino_Checked = IsChecked
                if IsChecked then
                    darkness = 0
                    filter = 0
                    DisplayRadar(false)
                    SetResourceKvpInt('cc_f2new-kino', 1)
                else
                    darkness = 0
                    filter = 0
                    DisplayRadar(true)
                    SetResourceKvpInt('cc_f2new-kino', 0)
                end
            end
        end)
    end, function(Panels)
    end)

    BlipSettingsMenu:IsVisible(function(Items)
        for blipIndex, blipData in pairs(blips) do
            Items:AddButton(blipData.label, 'Bearbeite das Blip', { isDisabled = false }, function(onSelected)
                if onSelected then
                    --Loading Blip Values
                    SelectedBlip.Index = blipIndex
                    SelectedBlip.data = blipData
                    if blipData.scale == 0.1 then
                        SelectedBlip.ScaleLevel = 1
                    elseif blipData.scale == 0.2 then
                        SelectedBlip.ScaleLevel = 2
                    elseif blipData.scale == 0.3 then
                        SelectedBlip.ScaleLevel = 3
                    elseif blipData.scale == 0.4 then
                        SelectedBlip.ScaleLevel = 4
                    elseif blipData.scale == 0.5 then
                        SelectedBlip.ScaleLevel = 5
                    elseif blipData.scale == 0.6 then
                        SelectedBlip.ScaleLevel = 6
                    elseif blipData.scale == 0.7 then
                        SelectedBlip.ScaleLevel = 7
                    elseif blipData.scale == 0.8 then
                        SelectedBlip.ScaleLevel = 8
                    elseif blipData.scale == 0.9 then
                        SelectedBlip.ScaleLevel = 9
                    elseif blipData.scale == 1.0 then
                        SelectedBlip.ScaleLevel = 10
                    end
                    if DoesBlipExist(SelectedBlip.data.blipId) then
                        SelectedBlip.Active = true
                    else
                        SelectedBlip.Active = false
                    end
                    ----Loading Blip Values End
                    return
                end
            end, CustomBlipMenu)
        end
    end, function(Panels)
    end)

    CustomBlipMenu:IsVisible(function(Items)
        for blipIndex, blipData in pairs(blips) do
            if SelectedBlip.Index == blipIndex then
                Items:AddList('Blip Größe', { '0.1', '0.2', '0.3', '0.4', '0.5', '0.6', '0.7', '0.8', '0.9', '1.0' }, SelectedBlip.ScaleLevel, 'Größe Bearbeiten -> Enter zum Setzen', { isDisabled = false }, function(Index, onSelected, onListChange)
                    if onListChange then
                        SelectedBlip.ScaleLevel = Index
                    end
                    if onSelected then
                        if SelectedBlip.ScaleLevel == 1 then
                            SelectedBlip.data.scale = 0.1
                        elseif SelectedBlip.ScaleLevel == 2 then
                            SelectedBlip.data.scale = 0.2
                        elseif SelectedBlip.ScaleLevel == 3 then
                            SelectedBlip.data.scale = 0.3
                        elseif SelectedBlip.ScaleLevel == 4 then
                            SelectedBlip.data.scale = 0.4
                        elseif SelectedBlip.ScaleLevel == 5 then
                            SelectedBlip.data.scale = 0.5
                        elseif SelectedBlip.ScaleLevel == 6 then
                            SelectedBlip.data.scale = 0.6
                        elseif SelectedBlip.ScaleLevel == 7 then
                            SelectedBlip.data.scalee = 0.7
                        elseif SelectedBlip.ScaleLevel == 8 then
                            SelectedBlip.data.scale = 0.8
                        elseif SelectedBlip.ScaleLevel == 9 then
                            SelectedBlip.data.scale = 0.9
                        elseif SelectedBlip.ScaleLevel == 10 then
                            SelectedBlip.data.scale = 1.0
                        end
                        SetBlipScale(SelectedBlip.data.blipId, SelectedBlip.data.scale)
                    end
                end)
                Items:CheckBox('Blip Anzeigen', nil, SelectedBlip.Active, { Style = 1, isDisabled = false }, function(onSelected, IsChecked)
                    if onSelected then
                        SelectedBlip.Active = IsChecked
                        if IsChecked then
                            local CreatedBlip = AddBlipForCoord(SelectedBlip.data.coord)
                            SetBlipScale(CreatedBlip, SelectedBlip.data.scale)
                            SetBlipSprite(CreatedBlip, SelectedBlip.data.sprite)
                            SetBlipAsShortRange(CreatedBlip, true)
                            BeginTextCommandSetBlipName('STRING')
                            AddTextComponentString(SelectedBlip.data.label)
                            EndTextCommandSetBlipName(CreatedBlip)
                            SelectedBlip.data.blipId = CreatedBlip
                        else
                            RemoveBlip(SelectedBlip.data.blipId)
                        end
                    end
                end)
                Items:AddButton('Blip Einstellung Speichern', SelectedBlip.data.label..' Größe : '..SelectedBlip.data.scale, { isDisabled = false }, function(onSelected)
                    if onSelected then
                        local SelectedBlipActive = 0
                        local SelectedBlipScale = SelectedBlip.data.scale
                        --BlipKVPSaving
                        for i = 0, 32 do
                            DeleteResourceKvp('cc:Blips:Size:'..SelectedBlip.Index)
                            DeleteResourceKvp('cc:Blips:Active:'..SelectedBlip.Index)
                        end
                        SetResourceKvpFloat('cc:Blips:Size:'..SelectedBlip.Index, SelectedBlipScale)
                        if SelectedBlip.Active then
                            SelectedBlipActive = 0
                        else
                            SelectedBlipActive = 1
                        end
                        SetResourceKvpInt('cc:Blips:Active:'..SelectedBlip.Index, SelectedBlipActive)
                        --BlipKVPSaving End
                    end
                end)
            end
        end
    end, function(Panels)
    end)

    FIBMessagesMenu:IsVisible(function(Items)
        Items:AddSeparator('Datenbank Suche: '..currNumber)

        Items:AddButton('Versendete Nachrichten', 'Siehe alle Nachrichten ein die von dieser Nummer aus versendet wurden', {IsDisabled=false,RightLabel='→→→'}, function(onSelected)
            if onSelected then
                currFilter = 'outMsg'
            end
        end)
        Items:AddButton('Versendete Fotos', 'Siehe alle Fotos ein die von dieser Nummer aus versendet wurden', {IsDisabled=false,RightLabel='→→→'}, function(onSelected)
            if onSelected then
                currFilter = 'outImg'
            end
        end)
        Items:AddButton('Erhaltene Nachrichten', 'Siehe alle Erhaltenen Nachrichten von dieser Nummer ein', {IsDisabled=false,RightLabel='→→→'}, function(onSelected)
            if onSelected then
                currFilter = 'inMsg'
            end
        end)
        Items:AddButton('Erhaltene Fotos', 'Siehe alle Erhaltenen Fotos von dieser Nummer ein', {IsDisabled=false,RightLabel='→→→'}, function(onSelected)
            if onSelected then
                currFilter = 'inImg'
            end
        end)
        Items:AddButton('~r~Suchergebniss Leeren~s~', 'Tut die Datenbank Daten Spalte leeren. Hierbei werden ~r~keine~s~ Nachrichten gelöscht!', {IsDisabled=false,RightLabel='→→→'}, function(onSelected)
            if onSelected then
                currFilter = ''
            end
        end)

        Items:AddSeparator('Datenbank Daten')

        if allPhoneData then
            for _, data in pairs(allPhoneData) do
                local whatStr = ''
                local infoStr = ''
                if data['mType'] == 'SI' then
                    whatStr = 'Foto Erhalten'
                    infoStr = 'Sender: '..data['target']..'\nEmpfänger: '..data['sender']..'\n~r~ESCAPE zum Schließen~s~'
                elseif data['mType'] == 'RI' then
                    whatStr = 'Foto Gesendet'
                    infoStr = 'Sender: '..data['sender']..'\nEmpfänger: '..data['target']..'\n~r~ESCAPE zum Schließen~s~'
                elseif data['mType'] == 'ST' then
                    whatStr = 'Nachricht Erhalten'
                    infoStr = 'Sender: '..data['target']..'\nEmpfänger: '..data['sender']
                elseif data['mType'] == 'RT' then
                    whatStr = 'Nachricht Gesendet'
                    infoStr = 'Sender: '..data['sender']..'\nEmpfänger: '..data['target']
                end
                whatStr = whatStr..' ['..data['time']..']'

                if currFilter == 'outImg' then
                    if data['mType'] == 'RI' then
                        Items:AddButton(whatStr, infoStr, {IsDisabled=false}, function(onSelected)
                            if onSelected then
                                SendNUIMessage({
                                    link = data['message']
                                })
                                SetNuiFocus(true, true)
                            end
                        end)
                    end
                elseif currFilter == 'inImg' then
                    if data['mType'] == 'SI' then
                        Items:AddButton(whatStr, infoStr, {IsDisabled=false}, function(onSelected)
                            if onSelected then
                                SendNUIMessage({
                                    link = data['message']
                                })
                                SetNuiFocus(true, true)
                            end
                        end)
                    end
                elseif currFilter == 'outMsg' then
                    if data['mType'] == 'RT' then
                        Items:AddButton(whatStr, infoStr, {IsDisabled=false}, function(onSelected)
                            if onSelected then
                                KeyboardInput('_', 'Nachricht:', data['message'], 9999)
                            end
                        end)
                    end
                elseif currFilter == 'inMsg' then
                    if data['mType'] == 'ST' then
                        Items:AddButton(whatStr, infoStr, {IsDisabled=false}, function(onSelected)
                            if onSelected then
                                KeyboardInput('_', 'Nachricht:', data['message'], 9999)
                            end
                        end)
                    end
                end
            end
        end
    end, function(Panels)
    end)

    FIBOwnerMenu:IsVisible(function(Items)
        Items:AddSeparator('Datenbank Daten')

        if allOwnerData then
            Items:AddButton('Vorname: ' .. allOwnerData.firstname, nil, {IsDisabled=false}, function(onSelected)
                if onSelected then
                end
            end)
            Items:AddButton('Nachname: ' .. allOwnerData.lastname, nil,{IsDisabled=false}, function(onSelected)
                if onSelected then
                end
            end)

            Items:AddButton('Geburtsdatum: ' .. allOwnerData.dateofbirth, nil,{IsDisabled=false}, function(onSelected)
                if onSelected then
                end
            end)

            Items:AddButton('Größe: ' .. allOwnerData.height, nil,{IsDisabled=false}, function(onSelected)
                if onSelected then
                end
            end)

            Items:AddButton('Geschlecht: ' .. allOwnerData.sex, nil, {IsDisabled=false}, function(onSelected)
                if onSelected then
                end
            end)
        end
    end, function(Panels)
    end)

    FIBBillingsMenu:IsVisible(function(Items)
        Items:AddSeparator('Datenbank Daten')

        if allBillingData then
            for _, data in pairs(allBillingData) do
                Items:AddButton(data.label .. ' - ' .. data.amount .. '~r~$', 'Rechnung besitzer: ' .. data.name, {IsDisabled=false,RightLabel='~r~' .. data.time}, function(onSelected)
                    if onSelected then
                        TriggerServerEvent('cc_menus:deleteBilling', data.id, data.identifier)
                    end
                end)
            end
        end
    end, function(Panels)
    end)
end

RegisterNetEvent('cc_menus:refresh_bills')
AddEventHandler('cc_menus:refresh_bills', function(bills)
    TotalBills = bills
end)

--Functions
function giveCarKeys()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    if IsPedInAnyVehicle(playerPed, false) then
        vehicle = GetVehiclePedIsIn(playerPed, false)			
    else
        vehicle = GetClosestVehicle(coords.x, coords.y, coords.z, 7.0, 0, 70)
    end
    local plate = GetVehicleNumberPlateText(vehicle)
    local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)
    ESX.TriggerServerCallback('esx_givecarkeys:requestPlayerCars', function(isOwnedVehicle)
        if isOwnedVehicle then
            local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
            if closestPlayer == -1 or closestDistance > 3.0 then
                TriggerEvent('cc_core:hud:notify', 'info', 'Information', 'Kein Spieler in der nähe!')
            else
                TriggerServerEvent('esx_givecarkeys:setVehicleOwnedPlayerId', GetPlayerServerId(closestPlayer), vehicleProps, GetDisplayNameFromVehicleModel(vehicleProps.model))
            end
        end
    end, GetVehicleNumberPlateText(vehicle))
end

function Round(num, numDecimalPlaces)
    local mult = 10^(numDecimalPlaces or 0)
    return math.floor(num * mult + 0.5) / mult
end

function HoldingCTRL(igp, cnt)
    local c_s = IsDisabledControlPressed(igp, cnt)
    if c_s then
        return true
    else
        return false
    end
end

function KeyboardInput(entryTitle, textEntry, inputText, maxLength)
    AddTextEntry(entryTitle, textEntry)
    DisplayOnscreenKeyboard(1, entryTitle, '', inputText, '', '', '', maxLength)
    blockinput = true
    while UpdateOnscreenKeyboard() ~= 1 and UpdateOnscreenKeyboard() ~= 2 do
        Citizen.Wait(0)
    end
    if UpdateOnscreenKeyboard() ~= 2 then
        local result = GetOnscreenKeyboardResult()
        Citizen.Wait(500)
        blockinput = false
        return result
    else
        Citizen.Wait(500)
        blockinput = false
        return nil
    end
end
function CheckQuantity(number)
    number = tonumber(number)
    if type(number) == 'number' then
        number = ESX.Math.Round(number)
        if number > 0 then
            return true, number
        end
    end
    return false, number
end
function OpenVehicleDoor(doorIndex)
    SetVehicleDoorOpen(GetVehiclePedIsIn(PlayerPedId(), false), doorIndex, false, false)
end
function CloseVehicleDoor(doorIndex)
    SetVehicleDoorShut(GetVehiclePedIsIn(PlayerPedId(), false), doorIndex, false)
end
--Functions End

--Keymappings
RegisterCommand('PersonalMenu31', function()
    if GetEntityHealth(PlayerPedId()) > 0 and ESXLoaded and not exports['cc_core']:isInZone() then
        currWeaHealthDescr = 'Warte auf ~r~Inspizierung~s~!'
        RageUI.UpdateHeader('https://tiziano.cc/finalallstars/v3/rageui/final_personal_rageui.gif', 374, 102)
        PlayerData = ESX.GetPlayerData()
        RageUI.Visible(MainMenu, true)
    end
end)

RegisterKeyMapping('PersonalMenu31','Personal Menu','keyboard','F3')
--Keymappings End

--Threads
Citizen.CreateThread(function() --Per Frame Shit
    --Pasted Crosshair Pastery Shit
    local crosshairParameters =
    {	
        ["width"] =
        {
            ["label"] = "Width",
            ["allValues"] = {0.002, 0.0025, 0.003, 0.0035, 0.004, 0.0045, 0.005, 0.0055, 0.006, 0.0065, 0.007, 0.0075, 0.008, 0.0085, 0.009, 0.0095, 0.010,
            0.0105, 0.011, 0.0115, 0.012, 0.0125, 0.013, 0.0135, 0.014, 0.0145, 0.015, 0.0155, 0.016, 0.0165, 0.017, 0.0175, 0.018, 0.0185, 0.019, 0.0195, 0.02},
            ["currentValue"] = 3,
        },
        ["gap"] =
        {
            ["label"] = "Gap",
            ["allValues"] = {0.0, 0.0005, 0.001, 0.0015, 0.002, 0.0025, 0.003, 0.0035, 0.004, 0.0045, 0.005, 0.0055, 0.006, 0.0065, 0.007, 0.0075, 0.008, 0.0085, 0.009, 0.0095, 0.01},
            ["currentValue"] = 3,
        },
        ["dot"] =
        {
            ["label"] = "Dot",
            ["allValues"] = {false, true},
            ["currentValue"] = 2,
        },
        ["thickness"] =
        {
            ["label"] = "Thickness",
            ["allValues"] = {0.002, 0.004, 0.006, 0.008, 0.01, 0.012, 0.014, 0.016, 0.018, 0.02},
            ["currentValue"] = 1,
        }
    }
    local allDefaultValues =
        {
            {param = "thickness", value = 1},
            {param = "width", value = 3},
            {param = "gap", value = 3},
            {param = "dot", value = 2},
            {param = "gtacross", value = 2},
            {param = "color", value = 1},
            {param = "opacity", value = 10},
        }
    local parameters = {"width", "gap", "dot", "thickness", "gtacross", "color", "opacity"}
    local currentParamIndex = 1
    local isEditing = false
    local customCrosshairState = true
    --Pasted Crosshair Shitty Pastery End

    local playerCoords = GetEntityCoords(PlayerPedId())
    local el_x, el_y el_z = table.unpack(playerCoords)
    while true do
        Citizen.Wait(0)
        if Drift_Checked then
            if IsPedInAnyVehicle(PlayerPedId(), false) then
                if HoldingCTRL(1, 21) then
                    if GetEntitySpeed(GetVehiclePedIsIn(PlayerPedId(), false)) <= 25.0 then --CLS 09.06.2022 Anti Spamm Shitty
                        SetVehicleReduceGrip(GetVehiclePedIsIn(PlayerPedId(), false), true)
                    end
                else
                    SetVehicleReduceGrip(GetVehiclePedIsIn(PlayerPedId(), false), false)
                end
            end
        end

        if Kino_Checked then
            darkness = darkness +1
            filter = filter +1
            if darkness >= 255 then
                darkness = 255
            end
            if filter >= 50 then
                filter = 50
            end
            DrawRect(1, 0.05, 3.0, 0.19, 0, 0, 0, darkness)
            DrawRect(1, 0.95, 3.0, 0.19, 0, 0, 0, darkness)
            --Make Screen lil darker [looks better]
            DrawRect(0, 0, 69.0, 69.0, 0, 0, 0, filter)
            if darkness >= 255 then
                ThefeedHideThisFrame() --Disable bottom Left Notifys
                --TriggerToHudDisable
            end
        end
        if not Drift_Checked and not Kino_Checked then
            Citizen.Wait(1250)
        end
    end
end)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(100)
        if MoneyDesc ~= nil then
            Citizen.Wait(2500)
            MoneyDesc = nil
        end
        if BlackDesc ~= nil then
            Citizen.Wait(2500)
            BlackDesc = nil
        end
        if AusweisDesc ~= nil then
            Citizen.Wait(2500)
            AusweisDesc = nil
        end
        if DriveLicDesc ~= nil then
            Citizen.Wait(2500)
            DriveLicDesc = nil
        end
        if DriveShowLicDesc ~= nil then
            Citizen.Wait(2500)
            DriveShowLicDesc = nil
        end
        if WaffenLicDesc ~= nil then
            Citizen.Wait(2500)
            WaffenLicDesc = nil
        end
        if WorkLicDesc ~= nil then
            Citizen.Wait(2500)
            WorkLicDesc = nil
        end
        if WorkLicDesc2 ~= nil then
            Citizen.Wait(2500)
            WorkLicDesc2 = nil
        end
        if SelectedItemDesc ~= nil then
            Citizen.Wait(2500)
            SelectedItemDesc = nil
        end
        if SelectedWeaponDesc ~= nil then
            Citizen.Wait(2500)
            SelectedWeaponDesc = nil
        end
        if EngineDesc ~= nil then
            Citizen.Wait(2500)
            EngineDesc = nil
        end
    end
end)

RegisterNUICallback('exit', function()
    SetNuiFocus(false, false)
end)

RegisterNetEvent('cc_menus:gps', function(posX, posY)
    if posX ~= nil and posY ~= nil then
        local xMath = math.random(-125, 125)
        local yMath = math.random(-125, 125)
        SetNewWaypoint(posX+xMath, posY+yMath)
        ESX.ShowHelpNotification('Position Markiert!')
    end
end)

RegisterNetEvent('cc_menus:buildMessages', function(data, num)
    local status, error = pcall(function()
        if data ~= nil then
            currNumber = num
            currFilter = ''
            allPhoneData = data
            RageUI.UpdateHeader('https://tiziano.cc/finalallstars/v3/rageui/final_personal_rageui.gif', 374, 102)
            RageUI.Visible(FIBMessagesMenu, true)
            menuOpen = true
        end
    end)
    if not status then
        print('ERROR: '..error)
    end
end)

RegisterNetEvent('cc_menus:searchOwnerOfNumber', function(data)
    local status, error = pcall(function()
        if data ~= nil then
            allOwnerData = data
            print(ESX.DumpTable(allOwnerData))
            RageUI.UpdateHeader('https://tiziano.cc/finalallstars/v3/rageui/final_personal_rageui.gif', 374, 102)
            RageUI.Visible(FIBOwnerMenu, true)
            menuOpen = true
        end
    end)
    if not status then
        print('ERROR: '..error)
    end
end)

RegisterNetEvent('cc_menus:buildRechnungen', function(data)
    local status, error = pcall(function()
        if data ~= nil then
            allBillingData = data
            RageUI.UpdateHeader('https://tiziano.cc/finalallstars/v3/rageui/final_personal_rageui.gif', 374, 102)
            RageUI.Visible(FIBBillingsMenu, true)
            menuOpen = true
        end
    end)
    if not status then
        print('ERROR: '..error)
    end
end)

RegisterNetEvent('cc_menus:buildPlate', function(plate, coords)
    local status, error = pcall(function()
        SetNewWaypoint(coords.x, coords.y)
    end)
    if not status then
        print('ERROR: '..error)
    end
end)
]]
RegisterNetEvent('cc_menus:searchPhone', function(number) --Check
    if number ~= nil then
        if ESX.GetPlayerJob(source).name == 'doj' or ESX.GetPlayerJob(source).name == 'Elite' then
            print('Used Track Number: ' .. number .. ' From: ' .. GetPlayerName(source))
            local _source = source
            local ownerID = ''
            local ownerSource = nil
            local ownerCoords = vector3(0.0, 0.0, 0.0)
            MySQL.Async.fetchAll('SELECT * FROM phone_phones WHERE phone_number = @phone_number',
                { ['phone_number'] = tonumber(number) }, function(result)
                if result then
                    print('result')

                    ownerID = result[1].id

                    ESX.DumpTable(result)

                    if ownerID ~= '' then
                        TriggerClientEvent('esx:showNotification', _source, 'Nummer ist ~g~Regestriert~s~!')
                    else
                        TriggerClientEvent('esx:showNotification', _source,
                            'Die Nummer ist nicht im Netzwerk Regestriert!')
                    end

                    local Players = exports['cc_core']:GetPlayersFix()
                    for _, player in pairs(Players) do
                        if player.identifier == ownerID then
                            ownerSource = player.playerId
                            if ESX.GetPlayerInventoryItem(ownerSource, 'phone').count >= 1 then
                                ownerCoords = GetEntityCoords(GetPlayerPed(ownerSource))
                                TriggerClientEvent('cc_menus:gps', _source, ownerCoords.x, ownerCoords.y)
                            end
                        end
                    end
                end
            end)
        end
    end
end)

RegisterNetEvent('cc_menus:searchPhoneMesssages', function(number) --@Leon Flogmodus Check!
    local playerId = source
    local allMessages = {}

    if number then
        local myJob = ESX.GetPlayerJob(playerId)
        if (myJob.name == 'doj' and myJob.grade >= 11) or (myJob.name == 'Elite' and myJob.grade >= 8) then
            print('Used Search Messages: ' .. number .. ' From: ' .. GetPlayerName(playerId))
            MySQL.Async.fetchAll('SELECT * FROM phone_phones WHERE `phone_number` = @phone_number', {
                ['@phone_number'] = number
            }, function(result)
                for _, data in pairs(result) do
                    local _date = os.date('%d.%m.%Y %H:%M:%S', data['date'])

                    if _date then
                        allMessages[#allMessages + 1] = {
                            sender = data['index'],
                            target = data['identifier'],
                            mType = data['type'],
                            message = data['message'],
                            time = _date,
                        }
                    end
                end

                if #allMessages > 0 then
                    exports['cc_core']:log(playerId, 'Search Phone Log',
                        GetPlayerName(playerId) .. ' request Number: ' .. number,
                        'https://canary.discord.com/api/webhooks/1378340984500391987/glytV7F9qz49K_0mfDh8Cd57dEj8aGAcXKu58pXLmaCMEqS4gtvKR8RviSXrYxdt02TD',
                        true)
                    TriggerClientEvent('cc_menus:buildMessages', playerId, allMessages, number)
                else
                    TriggerClientEvent('esx:showNotification', playerId, '~r~Keine Daten gefunden~s~!')
                end
            end)
        end
    end
end)

RegisterNetEvent('cc_menus:searchOwnerOfNumber', function(number)
    local playerId = source

    if number then
        local myJob = ESX.GetPlayerJob(playerId)

        if (myJob.name == 'doj' and myJob.grade >= 12) or (myJob.name == 'Elite' and myJob.grade >= 8) then
            MySQL.Async.fetchAll('SELECT * FROM phone_phones WHERE `phone_number` = @number', {
                ['@number'] = number
            }, function(result)
                if #result == 1 then
                    MySQL.Async.fetchAll(
                    'SELECT firstname, lastname, dateofbirth, height, sex FROM users WHERE `identifier` = @identifier', {
                        ['@identifier'] = result[1].identifier
                    }, function(result)
                        if #result == 1 then
                            TriggerClientEvent('cc_menus:searchOwnerOfNumber', playerId, {
                                firstname = result[1].firstname,
                                lastname = result[1].lastname,
                                height = result[1].height,
                                dateofbirth = result[1].dateofbirth,
                                sex = result[1].sex
                            })
                        else
                            Notify(playerId, 'Information', 'Fehler beim suchen!', 'error')
                        end
                    end)
                else
                    Notify(playerId, 'Information', 'Nummer existiert nicht!', 'error')
                end
            end)
        end
    end
end)

RegisterNetEvent('cc_menus:searchPlate', function(plate)
    local playerId = source
    local myJob = ESX.GetPlayerJob(playerId)

    if (myJob.name == 'doj' and myJob.grade >= 9) or (myJob.name == 'Elite' and myJob.grade >= 8) then
        local found = false
        local coords = vector3(0, 0, 0)

        for k, v in pairs(GetAllVehicles()) do
            local vPlate = GetVehicleNumberPlateText(v)

            if string.match(string.lower(vPlate), string.lower(plate)) then
                coords = GetEntityCoords(v)
                found = true
                break
            end
        end

        if found then
            TriggerClientEvent('cc_menus:buildPlate', playerId, plate, coords)
            Notify(playerId, 'Information', 'Kennzeichen gefunden, position auf der Karte markiert!', 'success')
        else
            Notify(playerId, 'Information', 'Dieses Kennzeichen wurde nicht auf dem Radar gefunden!', 'info')
        end
    end
end)

RegisterNetEvent('cc_menus:buildRechnungen', function()
    local playerId = source
    local allBillings = {}
    local myJob = ESX.GetPlayerJob(playerId)

    if (myJob.name == 'Elite' and myJob.grade >= 8) or (myJob.name == 'doj' and myJob.grade >= 9) then
        MySQL.Async.fetchAll('SELECT * FROM billing', {
        }, function(result)
            if #result ~= 0 then
                for k, v in pairs(result) do
                    local xPlayer = ESX.GetPlayerFromIdentifier(v.identifier)

                    if xPlayer then
                        local day, month, year, hour, minute = 1, 1, 1900, 0, 0

                        if v.time ~= 0 then
                            day, month, year, hour, minute = tonumber(os.date('%d', v.time)),
                                tonumber(os.date('%m', v.time)), tonumber(os.date('%Y', v.time)),
                                tonumber(os.date('%H', v.time)), tonumber(os.date('%M', v.time))
                        end

                        date = day .. '.' .. month .. '.' .. year .. ' ' .. hour .. ':' .. minute

                        allBillings[#allBillings + 1] = {
                            id = v.id,
                            name = xPlayer.rpName,
                            identifier = v.identifier,
                            sender = v.sender,
                            targetType = v.target_type,
                            target = v.target,
                            label = v.label,
                            amount = v.amount,
                            time = date
                        }
                    end
                end
            end

            TriggerClientEvent('cc_menus:buildRechnungen', playerId, allBillings)
        end)
    end
end)

RegisterNetEvent('cc_menus:deleteBilling', function(billingId, targetIdentifier)
    local playerId = source
    local allBillings = {}
    local myJob = ESX.GetPlayerJob(playerId)

    if (myJob.name == 'Elite' and myJob.grade >= 8) or (myJob.name == 'doj' and myJob.grade >= 11) then
        MySQL.Async.execute('DELETE FROM billing WHERE id = ?', {
            billingId
        }, function()
            Notify(playerId, 'Information', 'Rechnung Erfolgreich gelöscht!', 'success')

            local xTarget = ESX.GetPlayerFromIdentifier(targetIdentifier)

            if xTarget then
                TriggerClientEvent('cc_core_billing:removeBill', xTarget.source, billingId)
            end

            MySQL.Async.fetchAll('SELECT * FROM billing WHERE identifier = @identifier', {
                ['@identifier'] = ESX.GetPlayerIdentifier(target)
            }, function(result)
                if #result ~= 0 then
                    for k, v in pairs(result) do
                        if v.amount >= 10000 then
                            allBillings[#allBillings + 1] = {
                                id = v.id,
                                identifier = v.identifier,
                                sender = v.sender,
                                targetType = v.target_type,
                                target = v.target,
                                label = v.label,
                                amount = v.amount
                            }
                        end
                    end
                end

                TriggerClientEvent('cc_menus:buildRechnungen', playerId, allBillings)
            end)
        end)
    end
end)

local NailShit = GetHashKey('p_ld_stinger_s')

RegisterCommand('deletespikes', function(source)
    if source == 0 or ESX.GetPlayerGroup(source) == 'superadmin' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'projektleitung' then
        for _, prop in pairs(GetAllObjects()) do
            if GetEntityModel(prop) == NailShit then
                DeleteEntity(prop)
                print('Deleted Spike: ' .. prop)
            end
        end
    end
end)

-- wenn was nicht geht schreibt mir @hilfsbereitschaft

local jobCooldown = {}

RegisterNetEvent('cc_fraktion:jobEinladen', function(schlampenId, job2)
    local src = source
    local xHurensohn = ESX.GetPlayerFromId(src)
    local xSchlampe = ESX.GetPlayerFromId(schlampenId)
    local isJob2 = job2

    if jobCooldown[src] then
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Job-System',
            'Du kannst keine Leute einladen du hast noch einen Cooldown.')
        return
    end

    if xSchlampe.getJob().name ~= "unemployed" then
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Job-System', xSchlampe.getRPName() .. ' ist kein Zivilist.')
        return
    end

    if xHurensohn.getJob().name == xSchlampe.getJob().name then
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Job-System', 'Dieser Spieler hat bereits den Job.')
        return
    end

    if not isJob2 then
        jobCooldown[src] = true
        xSchlampe.setJob(xHurensohn.getJob().name, 1)
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Frakinvite',
            'Du hast ' .. xSchlampe.getRPName() .. ' in ' .. xHurensohn.getJob().label .. ' eingeladen!')
        TriggerClientEvent('cc_core:hud:notify', xSchlampe.source, 'info', 'Frakinvite',
            'Du wurdest zu : ' .. xHurensohn.getJob().label .. ' eingeladen!')
        ESX.SetTimeout(2500, function()
            jobCooldown[src] = nil
        end)
    end

    if isJob2 then
        jobCooldown[src] = true
        xSchlampe.setJob3(xHurensohn.getJob2().name, 1)
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Frakinvite', 'Du hast ' .. xSchlampe.getRPName() .. ' in ' .. xHurensohn.getJob2().label .. ' eingeladen!')
        TriggerClientEvent('cc_core:hud:notify', xSchlampe.source, 'info', 'Frakinvite', 'Du wurdest zu : ' .. xHurensohn.getJob2().label .. ' eingeladen!')
        ESX.SetTimeout(2500, function()
            jobCooldown[src] = nil
        end)
    end
end)

RegisterNetEvent('cc_fraktion:jobFeuern', function(schlampenId, job2)
    local src = source
    local xHurensohn = ESX.GetPlayerFromId(src)
    local xSchlampe = ESX.GetPlayerFromId(schlampenId)
    local isJob2 = job2

    if jobCooldown[src] then
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Job-System',
            'Du kannst keine Leute feuern du hast noch einen Cooldown.')
        return
    end

    if xHurensohn.getJob().name ~= xSchlampe.getJob().name then
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Job-System', xSchlampe.getRPName() .. ' hat nicht den selben Job.')
        return
    end

    if not isJob2 then
        jobCooldown[src] = true
        xSchlampe.setJob("unemployed", 0)
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Frakinvite',
            'Du hast ' .. xSchlampe.getRPName() .. ' aus ' .. xHurensohn.getJob().label .. ' gekickt!')
        TriggerClientEvent('cc_core:hud:notify', xSchlampe.source, 'info', 'Frakinvite',
            'Du wurdest aus ' .. xHurensohn.getJob().label .. ' gekickt!')
        ESX.SetTimeout(2500, function()
            jobCooldown[src] = nil
        end)
    end

    if isJob2 then
        jobCooldown[src] = true
        xSchlampe.setJob3("unemployed", 0)
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Frakinvite', 'Du hast ' .. xSchlampe.getRPName() .. ' aus ' .. xHurensohn.getJob2().label .. ' gekickt!')
        TriggerClientEvent('cc_core:hud:notify', xSchlampe.source, 'info', 'Frakinvite', 'Du wurdest aus ' .. xHurensohn.getJob2().label .. ' gekickt!')
        ESX.SetTimeout(2500, function()
            jobCooldown[src] = nil
        end)
    end
end)

RegisterNetEvent('cc_fraktion:jobDegradieren', function(schlampenId, job2)
    local src = source
    local xHurensohn = ESX.GetPlayerFromId(src)
    local xSchlampe = ESX.GetPlayerFromId(schlampenId)
    local isJob2 = job2

    if jobCooldown[src] then
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Job-System',
            'Du kannst keine Leute deranken du hast noch einen Cooldown.')
        return
    end

    if xHurensohn.getJob().name ~= xSchlampe.getJob().name then
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Job-System', xSchlampe.getRPName() .. ' hat nicht den selben Job.')
        return
    end

    if xSchlampe.getJob().grade == 1 then
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Job-System', xSchlampe.getRPName() .. ' ist Rang 1 du kannst ihn nicht weiter downranken.')
        return
    end

    if not isJob2 then
        jobCooldown[src] = true
        local newGrade = xSchlampe.getJob().grade - 1
        xSchlampe.setJob(xSchlampe.getJob().name, newGrade)
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Frakinvite',
            'Du hast ' .. xSchlampe.getRPName() .. ' auf Rang ' .. newGrade .. ' gedownranked!')
        TriggerClientEvent('cc_core:hud:notify', xSchlampe.source, 'info', 'Frakinvite',
            'Du wurdest auf Rang ' .. newGrade .. ' gedownranked!')
        ESX.SetTimeout(2500, function()
            jobCooldown[src] = nil
        end)
    end

    if isJob2 then
        jobCooldown[src] = true
        local newGrade2 = xSchlampe.getJob2().grade - 1
        xSchlampe.setJob3(xSchlampe.getJob().name, newGrade2)
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Frakinvite', 'Du hast ' .. xSchlampe.getRPName() .. ' auf Rang ' .. newGrade2 .. ' gedownranked!')
        TriggerClientEvent('cc_core:hud:notify', xSchlampe.source, 'info', 'Frakinvite', 'Du wurdest auf Rang ' .. newGrade2 .. ' gedownranked!')
        ESX.SetTimeout(2500, function()
            jobCooldown[src] = nil
        end)
    end
end)

RegisterNetEvent('cc_fraktion:jobPromote', function(schlampenId, job2)
    local src = source
    local xHurensohn = ESX.GetPlayerFromId(src)
    local xSchlampe = ESX.GetPlayerFromId(schlampenId)
    local isJob2 = job2

    if jobCooldown[src] then
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Job-System',
            'Du kannst keine Leute deranken du hast noch einen Cooldown.')
        return
    end

    if xHurensohn.getJob().name ~= xSchlampe.getJob().name then
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Job-System', xSchlampe.getRPName() .. ' hat nicht den selben Job.')
        return
    end

    if xSchlampe.getJob().grade == 12 then
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Job-System', xSchlampe.getRPName() .. ' ist Rang 12 du kannst ihn nicht weiter upranken.')
        return
    end

    if not isJob2 then
        jobCooldown[src] = true
        local newGrade = xSchlampe.getJob().grade + 1
        xSchlampe.setJob(xSchlampe.getJob().name, newGrade)
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Frakinvite',
            'Du hast ' .. xSchlampe.getRPName() .. ' auf Rang ' .. newGrade .. ' geupranked!')
        TriggerClientEvent('cc_core:hud:notify', xSchlampe.source, 'info', 'Frakinvite',
            'Du wurdest auf Rang ' .. newGrade .. ' geupranked!')
        ESX.SetTimeout(2500, function()
            jobCooldown[src] = nil
        end)
    end

    if isJob2 then
        jobCooldown[src] = true
        local newGrade2 = xSchlampe.getJob3().grade + 1
        xSchlampe.setJob3(xSchlampe.getJob().name, newGrade2)
        TriggerClientEvent('cc_core:hud:notify', src, 'info', 'Frakinvite', 'Du hast ' .. xSchlampe.getRPName() .. ' auf Rang ' .. newGrade2 .. ' geupranked!')
        TriggerClientEvent('cc_core:hud:notify', xSchlampe.source, 'info', 'Frakinvite', 'Du wurdest auf Rang ' .. newGrade2 .. ' geupranked!')
        ESX.SetTimeout(2500, function()
            jobCooldown[src] = nil
        end)
    end
end)