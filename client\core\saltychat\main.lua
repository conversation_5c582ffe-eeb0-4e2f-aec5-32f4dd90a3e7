local isMuted = true

AddEventHandler('SaltyChat_PluginStateChanged', function(state)
    if state == 0 then
        SendNUIMessage({
            script = 'salty',
            action = 'saltynui',
            state = true
        })
    
        isMuted = true
    elseif state == 1 then
        SendNUIMessage({
            script = 'salty',
            action = 'saltynui',
            state = false
        })
    
        isMuted = false
    elseif state == 2 then
        SendNUIMessage({
            script = 'salty',
            action = 'saltynui',
            state = false
        })
    
        isMuted = false
    end
end)

AddEventHandler('SaltyChat_SoundStateChanged', function(state)
    SendNUIMessage({
        script = 'salty',
        action = 'muted',
        state = state
    })
    
    isMuted = state
end)

RegisterNUICallback('saltychat/getState', function(data, cb)
    cb({isMuted = isMuted})
end)

-- CreateThread(function()
--     while true do
--         Wait(0)

--         if isMuted then
--             DisableAllControlActions(0)
--             DisableAllControlActions(1)
--             DisableAllControlActions(2)
--         else
--             Wait(1000)
--         end
--     end
-- end)