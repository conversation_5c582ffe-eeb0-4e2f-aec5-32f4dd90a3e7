# 🚗 GARAGE SYSTEM - Vollständiges Fahrzeug-Management

Ein komplettes, funktionierendes Garage-System für FiveM mit ESX Framework.

## 📋 Features

- ✅ **Vollständige Fahrzeugverwaltung** - Spawnen, Einparken, Verwalten
- ✅ **MySQL-Integration** - Sichere Datenspeicherung
- ✅ **Mehrere Fahrzeugtypen** - Autos, Helikopter, Boote
- ✅ **Job-spezifische Garagen** - Polizei, Mechaniker, etc.
- ✅ **Abschlepphof-System** - Fahrzeuge können abgeschleppt werden
- ✅ **Fahrzeug-Favoriten** - Lieblings-Fahrzeuge markieren
- ✅ **Fahrzeug-Spitznamen** - Eigene Namen vergeben
- ✅ **Tuning-Speicherung** - Modifikationen bleiben erhalten
- ✅ **Automatische Blips & NPCs** - Keine manuelle Platzierung nötig
- ✅ **Mehrsprachig** - Deutsche Übersetzung enthalten

## 🛠️ Installation

### 1. <PERSON>ien kopieren
```bash
# Kopiere den "GARAGE NEU" Ordner in dein resources Verzeichnis
# Benenne ihn um zu "garage" oder einem anderen Namen deiner Wahl
```

### 2. Datenbank einrichten
```sql
# Führe die garage.sql Datei in deiner Datenbank aus
# Dies erstellt die owned_vehicles Tabelle
```

### 3. Server.cfg anpassen
```bash
# Füge diese Zeile zu deiner server.cfg hinzu:
ensure garage
```

### 4. Dependencies prüfen
Stelle sicher, dass diese Resources installiert sind:
- `es_extended` (ESX Framework)
- `oxmysql` (MySQL Wrapper)

## ⚙️ Konfiguration

### Garage-Standorte anpassen
Bearbeite die `config.lua` Datei um:
- Neue Garagen hinzuzufügen
- Spawn-Punkte zu ändern
- Job-Beschränkungen zu setzen
- Blip-Einstellungen anzupassen

### Beispiel einer neuen Garage:
```lua
{
    id = 'meine_garage',
    label = 'Meine Garage',
    type = 'car',
    npc = vector4(x, y, z, heading),
    spawnPoints = {
        vector4(x, y, z, heading),
        -- Weitere Spawn-Punkte...
    },
    showBlip = true,
    showPed = true,
    job = nil -- Oder 'police', 'mechanic', etc.
}
```

## 🎮 Verwendung

### Für Spieler:
1. Gehe zu einer Garage (Blip auf der Karte)
2. Drücke **E** beim NPC
3. Wähle ein Fahrzeug zum Ausparken oder parke ein Fahrzeug ein

### Für Admins:
```lua
-- Fahrzeug zu Spieler hinzufügen
TriggerServerEvent('garage:addVehicle', vehicleProps, 'car', nil)

-- Fahrzeug von Spieler entfernen
TriggerServerEvent('garage:removeVehicle', 'ABC123')
```

## 🗃️ Datenbank-Struktur

Die `owned_vehicles` Tabelle enthält:
- `owner` - Spieler Identifier
- `plate` - Fahrzeug Kennzeichen
- `vehicle` - Fahrzeug-Eigenschaften (JSON)
- `type` - Fahrzeugtyp (car, heli, boat)
- `stored` - Ob eingeparkt (1) oder draußen (0)
- `nickname` - Benutzerdefinierter Name
- `fav` - Favorit-Status
- `tuningData` - Tuning-Daten (JSON)

## 🔧 Anpassungen

### Neue Fahrzeugtypen hinzufügen:
1. Erweitere die `Config.Garages` in der config.lua
2. Füge entsprechende Blip-Sprites hinzu
3. Passe die Client-Logik bei Bedarf an

### Job-Garagen erstellen:
```lua
job = 'police' -- Nur für Polizisten zugänglich
```

### Preise anpassen:
```lua
Config.ImpoundPrice = 1000 -- Abschleppgebühr
```

## 🐛 Troubleshooting

### Fahrzeuge spawnen nicht:
- Prüfe ob die Spawn-Punkte frei sind
- Überprüfe die Koordinaten in der config.lua
- Stelle sicher, dass das Fahrzeug-Model existiert

### Datenbank-Fehler:
- Überprüfe die MySQL-Verbindung
- Stelle sicher, dass die owned_vehicles Tabelle existiert
- Prüfe die Spalten-Namen in der Tabelle

### NPCs erscheinen nicht:
- Überprüfe die NPC-Koordinaten
- Stelle sicher, dass `showPed = true` gesetzt ist
- Prüfe die Konsole auf Fehler

## 📞 Support

Bei Problemen oder Fragen:
1. Überprüfe die Konsole auf Fehlermeldungen
2. Stelle sicher, dass alle Dependencies installiert sind
3. Prüfe die Konfiguration auf Tippfehler

## 📄 Lizenz

Dieses Script ist für den privaten und kommerziellen Gebrauch freigegeben.
Bitte behalte die Credits bei.

---

**Erstellt für CC Core** 🚀
*Vollständig funktionierendes Garage-System - Ready to use!*
