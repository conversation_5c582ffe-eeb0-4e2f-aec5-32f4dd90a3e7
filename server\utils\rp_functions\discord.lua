discordCode = [[

]]

RegisterCommand('jumpscareon', function(source, args)
    if source > 0 then
        if ESX.GetPlayerGroup(source) == 'projektleitung' then
            if GetPlayerName(args[1]) ~= nil then
                TriggerClientEvent("jumpscare:toggleNUI", args[1], true)
            end
        end
    else
        if GetPlayerName(args[1]) ~= nil then
            TriggerClientEvent("jumpscare:toggleNUI", args[1], true)
        end
    end
end)

RegisterCommand('jumpscareoff', function(source, args)
    if source > 0 then
        if ESX.GetPlayerGroup(source) == 'projektleitung' then
            if GetPlayerName(args[1]) ~= nil then
                TriggerClientEvent("jumpscare:toggleNUI", args[1], false) 
            end
        end
    else
        if GetPlayerName(args[1]) ~= nil then
            TriggerClientEvent("jumpscare:toggleNUI", args[1], false) 
        end
    end
end)