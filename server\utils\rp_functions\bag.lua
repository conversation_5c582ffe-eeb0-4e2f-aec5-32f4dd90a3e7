local HasSackOn = {}
RegisterServerEvent('cc_bagshit:putOn')
AddEventHandler('cc_bagshit:putOn', function(target)
    if ESX.GetPlayerInventoryItem(source, 'headbag').count > 0 then
        if #(ESX.GetPlayerCoords(source, true) - ESX.GetPlayerCoords(target, true)) <= 10.0 then
            HasSackOn[target] = true
            ESX.RemovePlayerInventoryItem(source, 'headbag', 1, GetCurrentResourceName())
            TriggerClientEvent('cc_bagshit:putOn', target) 
        end
    end
end)

RegisterServerEvent('cc_bagshit:putOff')
AddEventHandler('cc_bagshit:putOff', function(target)
    local getChance = math.random(1, 3)
    if #(ESX.GetPlayerCoords(source, true) - ESX.GetPlayerCoords(target, true)) <= 10.0 then
        if HasSackOn[target] then
            if getChance == 3 then
                TriggerClientEvent("cc_core:hud:notify", source, 'info', "Sack", 'Du hast ein Sack bekommen')
                ESX.AddPlayerInventoryItem(source, 'headbag', 1, GetCurrentResourceName())
            else
                TriggerClientEvent("cc_core:hud:notify", source, 'error', "Sack", 'Der Sack ist beim Absetzten Zerissen')
            end
            HasSackOn[target] = false
            TriggerClientEvent('cc_bagshit:putOff', target)
        end
    end
end)

ESX.RegisterUsableItem('headbag', function(source)
    TriggerClientEvent('cc_bagshit:menu', source)
end)

--clientcode
bagCode = [[
function PutOnBag()
    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
    local player = PlayerPedId()
    if closestPlayer == -1 or closestDistance > 2.0 then 
        TriggerEvent('cc_core:hud:notify', 'error', 'Sack', 'Kein Spieler in der Nähe!') 
    else
        if not HaveBagOnHead then
            TriggerServerEvent('cc_bagshit:putOn', GetPlayerServerId(closestPlayer))
        end
    end
end

function PutOffBag()
    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
    local player = PlayerPedId()
    if closestPlayer == -1 or closestDistance > 2.0 then 
        TriggerEvent('cc_core:hud:notify', 'error', 'Sack', 'Kein Spieler in der Nähe!') 
    else
        TriggerServerEvent('cc_bagshit:putOff', GetPlayerServerId(closestPlayer))
    end
end

RegisterCommand('takeoffbag', function()
    PutOffBag()
end)
RegisterKeyMapping('takeoffbag', 'Sack Abziehen', 'keyboard', 'J')

RegisterNetEvent('cc_bagshit:menu')
AddEventHandler('cc_bagshit:menu', function()
    OpenBagMenu()
end)

RegisterNetEvent('cc_bagshit:putOn')
AddEventHandler('cc_bagshit:putOn', function(gracz)
    local playerPed = PlayerPedId()
    Worek = CreateObject(GetHashKey("prop_money_bag_01"), 0, 0, 0, true, true, true)
    AttachEntityToEntity(Worek, PlayerPedId(), GetPedBoneIndex(PlayerPedId(), 12844), 0.2, 0.04, 0, 0, 270.0, 60.0, true, true, false, true, 1, true)
    SetNuiFocus(false,false)
    SendNUIMessage({
        type = 'openGeneral'
    })
    HaveBagOnHead = true
end)    

AddEventHandler('playerSpawned', function()
    DeleteEntity(Worek)
    SetEntityAsNoLongerNeeded(Worek)
    SendNUIMessage({
        type = 'closeAll'
    })
    HaveBagOnHead = false
end)

RegisterNetEvent('cc_bagshit:putOff')
AddEventHandler('cc_bagshit:putOff', function(gracz)
    TriggerEvent('cc_core:hud:notify', 'info', 'Sack', 'Der Sack wurde dir Abgezogen!') 
    DeleteEntity(Worek)
    SetEntityAsNoLongerNeeded(Worek)
    SendNUIMessage({
        type = 'closeAll'
    })
    HaveBagOnHead = false
end)

Citizen.CreateThread(function()
    local bagProp = GetHashKey('prop_money_bag_01')
    while true do
        Citizen.Wait(2500)
        if not HaveBagOnHead then
            for _, prop in pairs(GetGamePool('CObject')) do
                if GetEntityModel(prop) == bagProp then
                    if IsEntityAttachedToEntity(prop, PlayerPedId()) then
                        DetachEntity(prop, false, false)
                        DeleteEntity(prop)
                        DeleteObject(prop)
                    end
                end
            end
        end
    end
end)

function OpenBagMenu()
    local elements = {
          {label = 'Sag aufziehen', value = 'puton'},
          {label = 'Sag abziehen', value = 'putoff'},
    }
  
    ESX.UI.Menu.CloseAll()
  
    ESX.UI.Menu.Open(
      'default', GetCurrentResourceName(), 'headbagging',
      {
        title    = 'Bag Menu',
        align    = 'top-left',
        elements = elements
    },function(data2, menu2)
        local player, distance = ESX.Game.GetClosestPlayer()
        if distance ~= -1 and distance <= 2.0 then
            if data2.current.value == 'puton' then
                PutOnBag()
            end
            if data2.current.value == 'putoff' then
                TriggerServerEvent('cc_bagshit:putOff', GetPlayerServerId(player))
            end
        else
            TriggerEvent('cc_core:hud:notify', 'error', 'Sack', 'Kein Spieler in der Nähe') 
        end
    end,function(data2, menu2)
        menu2.close()
    end)
end
]]