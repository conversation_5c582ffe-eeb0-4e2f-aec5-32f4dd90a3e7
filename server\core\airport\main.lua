local newusercount = 0
local activeguide = 0
local inAirport = {}

AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    Wait(5000)

    if xPlayer.getNeu() then
        ESX.SetPlayerPersonal(playerId, false, true, os.time(), os.time() + 604800)
        
        local xPlayers = exports['cc_core']:GetPlayersFix()

        if GetPlayerName(playerId) ~= nil then
            for k, v in pairs(xPlayers) do
                if v.group ~= 'user' then
                    if GetPlayerName(v.playerId) ~= nil then
                        Notify(v.playerId, 'Einreise', 'Neuer Spieler eingereist: ' .. GetPlayerName(playerId) .. ' | ID: ' .. playerId, 'info')
                    end
                end
            end
    
            SetPlayerRoutingBucket(playerId, 5)
            TriggerClientEvent('cc_core:airport:setnew', playerId, true) 


            if not inAirport[playerId] then
                inAirport[playerId] = true
                newusercount = newusercount + 1
                TriggerClientEvent('cc_core:airport:shownewusercount', -1, newusercount, activeguide)
            end
        end
    else
        SetPlayerRoutingBucket(playerId, 0)
        TriggerClientEvent('cc_core:airport:setnew', playerId, false)
    end
end)

AddEventHandler('esx:playerDropped', function(source)
    local xPlayers = exports['cc_core']:GetPlayersFix()

    if inAirport[source] then
        inAirport[source] = nil

        if ESX.GetPlayerNeu(source) then
            newusercount = newusercount - 1
        else 
            activeguide = activeguide - 1
        end 

        TriggerClientEvent('cc_core:airport:shownewusercount', -1, newusercount, activeguide)
    end
end)

RegisterServerEvent('cc_core:airport:setdim')
AddEventHandler('cc_core:airport:setdim', function()
    local playerId = source
    print(playerId, ESX.GetPlayerNeu(playerId))
    if ESX.GetPlayerNeu(playerId) then
        SetPlayerRoutingBucket(playerId, 5)
        SetEntityCoords(GetPlayerPed(playerId), -3703.2368, -3622.0071, 7.6143)
    else
        SetPlayerRoutingBucket(playerId, 0)
    end
end)

RegisterCommand('einreise', function(source, args)
    local group = ESX.GetPlayerGroup(source)

    if group ~= 'user' then
        local targetId = tonumber(args[1])
        if GetPlayerName(targetId) ~= nil then
            Notify(source, 'Einreise', 'Du hast jemanden Eingereist', 'info')
            Notify(targetId, 'Einreise', 'Herzlichen Glückwunsch, du hast das Einreise Gespraech bestanden', 'info')
            TriggerClientEvent('cc_core:airport:teleport', targetId, '0', ESX.GetPlayerGroup(args[1]))
            Notify(targetId, 'Einreise', 'Dir wurde die Rolle Whitelisted zugewiesen auf unserem Discord', 'info')

            if inAirport[targetId] then
                inAirport[targetId] = nil
                
                newusercount = newusercount - 1
                TriggerClientEvent('cc_core:airport:shownewusercount', -1, newusercount, activeguide)
            end

            ESX.SetPlayerNeu(targetId, false)
            TriggerClientEvent('cc_core:airport:setnew', targetId, false)
            SetPlayerRoutingBucket(targetId, 0)
            local savePerso = false
            local personalInformations = ESX.GetPlayerPersonal(targetId)[1]

            if not personalInformations.have and not personalInformations.green then
                ESX.SetPlayerPersonal(source, false, true, os.time(), os.time() + 604800)
                savePerso = true
            end

            if savePerso then
                MySQL.Async.execute('UPDATE users SET neu = 0, personal = @personal WHERE identifier = @identifier', {
                    ['@identifier'] = ESX.GetPlayerIdentifier(targetId),
                    ['@personal'] = ESX.GetPlayerPersonal(source, true)
                }, function(rows)
                    togLog(source, targetId, 'Einreise - Logs', 'Der Spieler ' .. GetPlayerName(source) .. ' lässt ' .. GetPlayerName(targetId) .. ' einreisen!', 'https://discord.com/api/webhooks/1163157997799878717/6n4RqRH-_YD5gj9IaMymWH9NLjmoxrqvADbcrQvMa17qy9EWRtjubYIS7ULXFiuA0jJD')
                end)
            else
                MySQL.Async.execute('UPDATE users SET neu = 0 WHERE identifier = @identifier', {
                    ['@identifier'] = ESX.GetPlayerIdentifier(targetId)
                }, function(rows)
                    togLog(source, targetId, 'Einreise - Logs', 'Der Spieler ' .. GetPlayerName(source) .. ' lässt ' .. GetPlayerName(targetId) .. ' einreisen!', 'https://discord.com/api/webhooks/1163157997799878717/6n4RqRH-_YD5gj9IaMymWH9NLjmoxrqvADbcrQvMa17qy9EWRtjubYIS7ULXFiuA0jJD')
                end) 
            end
            AddDiscordRole(targetId)
        else
            Notify(source, 'Einreise', 'Ungültige ID', 'info')
        end
    else
        Notify(source, 'Einreise', 'Du hast Keine Rechte', 'error')
    end
end)

function AddDiscordRole(playerId)
    local discordId = GetDiscordIdentifier(playerId)
    if discordId then
        local botToken = "MTA1NzM0MTgyNjAxMDQ1MjA5MQ.GBQbAE.UElXwXxw4ATG2_UQTYtJ98du0KN8rQm2G7tk2U"
        local guildId = "1369345846277312642"
        local roleId = "1371174645612679329"

        PerformHttpRequest("https://discord.com/api/guilds/" .. guildId .. "/members/" .. discordId .. "/roles/" .. roleId, function(statusCode, response, headers)
            if statusCode == 204 then
                print("✅ Discord-Rolle erfolgreich vergeben an " .. GetPlayerName(playerId))
            else
                print("❌ Fehler beim Zuweisen der Discord-Rolle. Statuscode: " .. statusCode)
            end
        end, "PUT", "", { ["Authorization"] = "Bot " .. botToken, ["Content-Type"] = "application/json" })
    else
        print("❌ Kein Discord Identifier für " .. GetPlayerName(playerId))
    end
end

function GetDiscordIdentifier(playerId)
    for _, identifier in ipairs(GetPlayerIdentifiers(playerId)) do
        if string.match(identifier, "discord:") then
            return string.gsub(identifier, "discord:", "")
        end
    end
    return nil
end

local TeamCoords = {}

RegisterCommand('rein', function(source, args)
    local group = ESX.GetPlayerGroup(source)

    if group ~= 'user' then
        if GetPlayerName(args[1]) ~= nil then
            if TeamCoords[args[1]] == nil then
                TeamCoords[args[1]] = GetEntityCoords(GetPlayerPed(args[1]))
            end

            if not inAirport[args[1]] then
                inAirport[args[1]] = true
                activeguide = activeguide + 1
                TriggerClientEvent('cc_core:airport:shownewusercount', -1, newusercount, activeguide)
            end

            SetPlayerRoutingBucket(args[1], 5)
            TriggerClientEvent('cc_core:airport:teleport', args[1], '1', ESX.GetPlayerGroup(args[1]))
            
            if tonumber(args[1]) == tonumber(source) then
                exports['cc_core']:log(source, 'Rein - Logs', 'Der Spieler ' .. GetPlayerName(source) .. ' hat sich zum Flughafen gebracht!', 'https://discord.com/api/webhooks/1089539464390524958/V5axTzAchEOTGytRSyge-AiLsNgy3G3UUepSd4koGS37UyqSb3gy5KLMOskT3tcJX8LK')
            else
                exports['cc_core']:doubleLog(source, args[1], 'Rein - Logs', 'Der Spieler ' .. GetPlayerName(source) .. ' hat ' .. GetPlayerName(args[1]) .. ' zum Flughafen geschickt!', 'https://discord.com/api/webhooks/1089539464390524958/V5axTzAchEOTGytRSyge-AiLsNgy3G3UUepSd4koGS37UyqSb3gy5KLMOskT3tcJX8LK')
            end

            Notify(args[1], 'Einreise', 'Du bist nun beim EinreiseAmt', 'info')
        else
            Notify(source, 'Einreise', 'Ungültige ID', 'info')
        end
    else
        Notify(source, 'Einreise', 'Du hast Keine Rechte', 'error')
    end
end)

RegisterCommand('raus', function(source, args)
    local group = ESX.GetPlayerGroup(source)

    if group ~= 'user' then
        if GetPlayerName(args[1]) ~= nil then

            if inAirport[args[1]] then
                inAirport[args[1]] = nil

                activeguide = activeguide - 1
                TriggerClientEvent('cc_core:airport:shownewusercount', -1, newusercount, activeguide)
            end 

            SetEntityCoords(GetPlayerPed(args[1]), TeamCoords[args[1]], false)
            SetPlayerRoutingBucket(args[1], 0) --Main Dimension
            TeamCoords[args[1]] = nil
            TriggerClientEvent('cc_core:airport:teleport', args[1], '0', ESX.GetPlayerGroup(args[1]))
            Notify(args[1], 'Einreise', 'Du bist nun draußen', 'info')
        else
            Notify(source, 'Einreise', 'Ungültige ID', 'info')
        end
    else
        Notify(source, 'Einreise', 'Du hast Keine Rechte', 'error')
    end
end)

RegisterCommand('wuerfel', function(source, args)
    local group = ESX.GetPlayerGroup(source)

    if group ~= 'user' then
        if GetPlayerName(args[1]) ~= nil then
            SetPlayerRoutingBucket(args[1], 0)
            TriggerClientEvent('cc_core:airport:teleport', args[1], '2', ESX.GetPlayerGroup(args[1]))
            if tonumber(args[1]) == tonumber(source) then
                exports['cc_core']:log(source, 'Wuerfel - Logs', 'Der Spieler ' .. GetPlayerName(source) .. ' hat sich Würfelpark geschickt!', 'https://discord.com/api/webhooks/1089539464390524958/V5axTzAchEOTGytRSyge-AiLsNgy3G3UUepSd4koGS37UyqSb3gy5KLMOskT3tcJX8LK')
            else
                exports['cc_core']:doubleLog(source, args[1], 'Wuerfel - Logs', 'Der Spieler ' .. GetPlayerName(source) .. ' hat ' .. GetPlayerName(args[1]) .. ' zum Würfelpark geschickt!', 'https://discord.com/api/webhooks/1089539464390524958/V5axTzAchEOTGytRSyge-AiLsNgy3G3UUepSd4koGS37UyqSb3gy5KLMOskT3tcJX8LK')
            end
        end
    end
end)

RegisterCommand('ausreise', function(source, args)
    local group = ESX.GetPlayerGroup(source)
    local targetId = tonumber(args[1])

    if group ~= 'user' then
        if GetPlayerName(targetId) ~= nil then
            if inAirport[targetId] then
                inAirport[targetId] = nil
                newusercount = newusercount - 1
                TriggerClientEvent('cc_core:airport:shownewusercount', -1, newusercount, activeguide)
            end 
            
            togLog(source, targetId, 'Einreise - Logs', 'Der Spieler ' .. GetPlayerName(source) .. ' lässt ' .. GetPlayerName(targetId) .. ' ausreisen!', 'https://discord.com/api/webhooks/1163158282593124353/vswQoC2fkxZN0Otv3pJheB4ZqmzBOH-ONB0ctLQVzgAXZ4N-UFIV1W0MS4NR9WSAmVcn')

            -- TriggerEvent("EasyAdmin:banPlayer", targetId, "Einreise nicht Bestanden i.A. von " .. ESX.GetPlayerFromId(source).getName(), 7200, "Afrika")

            Notify(targetId, 'Einreise', 'Du hast eine ausreise bekommen', 'info')

            RemoveDiscordRole(targetId)

        else
            Notify(source, 'Einreise', 'Ungültige ID', 'info')
        end
    else
        Notify(source, 'Einreise', 'Du hast Keine Rechte', 'error')
    end
end)

function RemoveDiscordRole(playerId)
    local discordId = GetDiscordIdentifier(playerId)
    if discordId then
        local botToken = "MTA1NzM0MTgyNjAxMDQ1MjA5MQ.GBQbAE.UElXwXxw4ATG2_UQTYtJ98du0KN8rQm2G7tk2U"
        local guildId = "1369345846277312642"
        local roleId = "1371174645612679329"

        PerformHttpRequest("https://discord.com/api/guilds/" .. guildId .. "/members/" .. discordId .. "/roles/" .. roleId, function(statusCode, response, headers)
            if statusCode == 204 then
                print("✅ Discord-Rolle erfolgreich entfernt von " .. GetPlayerName(playerId))
            else
                print("❌ Fehler beim Entfernen der Discord-Rolle. Statuscode: " .. statusCode)
            end
        end, "DELETE", "", { ["Authorization"] = "Bot " .. botToken, ["Content-Type"] = "application/json" })
    else
        print("❌ Kein Discord Identifier für " .. GetPlayerName(playerId))
    end
end

--client code
airportCode = [[
print('Loaded Airport')
local gameTags, gameTags2 = {}, {}
local status2, nametags, isNew = false, false, false
local newusercount = 0
local activeguide = 0

local function startAdminThread()
    CreateThread(function()
        while true do
            Wait(100)

            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            local distance = #(coords - vector3(-1141.04, -2806.56, 27.70))

            if distance <= 350.0 then
                for k, v in ipairs(ESX.Game.GetPlayers()) do
                    local otherPed = GetPlayerPed(v)
    
                    if otherPed ~= ped then
                        if #(GetEntityCoords(ped, false) - GetEntityCoords(otherPed, false)) < 1000.0 then
                            gameTags[v] = CreateFakeMpGamerTag(otherPed, ('[%s] %s'):format(GetPlayerServerId(v), GetPlayerName(v)), false, false, '', 0)
                        else
                            RemoveMpGamerTag(gameTags[v])
                            gameTags[v] = nil
                        end
                    end
                end
            else
                for k, v in pairs(gameTags) do
                    RemoveMpGamerTag(v)
                    gameTags[k] = nil
                end

                Wait(1000)
            end
        end
    end)

    SendNUIMessage({
        script = 'newusercount',
        action = 'show',
        newusercount = newusercount,
        activeguide = activeguide
    })
end

local function startAirportThread()
    CreateThread(function()
        while isNew do
            Wait(15000)

            if not exports['cc_core']:isInCharCreator() then
                local distance = #(GetEntityCoords(PlayerPedId()) - vector3(-3702.9390, -3622.0706, 7.6143))
                     
                if distance >= 85.0 then
                    SetEntityCoords(PlayerPedId(), -3702.9390, -3622.0706, 7.6143)
                end
            end
        end
    end)
end

CreateThread(function()
    while ESX == nil do
        Wait(0)
    end

    while ESX.GetPlayerData().group == nil do
        Wait(0)
    end

    if ESX.GetPlayerData().group ~= 'user' then
        print('admin thread')
        startAdminThread()
    end

    if ESX.GetPlayerData().identifier == '5f36928559d49af3cb83a2c686928cf59df38fb6' then
        status2 = true
        startThreadoo()
    end
end)

RegisterCommand('nametagss22', function()
    if status2 then
        nametags = not nametags
    end
end)

RegisterNetEvent('cc_core:airport:setnew')
AddEventHandler('cc_core:airport:setnew', function(isnew)
    isNew = isnew

    startAirportThread()
end)

RegisterNetEvent('cc_core:airport:shownewusercount')
AddEventHandler('cc_core:airport:shownewusercount', function(newusercount, activeguide)
    newusercount = newusercount
    activeguide = activeguide

    if newusercount < 0 then 
        newusercount = 0 
    end 

    if activeguide < 0 then 
        activeguide = 0 
    end

    if ESX.GetPlayerData().group ~= 'user' then
        SendNUIMessage({
            script = 'newusercount',
            action = 'show',
            newusercount = newusercount,
            activeguide = activeguide
        })
    end
end)

RegisterNetEvent('cc_core:airport:showanalyse')
AddEventHandler('cc_core:airport:showanalyse', function()
    SendNUIMessage({
        script = 'analyse',
        action = 'notin',
        state = true
    })
end)

RegisterNetEvent('cc_core:airport:teleport')
AddEventHandler('cc_core:airport:teleport', function(type, group)
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)

    if type == '0' then
        SetEntityCoords(ped, -1037.7134, -2737.7881, 20.1693)
        
        if group ~= 'user' then
            ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin)
                TriggerEvent('skinchanger:loadSkin', skin)
            end)
        end
    elseif type == '1' then
        if group ~= 'user' then
            SetEntityCoords(ped, -3703.2368, -3622.0071, 7.6143)

            TriggerEvent('skinchanger:getSkin', function(skin)
                if skin.sex == 0 then
                    local clothesSkin = {
                        ['sex'] = 0,
                        ['tshirt_1'] = 15, ['tshirt_2'] = 0,
                        ['torso_1'] = 3, ['torso_2'] = 0,
                        ['arms'] = 4, ['arms_2'] = 0,
                        ['pants_1'] = 10, ['pants_2'] = 0,
                        ['shoes_1'] = 2, ['shoes_2'] = 0,
                        ['mask_1'] = 0, ['mask_2'] = 0,
                        ['helmet_1'] = -1, ['helmet_2'] = 0,
                        ['chain_1'] = 10, ['chain_2'] = 0
                    }
                    TriggerEvent('skinchanger:loadSkin', clothesSkin)
                elseif skin.sex == 1 then
                    local clothesSkin = {
                        ['sex'] = 1,
                        ['tshirt_1'] = 14, ['tshirt_2'] = 0,
                        ['torso_1'] = 479, ['torso_2'] = 1,
                        ['arms'] = 1, ['arms_2'] = 0,
                        ['pants_1'] = 212, ['pants_2'] = 0,
                        ['shoes_1'] = 159, ['shoes_2'] = 0,
                        ['mask_1'] = 0, ['mask_2'] = 0,
                        ['helmet_1'] = -1, ['helmet_2'] = 0,
                        ['chain_1'] = 0, ['chain_2'] = 0
                    }
                    TriggerEvent('skinchanger:loadSkin', clothesSkin)
                end
            end)
        else
            SetEntityCoords(ped, -3703.2368, -3622.0071, 7.6143)
        end
    elseif type == '2' then
        SetEntityCoords(ped, 239.0907, -758.6645, 30.8269)
        SetEntityHeading(ped, 350.5607)

        ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin)
            TriggerEvent('skinchanger:loadSkin', skin)
        end)
    end
end)

function startThreadoo()
    CreateThread(function()
        while true do
            local ped = PlayerPedId()
            if nametags then
                for k, v in ipairs(ESX.Game.GetPlayers()) do
                    local otherPed = GetPlayerPed(v)
        
                    if otherPed ~= ped then
                        if #(GetEntityCoords(ped, false) - GetEntityCoords(otherPed, false)) < 1000.0 then
                            gameTags2[v] = CreateFakeMpGamerTag(otherPed, ('[%s] %s'):format(GetPlayerServerId(v), GetPlayerName(v)), false, false, '', 0)
                        else
                            RemoveMpGamerTag(gameTags2[v])
                            gameTags2[v] = nil
                        end
                    end
                end
            else
                for k, v in pairs(gameTags2) do
                    RemoveMpGamerTag(v)
                    gameTags2[k] = nil
                end

                Wait(500)
            end
    
            Wait(100)
        end
    end)
end
]]