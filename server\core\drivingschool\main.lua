AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    Citizen.Wait(15000)

    TriggerEvent('esx_license:getLicenses', playerId, function(licenses)
        TriggerClientEvent('cc_core:drivingschool:loadLicenses', playerId, licenses)
    end)
end)

local players, isInUse = {}, {}

-- AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
--     if players[playerId] == nil then
--         players[playerId] = {}

--         players[playerId].job = xPlayer.getJob().name
--     end
-- end)

-- AddEventHandler('esx:setJob', function(playerId, job, lastJob)
--     if job.name == 'driver' then
--         if players[playerId] == nil then
--             players[playerId] = {}

--             players[playerId].job = job.name
--         end
--     end
    
--     if players[playerId] then
--         players[playerId].job = job.name
--     end
-- end)

-- AddEventHandler('playerDropped', function(reason)
--     local playerId = source

--     if players[playerId] then
--         players[playerId] = nil
--     end
-- end)

ESX.RegisterServerCallback('cc_core:drivingschool:canDrivingschool', function(source, cb)
    local jobAmount = 0

    -- for k, v in pairs(players) do
    --     if v.job == 'driver' then
    --         jobAmount = jobAmount + 1
    --     end
    -- end

    cb(jobAmount < 4)
end)

RegisterServerEvent('cc_core:drivingschool:addLicense')
AddEventHandler('cc_core:drivingschool:addLicense', function(type)
    local playerId = source

    if isInUse[playerId] == nil then
        isInUse[playerId] = false
    end

    if not isInUse[playerId] then
        isInUse[playerId] = true
        
        if type == 'dmv' or type == 'drive' or type == 'dmv_bike' or type == 'drive_bike' or type == 'dmv_boat' or type == 'drive_boat' or type == 'dmv_airplane' or type == 'drive_airplane' or type == 'dmv_helicopter' or type == 'drive_helicopter' then
            TriggerEvent('esx_license:addLicense', playerId, type, function()
                TriggerEvent('esx_license:getLicenses', playerId, function(licenses)
                    TriggerClientEvent('cc_core:drivingschool:loadLicenses', playerId, licenses)
                    isInUse[playerId] = false
                end)
            end)
        else
            TriggerEvent("EasyAdmin:banPlayer", playerId, 'drivingschool [c-1]', false, "Afrika")
        end
    end
end)

RegisterServerEvent('cc_core:drivingschool:pay')
AddEventHandler('cc_core:drivingschool:pay', function(price)
    local playerId = source

    if price >= 15100 then
        return
    end

    ESX.RemovePlayerMoney(playerId, price, GetCurrentResourceName())
end)

local uniqueId = 1000
local playerIds = {}

ESX.RegisterServerCallback('cc_core:drivingschool:enter', function(source, cb, force)
    local playerId = source

    print('cc_core:drivingschool:enter', playerId, force)

    if force then
        playerIds[playerId] = 0
        SetPlayerRoutingBucket(playerId, 0)
        cb(true)
        return
    end

    if ESX.GetPlayerNeu(playerId) then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [DS-22]", false, "Afrika")
        return
    end

    if playerIds[playerId] == nil then
        playerIds[playerId] = 0
    end

    if playerIds[playerId] == 0 then
        uniqueId = uniqueId + 1
        playerIds[playerId] = uniqueId
        SetPlayerRoutingBucket(playerId, uniqueId)
        cb(true)
    else
        playerIds[playerId] = 0
        SetPlayerRoutingBucket(playerId, 0)
        cb(true)
    end
end)

--clientcode
drivingschoolCode = [[
local licenses = {}
local show = false
local currentTest = ''
local isInPraxis = false

local startCoords = nil

local currentTestType, currentBlip, currentZoneType, currentVehicle, lastVehicleHealth = nil, nil, nil, nil, nil
local currentCheckPoint, lastCheckPoint, driveErrors = 0, -1, 0
local isAboveSpeedLimit = false

RegisterNetEvent('cc_core:drivingschool:loadLicenses')
AddEventHandler('cc_core:drivingschool:loadLicenses', function(license)
    licenses = license
end)

local function canPraxis(lic)
    local count = 0

    for k, v in pairs(licenses) do
        for k1, v1 in pairs(lic) do
            if v1.license == v.type then
                count = count + 1
            end
        end
    end

    return count
end

local function setCurrentZoneType(type)
    currentZoneType = type
end

local function stopDrive(success)
    isInPraxis = false

    print('cc_core:drivingschool:enter force')
    ESX.TriggerServerCallback('cc_core:drivingschool:enter', function()
    end, true)

    if success then
        TriggerServerEvent('cc_core:drivingschool:addLicense', currentTest)
        Notify('Information', 'Du hast die Prüfung bestanden! Herzlichen Glückwunsch', 'success')
    else
        Notify('Fahrschule', 'Du hast die Prüfung nicht bestanden', 'info')
    end

    currentTest = nil
end

local function startThread()
    Citizen.CreateThread(function()
        while isInPraxis do
            Citizen.Wait(0)

            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            local nextCheckPoint = currentCheckPoint + 1

            if Config_Drivingschool.Route[currentTestType].route[nextCheckPoint] ~= nil then
                if currentCheckPoint ~= lastCheckPoint then
                    if DoesBlipExist(currentBlip) then
                        RemoveBlip(currentBlip)
                    end

                    currentBlip = AddBlipForCoord(Config_Drivingschool.Route[currentTestType].route[nextCheckPoint].coords)
                    SetBlipRoute(currentBlip, 1)
                    SetBlipColour(currentBlip, 3)
                    SetBlipRouteColour(currentBlip, 3)

                    lastCheckPoint = currentCheckPoint
                end

                local distance = #(coords - Config_Drivingschool.Route[currentTestType].route[nextCheckPoint].coords)

                if distance <= 500.0 then
                    DrawMarker(Config_Drivingschool.Route[currentTestType].marker, Config_Drivingschool.Route[currentTestType].route[nextCheckPoint].coords, Config_Drivingschool.Route[currentTestType].dir, Config_Drivingschool.Route[currentTestType].rotation, Config_Drivingschool.Route[currentTestType].scale, 255, 156, 35, 100, false, true, 2, false, false, false, false)
                end

                if distance <= Config_Drivingschool.Route[currentTestType].scale.x then
                    Config_Drivingschool.Route[currentTestType].route[nextCheckPoint].action(ped, currentVehicle, setCurrentZoneType)
                    currentCheckPoint = currentCheckPoint + 1
                end
            else
                if DoesBlipExist(currentBlip) then
                    RemoveBlip(currentBlip)
                end

                if driveErrors < 6 then
                    stopDrive(true)
                else
                    stopDrive(false)
                end
            end
        end
    end)

    Citizen.CreateThread(function()
        while isInPraxis do
            Citizen.Wait(500)

            local ped = PlayerPedId()
            local vehicle = GetVehiclePedIsIn(ped, false)
            
            if vehicle ~= currentVehicle and isInPraxis then
                DeleteEntity(currentVehicle)
                SetEntityCoords(ped, startCoords.x, startCoords.y, startCoords.z)
                SetEntityHeading(ped, startCoords.w)
                print('cc_core:drivingschool:enter force')
                ESX.TriggerServerCallback('cc_core:drivingschool:enter', function()
                end, true)
                isInPraxis = false
            end
        end
    end)

    Citizen.CreateThread(function()
        while isInPraxis do
            Citizen.Wait(0)

            local ped = PlayerPedId()
            
            if IsPedInAnyVehicle(ped, false) then
                local vehicle = GetVehiclePedIsIn(ped, false)
                local vehicleHealth = GetEntityHealth(vehicle)
                local vehicleSpeed = GetEntitySpeed(vehicle) * 3.6
                local tooMuchSpeed = false

                for k, v in pairs(Config_Drivingschool.SpeedLimits[currentTestType]) do
                    if k == currentZoneType then
                        if vehicleSpeed > v then
                            tooMuchSpeed = true

                            if not isAboveSpeedLimit then
                                driveErrors = driveErrors + 1
                                isAboveSpeedLimit = true

                                Notify('Fahrschule', 'Du fährst zu schnell! Die akutelle erlaubte Geschwindigkeit liegt bei ' .. v .. ' km/h', 'info')
                                Notify('Fahrschule', 'Fehler: ' .. driveErrors .. '/5', 'info')
                            end
                        end
                    end
                end

                if not tooMuchSpeed then
                    isAboveSpeedLimit = false
                end

                if vehicleHealth < lastVehicleHealth then
                    driveErrors = driveErrors + 1

                    Notify('Fahrschule', 'Du hast das Fahrzeug beschädigt', 'info')
                    Notify('Fahrschule', 'Fehler: ' .. driveErrors .. '/5', 'info')

                    lastVehicleHealth = vehicleHealth
                    Citizen.Wait(1500)
                end
            end

            if driveErrors > 5 then
                DeleteEntity(currentVehicle)
                SetEntityCoords(ped, startCoords.x, startCoords.y, startCoords.z)
                SetEntityHeading(ped, startCoords.w)
                Notify('Fahrschule', 'Du hast die Prüfung nicht bestanden', 'info')
                print('cc_core:drivingschool:enter force')
                ESX.TriggerServerCallback('cc_core:drivingschool:enter', function()
                end, true)
            end
        end
    end)
end

Citizen.CreateThread(function()
    for k, v in pairs(Config_Drivingschool.Points) do
        if v.blip.enabled then
            local blip = AddBlipForCoord(v.coords)

            SetBlipSprite(blip, v.blip.sprite)
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, v.blip.scale)
            SetBlipAsShortRange(blip, true)
        
            BeginTextCommandSetBlipName('STRING')
            AddTextComponentString(v.blip.text)
            EndTextCommandSetBlipName(blip) 
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local letSleep, inRange = true, false

        for k, v in pairs(Config_Drivingschool.Points) do
            local distance = #(v.coords - coords)

            if distance <= 25.0 then
                letSleep = false
                DrawMarker(nil, v.coords.x, v.coords.y, v.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 255, 156, 35, 100, false, false, false, false, false, false, false)
            end

            if distance <= 1.0 then
                inRange = true

                if IsControlJustReleased(1, 38) then
                    local count = canPraxis(v.licenses)
                    ESX.TriggerServerCallback('cc_core:drivingschool:canDrivingschool', function(can)
                        if can then
                            SetNuiFocus(true, true)
                            hideHud(true)
                            SendNUIMessage({
                                script = 'drivingschool',
                                action = 'show',
                                type = v.type,
                                praxis = count
                            })
        
                            if v.licenses[count + 1] ~= nil then
                                local name = v.licenses[count + 1].license
                                currentTest = name
                            end
                        else
                            Notify('Information', 'Es sind noch Fahrlehrer wach!', 'info')
                        end
                    end)
                end
            end
        end

        helpNotify(inRange, show, 'Drücke E um Fahrschule zu machen', function(bool)
            show = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

RegisterNUICallback('drivingschool/getQuestions', function(data, cb)
    TriggerServerEvent('cc_core:drivingschool:pay', 500)
    cb({
        can = true,
        questions = Config_Drivingschool.Questions[data.type],
        length = #Config_Drivingschool.Questions[data.type]
    })
end)

RegisterNUICallback('drivingschool/startPraxis', function(data, cb)
    SetNuiFocus(false, false)
    hideHud(false)
    local coords = GetEntityCoords(PlayerPedId())
    startCoords = vector4(coords.x, coords.y, coords.z, GetEntityHeading(PlayerPedId()))
    
    print('cc_core:drivingschool:enter')

    ESX.TriggerServerCallback('cc_core:drivingschool:enter', function(start)
        print('cc_core:drivingschool:enter', start)
        if start then
            TriggerServerEvent('cc_core:drivingschool:pay', 500)

            local options = Config_Drivingschool.Route[data.type]

            while not HasModelLoaded(GetHashKey(options.vehicle)) do
                RequestModel(GetHashKey(options.vehicle))
                Citizen.Wait(100)
            end
        
            local cVehicle = CreateVehicle(GetHashKey(options.vehicle), options.start.x, options.start.y, options.start.z, options.start.w, false, false)
            SetVehicleNumberPlateText(cVehicle, 'FinalU21')
            isInPraxis = true
        
            exports['cc_core']:setFuel(cVehicle, 100)
        
            currentTestType = data.type
            currentCheckPoint = 0
            lastCheckPoint = -1
            currentZoneType = 'residence'
            driveErrors = 0
            isAboveSpeedLimit = false
            currentVehicle = cVehicle
            lastVehicleHealth = GetEntityHealth(cVehicle)
            
            startThread()
        
            TaskWarpPedIntoVehicle(PlayerPedId(), cVehicle, -1)
        end
    end)
end)

local can = false

RegisterNUICallback('drivingschool/addLicense', function(data, cb)
    if not can then
        can = true
        TriggerServerEvent('cc_core:drivingschool:addLicense', currentTest)
        Citizen.Wait(3500)
        can = false
    end
end)

RegisterNUICallback('drivingschool/startThisShit', function()
    TriggerServerEvent('cc_core:anticheat:slap')
end)

local oldCoords = vector3(0, 0, 0)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(5500)

        local ped = PlayerPedId()

        if DoesEntityExist(ped) then
            local coords = GetEntityCoords(ped)
            local distance = #(coords - oldCoords)

            if distance > 5 then
                oldCoords = coords    
            end
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        local ped = PlayerPedId()
        local vehicle = GetVehiclePedIsIn(ped, false)
        local coords = GetEntityCoords(ped)
        
        if IsPedInAnyVehicle(ped) then
            local distance = #(coords - vector3(10000.0, 10000.0, 500.0))
            
            if distance < 50.5 then
                SetPedCoordsKeepVehicle(ped, oldCoords.x, oldCoords.y, oldCoords.z)
            else
                Citizen.Wait(100)
            end
        else
            Citizen.Wait(1000)
        end
    end
end)
]]