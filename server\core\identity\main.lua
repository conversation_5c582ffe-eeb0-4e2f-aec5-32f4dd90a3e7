local function getIdentity(identifier, callback)
    MySQL.Async.fetchAll('SELECT firstname, lastname, dateofbirth, sex, height FROM users WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    }, function(result)
        local data = {
            identifier = '',
            firstname = '',
            lastname = '',
            dateofbirth = '',
            sex = '',
            height = 188
        }

        if #result ~= 0 then
            if result[1].firstname ~= nil then
                data = {
                    identifier = identifier,
                    firstname = result[1].firstname,
                    lastname = result[1].lastname,
                    dateofbirth = result[1].dateofbirth,
                    sex = result[1].sex,
                    height = result[1].height
                }

                callback(data)
            else
                callback(data)
            end
        else
            callback(data)
        end
    end)
end

local function setIdentity(identifier, data, callback)
    MySQL.Async.execute('UPDATE users SET firstname = @firstname, lastname = @lastname, dateofbirth = @dateofbirth, sex = @sex, height = @height WHERE identifier = @identifier', {
        ['@identifier'] = identifier,
        ['@firstname'] = data.firstname,
        ['@lastname'] = data.lastname,
        ['@dateofbirth'] = data.dateofbirth,
        ['@sex'] = data.sex,
        ['@height'] = data.height
    }, function(rows)
        if rows >= 1 then
            createReferralCodeForPlayer(identifier)

            callback(true)
        else
            callback(false)
        end
    end)
end

local uniqueId = 1337

local function setUniqueId(id)
    uniqueId = id
end

local function getUniqueId()
    return uniqueId
end

RegisterServerEvent('cc_core:identity:setIdentity')
AddEventHandler('cc_core:identity:setIdentity', function(data)
    local playerId = source
    local identifier = ESX.GetPlayerIdentifier(playerId)

    if ESX.GetPlayerRPName(playerId) == 'Max Mustermann' then
        setIdentity(identifier, data, function(callback)
            if not callback then
                print('Failed to create a character for player ID: ' .. playerId)
            else
                ESX.SetPlayerRPName(playerId, data.firstname .. ' ' .. data.lastname)
                ESX.SetPlayerFirstName(playerId, data.firstname)
                ESX.SetPlayerLastName(playerId, data.lastname)
                ESX.SetPlayerDateOfBirth(playerId, data.dateofbirth)
                ESX.SetPlayerSex(playerId, data.sex)
                ESX.SetPlayerHeight(playerId, data.height)
                
                if data.referral and data.referral ~= "" then
                    MySQL.Async.fetchScalar("SELECT identifier, firstname, lastname FROM users WHERE referral_code = @referral", {
                        ['@referral'] = data.referral
                    }, function(referrerIdentifier)
                        if referrerIdentifier then
                            MySQL.Async.execute("UPDATE users SET referral_bonus = referral_bonus + 1 WHERE identifier = @identifier", {
                                ['@identifier'] = referrerIdentifier
                            })
                            
                            local referrerPlayer = ESX.GetPlayerFromIdentifier(referrerIdentifier)
                            if referrerPlayer then
                                referrerPlayer.addMoney(25000)
                                referrerPlayer.addInventoryItem("phone", 1)
                                referrerPlayer.addInventoryItem("water", 10)
                                referrerPlayer.addInventoryItem("bread", 10)
                                
                                TriggerClientEvent('cc_core:hud:notify', referrerPlayer.source, 'info', 'Refrreal Code', 'Jemand hat deinen Referral-Code benutzt!')
                            end
                        else
                            print("Ungültiger Referral-Code: " .. data.referral)
                        end
                    end)
                end
            end
        end)
    end
end)

RegisterCommand("referralcode", function(source, args, rawCommand)
    local playerId = source
    local identifier = ESX.GetPlayerIdentifier(playerId)
    
    MySQL.Async.fetchScalar("SELECT referral_code FROM users WHERE identifier = @identifier", {
        ['@identifier'] = identifier
    }, function(referralCode)
        if referralCode then
            TriggerClientEvent('cc_core:referral:copyToClipboard', playerId, referralCode)
            TriggerClientEvent('esx:showNotification', playerId, "Dein Referral-Code wurde in die Zwischenablage kopiert!")
        else
            TriggerClientEvent('esx:showNotification', playerId, "Du hast noch keinen Referral-Code.")
        end
    end)
end)

RegisterServerEvent('cc_core:identity:leaveServer')
AddEventHandler('cc_core:identity:leaveServer', function()
    DropPlayer(source, '[Server Name] Einreise abgebrochen')
end)

AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    Wait(math.random(6000, 6500))
    if string.match(xPlayer.getRPName(), "<") or string.match(xPlayer.getRPName(), ">") or string.match(xPlayer.getRPName(), "script") then
        DropPlayer(playerId, "Fehler #1337 bitte im Support melden!")
        return
    end
    if xPlayer.getRPName() == 'Max Mustermann' then
        uniqueId = uniqueId + 1
        SetPlayerRoutingBucket(playerId, uniqueId)
        TriggerClientEvent('cc_core:multichar:showmultichar', playerId, true, true)
    else
        uniqueId = uniqueId + 1
        SetPlayerRoutingBucket(playerId, uniqueId)
        TriggerClientEvent('cc_core:multichar:showmultichar', playerId, false, false)
    end
end)

RegisterServerEvent('cc_core:tiziano:dupic')
AddEventHandler('cc_core:tiziano:dupic', function()
    local playerId = source

    SetPlayerRoutingBucket(playerId, 0)
end)

RegisterCommand('resetchar', function(source, args, rawCommand)
    local playerId = tonumber(args[1])
    
    if source == 0 then
        if GetPlayerName(playerId) ~= nil then
            ESX.SetPlayerRPName(playerId, 'Max Mustermann')
            uniqueId = uniqueId + 1
            SetPlayerRoutingBucket(playerId, uniqueId)
            TriggerClientEvent('cc_core:identity:showIdentity', playerId)
        end
    else
        if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'superadmin' or ESX.GetPlayerGroup(source) == 'administrator' or ESX.GetPlayerGroup(source) == 'frakverwaltung'or ESX.GetPlayerGroup(source) == 'moderator' then
            if GetPlayerName(playerId) ~= nil then
                ESX.SetPlayerRPName(playerId, 'Max Mustermann')
                uniqueId = uniqueId + 1
                SetPlayerRoutingBucket(playerId, uniqueId)
                TriggerClientEvent('cc_core:identity:showIdentity', playerId)
                exports['cc_core']:log(source, 'Skinchanger - Log', 'Der Teamler ' .. GetPlayerName(source) .. ' hat ' ..GetPlayerName(playerId) .. ' /resetchar gegeben!', 'https://canary.discord.com/api/webhooks/1378341144362090616/oHIBMmWvqoT4WNtPBU78tS3KOJrgjrB_BliRVg25iz6bD82jQ54AkfMa91WyUxo6MFFY')
            end
        end
    end
end)

RegisterCommand('skinchanger', function(source, args, rawCommand)
    local playerId = tonumber(args[1])

    if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'superadmin' or ESX.GetPlayerGroup(source) == 'administrator' or ESX.GetPlayerGroup(source) == 'moderator' or ESX.GetPlayerGroup(source) == 'support' or ESX.GetPlayerGroup(source) == 'testsupporter' or ESX.GetPlayerGroup(source) == 'frakverwaltung' then
        if GetPlayerName(playerId) ~= nil then
            uniqueId = uniqueId + 1
            SetPlayerRoutingBucket(playerId, uniqueId)
            TriggerClientEvent('cc_core:identity:resetSkin', playerId)
            exports['cc_core']:log(source, 'Skinchanger - Log', 'Der Teamler ' .. GetPlayerName(source) .. ' hat ' ..GetPlayerName(playerId) .. ' /skinchanger gegeben!', 'https://canary.discord.com/api/webhooks/1378341144362090616/oHIBMmWvqoT4WNtPBU78tS3KOJrgjrB_BliRVg25iz6bD82jQ54AkfMa91WyUxo6MFFY')
        end
    end
end)

function generateReferralCode()
    local charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    local code = ""
    for i = 1, 8 do
        local rand = math.random(1, #charset)
        code = code .. charset:sub(rand, rand)
    end
    return code
end

function createReferralCodeForPlayer(identifier)
    local referralCode = generateReferralCode()

    MySQL.Async.fetchScalar("SELECT referral_code FROM users WHERE referral_code = @referralCode", {
        ['@referralCode'] = referralCode
    }, function(existingCode)
        if existingCode then
            createReferralCodeForPlayer(identifier)
        else
            MySQL.Async.execute("UPDATE users SET referral_code = @referralCode WHERE identifier = @identifier", {
                ['@referralCode'] = referralCode,
                ['@identifier'] = identifier
            }, function(rowsChanged)
                if rowsChanged > 0 then
                    print("Referral Code für Spieler " .. identifier .. " wurde gespeichert: " .. referralCode)
                else
                    print("Fehler beim Speichern des Referral Codes für Spieler " .. identifier)
                end
            end)
        end
    end)
end

--clientcode
identityCode = [[
local cam = nil
local oldCoords = vector3(-3703.2368, -3622.0071, 7.6143)
local oldHeading = 274.5175
local config = nil
local lastPosition = nil
local isInMultichar = false
local isInIdentity = false

local charCreator = false

local function isInCharCreator()
    return charCreator
end

exports('isInCharCreator', isInCharCreator)

local function getSex(skin)
    for k, v in pairs(skin) do
        if v.name == "sex" then
            return v.value
        end
    end

    return false
end

local function offsetPosition(x, y, z, distance)
    local object = {
        x = x + math.sin(-z * math.pi / 180) * distance,
        y = y + math.cos(-z * math.pi / 180) * distance
    }

    return object
end

local function CreationCamHead(coords, heading)
    DestroyCam(cam, true)
    
    if coords ~= nil then
        SetEntityCoordsNoOffset(PlayerPedId(), coords)
    end
    
    if heading ~= nil then
        SetEntityHeading(PlayerPedId(), heading)
    end

	cam = CreateCam('DEFAULT_SCRIPTED_CAMERA')

    local player = {
        position = GetEntityCoords(PlayerPedId())
    }

	local coordsCam = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 2.50, 0.0)
    local offset = offsetPosition(player.position.x, player.position.y, GetEntityHeading(PlayerPedId()), 2.10)
    local targetPositionFly = vector3(offset.x, offset.y, player.position.z + 0.5)
    local targetPositionPoint = vector3(player.position.x, player.position.y, player.position.z + 0.3)
    
    if targetPositionPoint == nil then 
        return 
    end

    SetCamCoord(cam, targetPositionFly)
    PointCamAtCoord(cam, targetPositionPoint)
	SetCamActive(cam, true)
	RenderScriptCams(true, false, 500, true, true)
end

RegisterNUICallback('charCreator/rotate', function(data, cb)
    SetEntityHeading(PlayerPedId(), data.heading + 0.0)
end)

RegisterNUICallback('charCreator/loadClothes', function(data, cb)
    TriggerEvent('skinchanger:getData', function(skin, maxValues)
        local current_value, current_label = 0, ''

        for k, v in pairs(skin) do
            if v.name == data.type then
                current_value = v.value
                current_label = v.label
            end
        end

        cb({
            value_max = maxValues[data.type],
            current_value = current_value,
            current_label = current_label
        })
    end)
end)

RegisterNUICallback('charCreator/setClothes', function(data, cb)
    TriggerEvent('skinchanger:getData', function(skin, maxValues)
        local sex = getSex(skin)
        TriggerEvent('skinchanger:change', data.type, tonumber(data.value))

        cb({
            value_max = maxValues[data.type]
        })
    end)
end)

RegisterNUICallback('charCreator/finishChar', function(data, cb)
    local ped = PlayerPedId()
    TriggerServerEvent('cc_core:airport:setdim')
    RenderScriptCams(0)
    SetNuiFocus(false, false)
    SwitchOutPlayer(ped, 0, 1)
    Citizen.Wait(2500)

    local einreiseCoords = vector3(-1037.7399, -2737.7009, 20.1693)
    local einreiseHeading = 328.6832
    SetEntityCoords(ped, einreiseCoords.x, einreiseCoords.y, einreiseCoords.z)
    SetEntityHeading(ped, einreiseHeading)

    Citizen.Wait(250)

    SwitchInPlayer(ped)

    while IsPlayerSwitchInProgress() do
        Citizen.Wait(100)
    end

    isInUI = false

    FreezeEntityPosition(ped, false)
    DisplayRadar(true)

    charCreator = false

    TriggerEvent('skinchanger:getSkin', function(skin)
        TriggerServerEvent('cc_core:skin:save', skin)
    end)
end)

-- identity

RegisterNUICallback('identity/createChar', function(data, cb)
    TriggerServerEvent('cc_core:identity:setIdentity', data)
    SetNuiFocus(false, false)

    local ped = PlayerPedId()

    FreezeEntityPosition(ped, true)
    SetEntityCoords(ped, -811.8445, 175.1555, 76.7453)

    Citizen.Wait(250)

    SwitchInPlayer(ped)

    while IsPlayerSwitchInProgress() do
        Citizen.Wait(100)
    end

    SetNuiFocus(true, true)
    SendNUIMessage({
        script = 'charCreator',
        action = 'show'
    })

    CreationCamHead(vector3(-811.8445, 175.1555, 76.7453), 113.2220)
end)

RegisterNetEvent('cc_core:identity:resetSkin')
AddEventHandler('cc_core:identity:resetSkin', function()
    
    local ped = PlayerPedId()
    oldCoords = GetEntityCoords(ped)
    oldHeading = GetEntityHeading(ped)
    SwitchOutPlayer(ped, 0, 1)

    Wait(2500)

    FreezeEntityPosition(ped, true)
    SetEntityCoords(ped, -811.8445, 175.1555, 76.7453)

    Wait(250)

    SwitchInPlayer(ped)

    while IsPlayerSwitchInProgress() do
        Wait(100)
    end

    SetNuiFocus(true, true)
    SendNUIMessage({
        script = 'charCreator',
        action = 'show'
    })

    CreationCamHead(vector3(-811.8445, 175.1555, 76.7453), 113.2220)
end)

RegisterNUICallback('identity/leaveServer', function(data, cb)
    TriggerServerEvent('cc_core:identity:leaveServer')
end)

RegisterNetEvent('cc_core:identity:showIdentity')
AddEventHandler('cc_core:identity:showIdentity', function()
    
    isInIdentity = true
    charCreator = true
    local ped = PlayerPedId()
    local playerPed = PlayerPedId()
    oldCoords = GetEntityCoords(ped)
    oldHeading = GetEntityHeading(ped)
    DisplayRadar(false)

    isInUI = true

    SetEntityCoords(playerPed, -3703.2327, -3621.9167, 7.6143, true, false, false, false)
    SetEntityHeading(playerPed, 190.6434)
    cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
    local offset = GetOffsetFromEntityInWorldCoords(playerPed, 0, 1.7, 0.4)
    SetCamActive(cam, true)
    RenderScriptCams(true, false, 1, true, true)
    SetCamCoord(cam, offset.x, offset.y, offset.z)
    PointCamAtCoord(cam, -3703.2327, -3621.9167, 7.6143)

    hideHud(true)
    
    SetNuiFocus(true, true)
    
    SendNUIMessage({
        script = 'identity',
        action = 'show'
    })
end)
]]