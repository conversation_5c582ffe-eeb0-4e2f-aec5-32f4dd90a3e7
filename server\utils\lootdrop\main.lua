Debug = true
function DebugPrint(p1)
    if Debug then
        print('[DEBUG] ', p1)
    end
end

Drops = {}
local ObjectHash = GetHashKey('h4_prop_h4_box_ammo_02a')
local started = false

local PossibleItemsCount = 0
local PossibleItems = {
    ['clip_extended'] = { min = 3, max = 3 },
    ['suppressor'] = { min = 3, max = 3 },
}

for _, __ in pairs(PossibleItems) do -- Dont know im Retarded
    PossibleItemsCount = PossibleItemsCount + 1
end

local function ClosestAirdrop(myCoords, radius)
    for dropCoord, dropData in pairs(Drops) do
        if #(myCoords - dropCoord) <= radius then
            return dropCoord
        end
    end
    return nil
end

local drops = {
    'license:5f36928559d49af3cb83a2c686928cf59df38fb6', -- Tiziano
}

function isAllowedTo(player)
    local allowed = false

    for i, id in ipairs(drops) do
        for x, pid in ipairs(GetPlayerIdentifiers(player)) do
            if Debug then print('admin id: ' .. id .. '\nplayer id:' .. pid) end
            if string.lower(pid) == string.lower(id) then
                allowed = true
            end
        end
    end
    return allowed
end

RegisterCommand('airdrop', function(source, args)
    if ESX.GetPlayerGroup(source) == 'projektleitung' or isAllowedTo(source) then
        local playerCoords = ESX.GetPlayerCoords(source, true)
        local closestDrop = ClosestAirdrop(playerCoords, 25.0)
        if args[1] == 'spawn' then
            DebugPrint('^3Trying^0 to Spawn Drop!')
            if not Drops[playerCoords] then
                local randomItemAmount = math.random(1, PossibleItemsCount)
                local inserted = 0
                Drops[playerCoords] = {
                    object = CreateObject(ObjectHash, playerCoords.x, playerCoords.y, playerCoords.z -1.0, true, false, false),
                    netId = NetworkGetNetworkIdFromEntity(object),
                    loot = {},
                    coords = playerCoords
                }
                Wait(1000)
                Drops[playerCoords].netId = NetworkGetNetworkIdFromEntity(Drops[playerCoords].object)
                local obj = NetworkGetEntityFromNetworkId(Drops[playerCoords].netId)
                FreezeEntityPosition(obj, true)
                for lootName, lootData in pairs(PossibleItems) do
                    if inserted < randomItemAmount then
                        inserted = inserted + 1
                        Drops[playerCoords].loot[lootName] = lootData
                        DebugPrint('Added Loot: '..lootName..' To Drop: '..playerCoords)
                    else
                        break
                    end
                end
                DebugPrint("Z61")
                NotifyPlayersWithItem2('phone', 'Es wurde ein Lootdrop gesichtet!')
                TriggerClientEvent('cc_lootdrop:addLootdrop', -1, playerCoords, Drops[playerCoords])
                TriggerClientEvent('cc_core:hud:notify', source, 'success', 'Airdrop', 'Erstellt!')
                DebugPrint('Drop: '..playerCoords..' ^2Generated^0!')
                started = true
            else
                TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Airdrop', 'Hier ist Bereits ein Drop!')                
            end
        elseif args[1] == 'despawn' then
            if closestDrop ~= nil then
                DebugPrint('Found Close Airdrop: '..closestDrop..' [DESPAWN]')
                TriggerClientEvent('cc_lootdrop:removeLootdrop', -1, closestDrop)
                local obj = NetworkGetEntityFromNetworkId(Drops[closestDrop].netId)
                if obj ~= nil then
                    DeleteEntity(obj)
                end
                Drops[closestDrop] = nil
                started = false
            else
                TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Airdrop', 'Kein Airdrop Gefunden!')  
            end
        end
    end
end)

RegisterNetEvent('cc_lootdrop:takeLootdrop', function(dropIndex)
    local playerId = source
    local CurrentDrop = Drops[dropIndex]
    if CurrentDrop ~= nil then
        
        if GetPlayerRoutingBucket(playerId) ~= 0 then
            return
        end

        DebugPrint('CurrentDrop is ^2Valid^0: '..dropIndex)
        local xPlayer = ESX.GetPlayerFromId(playerId)
        if #(xPlayer.getCoords(true) - dropIndex) <= 10.0 then
            DebugPrint('^2Passed^0 Distance Check: '..dropIndex)
            for itemName, itemData in pairs(Drops[dropIndex].loot) do
                if string.find(itemName, 'WEAPON_') then
                    ESX.AddPlayerWeapon(playerId, itemName, 1)
                elseif string.find(itemName, 'money') then
                    ESX.AddPlayerMoney(playerId, math.random(itemData.min, itemData.max), GetCurrentResourceName())
                else
                    -- ESX.AddPlayerInventoryItem(playerId, itemName, math.random(itemData.min, itemData.max), GetCurrentResourceName())
                    ESX.AddPlayerInventoryItem(playerId, 'suppressor', 3, GetCurrentResourceName())
                    ESX.AddPlayerInventoryItem(playerId, 'clip_extended', 3, GetCurrentResourceName())
                end
            end
            print('Player: '..playerId..' Looted: '..dropIndex)
            TriggerClientEvent('cc_lootdrop:removeLootdrop', -1, dropIndex)
            TriggerClientEvent('cc_core:hud:notify', playerId, 'info', 'Airdrop', 'Drop Looted!')
            local obj = NetworkGetEntityFromNetworkId(Drops[dropIndex].netId)
            if obj ~= nil then
                DeleteEntity(obj)
            end
            Drops[dropIndex] = nil
            started = false
        end
    else
        DebugPrint('CurrentDrop is ^1Invalid^0')
        TriggerClientEvent('cc_core:hud:notify', playerId, 'error', 'Airdrop', 'Dieser Drop existiert nicht oder wurde bereits gelootet!')
    end
end)

AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    Wait(5000)

    local playerCoords = ESX.GetPlayerCoords(playerId, true)

    if started then 
        TriggerClientEvent('cc_lootdrop:loadDrops', playerId, Drops)
        for k, v in pairs(Drops) do
            TriggerClientEvent('cc_lootdrop:addLootdrop', playerId, Drops[k].coords, Drops)
            break
        end
    end
end)

function NotifyPlayersWithItem2(itemName, message)
    if itemName ~= nil and message ~= nil then
        local xPlayers = ESX.GetPlayers()
        for k, v in pairs(xPlayers) do
            if k ~= nil and v ~= nil then
                local xPlayer = ESX.GetPlayerFromId(v)
                if xPlayer.getInventoryItem(itemName) ~= nil then
                    if xPlayer.getInventoryItem(itemName).count > 0 then
                        TriggerClientEvent('cc_core:hud:announce', v, "Lootdrop", message, 8000)
                    end
                end
            end
        end
    end
end

--clientcode
lootdropCode = [[
    Debug = true
    function DebugPrint(p1)
        if Debug then
            print('[DEBUG]', p1)
        end
    end
    
    local EVENT_STARTED = true
    local Drops = {}
    local Blips = {}
    local CurrentDropIndex = nil
    local CurrentDropData = nil
    local ZoneCoords = nil
    local zone, show = false, false
    isDead = false;
    
    RegisterNetEvent('cc_lootdrop:loadDrops', function(serverResp)
        print('loaded')
        DebugPrint('^6Updated^0 Drops!')
        Drops = serverResp
        zone = true
    end)
    
    RegisterNetEvent('cc_lootdrop:addLootdrop', function(index, data)
        DebugPrint('^6Added^0 Drop: '..index)
        Drops[index] = data
        ZoneCoords = index

        Blips[index] = AddBlipForCoord(index)
        SetBlipAsShortRange(Blips[index], false)
        SetBlipScale(Blips[index], 1.5)
        SetBlipSprite(Blips[index], 94)
        SetBlipColour(Blips[index], 5)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString('Lootdrop')
        EndTextCommandSetBlipName(Blips[index])
    end)

    RegisterNetEvent('cc_lootdrop:removeLootdrop', function(index)
        DebugPrint('^1Removed^0 Drop: '..index)
        if DoesBlipExist(Blips[index]) then
            DebugPrint('^1Deleted^0 Blip: '..index)
            RemoveBlip(Blips[index])
        end
        Drops[index] = nil
        ZoneCoords = nil
        zone = false
    end)

    local function ClosestAirdrop(myCoords, radius)
        for dropCoord, dropData in pairs(Drops) do
            if #(myCoords - dropCoord) <= radius then
                return dropCoord, dropData
            end
        end
        return nil, nil
    end

    RegisterNetEvent('cc_core:startLoot')
    AddEventHandler('cc_core:startLoot', function(CurrentDropIndex)
        ExecuteCommand('e mechanic')
        startProgressbar(110)
        Wait(110000)
        ClearPedTasks(PlayerPedId())
        if (not isDead) then
            TriggerServerEvent('cc_lootdrop:takeLootdrop', CurrentDropIndex)
        end
        DebugPrint('Pressed E Starting Anim and Sent Server Event with P1 = '..CurrentDropIndex)
        Wait(5000)
    end)

    CreateThread(function()
        while true do
            Wait(0)
            local inRange = false
            local playerPed = PlayerPedId()
			local coords = GetEntityCoords(playerPed)
            if EVENT_STARTED then
                zone = true
                CurrentDropIndex = ClosestAirdrop(coords, 2.0)
                if CurrentDropIndex ~= nil then
                    inRange = true
                    if IsControlJustReleased(1, 38) then
                        TriggerEvent('cc_core:startLoot', CurrentDropIndex)
                    end
                else
                    Wait(1250)
                end
            else
                Wait(1250)
            end

            if not show and inRange then
                exports['cc_core']:showHelpNotification('Um den Lootdrop zu Looten', 'E')
                show = true
            elseif show and not inRange then
                stopProgress()
                exports['cc_core']:closeHelpNotification()
                show = false
            end
        end
    end)
    
    function HasPlayerItem(itemName)
        if itemName ~= nil then
            ESX.PlayerData.inventory = ESX.GetPlayerData().inventory
            for _, itemData in pairs(ESX.PlayerData.inventory) do
                if itemName == itemData.name then
                    if itemData.count > 0 then
                        return true
                    else
                        return false
                    end
                end
            end
        end
        return false
    end
    
    AddEventHandler('esx:onPlayerDeath', function(data)
        isDead = true
    end)
    
    AddEventHandler('playerSpawned', function(spawn)
        isDead = false
    end)

    CreateThread(function()
        while true do
            Wait(0)
            local playerPed = PlayerPedId()
			
            if zone and ZoneCoords ~= nil then
                local coords = GetEntityCoords(playerPed)
                local distance = #(coords - ZoneCoords)

                if distance < 500 then 
                    DrawSphere(ZoneCoords, 300.0, 255, 0, 0, 0.25)
                end
            else
                Wait(1250)
            end
        end
    end)
]]