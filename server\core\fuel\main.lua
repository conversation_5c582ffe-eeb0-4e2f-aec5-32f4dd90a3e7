local gasStations = {}

local function syncDataToDatabase()
    for k, v in pairs(gasStations) do
        MySQL.Async.execute('UPDATE gas_stations SET owner_id = @owner_id, bank_balance = @bank_balance, fuel = @fuel, price_liter = @price_liter, buy_price = @buy_price, name = @name, fuel_limit = @fuel_limit WHERE id = @id', {
			['@id'] = v.id,
            ['@owner_id'] = v.owner_id,
			['@bank_balance'] = v.bank_balance,
			['@fuel'] = v.fuel,
			['@price_liter'] = tonumber(v.price_liter) + 0.0,
			['@fuel_limit'] = tonumber(v.fuel_limit) + 0.0,
			['@buy_price'] = tonumber(v.buy_price) + 0.0,
			['@name'] = v.name
		})
        Citizen.Wait(2000)
    end
end

RegisterCommand('settanke', function(source, args)
    if source == 0 then
        local gasId = tonumber(args[1])
        local owner = args[2]

        gasStations[gasId].owner_id = owner
    end
end)

ESX.RegisterServerCallback('cc_core:fuel:requestGasStations', function(source, cb)
    cb(gasStations)
end)

RegisterServerEvent('cc_core:fuel:depositMoney')
AddEventHandler('cc_core:fuel:depositMoney', function(amount, id)
    local playerId = source

    if gasStations[id] ~= nil then
        if gasStations[id].owner_id ~= ESX.GetPlayerIdentifier(playerId) then
            return
        end

        if ESX.GetPlayerMoney(playerId) >= amount then
            ESX.RemovePlayerMoney(playerId, amount, GetCurrentResourceName())
            gasStations[id].bank_balance = gasStations[id].bank_balance + amount
            Notify(playerId, 'Tankstelle', 'Es wurde ' .. amount .. '$ auf das Konto der Tankstelle gebucht', 'info')
            TriggerClientEvent('cc_core:fuel:syncGasStations', playerId, gasStations)
        end
    end
end)

RegisterServerEvent('cc_core:fuel:withdrawMoney')
AddEventHandler('cc_core:fuel:withdrawMoney', function(amount, id)
    local playerId = source

    if gasStations[id] ~= nil then
        if gasStations[id].owner_id ~= ESX.GetPlayerIdentifier(playerId) then
            return
        end

        if gasStations[id].bank_balance - 15000 >= amount then
            ESX.AddPlayerMoney(playerId, amount, GetCurrentResourceName())
            gasStations[id].bank_balance = gasStations[id].bank_balance - amount
            Notify(playerId, 'Tankstelle', 'Es wurden ' .. amount .. '$ von dem Konto der Tankstelle abgebucht', 'info')
            TriggerClientEvent('cc_core:fuel:syncGasStations', playerId, gasStations)
        else
            Notify(playerId, 'Tankstelle', 'Es ist nicht genug Geld in der Kasse. Du musst 15k in der Kasse lassen', 'info')
        end
    end
end)

RegisterServerEvent('cc_core:fuel:setBuyPrice')
AddEventHandler('cc_core:fuel:setBuyPrice', function(amount, id)
    local playerId = source

    if amount > 40 then
        Notify(playerId, 'Tankstelle', 'Der Maximal mögliche Treibstoff Ankaufspreis ist 40$/L', 'info')
        return
    end

    if gasStations[id] ~= nil then
        if gasStations[id].owner_id ~= ESX.GetPlayerIdentifier(playerId) then
            return
        end
        if amount >= 5 then
            gasStations[id].buy_price = amount
            Notify(playerId, 'Tankstelle', 'Der Treibstoff Ankaufspreis wurde auf ' .. amount .. '$/L gesetzt', 'info')
            TriggerClientEvent('cc_core:fuel:syncGasStations', playerId, gasStations)
        else
            Notify(playerId, 'Tankstelle', 'Der Ankaufspreis muss min. 5$/L betragen', 'info')
        end
    end
end)

RegisterServerEvent('cc_core:fuel:setSellPrice')
AddEventHandler('cc_core:fuel:setSellPrice', function(amount, id)
    local playerId = source

    if amount > 40 then
        Notify(playerId, 'Tankstelle', 'Der Maximal mögliche Treibstoff Verkaufspreis ist 40$/L', 'info')
        return
    end

    if gasStations[id] ~= nil then
        if gasStations[id].owner_id ~= ESX.GetPlayerIdentifier(playerId) then
            return
        end

        gasStations[id].price_liter = amount
        Notify(playerId, 'Tankstelle', 'Der Treibstoff Verkaufstpreis wurde auf ' .. amount .. '$/L gesetzt', 'info')
        TriggerClientEvent('cc_core:fuel:syncGasStations', playerId, gasStations)
    end
end)

RegisterServerEvent('cc_core:fuel:setTanklimit')
AddEventHandler('cc_core:fuel:setTanklimit', function(amount, id)
    local playerId = source
    
    if amount > 4000 then
        Notify(playerId, 'Tankstelle', 'Das Maximal mögliche Treibstoff-Ankauf-Limit ist 4000 Liter', 'info')
        return
    end

    if gasStations[id] ~= nil then
        if gasStations[id].owner_id ~= ESX.GetPlayerIdentifier(playerId) then
            return
        end

        gasStations[id].fuel_limit = amount
        Notify(playerId, 'Tankstelle', 'Das Treibstoff-Ankauf-Limit wurde auf ' .. amount .. 'L gesetzt', 'info')
        TriggerClientEvent('cc_core:fuel:syncGasStations', playerId, gasStations)
    end
end)

RegisterServerEvent('cc_core:fuel:setStationName')
AddEventHandler('cc_core:fuel:setStationName', function(name, id)
    local playerId = source

    if gasStations[id] ~= nil then
        if gasStations[id].owner_id ~= ESX.GetPlayerIdentifier(playerId) then
            return
        end

        gasStations[id].name = name
        Notify(playerId, 'Tankstelle', 'Tankstellen-Name wurde gesetzt', 'info')
        TriggerClientEvent('cc_core:fuel:syncGasStations', playerId, gasStations)
    end
end)

local function playerOwnsGasStation(playerIdentifier)
    for _, station in pairs(gasStations) do
        if station.owner_id == playerIdentifier then
            return true
        end
    end
    return false
end

RegisterServerEvent('cc_core:fuel:buyGasStation')
AddEventHandler('cc_core:fuel:buyGasStation', function(id)
    local playerId = source
    local playerIdentifier = ESX.GetPlayerIdentifier(playerId)
    local playerIdentifier2 = playerIdentifier:gsub('"', '')

    if gasStations[id] ~= nil then
        if playerOwnsGasStation(playerIdentifier) then
            Notify(playerId, 'Tankstelle', 'Du besitzt bereits eine Tankstelle!', 'info')
            return
        end
        if gasStations[id].owner_id == 'none' then
            if ESX.GetPlayerMoney(playerId) >= gasStations[id].buyGasStationPrice then
                ESX.RemovePlayerMoney(playerId, gasStations[id].buyGasStationPrice, GetCurrentResourceName())
                gasStations[id].owner_id = playerIdentifier
                TriggerClientEvent('cc_core:fuel:syncGasStations', -1, gasStations)
                Notify(playerId, 'Tankstelle', 'Du hast dir eine Tankstelle gekauft!', 'success')
                exports['cc_core']:log(playerId, 'Shop Kauf', 'Der Spieler hat die Tankstelle mit der Id: ' .. id .. ' für ' .. gasStations[id].buyGasStationPrice .. ' gekauft!', 'https://canary.discord.com/api/webhooks/1337038596758310972/UH6CW1MEe0C9O715akmzulYbV2ltKFRdpJJ9d2yQM98y040bi29s7BZ_Fw8c8kth_APU')
            
                MySQL.Async.execute('UPDATE gas_stations SET `owner_id` = @owner WHERE id = @tankid', {
                    ['@tankid'] = id,
                    ['@owner'] = playerIdentifier2
                })
            else
                Notify(playerId, 'Tankstelle', 'Du hast nicht genug Geld dabei', 'info')
            end
        else
            Notify(playerId, 'Tankstelle', 'Die Tankstelle gehört schon jemanden!', 'info')
        end
    end
end)

RegisterServerEvent('cc_core:fuel:sellGasStation')
AddEventHandler('cc_core:fuel:sellGasStation', function(id)
    local playerId = source

    if gasStations[id] ~= nil then
        if gasStations[id].owner_id == ESX.GetPlayerIdentifier(playerId) then
            local price = gasStations[id].buyGasStationPrice * 0.75

            ESX.AddPlayerMoney(playerId, price, GetCurrentResourceName())
            gasStations[id].owner_id = 'none'
            Notify(playerId, 'Tankstelle', 'Du hast deine Tankstelle für ' .. price .. '$ an denn Staat verkauft!', 'info')
            TriggerClientEvent('cc_core:fuel:syncGasStations', -1, gasStations)
            exports['cc_core']:log(playerId, 'Shop Kauf', 'Der Spieler hat die Tankstelle mit der Id: ' .. id .. ' für ' .. gasStations[id].buyGasStationPrice * 0.75 .. ' verkauft!', 'https://canary.discord.com/api/webhooks/1337038596758310972/UH6CW1MEe0C9O715akmzulYbV2ltKFRdpJJ9d2yQM98y040bi29s7BZ_Fw8c8kth_APU')
        else
            Notify(playerId, 'Tankstelle', 'Die Tankstelle gehört nicht dir!', 'info')
        end
    end
end)

RegisterServerEvent('cc_core:fuel:removePetrolcan')
AddEventHandler('cc_core:fuel:removePetrolcan', function()
    local playerId = source

    if ESX.HasPlayerWeapon(playerId, 'WEAPON_PETROLCAN') then 
        ESX.RemovePlayerWeapon(playerId, 'WEAPON_PETROLCAN')
    end
end)

local function isFuelOwner(playerId, id)
    if gasStations[id] ~= nil then
        if gasStations[id].owner_id == ESX.GetPlayerIdentifier(playerId) then
            return true
        end
    end

    return false
end

exports('isFuelOwner', isFuelOwner)

RegisterServerEvent('cc_core:fuel:hireMember')
AddEventHandler('cc_core:fuel:hireMember', function(target, id)
    local playerId = source

    if gasStations[id] ~= nil then
        if gasStations[id].owner_id ~= ESX.GetPlayerIdentifier(playerId) then
            return
        end

        ESX.SetPlayerJob2(target, id, 0, 'fuel')
        Notify(target, 'Tankstelle', 'Du wurdest in einem Business eingestellt', 'info')
        Notify(playerId, 'Tankstelle', 'Du hast jemanden in einem Business eingestellt', 'info')
    end
end)

RegisterServerEvent('cc_core:fuel:sellLiters')
AddEventHandler('cc_core:fuel:sellLiters', function(liters, id)
    local playerId = source

    if gasStations[id] ~= nil then
        gasStations[id].fuel = gasStations[id].fuel - liters
        gasStations[id].bank_balance = gasStations[id].bank_balance + gasStations[id].price_liter * liters
    end
end)

RegisterServerEvent('cc_core:fuel:pay')
AddEventHandler('cc_core:fuel:pay', function(price)
    local playerId = source
    xPlayer = ESX.GetPlayerFromId(playerId)

    if price >= 0 and price <= 100000 then
        if xPlayer.getAccount("money").money < price then
            xPlayer.removeAccountMoney("bank", price)
        else
            xPlayer.removeAccountMoney("money", price)
        end
    end
end)

ESX.RegisterServerCallback('cc_core:fuel:gasDelivery', function(source, cb, amount, id)
    if GetPlayerRoutingBucket(source) ~= 0 then
		return
    end

	if amount >= 200000 then
		return
	end

    price = gasStations[id].buy_price * amount

    if gasStations[id].bank_balance - price > 0 and gasStations[id].fuel + amount < gasStations[id].fuel_limit then
        ESX.AddPlayerMoney(source, price, GetCurrentResourceName())
        gasStations[id].fuel = gasStations[id].fuel + amount
        gasStations[id].bank_balance = gasStations[id].bank_balance - price
        cb(false, gasStations[id].fuel)
    else
        cb(true, gasStations[id].fuel)
    end
end)

Citizen.CreateThread(function()
    while true do
        TriggerClientEvent('cc_core:fuel:syncGasStations', -1, gasStations)
        Citizen.Wait(1000 * 60 * 5)
        syncDataToDatabase()
    end
end)

AddEventHandler('txAdmin:events:scheduledRestart', function(eventData)
    if eventData.secondsRemaining == 60 then
        Citizen.Wait(30000)
        syncDataToDatabase()
    end
end)

MySQL.ready(function()
    MySQL.Async.fetchAll('SELECT gas_stations.*, users.firstname as firstname, users.lastname as lastname FROM gas_stations LEFT JOIN users ON gas_stations.owner_id = users.identifier', {}, function(result)
        if #result ~= 0 then
            for k, v in pairs(result) do
                gasStations[v.id] = v
            end
        end
    end)
end)

--clientcode
fuelCode = [[
local gasStations, blips = {}, {}
local show, display = false, false
local isNearPump, isFueling, IsPaused, inBlacklisted, fuelSynced = false, false, false, false, false
local currentFuel, currentCost = 0.0, 0.0

local isJobActive, isFilling, oilFillingCompleted, isEmptying, oilEmptyingCompleted, isGasFilling, gasFillingCompleted, isGasEmptying, gasEmptyingCompleted = false, false, false, false, false, false, false, false, false
local showStart, work1, work2, work3 = false, false, false, false
local truck, trailer = nil, nil
local currentTankerFuel, maximumTankerFuel = 0, 2000

local function haveBarMoney(price)
    for k, v in pairs(ESX.GetPlayerData().accounts) do
        if v.name == 'money' then
            if v.money >= price then
                return true
            end
        end
    end

    return false
end

local function getFuel(vehicle)
    return DecorGetFloat(vehicle, Config_Fuel.FuelDecor)
end

local function setFuel(vehicle, fuel)
    if type(fuel) == 'number' and fuel >= 0 and fuel <= 100 then
        SetVehicleFuelLevel(vehicle, fuel + 0.0)
        DecorSetFloat(vehicle, Config_Fuel.FuelDecor, GetVehicleFuelLevel(vehicle))
    end
end

local function DrawText3Ds(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)

    if onScreen then
        SetTextScale(0.35, 0.35)
		SetTextFont(4)
		SetTextProportional(1)
		SetTextColour(255, 255, 255, 215)
		SetTextEntry("STRING")
		SetTextCentre(1)
		AddTextComponentString(text)
		DrawText(_x,_y)
    end
end

local function FindNearestFuelPump()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
	local fuelPumps = {}
	local handle, object = FindFirstObject()
	local success

    repeat
		if Config_Fuel.PumpModels[GetEntityModel(object)] then
			table.insert(fuelPumps, object)
		end

		success, object = FindNextObject(handle, object)
	until not success

	EndFindObject(handle)

	local pumpObject = 0
	local pumpDistance = 1000

	for k, v in pairs(fuelPumps) do
		local distance = #(coords - GetEntityCoords(v))

		if distance < pumpDistance then
			pumpDistance = distance
			pumpObject = v
		end
	end

	return pumpObject, pumpDistance
end

local function ManageFuelUsage(vehicle)
    local status, error = pcall(function()
        if vehicle ~= nil then
            if not DecorExistOn(vehicle, Config_Fuel.FuelDecor) then
                setFuel(vehicle, math.random(200, 800) / 10)
            elseif not fuelSynced then
                setFuel(vehicle, getFuel(vehicle))
    
                fuelSynced = true
            end
    
            if IsVehicleEngineOn(vehicle) then
                setFuel(vehicle, GetVehicleFuelLevel(vehicle) - Config_Fuel.FuelUsage[round(GetVehicleCurrentRpm(vehicle, 1))] * (Config_Fuel.Classes[GetVehicleClass(vehicle)] or 1.0) / 10)
            end
        end
    end)

    if not status then
        print('ERROR: ' .. error)
    end
end

local function setDisplay()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local streetName = GetStreetNameFromHashKey(GetStreetNameAtCoord(coords.x, coords.y, coords.z))

    hideHud(true)

    SetNuiFocus(true, true)
    SendNUIMessage({
        script = 'fuel',
        type = 'fuel',
        action = 'ui',
        streetName = streetName
    })
end

local function createBlips()
    local status, error = pcall(function()
        RequestStreamedTextureDict("tanke", 1)

        while not HasStreamedTextureDictLoaded("tanke") do
            Citizen.Wait(0)
        end
    
        while true do
            Citizen.Wait(300)
            if IsPauseMenuActive() and not IsPaused then
                IsPaused = true
    
                if #blips ~= 0 then
                    for k, v in pairs(blips) do
                        RemoveBlip(v)
                    end
                end
    
                for k, v in pairs(gasStations) do
                    if v then
                        if v.firstname ~= nil and v.lastname ~= nil then
                            owner = v.firstname .. ' ' .. v.lastname
                        else
                            owner = 'Kein Besitzer'
                        end
        
                        local blip = AddBlipForCoord(v.coordinate_x, v.coordinate_y, v.coordinate_z)
                        
                        blips[k] = blip
        
                        SetBlipSprite(blip, 361)
                        SetBlipScale(blip, 0.5)
                        SetBlipColour(blip, 64)
                        SetBlipDisplay(blip, 4)
                        SetBlipAsShortRange(blip, true)
        
                        BeginTextCommandSetBlipName('STRING')
                        AddTextComponentString('Tankstelle')
                        EndTextCommandSetBlipName(blip)
        
                        SetBlipInfoTitle(blip, v.name, false)
                        SetBlipInfoEconomy(blip, '', tostring(v.buy_price))
        
                        AddBlipInfoName(blip, 'Besitzer', owner)
        
                        AddBlipInfoHeader(blip, 'Kauf-Preis', tostring(v.buy_price))
                        AddBlipInfoName(blip, 'Verkaufspreis-Preis', tostring(v.price_liter))
                        AddBlipInfoName(blip, 'Vorrat', tostring(v.fuel))
                        AddBlipInfoName(blip, 'Kapazität', tostring(v.fuel_limit))
                        SetBlipInfoImage(blip, 'tanke', 'tanke')
                    end 
                end

                Citizen.Wait(1000 * 10)
            elseif not IsPauseMenuActive() and IsPaused then
                IsPaused = false
            else
                Citizen.Wait(500)
            end
        end
    end)

    if not status then
        print(error)
    end
end

local function startFuelUpTick(pumpObject, ped, vehicle)
    local currentFuel = GetVehicleFuelLevel(vehicle)
    local currentGasStation = {}
    local currentGasStationIndex = nil
    local foundGasStation = false

    ESX.TriggerServerCallback('cc_core:fuel:requestGasStations', function(gasStation) 
        for k, v in pairs(gasStation) do
            print(k, v)
            print(json.encode(v))
            if v then
                if v ~= nil then
                    local coords = GetEntityCoords(ped)
                    local distance = #(coords - vector3(v.coordinate_x, v.coordinate_y, v.coordinate_z))
            
                    if distance <= 25.0 then
                        currentGasStation = v
                        foundGasStation = true
                    end
            
                    currentGasStationIndex = k     
                end
            end
        end
    end)

    while not foundGasStation do
        Citizen.Wait(50)
    end

    local enoughFuel = currentGasStation.fuel > 1

    while isFueling and enoughFuel do
        Citizen.Wait(150) --FUELING SPEED MEN

        local oldFuel = DecorGetFloat(vehicle, Config_Fuel.FuelDecor)
        local fuelToAdd = math.random(10, 20) / 10.0
        local extraCost = currentGasStation.price_liter * Config_Fuel.CostMultiplier

        if not pumpObject then
            if GetAmmoInPedWeapon(ped, GetHashKey('WEAPON_PETROLCAN') - fuelToAdd * 100 >= 0) then
                currentFuel = oldFuel + fuelToAdd

                SetPedAmmo(ped, GetHashKey('WEAPON_PETROLCAN'), math.floor(GetAmmoInPedWeapon(ped, GetHashKey('WEAPON_PETROLCAN')) - fuelToAdd * 100))
            else
                isFueling = false
            end
        else
            currentFuel = oldFuel + fuelToAdd
        end

        if currentFuel > 100.0 then
            currentFuel = 100.0
            isFueling = false
        end

        currentCost = currentCost + extraCost

        if haveBarMoney(currentCost) then
            setFuel(vehicle, currentFuel)
        else
            isFueling = false
        end

        TriggerServerEvent('cc_core:fuel:sellLiters', fuelToAdd, currentGasStation.id)
        currentGasStation.fuel = round(currentGasStation.fuel - fuelToAdd, 1)
        
        gasStations[currentGasStationIndex] = currentGasStation

        if currentGasStation.fuel < 1 then
            currentGasStation.fuel = false
        end
    end
end

local function refuelFromPump(pumpObject, ped, vehicle)
    local currentGasStation, time = nil, 0
    TaskTurnPedToFaceEntity(ped, vehicle, 1000)
    SetCurrentPedWeapon(ped, GetHashKey('WEAPON_UNARMED'), true)
	loadAnimDict("timetable@gardener@filling_can", function()
        TaskPlayAnim(ped, "timetable@gardener@filling_can", "gar_ig_5_filling_can", 2.0, 8.0, -1, 50, 0, 0, 0, 0)
        startFuelUpTick(pumpObject, ped, vehicle)
    
        while isFueling do
            for k, v in pairs(Config_Fuel.DisableKeys) do
                DisableControlAction(0, v)
            end
    
            local vehicleCoords = GetEntityCoords(vehicle)
    
            if not IsEntityPlayingAnim(ped, "timetable@gardener@filling_can", "gar_ig_5_filling_can", 3) then
                TaskPlayAnim(ped, "timetable@gardener@filling_can", "gar_ig_5_filling_can", 2.0, 8.0, -1, 50, 0, 0, 0, 0)
            end
    
            if IsControlJustReleased(0, 38) or DoesEntityExist(GetPedInVehicleSeat(vehicle, -1)) or isNearPump and GetEntityHealth(pumpObject) <= 0 then
                isFueling = false
            end
    
            Citizen.Wait(0)
        end
    end)

    ClearPedTasks(ped)
	RemoveAnimDict("timetable@gardener@filling_can")
end

local function setIndicator()
    SendNUIMessage({
        script = 'fuel',
        type = 'job',
        action = 'show',
        current = currentTankerFuel,
        max = maximumTankerFuel
    })
end

local function hideIndicator()
    SendNUIMessage({
        script = 'fuel',
        type = 'job',
        action = 'hide'
    })
end

local function startFillTicking(ped, oilfield)
    if not isFilling then
        if IsEntityAttachedToEntity(trailer, truck) and IsPedInVehicle(ped, truck, false) then
            Citizen.CreateThread(function()
                isFilling = true

                Notify('Information', 'Der Tanker wird nun mit Rohöl befüllt', 'info')

                while currentTankerFuel < maximumTankerFuel do
                    local coords = GetEntityCoords(ped)
                    local distance = #(coords - oilfield)

                    if distance <= 5.0 then
                        if currentTankerFuel < maximumTankerFuel then
                            if currentTankerFuel + 8 >= maximumTankerFuel then
                                currentTankerFuel = maximumTankerFuel
                            else
                                currentTankerFuel = currentTankerFuel + 8
                            end

                            setIndicator()
                        else
                            Notify('Information', 'Hänger vollgeladen. Los gehts zum raffinerieren', 'info')
                        end
                    else
                        Notify('Information', 'Du hast dich zu weit entfernt, der Befüllvorgang wurde abgebrochen', 'info')
                        isFilling = false
                        return
                    end

                    Citizen.Wait(500)
                end

                Notify('Information', 'Hänger vollgeladen. Los gehts zum raffinerieren', 'info')
                isFilling = false
                gasEmptyingCompleted = false
                oilFillingCompleted = true
            end)
        else
            Notify('Information', 'Du musst im Truck sitzen bleiben damit der Tanker befüllt werden kann', 'info')
        end
    end
end

local function startEmptyTicking(ped, oilrefinery)
    if not isEmptying and oilFillingCompleted then
        if IsEntityAttachedToEntity(trailer, truck) and IsPedInVehicle(ped, truck, false) then
            Citizen.CreateThread(function()
                isEmptying = true

                while currentTankerFuel > 0 do
                    local coords = GetEntityCoords(ped)
                    local distance = #(coords - oilrefinery)

                    if distance <= 5.0 then
                        if currentTankerFuel > 0 then
                            if currentTankerFuel - 8 <= 0 then
                                currentTankerFuel = 0
                            else
                                currentTankerFuel = currentTankerFuel - 8
                            end

                            setIndicator()
                        else
                            Notify('Information', 'Hänger entleert. Du kannst ihn jetzt mit Benzin füllen.', 'info')
                        end
                    else
                        Notify('Information', 'Du hast dich zu weit entfernt, der Befüllvorgang wurde abgebrochen', 'info')
                        isEmptying = false
                        return
                    end

                    Citizen.Wait(500)
                end

                Notify('Information', 'Hänger entleert. Du kannst ihn jetzt mit Benzin füllen.', 'info')
                isEmptying = false
                oilEmptyingCompleted = true
                oilFillingCompleted = false
            end)
        else
            Notify('Information', 'Du musst im Truck sitzen bleiben damit der Tanker entleert werden kann', 'info')
        end
    end
end

local function startGasTicking(ped, oilfield)
    if not isGasFilling and oilEmptyingCompleted then
        if IsEntityAttachedToEntity(trailer, truck) and IsPedInVehicle(ped, truck, false) then
            Citizen.CreateThread(function()
                isGasFilling = true

                while currentTankerFuel < maximumTankerFuel do
                    local coords = GetEntityCoords(ped)
                    local distance = #(coords - oilfield)

                    if distance <= 5.0 then
                        if currentTankerFuel < maximumTankerFuel then
                            if currentTankerFuel + 8 >= maximumTankerFuel then
                                currentTankerFuel = maximumTankerFuel
                            else
                                currentTankerFuel = currentTankerFuel + 8
                            end

                            setIndicator()
                        else
                            --Notify('Information', 'Hänger vollgeladen. Du kannst jetzt zur deine Tankstelle mit der Id ' .. ESX.PlayerData.job2.id .. ' beliefern', 'info')
                            Notify('Information', 'Hänger vollgeladen. Du kannst jetzt zur einer beliebigen Tankstelle Fahren', 'info')
                        end
                    else
                        Notify('Information', 'Du hast dich zu weit entfernt, der Tankvorgang wurde abgebrochen', 'info')
                        isGasFilling = false
                        return
                    end

                    Citizen.Wait(500)
                end

                --Notify('Information', 'Hänger vollgeladen. Du kannst jetzt zur deine Tankstelle mit der Id ' .. ESX.PlayerData.job2.id .. ' beliefern', 'info')
                Notify('Information', 'Hänger vollgeladen. Du kannst jetzt zur einer beliebigen Tankstelle Fahren', 'info')
                isGasFilling = false
                gasFillingCompleted = true
                oilEmptyingCompleted = false
            end)
        else
            Notify('Information', 'Du musst im Truck sitzen bleiben damit der Tanker befüllt werden kann', 'info')
        end
    end
end

local function startGasEmptyingTicking(ped, gasStation)
    if gasStation.fuel >= gasStation.fuel_limit then
        fuelLimitReached = true
    else
        fuelLimitReached = false
    end

    if not isGasEmptying and gasFillingCompleted then
        if IsEntityAttachedToEntity(trailer, truck) and IsPedInVehicle(ped, truck, false) then
            Citizen.CreateThread(function()
                isGasEmptying = true

                Notify('Information', 'Die Tankstelle wird nun mit Benzin befüllt', 'info')

                while currentTankerFuel > 0 and not fuelLimitReached do
                    local coords = GetEntityCoords(ped)
                    local distance = #(coords - vector3(gasStation.coordinate_x, gasStation.coordinate_y, gasStation.coordinate_z))

                    if distance <= 25.0 then
                        if currentTankerFuel > 0 then
                            if currentTankerFuel - 8 <= 0 then
                                fuelAmountThisTick = currentTankerFuel
                            else
                                fuelAmountThisTick = 8
                            end

                            ESX.TriggerServerCallback('cc_core:fuel:gasDelivery', function(serverFuelLimitReached, now) 
                                fuelLimitReached = serverFuelLimitReached

                                if fuelLimitReached then
                                    Notify('Information', 'Tankvorgang abgebrochen. Die Tankstelle nimmt kein Benzin mehr an', 'info')
                                    isGasEmptying = false
                                    return
                                else
                                    currentTankerFuel = currentTankerFuel - fuelAmountThisTick
                                    gasStation.fuel = now
                                    setIndicator()
                                end
                            end, fuelAmountThisTick, gasStation.id)
                        else
                            Notify('Information', 'Hänger entladen. Du kannst jetzt deinen Truck zum Anfangspunkt zurückbringen', 'info')
                        end
                    else
                        Notify('Information', 'Du hast dich zu weit entfernt, der Tankvorgang wurde abgebrochen', 'info')
                        isGasEmptying = false
                        return
                    end

                    Citizen.Wait(500)
                end

                Notify('Information', 'Hänger entladen. Du kannst jetzt deinen Truck zum Anfangspunkt zurückbringen', 'info')


                if not fuelLimitReached then
                    gasEmptyingCompleted = true
                    gasFillingCompleted = false
                end

                isGasEmptying = false
            end)
        else
            Notify('Information', 'Du musst im Truck sitzen bleiben damit die Tankstelle befüllt werden kann', 'info')
        end
    end
end

local function openOwnerMenu(gasStation)
    ESX.UI.Menu.CloseAll()
    local elements = {}

    if gasStation.owner_id == 'none' then
        elements = {
            { label = 'Tankstelle Kaufen - ' .. ESX.Math.GroupDigits(gasStation.buyGasStationPrice) .. '$', value = 'buyGasStation' }
        }
    else
        elements = {
            { label = 'Kontostand: ' .. gasStation.bank_balance .. '$', value = 'balance' },
            { label = 'Füllstand: ' .. gasStation.fuel .. ' Liter', value = 'fuel' },
            { label = 'Aufnahmestopp: ' .. gasStation.fuel_limit .. ' Liter', value = 'tanklimit' },
            { label = 'Kaufpreis für Benzin: ' .. gasStation.buy_price .. '$/L', value = 'setBuyPrice' },
            { label = 'Verkaufspreis für Benzin: ' .. gasStation.price_liter .. '$/L', value = 'setSellPrice' },
            { label = 'Tankstellen-Name: ' .. gasStation.name, value = 'setStationName' },
            { label = 'Geld einzahlen', value = 'deposit' },
            { label = 'Geld auszahlen', value = 'withdraw' },
            { label = 'Tankstelle verkaufen - ' .. ESX.Math.GroupDigits(gasStation.buyGasStationPrice * 0.75), value = 'sellGasStation' },
            --{ label = 'Mitglied Einstellen', value = 'hire_member' },
            --{ label = 'Mitgliederliste', value = 'member_list' }
        }
    end

    ESX.UI.Menu.Open('default', 'ownerGas', 'ownerGas', {
        ttle = 'Tankstelle',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        if data.current.value == 'buyGasStation' then
            menu.close()
            TriggerServerEvent('cc_core:fuel:buyGasStation', gasStation.id)
        elseif data.current.value == 'sellGasStation' then
            menu.close()
            TriggerServerEvent('cc_core:fuel:sellGasStation', gasStation.id)
        elseif data.current.value == 'tanklimit' then
            ESX.UI.Menu.Open('dialog', 'ownerGas', 'ownerGas_tanklimit', {
                title = 'Tankstopp bei'
            }, function(data2, menu2)
                local amount = tonumber(data2.value)

                TriggerServerEvent('cc_core:fuel:setTanklimit', amount, gasStation.id)

                menu2.close()
                menu.close()
            end, function(data2, menu2)
                menu2.close()
            end)
        elseif data.current.value == 'setBuyPrice' then
            ESX.UI.Menu.Open('dialog', 'ownerGas', 'ownerGas_buyprice', {
                title = 'Kaufpreis für Benzin'
            }, function(data2, menu2)
                local amount = tonumber(data2.value) + 0.0

                TriggerServerEvent('cc_core:fuel:setBuyPrice', amount, gasStation.id)

                menu2.close()
                menu.close()         
            end, function(data2, menu2)
                menu2.close()
            end)
        elseif data.current.value == 'setSellPrice' then
            ESX.UI.Menu.Open('dialog', 'red_gas', 'red_gas_sellprice', {
                title = 'Verkaufspreis für Benzin'
            }, function(data2, menu2)
                local amount = tonumber(data2.value) + 0.0

                TriggerServerEvent('cc_core:fuel:setSellPrice', amount, gasStation.id)

                menu2.close()
                menu.close()
            end, function(data2, menu2)
                menu2.close()
            end)
        elseif data.current.value == 'setStationName' then
            ESX.UI.Menu.Open('dialog', 'red_gas', 'red_gas_stationname', {
                title = 'Tankstellen-Name'
            }, function(data2, menu2)
                local name = tostring(data2.value)
                
                TriggerServerEvent('cc_core:fuel:setStationName', name, gasStation.id)

                menu2.close()
                menu.close()
            end, function(data2, menu2)
                menu2.close()
            end)
        elseif data.current.value == 'deposit' then
            ESX.UI.Menu.Open('dialog', 'ownerGas', 'ownerGas_deposit', {
                title = 'Einzahlungsbetrag'
            }, function(data2, menu2)
                local amount = tonumber(data2.value)

                TriggerServerEvent('cc_core:fuel:depositMoney', amount, gasStation.id)

                menu2.close()
                menu.close()
            end, function(data2, menu2)
                menu2.close()
            end)
        elseif data.current.value == 'withdraw' then
            ESX.UI.Menu.Open('dialog', 'ownerGas', 'ownerGas_withdraw', {
                title = 'Auszahlungsbetrag'
            }, function(data2, menu2)
                local amount = tonumber(data2.value)

                TriggerServerEvent('cc_core:fuel:withdrawMoney', amount, gasStation.id)

                menu2.close()
                menu.close()
            end, function(data2, menu2)
                menu2.close()
            end)
        elseif data.current.value == 'hire_member' then
            local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
			
            if closestPlayer ~= -1 and closestDistance < 3.0 then
                TriggerServerEvent("cc_core:fuel:hireMember", GetPlayerServerId(closestPlayer), gasStation.id)
            else
                menu.close()
                Notify('Tankstelle', 'Kein Spieler in deiner Nähe', 'info')
            end
        elseif data.current.value == 'member_list' then
            viewFuelMemberList(gasStation)
        end
    end, function(data, menu)
        menu.close()
    end)
end

function viewFuelMemberList(gasStation)
    ESX.TriggerServerCallback('cc_core:society:getEmployees_2', function(employees) 
        local elements = {
            head = { 'Mitarbeiter', 'Status', 'Tankstellen-Id', 'Aktionen' },
            rows = {}
        }

        for k, v in pairs(employees) do
            table.insert(elements.rows, {
                data = v,
                cols = {
                    v.name,
                    v.status,
                    gasStation.id,
                    '{{' .. 'feuern' .. '|fire}}'
                }
            })
        end

        ESX.UI.Menu.Open('list', GetCurrentResourceName(), 'employee_list_' .. ESX.GetPlayerData().job.name, elements, function(data, menu)
            local employee = data.data

            if data.value == 'fire' then
                Notify('Information', 'Du hast ' .. employee.name .. ' gefeuert', 'info')
                
                TriggerServerEvent('cc_core:society:setJob2', employee.identifier, 'unemployed', 0, 'fire', 'fuel', gasStation.id)
                
                Citizen.Wait(500)

                viewFuelMemberList(gasStation)
            end
        end, function(data, menu)
            menu.close()
            openOwnerMenu(gasStation)
        end)
    end, gasStation.id, 'fuel')
end

local function init()
    Citizen.CreateThread(function()
        local status, error = pcall(function()
            while true do
                Citizen.Wait(0)
                local ped = PlayerPedId()
                local coords = GetEntityCoords(ped)
                local letSleep, inRange, showUI = true, false, false
                local message = ''
    
                for k, v in ipairs(gasStations) do
                    if v then
                        if v.coordinate_x ~= nil and v.coordinate_y ~= nil and v.coordinate_z ~= nil then
                            local distance = #(coords - vector3(v.coordinate_x, v.coordinate_y, v.coordinate_z))
        
                            if distance <= 25.0 then
                                if not isFueling then
                                    if IsPedInAnyVehicle(ped, false) then
                                        letSleep = false
            
                                        SendNUIMessage({
                                            script = 'fuel',
                                            type = 'info',
                                            action = 'show',
                                            sell = v.price_liter,
                                            buy = v.buy_price,
                                            fuel = v.fuel
                                        })
                        
                                        showUI = true
                                    end
                                end
            
                                if ESX.PlayerData.identifier == v.owner_id then
                                    if isJobActive then
                                        letSleep = false
                                        inRange = true
            
                                        if not IsPedInAnyVehicle(ped, false) then
                                            message = 'Drücke F11 um deine Tankstelle zu verwalten'
                                            if IsControlJustReleased(1, 344) then
                                                openOwnerMenu(v)
                                            end
                                        else
                                            if IsControlJustReleased(1, 38) then
                                                startGasEmptyingTicking(PlayerPedId(), v)
                                            end
            
                                            message = 'Drücke E um die Tankstelle zu beliefern'
                                        end
                                    else
                                        inRange = true
                                        letSleep = false
                                        message = 'Drücke F11 um deine Tankstelle zu verwalten'
                                        
                                        if IsControlJustReleased(1, 344) then
                                            openOwnerMenu(v)
                                        end
                                    end
                                else
                                    if isJobActive then
                                        --if ESX.PlayerData.job2.type == 'fuel' and tostring(ESX.PlayerData.job2.id) == tostring(v.id) or tonumber(ESX.PlayerData.job2.id) == tonumber(v.id) then
                                            if IsPedInAnyVehicle(ped, false) then
                                                inRange = true
                                                letSleep = false
                                                message = 'Drücke E um die Tankstelle zu beliefern'
                        
                                                if IsControlJustReleased(1, 38) then
                                                    startGasEmptyingTicking(PlayerPedId(), v)
                                                end
                                            end  
                                        --end
                                    elseif v.owner_id == 'none' then
                                        inRange = true
                                        letSleep = false
                                        message = 'Drücke F11 um die Tankstelle zu kaufen'

                                        if IsControlJustReleased(1, 344) then
                                            openOwnerMenu(v)
                                        end
                                    end
                                end
                            end 
                        end 
                    end
                end
    
                helpNotify(inRange, show, message, function(bool)
                    show = bool
                end)
    
                if not showUI and not isFueling then
                    SendNUIMessage({
                        script = 'fuel',
                        type = 'info',
                        action = 'exit'
                    })
                end
    
                if letSleep then
                    Citizen.Wait(1000)
                end
            end
        end)

        if not status then
            print(error)
        end
    end)
end

Citizen.CreateThread(function()
    DecorRegister(Config_Fuel.FuelDecor, 1)

    for k, v in pairs(Config_Fuel.Blacklist) do
        if type(v) == 'string' then
            Config_Fuel.Blacklist[GetHashKey(v)] = true
        else
            Config_Fuel.Blacklist[v] = true
        end
    end

    for i = #Config_Fuel.Blacklist, 1, -1 do
        table.remove(Config_Fuel.Blacklist, i)
    end

    while true do
        Citizen.Wait(1000)

        local ped = PlayerPedId()

        if IsPedInAnyVehicle(ped) then
            local vehicle = GetVehiclePedIsIn(ped)

            if Config_Fuel.Blacklist[GetEntityModel(vehicle)] then
                inBlacklisted = true
            else
                inBlacklisted = false
            end

            if not inBlacklisted and GetPedInVehicleSeat(vehicle, -1) == ped then
                ManageFuelUsage(vehicle)
            end
        else
            if fuelSynced then
                fuelSynced = false
            end

            if inBlacklisted then
                inBlacklisted = false
            end
        end
    end
end)

Citizen.CreateThread(function()
    local status, error = pcall(function()
        while true do
            Citizen.Wait(1000)
    
            local pumpObject, pumpDistance = FindNearestFuelPump()
            
            if pumpDistance <= 2.5 then
                isNearPump = pumpObject
                
                local vehicle = GetPlayersLastVehicle()
    
                if vehicle ~= 0 then
                    currentFuel = round(GetVehicleFuelLevel(vehicle), 1)
                    local fuelToAdd = round(100 - currentFuel)
    
                    for k, v in pairs(gasStations) do
                        if v then
                            if v.coordinate_x ~= nil then
                                local ped = PlayerPedId()
                                local coords = GetEntityCoords(ped)
                                local distance = #(coords - vector3(v.coordinate_x, v.coordinate_y, v.coordinate_z))
                                
                                if distance <= 25.0 then
                                    currentGasStation = v
                                    SendNUIMessage({
                                        script = 'fuel',
                                        type = 'fuel',
                                        action = 'anzahl',
                                        current = currentFuel,
                                        need = fuelToAdd
                                    })
            
                                    SendNUIMessage({
                                        script = 'fuel',
                                        type = 'fuel',
                                        action = 'price',
                                        price = round(currentCost, 1)
                                    })
                                end     
                            end
                        end
                    end
                else
                    SendNUIMessage({
                        script = 'fuel',
                        type = 'fuel',
                        action = 'anzahl',
                        current = 'Kein Fahrzeug',
                        need = 'Kein Fahrzeug'
                    })
                end
            else
                Citizen.Wait(math.ceil(pumpDistance * 20))
            end
        end
    end)

    if not status then
        print(error)
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(250)

        local pumpObject, pumpDistance = FindNearestFuelPump()

        if pumpDistance <= 3.5 then
            isNearPump = pumpObject
        else
            isNearPump = false

            Citizen.Wait(math.ceil(pumpDistance * 20))
        end
    end
end)

local canabc = false

Citizen.CreateThread(function()
    local status, error = pcall(function()
        while true do
            Citizen.Wait(0)
            local ped = PlayerPedId()
    
            if not isFueling and isNearPump and GetEntityHealth(isNearPump > 0) or GetSelectedPedWeapon(ped) == GetHashKey('WEAPON_PETROLCAN') and not isNearPump then
                if IsPedInAnyVehicle(ped) and GetPedInVehicleSeat(GetVehiclePedIsIn(ped), -1) == ped then
                    local pumpCoords = GetEntityCoords(isNearPump)
                else
                    local vehicle = GetPlayersLastVehicle()
                    local vehicleCoords = GetEntityCoords(vehicle)
    
                    if DoesEntityExist(vehicle) and #(GetEntityCoords(ped) - vehicleCoords) < 3.5 and isNearPump then
                        if not DoesEntityExist(GetPedInVehicleSeat(vehicle, -1)) then
                            local stringCoords = GetEntityCoords(isNearPump)
    
                            if GetVehicleFuelLevel(vehicle) < 95 then
                                if haveBarMoney(1) then
                                    DrawText3Ds(stringCoords.x, stringCoords.y, stringCoords.z + 1.6, "Drücke ~g~E~w~ um das Tankmenü zu öffnen.")
    
                                    if IsControlJustReleased(0, 38) then
                                        isFueling = true
                                        setDisplay()
                                    end
                                else
                                    DrawText3Ds(stringCoords.x, stringCoords.y, stringCoords.z + 1.2, "~r~Du hast kein Geld bei dir, willst du mich bestehlen?")
                                end
                            else
                                DrawText3Ds(stringCoords.x, stringCoords.y, stringCoords.z + 1.6, "Du kannst keinen ~r~Treibstoff~w~ mehr tanken")
                            end
                        end
                    elseif DoesEntityExist(vehicle) and #(GetEntityCoords(ped) - vehicleCoords) < 2.5 and not isNearPump then
                        if GetSelectedPedWeapon(ped) == 883325847 then
                            local vehicleCoords = GetEntityCoords(vehicle)
                            currentFuelLevel = DecorGetFloat(vehicle, Config_Fuel.FuelDecor)
    
                            if currentFuelLevel < 50 then
                                DrawText3Ds(vehicleCoords.x, vehicleCoords.y, vehicleCoords.z + 1.6, "Drücke ~g~E~w~ um das Fahrzeug zu betanken")
    
                                if IsControlJustReleased(0, 38) then
                                    if not IsEntityPlayingAnim(ped, "timetable@gardener@filling_can", "gar_ig_5_filling_can", 3) then
                                        TaskPlayAnim(ped, "timetable@gardener@filling_can", "gar_ig_5_filling_can", 2.0, 8.0, 5000, 50, 0, 0, 0, 0)
                                    end
                                    
                                    if vehicle ~= nil and not canabc then
                                        canabc = true
                                        setFuel(vehicle, tonumber(currentFuelLevel) + 20)
                                        RemoveWeaponFromPed(ped, GetHashKey('WEAPON_PETROLCAN'))
                                        TriggerServerEvent('cc_core:fuel:removePetrolcan')
                                        Notify('Tankstelle', 'Dein Auto wurde um 20 Liter betankt', 'info')
                                        SetTimeout(4000, function()
                                            canabc = false
                                        end)
                                    end
                                end
                            end
                        end
                    else
                        Citizen.Wait(1000)
                    end
                end
            else
                Citizen.Wait(1000)
            end
        end 
    end)

    if not stauts then
        print(error)
    end
end)

Citizen.CreateThread(function()
    Citizen.Wait(500)
    for k,v in pairs(Config_Fuel.Zones) do
        local blip = AddBlipForCoord(v.coords)

        SetBlipSprite(blip, v.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.5)
        SetBlipColour(blip, v.color)
        SetBlipAsShortRange(blip, true)

        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(v.name)
        EndTextCommandSetBlipName(blip)
    end
end)

local function isOwnerOfFuel()
    for k, v in pairs(gasStations) do
        if v ~= nil then
            if v.owner_id ~= nil then
                print(ESX.GetPlayerData().identifier == v.owner_id)
                if ESX.GetPlayerData().identifier == v.owner_id then
                    return true
                end
            end
        end
    end

    return false
end

local function startFuelThread()
    while isJobActive do
        Citizen.Wait(10000)
        
        if DoesEntityExist(truck) then
            setFuel(truck, 100)
        else
            isJobActive = false
            
            if DoesEntityExist(trailer) then
                DeleteVehicle(trailer) 
            end

            isJobActive, isFilling, oilFillingCompleted, isEmptying, oilEmptyingCompleted, isGasFilling, gasFillingCompleted, isGasEmptying, gasEmptyingCompleted = false, false, false, false, false, false, false, false, false
            truck, trailer = nil, nil
            currentTankerFuel = 0
        end
    end
end

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local distance = #(coords - Config_Fuel.Spawn)
        local letSleep, inRange = true, false

        if distance <= 50.0 then
            letSleep = false
            DrawMarker(1, Config_Fuel.Spawn.x, Config_Fuel.Spawn.y, Config_Fuel.Spawn.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0, 3.0, 1.0, 140, 73, 184, 100, false, false, false, false, false, false, false)

            if distance <= 3.0 then
                inRange = true

                if IsControlJustReleased(1, 38) then
                    ESX.UI.Menu.CloseAll()

                    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'refiner', {
                        title = 'Arbeitsmenu',
                        align = 'top-left',
                        elements = {
                            { label = 'Schicht antreten', value = 'start' },
                            { label = 'Schicht beenden', value = 'end' }
                        }
                    }, function(data, menu)
                        if data.current.value == 'start' then
                            --if ESX.PlayerData.job2.type == 'fuel' or isOwnerOfFuel() then
                                Notify('Information', 'Dein LKW steht in der Umgebung für dich bereit', 'info')
                        
                                if not isJobActive then
                                    isJobActive = true
    
                                    ESX.Game.SpawnVehicle(GetHashKey('Packer'), vector3(1546.99, -2095.52, 76.79), 10.0, function(newVehicle)
                                        truck = newVehicle
                                        SetVehicleNumberPlateText(newVehicle, 'FinalU21')
                                        startFuelThread()
                                    end)
    
                                    Citizen.Wait(250)
    
                                    ESX.Game.SpawnVehicle(GetHashKey('Tanker'), vector3(1549.51, -2110.93, 76.82), 10.0, function(newVehicle)
                                        trailer = newVehicle
                                        SetVehicleNumberPlateText(newVehicle, 'FinalU21')
                                    end)
                                end
                            --else
                                --Notify('Information', 'Du bist in keinem Tankstellen Business angestellt', 'info')
                            --end
                        elseif data.current.value == 'end' then
                            if isJobActive then
                                DeleteVehicle(trailer)
                                DeleteVehicle(truck)
                                isJobActive, isFilling, oilFillingCompleted, isEmptying, oilEmptyingCompleted, isGasFilling, gasFillingCompleted, isGasEmptying, gasEmptyingCompleted = false, false, false, false, false, false, false, false, false
                                truck, trailer = nil, nil
                                currentTankerFuel = 0 
                            end
                        end
                        
                        menu.close()
                    end, function(data, menu)
                        menu.close()
                    end)
                end
            end
        end

        helpNotify(inRange, showStart, 'Drücke E um mit der Arbeit zu beginnen', function(bool)
            showStart = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local letSleep, inRange = true, false
        local message = ''

        for k, v in pairs(Config_Fuel.OilFields) do
            local distance = #(coords - v)

            if distance <= 20.0 then
                letSleep = false

                DrawMarker(1, v.x, v.y, v.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0, 3.0, 1.0, 140, 73, 184, 100, false, false, false, false, false, false, false)
            
                if distance <= 5.0 then
                    inRange = true

                    if IsPedInAnyVehicle(ped, false) then
                        message = 'Drücke E um deinen Tanker zu befüllen'
                        if IsControlJustReleased(1, 38) then
                            startFillTicking(ped, v)
                        end
                    else
                        message = 'Setze dich in deinen Truck um Rohöl in diesen zu laden'
                    end
                end
            end
        end

        helpNotify(inRange, work1, message, function(bool)
            work1 = bool
        end)
        
        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local letSleep, inRange = true, false
        local message = ''

        for k, v in pairs(Config_Fuel.OilRefineries) do
            local distance = #(coords - v)

            if distance <= 20.0 then
                letSleep = false

                DrawMarker(1, v.x, v.y, v.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0, 3.0, 1.0, 140, 73, 184, 100, false, false, false, false, false, false, false)
            
                if distance <= 5.0 then
                    inRange = true

                    if IsPedInAnyVehicle(ped, false) then
                        message = 'Drücke E um deinen Tanker zu entleeren'
                        if IsControlJustReleased(1, 38) then
                            startEmptyTicking(ped, v)
                        end
                    else
                        message = 'Setze dich in deinen Truck um Rohöl aus dem Tanker zu laden'
                    end
                end
            end
        end

        helpNotify(inRange, work2, message, function(bool)
            work2 = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local letSleep, inRange = true, false
        local message = ''

        for k, v in pairs(Config_Fuel.GasRefineries) do
            local distance = #(coords - v)

            if distance <= 20.0 then
                letSleep = false

                DrawMarker(1, v.x, v.y, v.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0, 3.0, 1.0, 140, 73, 184, 100, false, false, false, false, false, false, false)
            
                if distance <= 5.0 then
                    inRange = true

                    if IsPedInAnyVehicle(ped, false) then
                        message = 'Drücke E um deinen Tanker mit Benzin zu befüllen'
                        if IsControlJustReleased(1, 38) then
                            startGasTicking(ped, v)
                        end
                    else
                        message = 'Setze dich in deinen Truck um Benzin in diesen zu laden'
                    end
                end
            end
        end

        helpNotify(inRange, work3, message, function(bool)
            work3 = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        local ped = PlayerPedId()
        
        if isJobActive then
            if IsEntityAttachedToEntity(trailer, truck) and IsPedInVehicle(ped, truck, false) then
                setIndicator()
            else
                hideIndicator()
            end
        end
        
        Citizen.Wait(1000)
    end
end)

Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(50)    
    end

    while ESX.GetPlayerData().job == nil do
        Citizen.Wait(50)
    end

    Citizen.Wait(5000)

    if #gasStations == 0 then
        ESX.TriggerServerCallback('cc_core:fuel:requestGasStations', function(gasStation) 
            gasStations = gasStation
        end)
    end

    init()
    createBlips()
end)

RegisterNUICallback('fuel/escape', function(data, cb)
    SetNuiFocus(false, false)
    hideHud(false)
    isFueling = false
end)

RegisterNUICallback('fuel/main', function(data, cb)
    local ped = PlayerPedId()
    local vehicle = GetPlayersLastVehicle()
    refuelFromPump(isNearPump, ped, vehicle)
end)

RegisterNUICallback('fuel/pay', function(data, cb)
    TriggerServerEvent('cc_core:fuel:pay', currentCost)
    Notify('Information', 'Du hast ' .. currentCost .. '$ gezahlt!', 'info')
    currentCost = 0.0
end)

RegisterNetEvent('cc_core:fuel:syncGasStations')
AddEventHandler('cc_core:fuel:syncGasStations', function(gasStation)
    gasStations = gasStation
end)

exports('setFuel', setFuel)
exports('getFuel', getFuel)
]]