-- ====================================
-- <PERSON>RA<PERSON> SYSTEM CONFIGURATION
-- ====================================

Config = {}

-- ====================================
-- GENERAL SETTINGS
-- ====================================
Config.Locale = 'de'
Config.ImpoundPrice = 500
Config.MaxVehiclesPerPlayer = 50
Config.SaveVehiclePropsOnPark = true
Config.DeleteVehicleOnPark = true
Config.SpawnInVehicle = false

-- ====================================
-- GARAGE LOCATIONS
-- ====================================
Config.Garages = {
    -- LEGION SQUARE GARAGE
    {
        id = 'legion_garage',
        label = 'Legion Square Garage',
        type = 'car',
        npc = vector4(214.0629, -808.5296, 31.0149, 159.9741),
        spawnPoints = {
            vector4(234.5981, -800.2065, 30.4806, 69.6666),
            vector4(225.1100, -796.5797, 30.6597, 247.2508),
            vector4(218.9938, -794.1202, 30.7611, 70.8367),
            vector4(210.8125, -791.2595, 30.9068, 248.8980)
        },
        showBlip = true,
        showPed = true,
        blipSprite = 357,
        blipColor = 29,
        blipLabel = 'Auto Garage',
        pedModel = `s_m_y_dealer_01`,
        helpText = 'Drücke ~INPUT_CONTEXT~ um die Auto Garage zu öffnen',
        job = nil -- Für alle Spieler verfügbar
    },

    -- PILLBOX HILL GARAGE
    {
        id = 'pillbox_garage',
        label = 'Pillbox Hill Garage',
        type = 'car',
        npc = vector4(275.3989, -345.6006, 45.1734, 343.3773),
        spawnPoints = {
            vector4(266.4544, -332.3681, 44.9199, 245.2763),
            vector4(268.7554, -325.7990, 44.9199, 253.6077),
            vector4(270.9882, -319.1339, 44.9199, 251.4301),
            vector4(296.8174, -339.7888, 44.9199, 65.5982)
        },
        showBlip = true,
        showPed = true,
        blipSprite = 357,
        blipColor = 29,
        blipLabel = 'Auto Garage',
        pedModel = `s_m_y_dealer_01`,
        helpText = 'Drücke ~INPUT_CONTEXT~ um die Auto Garage zu öffnen',
        job = nil
    },

    -- SANDY SHORES GARAGE
    {
        id = 'sandy_garage',
        label = 'Sandy Shores Garage',
        type = 'car',
        npc = vector4(1728.0157, 2555.3877, 47.2158, 270.2846),
        spawnPoints = {
            vector4(1854.7400, 2560.0400, 45.6705, 270.2846),
            vector4(1854.6226, 2552.9136, 45.6720, 272.1149),
            vector4(1854.6888, 2545.5747, 45.6720, 270.4748),
            vector4(1870.4613, 2546.2976, 45.6720, 90.5515)
        },
        showBlip = true,
        showPed = true,
        blipSprite = 357,
        blipColor = 29,
        blipLabel = 'Auto Garage',
        pedModel = `s_m_y_dealer_01`,
        helpText = 'Drücke ~INPUT_CONTEXT~ um die Auto Garage zu öffnen',
        job = nil
    },

    -- PALETO BAY GARAGE
    {
        id = 'paleto_garage',
        label = 'Paleto Bay Garage',
        type = 'car',
        npc = vector4(83.5310, 6420.8638, 31.7605, 227.0100),
        spawnPoints = {
            vector4(72.3590, 6404.3960, 31.2258, 132.9888),
            vector4(78.2226, 6398.9849, 31.2258, 132.4984),
            vector4(62.3530, 6403.1616, 31.2258, 213.1104)
        },
        showBlip = true,
        showPed = true,
        blipSprite = 357,
        blipColor = 29,
        blipLabel = 'Auto Garage',
        pedModel = `s_m_y_dealer_01`,
        helpText = 'Drücke ~INPUT_CONTEXT~ um die Auto Garage zu öffnen',
        job = nil
    },

    -- HELICOPTER GARAGE - LSIA
    {
        id = 'lsia_heli_garage',
        label = 'LSIA Helikopter Garage',
        type = 'heli',
        npc = vector4(-1618.3894, -3147.0791, 13.9918, 46.0384),
        spawnPoints = {
            vector4(-1631.5092, -3143.6091, 13.9922, 333.8868),
            vector4(-1647.1177, -3133.9360, 13.9922, 332.4254),
            vector4(-1662.6598, -3124.5474, 13.9922, 334.6557)
        },
        showBlip = true,
        showPed = true,
        blipSprite = 43,
        blipColor = 29,
        blipLabel = 'Helikopter Garage',
        pedModel = `s_m_y_pilot_01`,
        helpText = 'Drücke ~INPUT_CONTEXT~ um die Helikopter Garage zu öffnen',
        job = nil
    },

    -- BOAT GARAGE - VESPUCCI
    {
        id = 'vespucci_boat_garage',
        label = 'Vespucci Boots Garage',
        type = 'boat',
        npc = vector4(-717.4523, -1326.5969, 1.5963, 52.4748),
        spawnPoints = {
            vector4(-729.8562, -1355.1234, 0.1500, 140.2981)
        },
        showBlip = true,
        showPed = true,
        blipSprite = 356,
        blipColor = 4,
        blipLabel = 'Boots Garage',
        pedModel = `s_m_y_marine_01`,
        helpText = 'Drücke ~INPUT_CONTEXT~ um die Boots Garage zu öffnen',
        job = nil
    },

    -- POLICE GARAGE (JOB RESTRICTED)
    {
        id = 'police_garage',
        label = 'Polizei Garage',
        type = 'car',
        npc = vector4(459.3323, -1000.2549, 25.6998, 359.6419),
        spawnPoints = {
            vector4(431.3468, -989.9656, 25.6998, 178.4069),
            vector4(438.2156, -989.7834, 25.6998, 178.4069)
        },
        showBlip = false,
        showPed = true,
        blipSprite = 357,
        blipColor = 38,
        blipLabel = 'Polizei Garage',
        pedModel = `s_m_y_cop_01`,
        helpText = 'Drücke ~INPUT_CONTEXT~ um die Polizei Garage zu öffnen',
        job = 'police'
    },

    -- MECHANIC GARAGE (JOB RESTRICTED)
    {
        id = 'mechanic_garage',
        label = 'Mechaniker Garage',
        type = 'car',
        npc = vector4(-1184.7976, -1510.1752, 4.6493, 302.2269),
        spawnPoints = {
            vector4(-1185.2499, -1493.5565, 4.3797, 123.6135),
            vector4(-1188.4480, -1488.1954, 4.3797, 125.3943),
            vector4(-1192.4357, -1483.0046, 4.3797, 124.6717)
        },
        showBlip = false,
        showPed = true,
        blipSprite = 357,
        blipColor = 5,
        blipLabel = 'Mechaniker Garage',
        pedModel = `s_m_y_construct_01`,
        helpText = 'Drücke ~INPUT_CONTEXT~ um die Mechaniker Garage zu öffnen',
        job = 'mechanic'
    }
}

-- ====================================
-- IMPOUND LOCATIONS
-- ====================================
Config.ImpoundLots = {
    -- MAIN IMPOUND LOT
    {
        id = 'main_impound',
        label = 'Hauptabschlepphof',
        npc = vector4(-193.2113, -1293.4053, 31.2960, 254.4378),
        spawnPoints = {
            vector4(-200.6298, -1298.7244, 31.2960, 168.1369),
            vector4(-216.4276, -1297.1694, 31.2960, 151.2937),
            vector4(-222.7800, -1290.9913, 31.2960, 187.1338)
        },
        showBlip = true,
        showPed = true,
        blipSprite = 67,
        blipColor = 1,
        blipLabel = 'Abschlepphof',
        pedModel = `s_m_y_dealer_01`,
        helpText = 'Drücke ~INPUT_CONTEXT~ um den Abschlepphof zu öffnen',
        price = 500
    },

    -- SANDY SHORES IMPOUND
    {
        id = 'sandy_impound',
        label = 'Sandy Shores Abschlepphof',
        npc = vector4(2765.3127, 1326.6973, 24.5240, 44.3802),
        spawnPoints = {
            vector4(2758.3281, 1330.2175, 24.5240, 4.4639)
        },
        showBlip = true,
        showPed = true,
        blipSprite = 67,
        blipColor = 1,
        blipLabel = 'Abschlepphof',
        pedModel = `s_m_y_dealer_01`,
        helpText = 'Drücke ~INPUT_CONTEXT~ um den Abschlepphof zu öffnen',
        price = 500
    }
}

-- ====================================
-- LOCALIZATION
-- ====================================
Config.Locales = {
    ['de'] = {
        ['garage_menu'] = 'Garage',
        ['impound_menu'] = 'Abschlepphof',
        ['spawn_vehicle'] = 'Fahrzeug ausholen',
        ['park_vehicle'] = 'Fahrzeug einparken',
        ['vehicle_spawned'] = 'Fahrzeug ~g~%s~s~ wurde ausgeparkt!',
        ['vehicle_parked'] = 'Fahrzeug ~g~eingeparkt~s~!',
        ['no_spawn_point'] = '~r~Alle Spawnpunkte sind belegt!',
        ['no_vehicles'] = '~r~Keine Fahrzeuge verfügbar',
        ['vehicle_impounded'] = 'Fahrzeug wurde abgeschleppt!',
        ['not_enough_money'] = '~r~Nicht genug Geld!',
        ['vehicle_released'] = 'Fahrzeug wurde freigegeben!',
        ['plate_taken'] = '~r~Kennzeichen bereits vergeben!',
        ['vehicle_added'] = 'Fahrzeug hinzugefügt!',
        ['vehicle_removed'] = 'Fahrzeug entfernt!'
    }
}

-- ====================================
-- FUNCTIONS
-- ====================================
function _U(str, ...)
    if Config.Locales[Config.Locale] and Config.Locales[Config.Locale][str] then
        return string.format(Config.Locales[Config.Locale][str], ...)
    else
        return 'Translation [' .. Config.Locale .. '][' .. str .. '] does not exist'
    end
end

print('[GARAGE] Konfiguration geladen!')
