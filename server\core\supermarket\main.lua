local loaded, loaded2 = false, false
local canShit = false
local shopItems = {
    supermarket = {

    },

    gunshop = {

    }
}

local items, shops, activeDeliver = {}, {}, {}

local function getItemData(shopId, itemName)
    for k, v in pairs(shops[shopId].items) do
        if v.name == itemName then
            return v
        end
    end

    return nil
end

local function getPriceByName(itemName)
    for k, v in pairs(Config_Supermarket.Items) do
        if v.itemName == itemName then
           return v.itemPrice, v.itemType
        end
    end

    return 0
end

local function getTypeByName(itemName)
    for k, v in pairs(Config_Supermarket.Items) do
        if v.itemName == itemName then
           return v.itemType 
        end
    end

    return 0
end

Citizen.CreateThread(function()
    for k, v in pairs(Config_Supermarket.Items) do
        if v.shopType == 'supermarket' then
            shopItems.supermarket[v.itemName] = {
                name = v.itemName,
                label = v.itemLabel,
                type = v.itemType,
                price = v.itemPrice,
                count = v.count,
                maxCount = v.maxCount
            }
        elseif v.shopType == 'gunshop' then
            shopItems.gunshop[v.itemName] = {
                name = v.itemName,
                label = v.itemLabel,
                type = v.itemType,
                price = v.itemPrice,
                count = v.count,
                maxCount = v.maxCount
            }
        end
    end
    
    local result = MySQL.query.await('SELECT shops.*, users.firstname as firstname, users.lastname as lastname FROM shops LEFT JOIN users ON shops.owner = users.identifier')

    Citizen.Wait(1000)

    for k, v in pairs(Config_Supermarket.Positions) do
        if #result == 0 then
            local items = ''

            if v.type == 'supermarket' then
                items = shopItems.supermarket
            elseif v.type == 'gunshop' then
                items = shopItems.gunshop
            end

            MySQL.Sync.execute('INSERT INTO shops (shoptype, items) VALUES (@shoptype, @items)', {
                ['@shoptype'] = v.type,
                ['@items'] = json.encode(items)
            })
        else
            shops[k] = {}

            if result[k] and result[k].owner == 'none' then
                shops[k].identifier = 'none'
                shops[k].owner = 'none'
            else
                if result[k] and result[k].owner ~= 'none' then
                    if result[k].firstname and result[k].lastname then
                        shops[k].owner = result[k].firstname .. ' ' .. result[k].lastname
                        shops[k].identifier = result[k].owner
                    else
                        shops[k].identifier = 'none'
                        shops[k].owner = 'none'
                    end
                else
                    shops[k].identifier = 'none'
                    shops[k].owner = 'none'
                end
            end

            shops[k].shopType = v.type
            shops[k].items = json.decode(result[k].items) 
            shops[k].balance = result[k].balance
            shops[k].name = result[k].name
            shops[k].buyPrice = result[k].buyPrice
            shops[k].price_delivery = result[k].price_delivery
        end 
    end

    print('shops loaded')

    Citizen.Wait(2000)
    
    while true do
        Citizen.Wait(1000 * 60 * 5)
        for k, v in pairs(Config_Supermarket.Positions) do
            MySQL.Async.execute('UPDATE shops SET owner = @owner, items = @items, balance = @balance, price_delivery = @price_delivery WHERE id = @id', {
                ['@items'] = json.encode(shops[k].items),
                ['@price_delivery'] = shops[k].price_delivery,
                ['@balance'] = shops[k].balance,
                ['@id'] = k,
                ['@owner'] = shops[k].identifier
            })

            Citizen.Wait(5000)
        end

        print(#Config_Supermarket.Positions .. ' shops synced to database')
    end
end)

RegisterCommand('setshop', function(source, args)
    if source == 0 then
        local shopId = tonumber(args[1])
        local owner = args[2]

        shops[shopId].identifier = owner
    end
end)

RegisterCommand('forcesaveshops', function(source)
    if source == 0 then
        for k, v in pairs(Config_Supermarket.Positions) do
            MySQL.Async.execute('UPDATE shops SET owner = @owner, items = @items, balance = @balance, price_delivery = @price_delivery WHERE id = @id', {
                ['@items'] = json.encode(shops[k].items),
                ['@price_delivery'] = shops[k].price_delivery,
                ['@balance'] = shops[k].balance,
                ['@id'] = k,
                ['@owner'] = shops[k].identifier
            })

            print(k, shops[k].balance)
    
            Citizen.Wait(100)
        end
    end
end)

-- /addItem supermarket switch Switch item 100 1000 1000
-- /addItem gunshop camperscanner Aufklärungsgerät item 2000 1000 1000
--addItem gunshop schweisgeraet Schweißgerät item 10000 500 1000

RegisterCommand('addItem', function(source, args)
    if source == 0 then
        for k, v in pairs(shops) do
            if v.shopType == args[1] then
                v.items[args[2]] = {
                    name = args[2],
                    label = args[3],
                    type = args[4],
                    price = tonumber(args[5]),
                    count = tonumber(args[6]),
                    maxCount = tonumber(args[7]),
                }

                print('item added ', args[2], args[3], args[4], args[5], args[6], args[7])
            end
        end
    end
end, true)

RegisterCommand('removeItem', function(source, args)
    if source == 0 then
        for k, v in pairs(shops) do
            if v.shopType == args[1] then
                if v.items[args[2]] ~= nil then
                    v.items[args[2]] = nil
                end
            end
        end
    end
end, true)

RegisterCommand('getItems', function(source, args)
    if source == 0 then
        for k, v in pairs(shops) do
            if v.shopType == args[1] then
                for k1, v1 in pairs(v.items) do
                    print(k, v1.name, v1.label, v1.count, v1.maxCount, v1.price, v1.type)
                end 
            end
        end
    end
end, true)

RegisterServerEvent('cc_core:supermarket:buy')
AddEventHandler('cc_core:supermarket:buy', function(type, basket, shopId)
    local playerId = source
    local price = 0

    if isRestart() then
        Notify(playerId, 'Shop', 'Du kannst 5 Minuten vor der Sonnenwende keine Sachen kaufen', 'info')
        return
    end

    for k, v in pairs(basket) do
        local pric = getPriceByName(v.name)
        price = price + pric * v.count
    end

    if price ~= 0 then
        if ESX.GetPlayerAccount(playerId, type).money >= price then
            for k, v in pairs(basket) do
                local itemType = getTypeByName(v.name)

                if itemType == 'item' then
                    ESX.AddPlayerInventoryItem(playerId, v.name, v.count, GetCurrentResourceName())
                elseif itemType == 'weapon' then
                    ESX.AddPlayerWeapon(playerId, v.name, 250, 100.0)
                end
            end

            ESX.RemovePlayerAccountMoney(playerId, type, price, GetCurrentResourceName())
            Notify(playerId, 'Shop', 'Du hast einen Einkauf für ' .. price .. '$ getätigt', 'success')
            shops[shopId].balance = shops[shopId].balance + price
        else
            Notify(playerId, 'Shop', 'Du kannst dir den Einkauf für ' .. price .. '$ nicht leisten', 'error')
        end
    end
end)

local function getGunShopPrice(shopId, name)
    for k, v in pairs(shops) do
        for k1, v1 in pairs(v.items) do
            if v1.name == name then
                return v1.price, v1.type
            end
        end
    end
end

RegisterServerEvent('cc_core:gunshop:buy')
AddEventHandler('cc_core:gunshop:buy', function(name, count, shopId)
    local playerId = source
    local price, type = getGunShopPrice(shopId, name)

    if isRestart() then
        Notify(playerId, 'Shop', 'Du kannst 5 Minuten vor der Sonnenwende keine Waffe kaufen', 'info')
        return
    end

    if ESX.GetPlayerMoney(playerId) >= price then
        local data = getItemData(shopId, name)
        data.count = tonumber(data.count)

        if data.count - count >= 0 then
            if type == 'weapon' then
                if not ESX.HasPlayerWeapon(playerId, name) then
                    shops[shopId].items[name].count = shops[shopId].items[name].count - 1
                    ESX.RemovePlayerMoney(playerId, price, GetCurrentResourceName())
                    ESX.AddPlayerWeapon(playerId, name, 250, 100.0)

                    Notify(playerId, 'Shop', 'Du hast dir eine ' .. ESX.GetWeaponLabel(name) .. ' für ' .. price .. '$ gekauft', 'info')

                    --price = price * 1.5
                    
                    shops[shopId].balance = shops[shopId].balance + price
                else
                    Notify(playerId, 'Shop', 'Du hast bereits die Waffe ' .. ESX.GetWeaponLabel(name) .. ' schon', 'info')
                end
            elseif type == 'item' then
                if ESX.PlayerCanCarryItem(playerId, name, count) then
                    price = price * count
                    shops[shopId].items[name].count = shops[shopId].items[name].count - tonumber(count)
                    ESX.RemovePlayerMoney(playerId, price, GetCurrentResourceName())
                    ESX.AddPlayerInventoryItem(playerId, name, count, GetCurrentResourceName())

                    Notify(playerId, 'Shop', 'Du hast dir ' .. count .. 'x ' .. ESX.GetItemLabel(name) .. ' für ' .. price .. '$ gekauft', 'info')

                    --price = price * 1.5
                    
                    shops[shopId].balance = shops[shopId].balance + price
                else
                    Notify(playerId, 'Shop', 'Du hast nicht genügend Platz im Inventar', 'info')
                end
            end
        else
            if type == 'weapon' then
                Notify(playerId, 'Shop', ESX.GetWeaponLabel(name) .. ' nicht mehr Vorrätig', 'info')
            elseif type == 'item' then
                Notify(playerId, 'Shop', ESX.GetItemLabel(name) .. ' nicht mehr Vorrätig', 'info')
            end
        end
    elseif ESX.GetPlayerAccount(playerId, 'bank').money >= price then
        local data = getItemData(shopId, name)
        data.count = tonumber(data.count)

        if data.count > 0 then
            if type == 'weapon' then
                if not ESX.HasPlayerWeapon(playerId, name) then
                    shops[shopId].items[name].count = shops[shopId].items[name].count - 1
                    ESX.AddPlayerWeapon(playerId, name, 250, 100.0)
                    ESX.RemovePlayerAccountMoney(playerId, 'bank', price, GetCurrentResourceName())

                    Notify(playerId, 'Shop', 'Du hast dir eine ' .. ESX.GetWeaponLabel(name) .. ' für ' .. price .. '$ gekauft', 'info')

                    --price = price * 0.60
                    
                    shops[shopId].balance = shops[shopId].balance + price
                else
                    Notify(playerId, 'Shop', 'Du hast bereits die Waffe ' .. ESX.GetWeaponLabel(name) .. ' schon', 'info')
                end
            elseif type == 'item' then
                if ESX.PlayerCanCarryItem(playerId, name, count) then
                    price = price * count
                    shops[shopId].items[name].count = shops[shopId].items[name].count - 1
                    ESX.AddPlayerInventoryItem(playerId, name, count, GetCurrentResourceName())
                    ESX.RemovePlayerAccountMoney(playerId, 'bank', price, GetCurrentResourceName())

                    Notify(playerId, 'Shop', 'Du hast dir ' .. count .. 'x ' .. ESX.GetItemLabel(name) .. ' für ' .. price .. '$ gekauft', 'info')

                    --price = price * 0.60
                    
                    shops[shopId].balance = shops[shopId].balance + price
                else
                    Notify(playerId, 'Shop', 'Du hast nicht genügend Platz im Inventar', 'info')
                end
            end
        else
            if type == 'weapon' then
                Notify(playerId, 'Shop', ESX.GetWeaponLabel(name) .. ' nicht mehr Vorrätig', 'info')
            elseif type == 'item' then
                Notify(playerId, 'Shop', ESX.GetItemLabel(name) .. ' nicht mehr Vorrätig', 'info')
            end
        end
    end
end)

RegisterServerEvent('cc_core:supermarket:changePrice')
AddEventHandler('cc_core:supermarket:changePrice', function(amount, shopId)
    local playerId = source

    if not isRestart() then
        if GetPlayerName(playerId) ~= nil then
            amount = tonumber(amount)
            
            if shops[shopId] ~= nil then
                if shops[shopId].identifier == ESX.GetPlayerIdentifier(playerId) then
                    shops[shopId].price_delivery = amount
    
                    Notify(playerId, 'Shop', 'Du hast erfolgreich denn Lieferpreis auf ' .. amount .. ' $ gesetzt', 'info')
                end
            end
        end
    else
        Notify(playerId, 'Shop', 'Du kannst 5 Minuten vor der Sonnenwende nicht denn Preis ändern', 'info')
    end
end)

local function playerOwnsShop(playerIdentifier)
    for _, shop in pairs(shops) do
        if shop.owner == playerIdentifier then
            return true
        end
    end
    return false
end

RegisterServerEvent('cc_core:supermarket:buyShop')
AddEventHandler('cc_core:supermarket:buyShop', function(shopId)
    local playerId = source
    local playerIdentifier = ESX.GetPlayerIdentifier(playerId)
    local playerIdentifier2 = playerIdentifier:gsub('"', '')

    if not isRestart() then
        if GetPlayerName(playerId) ~= nil then
            if shops[shopId] ~= nil then
                if playerOwnsShop(playerIdentifier) then
                    Notify(playerId, 'Shop', 'Du besitzt bereits einen Shop!', 'info')
                    return
                end
                print(shops[shopId].identifier)
                if shops[shopId].identifier == 'none' then
                    if ESX.GetPlayerMoney(playerId) >= shops[shopId].buyPrice then
                        ESX.RemovePlayerMoney(playerId, shops[shopId].buyPrice, GetCurrentResourceName())
                        shops[shopId].owner = ESX.GetPlayerRPName(playerId)
                        shops[shopId].identifier = playerIdentifier
                        Notify(playerId, 'Shop', 'Du hast dir ein Shop gekauft!', 'success')

                        MySQL.Async.execute('UPDATE shops SET `owner` = @owner WHERE id = @shopnumber', {
                            ['@shopnumber'] = shopId,
                            ['@owner'] = playerIdentifier2
                        })
                        triggerServerEvent('cc_core:society:setJob2', GetPlayerIdentifier(playerId), 'supermarket', shopId)
                        triggerClientEvent('cc_core:supermarket:syncShops', -1, shops)
                        exports['cc_core']:log(playerId, 'Shop Kauf', 'Der Spieler hat denn Shop mit der Id: ' .. shopId .. ' für ' .. shops[shopId].buyPrice .. ' gekauft!', 'https://discord.com/api/webhooks/1337040706467795049/gJ7S5iZ8iR3gYCFBoEx4cQEWN14azovxVMOm_X-Bvd2q5S9k75VZx56iQ8jEoyqp9a9b')
                    else
                        Notify(playerId, 'Shop', 'Du hast nicht genügend Geld dabei!', 'info')
                    end
                end
            end
        end
    else
        Notify(playerId, 'Shop', 'Du kannst 5 Minuten vor der Sonnenwende nicht denn Namen ändern', 'info')
    end
end)

RegisterServerEvent('cc_core:supermarket:sellShop')
AddEventHandler('cc_core:supermarket:sellShop', function(shopId)
    local playerId = source

    if not isRestart() then
        if GetPlayerName(playerId) ~= nil then
            if shops[shopId] ~= nil then
                if shops[shopId].identifier == ESX.GetPlayerIdentifier(playerId) then
                    local price = shops[shopId].buyPrice * 0.75
                    ESX.AddPlayerMoney(playerId, price, GetCurrentResourceName())
                    shops[shopId].owner = 'none'
                    shops[shopId].identifier = 'none'
                    Notify(playerId, 'Shop', 'Du hast dein Shop verkauft!', 'success')
                    TriggerClientEvent('cc_core:supermarket:syncShops', -1, shops)
                    exports['cc_core']:log(playerId, 'Shop Kauf', 'Der Spieler hat denn Shop mit der Id: ' .. shopId .. ' für ' .. shops[shopId].buyPrice * 0.75 .. ' verkauft!', 'https://canary.discord.com/api/webhooks/1378340984500391987/glytV7F9qz49K_0mfDh8Cd57dEj8aGAcXKu58pXLmaCMEqS4gtvKR8RviSXrYxdt02TD')
                end
            end
        end
    else
        Notify(playerId, 'Shop', 'Du kannst 5 Minuten vor der Sonnenwende nicht denn Namen ändern', 'info')
    end
end)

RegisterServerEvent('cc_core:supermarket:changeName')
AddEventHandler('cc_core:supermarket:changeName', function(name, shopId)
    local playerId = source

    if not isRestart() then
        if GetPlayerName(playerId) ~= nil then
            if shops[shopId] ~= nil then
                shops[shopId].name = name

                Notify(playerId, 'Shop', 'Du hast erfolgreich denn Namen von deinen Supermarket auf ' .. name .. ' geändert', 'info')
            end
        end
    else
        Notify(playerId, 'Shop', 'Du kannst 5 Minuten vor der Sonnenwende nicht denn Namen ändern', 'info')
    end
end)

ESX.RegisterServerCallback('cc_core:supermarket:requestMarkets', function(source, cb)
    cb(shops)
end)

ESX.RegisterServerCallback('cc_core:supermarket:canStart', function(source, cb, shopId, name, count, shopType)
    local canDeliver = false

    print('Try to delivery: ', shopId, name, count, shopType)

    if not isRestart() then
        if GetPlayerName(source) ~= nil then
            if activeDeliver[source] == nil then
                for k, v in pairs(shops[shopId].items) do
                    if v.name == name then
                        if v.count <= 500 - count then
                            local price = 0

                            if shopType == 'gunshop' then
                                price = getPriceByName(name) * count * 0.80
                            elseif shopType == 'supermarket' then
                                price = getPriceByName(name) * count * 0.80
                            end

                            print('Balance: ' .. shops[shopId].balance, 'Preis: ' .. price)
                            print(shops[shopId].balance >= price)
                            
                            if shops[shopId].balance >= price then
                                canDeliver = true
                                activeDeliver[source] = {}
                                activeDeliver[source].active = true
                                activeDeliver[source].name = name
                                activeDeliver[source].count = count
                                activeDeliver[source].price = price
                                break
                            end
                        end
                    end
                end
    
                cb(canDeliver)
            else
                cb(canDeliver)
            end
        end
    else
        print('is restart')
        cb(false) 
    end
end)

local function isShopOwner(playerId, shopId)
    if GetPlayerName(playerId) ~= nil then
        if shops[shopId] ~= nil then
            if shops[shopId].identifier == ESX.GetPlayerIdentifier(playerId) then
                return true
            end
        end
    end

    return false
end

exports('isShopOwner', isShopOwner)

ESX.RegisterServerCallback('cc_core:supermarket:isOwner', function(source, cb, shopId)
    if GetPlayerName(source) ~= nil then
        if shops[shopId] ~= nil then
            if shops[shopId].identifier == ESX.GetPlayerIdentifier(source) then
                cb(true, shops[shopId].balance)
            else
                cb(false, 0)
            end
        else
            cb(false, 0)
        end
    end
end)

RegisterServerEvent('cc_core:supermarket:deliver')
AddEventHandler('cc_core:supermarket:deliver', function(shopId)
    local playerId = source
    local itemCount = 0
    local can = false

    if GetPlayerName(playerId) ~= nil then
        if activeDeliver[playerId] ~= nil then
            if activeDeliver[playerId].active then
                for k, v in pairs(shops[shopId].items) do
                    if v.name == activeDeliver[playerId].name then
                        if v.count <= 500 then
                            if shops[shopId].balance >= activeDeliver[playerId].price then 
                                itemCount = v.count + activeDeliver[playerId].count
                            
                                if itemCount >= 500 then
                                    itemCount = 500
                                end
        
                                v.count = itemCount
                                can = true
                                break
                            else
                                can = false
                                break
                            end
                        else
                            can = false
                            break
                        end
                    end
                end
            end
            
            if can then
                local reward = activeDeliver[playerId].price
                ESX.AddPlayerMoney(playerId, reward, GetCurrentResourceName())
                Notify(playerId, 'Shop', 'Du hast für die Lieferung von ' .. activeDeliver[playerId].count .. 'x Items ' .. reward .. '$ bekommen', 'info')
                shops[shopId].balance = shops[shopId].balance - reward
            else
                Notify(playerId, 'Shop', 'In Der Kasse ist nicht genug Geld drinne oder Der Supermarket nimmt diese Lieferung nicht an, da er Überfüllt ist!', 'info')
                activeDeliver[playerId] = nil
            end
        else
            DropPlayer(playerId, 'Du wurdest gebannt')
        end
    end
end)

RegisterServerEvent('cc_core:supermarket:delay')
AddEventHandler('cc_core:supermarket:delay', function(networkId)
    local playerId = source

    if activeDeliver[playerId] ~= nil then
        activeDeliver[playerId] = nil
        SetTimeout(1000 * 60 * 5, function()
            local vehicle = NetworkGetEntityFromNetworkId(networkId)
            DeleteEntity(vehicle)
        end)
    end
end)

RegisterServerEvent('cc_core:supermarket:deposit')
AddEventHandler('cc_core:supermarket:deposit', function(amount, shopId)
    local playerId = source

    if not isRestart() then
        if GetPlayerName(playerId) ~= nil then
            amount = tonumber(amount)
    
            if ESX.GetPlayerMoney(playerId, amount) >= amount then
                if shops[shopId] ~= nil then
                    if shops[shopId].identifier == ESX.GetPlayerIdentifier(playerId) then
                        shops[shopId].balance = shops[shopId].balance + amount
    
                        Notify(playerId, 'Shop', 'Du hast erfolgreich ' .. amount .. '$ eingezahlt', 'info')
                        ESX.RemovePlayerMoney(playerId, amount, GetCurrentResourceName())
                    end
                end
            else
                Notify(playerId, 'Shop', 'Du hast nicht genügen Geld dabei', 'error')
            end
        end
    else
        Notify(playerId, 'Shop', 'Du kannst 5 Minuten vor der Sonnenwende kein Geld einzahlen', 'info') 
    end
end)

RegisterServerEvent('cc_core:supermarket:withdraw')
AddEventHandler('cc_core:supermarket:withdraw', function(amount, shopId)
    local playerId = source

    if not isRestart() then
        if GetPlayerName(playerId) ~= nil then
            amount = tonumber(amount)
    
            if shops[shopId] ~= nil then
                if shops[shopId].identifier == ESX.GetPlayerIdentifier(playerId) then
                    if shops[shopId].balance - 10000 >= amount then
                        shops[shopId].balance = shops[shopId].balance - amount
    
                        Notify(playerId, 'Shop', 'Du hast erfolgreich ' .. amount .. '$ ausgezahlt', 'info')
                        ESX.AddPlayerMoney(playerId, amount, GetCurrentResourceName()) 
                    else
                        Notify(playerId, 'Shop', 'Es ist nicht genug Geld in der Kasse. Du musst 10k in der Kasse lassen', 'info')
                    end
                end
            end
        end
    else
        Notify(playerId, 'Shop', 'Du kannst 5 Minuten vor der Sonnenwende kein Geld abbuchen', 'info')
    end
end)

RegisterServerEvent('cc_core:supermarket:hireMember')
AddEventHandler('cc_core:supermarket:hireMember', function(target, shopId)
    local playerId = source

    if GetPlayerName(target) ~= nil then
        if shops[shopId].identifier == ESX.GetPlayerIdentifier(source) then
            local distance = #(GetEntityCoords(GetPlayerPed(playerId)) - GetEntityCoords(GetPlayerPed(target)))
        
            if distance <= 3.0 then
                ESX.SetPlayerJob2(target, shopId, 0, 'supermarket')
                Notify(target, 'Shop', 'Du wurdest in einem Business eingestellt', 'info')
                Notify(playerId, 'Shop', 'Du hast jemanden in einem Business eingestellt', 'info')
            end
        else
            Notify(playerId, 'Shop', 'Du bist nicht der Shop Besitzter', 'info')
        end
    end
end)

ESX.RegisterServerCallback('cc_core:supermarket:getStock', function(source, cb, shopId)
    cb(shops[shopId])
end)

--clientcode
supermarketCode = [[
local show, show2, show3, add, remove, isOwner, isOwnerTrigger = false, false, false, true, false, false
local currentShopId, currentDelivery, blip, gotTruck = {}, {}, nil, nil, nil, true
local shopOwner = {}
local dcoord = nil

local function GetIntFromBlob(b, s, o)
	r = 0
	
    for i = 1, s, 1 do
		r = r | (string.byte(b, o + i) << (i - 1) * 8)
	end
	
    return r
end

local function GetWeaponHudStats(weaponHash, none)
	blob = '\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0'
	retval = Citizen.InvokeNative(0xD92C739EE34C9EBA, weaponHash, blob, Citizen.ReturnResultAnyway())
    hudDamage, hudSpeed, hudCapacity, hudAccuracy, hudRange = GetIntFromBlob(blob, 8, 0), GetIntFromBlob(blob, 8, 8), GetIntFromBlob(blob, 8, 16), GetIntFromBlob(blob, 8, 24), GetIntFromBlob(blob, 8, 32)
    
    return hudDamage, hudSpeed, hudCapacity, hudAccuracy, hudRange
end

local function getPriceByName(itemName)
    for k, v in pairs(Config_Supermarket.Items) do
        if v.itemName == itemName then
           return v.itemPrice, v.itemType
        end
    end

    return 0
end

local function AddItem(items)
    for k, v in pairs(items.items) do
        SendNUIMessage({
            script = 'supermarket',
            action = 'addItem',
            name = v.name,
            label = v.label,
            price = getPriceByName(v.name),
            stock = v.count
        }) 
    end
end

local function AddWeapon(items)
    for k, v in pairs(items.items) do
        if v.count >= 1 then
            local hudDamage, hudSpeed, hudCapacity, hudAccuracy, hudRange = GetWeaponHudStats(GetHashKey(v.name))
            SendNUIMessage({
                script = 'gunshop',
                action = 'addWeapon',
                name = v.name,
                label = v.label,
                price = v.price,
                stock = v.count,
                hudDamage = hudDamage,
                hudSpeed = hudSpeed,
                hudCapacity = hudCapacity,
                hudAccuracy = hudAccuracy,
                hudRange = hudRange
            }) 
        end
    end
end

local function RemoveItems()
    SendNUIMessage({
        script = 'supermarket',
        action = 'removeItems'
    })
end

local function LoadShopMenu(shopId, position, shopType)
    ESX.UI.Menu.CloseAll()
    local elements = {}
    local loaded = false

    if shopOwner[shopId] ~= nil then
        if shopOwner[shopId].identifier == 'none' then
            elements = {
                { label = 'Shop Kaufen - ' .. ESX.Math.GroupDigits(shopOwner[shopId].buyPrice) .. '$', value = 'buy_shop' },
                { label = 'Lieferung starten', value = 'start' }
            }

            loaded = true
        elseif shopOwner[shopId].identifier == ESX.PlayerData.identifier then
            ESX.TriggerServerCallback('cc_core:supermarket:isOwner', function(owner, balance)
                if owner then
                    elements = {
                        { label = 'Balance: ' .. balance .. '$', value = 'balance' },
                        { label = 'Einzahlen', value = 'deposit' },
                        { label = 'Auszahlen', value = 'withdraw' },
                        { label = 'Name Ändern', value = 'changename' },
                        { label = 'Shop Verkaufen - ' .. ESX.Math.GroupDigits(shopOwner[shopId].buyPrice * 0.75) .. '$', value = 'sell_shop' },
                        -- { label = 'Lieferungs Preis Ändern', value = 'changeprice' },
                        { label = 'Lieferung starten', value = 'start' },
                        --{ label = 'Mitglied Einstellen', value = 'hire_member' },
                        --{ label = 'Mitgliederliste', value = 'member_list' }
                    }
    
                    loaded = true
                end
            end, shopId)
        else
            elements = {
                { label = 'Lieferung starten', value = 'start' }
            }
            loaded = true
        end
    end

    while not loaded do
        Citizen.Wait(0)
    end

    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'shop_menu', {
        title = 'Supermarkt beliefern?',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        if data.current.value == 'buy_shop' then
            menu.close()
            TriggerServerEvent('cc_core:supermarket:buyShop', shopId)
        elseif data.current.value == 'sell_shop' then
            menu.close()
            TriggerServerEvent('cc_core:supermarket:sellShop', shopId)
        elseif data.current.value == 'start' then
            local elements = {}

            for k, v in pairs(Config_Supermarket.Items) do
                if v.shopType == shopType then
                    table.insert(elements, {
                        name = v.itemName,
                        label = v.itemLabel,
                        type = 'slider',
                        value = 1,
                        min = 1,
                        max = 250
                    })
                end
            end

            if shopType == 'gunshop' then
                table.insert(elements, {
                    name = 'medikit',
                    label = 'Medikit',
                    type = 'slider',
                    value = 1,
                    min = 1,
                    max = 250
                })
            end

            ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'shop_menu_step_2', {
                title = 'Supermarkt beliefern?',
                align = 'top-left',
                elements = elements
            }, function(data, menu)
                menu.close()

                ESX.TriggerServerCallback('cc_core:supermarket:canStart', function(can)
                    if can then
                        dcoord = getRandomKoordinate()
                        blip = AddBlipForCoord(dcoord)
                        SetBlipRoute(blip, true)
                        SetBlipColour(blip, 3)
                        SetBlipRouteColour(blip, 3)
                        Notify('Information', 'Begib dich zum Lager um dort die Lieferung von ' .. data.current.label .. ' ' .. data.current.name .. ' abzuholen', 'info')
                        currentDelivery = {}
                        currentDelivery.id = shopId
                        currentDelivery.coords = position
                        currentDelivery.vehicle = nil
                        gotTruck = false
                    else
                        Notify('Information', 'Derzeit kann keine Lieferung gestartet werden (Zu wenig Geld in der Kasse, Bestand ist schon aufm Maximum)!', 'info')
                    end
                end, shopId, data.current.name, data.current.value, shopType)
            end, function(data, menu)
                menu.close()
            end)
        elseif data.current.value == 'changename' then
            menu.close()

            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'changename', {
                title = "Name Ändern"
            }, function(data, menu)
                menu.close()

                TriggerServerEvent("cc_core:supermarket:changeName", data.value, shopId)
            end, function(data, menu)
                menu.close()
            end)
        elseif data.current.value == 'changeprice' then
            menu.close()

            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'changeprice', {
                title = "Geld Setzten"
            }, function(data, menu)
                if data.value == nil or data.value == "" then 
                    return 
                end

                menu.close()

                TriggerServerEvent("cc_core:supermarket:changePrice", data.value, shopId)
            end, function(data, menu)
                menu.close()
            end)
        elseif data.current.value == 'deposit' then
            menu.close()

            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'deposit', {
                title = "Geld einzahlen"
            }, function(data, menu)
                if data.value == nil or data.value == "" then 
                    return 
                end

                menu.close()

                TriggerServerEvent("cc_core:supermarket:deposit", data.value, shopId)
            end, function(data, menu)
                menu.close()
            end)
        elseif data.current.value == 'withdraw' then
            menu.close()

            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'withdraw', {
                title = "Geld auszahlen"
            }, function(data, menu)
                if data.value == nil or data.value == "" then 
                    return 
                end

                menu.close()

                TriggerServerEvent("cc_core:supermarket:withdraw", data.value, shopId)
            end, function(data, menu)
                menu.close()
            end)
        elseif data.current.value == 'hire_member' then
            local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
			
            if closestPlayer ~= -1 and closestDistance < 3.0 then
                TriggerServerEvent("cc_core:supermarket:hireMember", GetPlayerServerId(closestPlayer), shopId)
            else
                Notify('Shop', 'Kein Spieler in deiner Nähe', 'info')
            end
        elseif data.current.value == 'member_list' then
            viewSupermarketMemberList(shopId, position)
        elseif data.current.value == 'exit' then
            menu.close()
        end
    end, function(data, menu)
        menu.close()
    end)
end

local koordinatenListe = {
    vector3(143.2470, -3208.7439, 5.8576),
    vector3(2525.95, 2624.03, 37.94),
    vector3(170.02, 6360.58, 31.43),
    vector3(-3170.01, 1101.23, 20.75)
}

function getRandomKoordinate()
    local index = math.random(1, #koordinatenListe)
    return koordinatenListe[index]
end

function viewSupermarketMemberList(shopId, position, shopType)
    ESX.TriggerServerCallback('cc_core:society:getEmployees_2', function(employees) 
        local elements = {
            head = { 'Mitarbeiter', 'Status', 'Supermarket-Id', 'Aktionen' },
            rows = {}
        }

        for k, v in pairs(employees) do
            table.insert(elements.rows, {
                data = v,
                cols = {
                    v.name,
                    v.status,
                    shopId,
                    '{{' .. 'feuern' .. '|fire}}'
                }
            })
        end

        ESX.UI.Menu.Open('list', GetCurrentResourceName(), 'employee_list_' .. ESX.GetPlayerData().job.name, elements, function(data, menu)
            local employee = data.data

            if data.value == 'fire' then
                Notify('Information', 'Du hast ' .. employee.name .. ' gefeuert', 'info')
                
                TriggerServerEvent('cc_core:society:setJob2', employee.identifier, 'unemployed', 0, 'fire', 'supermarket', shopId)
                
                Citizen.Wait(500)

                viewMemberList(shopId, position)
            end
        end, function(data, menu)
            menu.close()
            LoadShopMenu(shopId, position, shopType)
        end)
    end, shopId, 'supermarket')
end

RegisterNUICallback('supermarket/buy', function(data, cb)
    SetNuiFocus(false, false)
    isInUI = false

    TriggerServerEvent('cc_core:supermarket:buy', data.type, data.basket, currentShopId)
end)

RegisterNUICallback('gunshop/buyWeapon', function(data, cb)
    SetNuiFocus(false, false)
    isInUI = false

    TriggerServerEvent('cc_core:gunshop:buy', data.name, data.count, currentShopId)
end)

Citizen.CreateThread(function()
    RequestModel(0x585C0B52)
    
    while not HasModelLoaded(0x585C0B52) do
        Citizen.Wait(100)
    end

    RequestStreamedTextureDict("tanke", 1)

    while not HasStreamedTextureDictLoaded("tanke") do
        Citizen.Wait(0)
    end

    while ESX == nil do
        Citizen.Wait(100)
    end

    ESX.TriggerServerCallback('cc_core:supermarket:requestMarkets', function(shops) 
        for k, v in pairs(Config_Supermarket.Positions) do
            if v.blip.enable then
                local blip = AddBlipForCoord(vector3(v.coords.x, v.coords.y, v.coords.z))
    
                SetBlipSprite(blip, v.blip.sprite)
                SetBlipScale(blip, v.blip.scale)
                SetBlipColour(blip, v.blip.colur)
                SetBlipDisplay(blip, v.blip.display)
                SetBlipAsShortRange(blip, true)
        
                BeginTextCommandSetBlipName('STRING')
                AddTextComponentString(v.blip.text)
                EndTextCommandSetBlipName(blip)
    
                --SetBlipInfoTitle(blip, shops[k].name, false)

                AddBlipInfoName(blip, 'Besitzer', shops[k].owner)

                shopOwner[k] = {}
                shopOwner[k].identifier = shops[k].identifier
                shopOwner[k].buyPrice = shops[k].buyPrice
    
                if v.type == 'supermarket' then
                    SetBlipInfoImage(blip, 'tanke', 'tanke')
                elseif v.type == 'gunshop' then
                    SetBlipInfoImage(blip, 'tanke', 'tanke')
                end
            end
    
            if v.npc.enable then
                exports['cc_core']:createPed(vector4(v.coords.x, v.coords.y, v.coords.z - 1.0, v.coords.w), 0x585C0B52)
            end
        end
    end)
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local name = ''

        for k, v in pairs(Config_Supermarket.Positions) do
            local distance = #(coords - vector3(v.coords.x, v.coords.y, v.coords.z))
            local playerCoords = GetEntityCoords(PlayerPedId())

            if distance <= 2.5 then
                letSleep = false
                inRange = true
                name = v.blip.text
                currentShopId = k

                if IsControlJustReleased(0, 38) and not isInUI then
                    -- NPCDialog Buttons definieren
                    local buttons = {
                        {name = "Shop öffnen", icon = "fas fa-store", func = "supermarket_open"},
                        {name = "Management öffnen", icon = "fas fa-cogs", func = "supermarket_management_open"},
                    }

                    local closest, coordfromnigg = getClosestDialogPed(playerCoords, 3.0)
                    ShowNPCDialog("Final U21", "Supermarkt", "Was möchtest du tun?", buttons, closest)
                end
            end
        end

        if not show2 and inRange then
            exports['cc_core']:showHelpNotification('Drücke E um auf den ' .. name .. ' zuzugreifen')
            show2 = true
        elseif show2 and not inRange then
            exports['cc_core']:closeHelpNotification()
            show2 = false
        end

        if letSleep then
            if not remove then
                RemoveItems()
                remove = true
            end
            add = false
            Citizen.Wait(1000)
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        if not gotTruck and blip ~= nil then
            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            local distance = #(coords - dcoord)
            local inRange = false

            if distance <= 30.0 then
                DrawMarker(nil, dcoord, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, false, false, false, false, false, false)

                if distance <= 2.0 then
                    inRange = true

                    if IsControlJustReleased(0, 38) then
                        ESX.Game.SpawnVehicle(GetHashKey('youga3'), dcoord, 267.5868, function(vehicle)
                            SetVehRadioStation(vehicle, 'OFF')
                            SetVehicleNumberPlateText(vehicle, 'FinalU21')
                            SetPedIntoVehicle(ped, vehicle, -1)
                            exports['cc_core']:setFuel(vehicle, 100)
                            currentDelivery.vehicle = vehicle

                            Citizen.Wait(1000)
                            RemoveBlip(blip)
                            blip = AddBlipForCoord(currentDelivery.coords)
                            SetBlipRoute(blip, true)
                            SetBlipColour(blip, 3)
                            SetBlipRouteColour(blip, 3)
                            Notify('Information', 'Begib dich zurück zum Shop um die Ware abzuliefern', 'info')
                            gotTruck = true
                        end)
                    end
                end

                if not show2 and inRange then
                    exports['cc_core']:showHelpNotification('Drücke E um dein Truck abzuliefern')
                    show2 = true
                elseif show2 and not inRange then
                    exports['cc_core']:closeHelpNotification()
                    show2 = false
                end
            else
                Citizen.Wait(1000)
            end
        elseif gotTruck and currentDelivery ~= nil then
            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            local distance = #(coords - vector3(currentDelivery.coords))
            local inRange = false
            local vehicle = GetVehiclePedIsIn(ped, false)

            if distance <= 30.0 and currentDelivery.vehicle == vehicle then
                inRange = true
                Citizen.Wait(0)

                if IsControlJustReleased(0, 38) then
                    if IsPedInAnyVehicle(ped) then
                        DeleteEntity(vehicle)
                    
                        TriggerServerEvent('cc_core:supermarket:deliver', currentDelivery.id)
                        currentDelivery = nil
                        RemoveBlip(blip)
                        Citizen.Wait(1000)

                        ESX.Game.SpawnVehicle(GetHashKey('bf400'), GetEntityCoords(PlayerPedId()), GetEntityHeading(PlayerPedId()), function(vehicle)
                            SetVehRadioStation(vehicle, 'OFF')
                            SetVehicleNumberPlateText(vehicle, 'FinalU21')
                            SetPedIntoVehicle(ped, vehicle, -1)
                            exports['cc_core']:setFuel(vehicle, 100)

                            Notify('Information', 'Du hast fürs Abliefern einer Lieferung eine BF400 für 5 Minuten bekommen!', 'info')

                            TriggerServerEvent('cc_core:supermarket:delay', VehToNet(vehicle))
                        end)
                    end
                end
            else
                Citizen.Wait(1000)
            end

            if not show3 and inRange then
                exports['cc_core']:showHelpNotification('Drücke E um deine Lieferung abzugeben')
                show3 = true
            elseif show3 and not inRange then
                exports['cc_core']:closeHelpNotification()
                show3 = false
            end
        else
            Citizen.Wait(1000)
            if show3 then
                exports['cc_core']:closeHelpNotification()
                show3 = false
            end
        end
    end
end)

RegisterNetEvent('cc_core:supermarket:syncShops')
AddEventHandler('cc_core:supermarket:syncShops', function(shop)
    --shops = shop
end)

RegisterNetEvent("cc_core:supermarket:openwhitnpcdialog")
AddEventHandler("cc_core:supermarket:openwhitnpcdialog", function()
    if currentShopId then
        local v = Config_Supermarket.Positions[currentShopId]
        local test = promise.new()

        ESX.TriggerServerCallback('cc_core:supermarket:getStock', function(items)
            if not add then
                if v.type ~= 'gunshop' then
                    AddItem(items)
                    add = true
                    remove = false
                else
                    AddWeapon(items)
                    add = true
                    remove = false
                end
            end

            test:resolve(true)
        end, currentShopId)

        Citizen.Await(test)

        SetNuiFocus(true, true)
        isInUI = true

        if v.type == 'gunshop' then
            SendNUIMessage({
                script = 'gunshop',
                action = 'open'
            })
        else
            SendNUIMessage({
                script = 'supermarket',
                action = 'open'
            })
        end
    end
end)

RegisterNetEvent("cc_core:supermarket:openshopwhitnpcdialog")
AddEventHandler("cc_core:supermarket:openshopwhitnpcdialog", function()
    if currentShopId then
        local shopData = Config_Supermarket.Positions[currentShopId]
        LoadShopMenu(currentShopId, shopData.menu, shopData.type)
    end
end)
]]