Config = {}

if not IsDuplicityVersion() then
    function Notify(title, message, type)
        TriggerEvent('cc_core:hud:notify', type, title, message)
    end
else
    function Notify(sendTo, title, message, type)
        TriggerClientEvent('cc_core:hud:notify', sendTo, type, title, message)
    end
    
    Config.BlacklistedStrings = {
        "<",
        ">",
        "script",
        "img",
        "video",
        "iframe",
        "audio",
        "mp3",
        "mp4",
        "ogg",
        "webm",
        "nigga",
        "nigger",
        "n1gga",
        "nlgga",
        "njgga",
    }
end