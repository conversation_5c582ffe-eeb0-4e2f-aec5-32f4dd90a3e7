-- ====================================
-- <PERSON><PERSON><PERSON> SYSTEM SQL STRUCTURE
-- ====================================
-- <PERSON><PERSON>elle die owned_vehicles Tabelle für das Garage-System
-- Diese Tabelle speichert alle Fahrzeuge der Spieler

CREATE TABLE IF NOT EXISTS `owned_vehicles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `owner` varchar(60) NOT NULL,
  `plate` varchar(12) NOT NULL,
  `vehicle` longtext DEFAULT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'car',
  `job` varchar(20) DEFAULT NULL,
  `stored` tinyint(1) NOT NULL DEFAULT 1,
  `parking` varchar(60) DEFAULT NULL,
  `pound` varchar(60) DEFAULT NULL,
  `glovebox` longtext DEFAULT NULL,
  `trunk` longtext DEFAULT NULL,
  `nickname` varchar(50) DEFAULT 'Fahrzeug',
  `fav` tinyint(1) DEFAULT 0,
  `tuningData` longtext DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `garage` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `plate` (`plate`),
  KEY `owner` (`owner`),
  KEY `stored` (`stored`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ====================================
-- SPALTEN ERKLÄRUNG:
-- ====================================
-- id: Eindeutige ID für jeden Eintrag
-- owner: Spieler Identifier (Steam, License, etc.)
-- plate: Fahrzeug Kennzeichen (eindeutig)
-- vehicle: JSON-Daten des Fahrzeugs (Eigenschaften, Farbe, etc.)
-- type: Fahrzeugtyp (car, heli, boat)
-- job: Job-Fahrzeug (falls zutreffend)
-- stored: Ob das Fahrzeug in der Garage ist (1) oder draußen (0)
-- parking: Parkplatz wo das Fahrzeug steht
-- pound: Abschlepphof wo das Fahrzeug ist
-- glovebox: Handschuhfach Inventar (JSON)
-- trunk: Kofferraum Inventar (JSON)
-- nickname: Benutzerdefinierter Name für das Fahrzeug
-- fav: Favorit-Status (1 = Favorit, 0 = Normal)
-- tuningData: Tuning-Daten des Fahrzeugs (JSON)
-- status: Status des Fahrzeugs (z.B. Fraktion)
-- garage: Garage-ID wo das Fahrzeug gespeichert ist

-- ====================================
-- BEISPIEL DATEN (Optional)
-- ====================================
-- INSERT INTO `owned_vehicles` (`owner`, `plate`, `vehicle`, `type`, `stored`, `nickname`) VALUES
-- ('steam:110000100000000', 'ABC 123', '{"model":**********,"plate":"ABC 123"}', 'car', 1, 'Mein Auto');
