Webhook = {
    Colors = {
        ["Main"] = 7495358,
    },
    group = "https://discord.com/api/webhooks/1337036943833436162/PWCf3MX9ndAn3MCha8N0eoCp48Q6rrfSbVruSsWUUYzm9APPIIHrsgTkynhCPOZejH5b",
}

function GetUserRole(source)
    local discordIdString = GetPlayerIdentifierByType(source, 'discord')

    if (not discordIdString) then
        return Config_ServerConfig.DefaultGroup
    end

    local discordId = string.sub(discordIdString, 9)

    local roles = exports[GetCurrentResourceName()]:getUserDiscordRoles(discordId, Config_ServerConfig.Guild, Config_ServerConfig.BotToken) -- 

    if roles then
        local discordGroupLookup = {}
        for _, value in pairs(Config_ServerConfig.Groups) do
            for _, discordGroup in pairs(value.discordGroups) do
                if (not discordGroupLookup[discordGroup]) then
                    discordGroupLookup[discordGroup] = value.name
                end
            end
        end

        for _, role in pairs(roles) do
            if discordGroupLookup[role] then
                return discordGroupLookup[role]
            end
        end
    end

    return Config_ServerConfig.DefaultGroup
end

AddEventHandler("esx:playerLoaded", function(id, xPlayer)
    local role = GetUserRole(id)
    local playerName = GetPlayerName(xPlayer.source)

    if (role ~= Config_ServerConfig.DefaultGroup) then
        if not string.match(playerName, "^F | ") then
            DropPlayer(id, "Du musst 'F | ' vor deinem Namen haben um zu joinen")
            print("^1[INFO] ^0"..playerName.." wurde gekickt da der Name nicht mit 'F | ' beginnt")
            return
        end
    end

    if (role ~= Config_ServerConfig.DefaultGroup) then
        SendWebHookWithPlayerID(Webhook.group, "Role Sync",
            string.format("%s hat sich am <t:%s:F> eingeloggt und die Gruppe ``%s`` zugewiesen bekommen.",
                GetPlayerName(xPlayer.source), os.time(), role),
            Webhook.Colors["Main"],
            xPlayer.source)
    end
    print("^2[SUCCESS] ^0"..GetPlayerName(xPlayer.source).." ["..id.."] hat erfolgreich die Gruppe "..role.." zugewiesen bekommen.")
    xPlayer.setGroup(role)
end)

function SendWebHookWithPlayerID(webhooklink, title, message, color, playerID, additionalPlayerID)
    local embedMsg = {}

    local primaryIdentifiers = GetPlayerIdentifiers(playerID)

    local filteredPrimaryIdentifiers = {}
    for _, identifier in ipairs(primaryIdentifiers) do
        if not string.match(identifier, "^ip:") then
            table.insert(filteredPrimaryIdentifiers, identifier)
        end
    end

    local additionalIdentifiers = {}
    if additionalPlayerID then
        additionalIdentifiers = GetPlayerIdentifiers(additionalPlayerID)
    end

    local filteredAdditionalIdentifiers = {}
    for _, identifier in ipairs(additionalIdentifiers) do
        if not string.match(identifier, "^ip:") then
            table.insert(filteredAdditionalIdentifiers, identifier)
        end
    end

    local discordIDPrimary = ""
    for _, identifier in ipairs(filteredPrimaryIdentifiers) do
        if string.match(identifier, "^discord:") then
            discordIDPrimary = string.sub(identifier, 9)
            break
        end
    end

    local discordIDAdditional = ""
    for _, identifier in ipairs(filteredAdditionalIdentifiers) do
        if string.match(identifier, "^discord:") then
            discordIDAdditional = string.sub(identifier, 9)
            break
        end
    end

    local primaryIdentifierString = ""
    if playerID ~= nil then
        primaryIdentifierString = "Player Identifiers: \n```" ..
            table.concat(filteredPrimaryIdentifiers, ", \n") .. "```\nDiscord Ping: <@" .. discordIDPrimary .. ">"
    else

    end

    local additionalIdentifierString = ""
    if additionalPlayerID then
        additionalIdentifierString = "\n\nAdditional Player Identifiers: \n```" ..
            table.concat(filteredAdditionalIdentifiers, ", \n") .. "```\nDiscord Ping: <@" .. discordIDAdditional .. ">"
    end

    local identifierString = primaryIdentifierString .. additionalIdentifierString

    embedMsg = {
        {
            ["color"] = color,
            ["title"] = title,
            ["description"] = message .. "\n\n" .. identifierString,
            ["author"] = {
                ["name"] = "Final Allstars Mod",
                ["icon_url"] =
                "https://cdn.discordapp.com/attachments/1024020851068260382/1352424437030453289/PROFILBILD.gif?ex=67df4868&is=67ddf6e8&hm=57431cd82b8706bf7dfc7a81f9fd6b015e611a63a9d584a8f8da1e37e8da553f&"
            },
            ["footer"] = {
                ["text"] = "© Final Allstars 2025",
                ["icon_url"] =
                "https://cdn.discordapp.com/attachments/1024020851068260382/1352424437030453289/PROFILBILD.gif?ex=67df4868&is=67ddf6e8&hm=57431cd82b8706bf7dfc7a81f9fd6b015e611a63a9d584a8f8da1e37e8da553f&"
            },
            ["image"] =
            {
                ["url"] =
                "https://cdn.discordapp.com/attachments/1024020851068260382/1352797138375147620/ESXBANNER.gif?ex=67df5203&is=67de0083&hm=39d4c7c7ed750019818347f64a9afaed7cc6612523da9866c3d6db2229797c03&"
            }
        }
    }

    PerformHttpRequest(webhooklink,
        function(err, text, headers) end, 'POST',
        json.encode({
            username = "Final Allstars Mod",
            embeds =
                embedMsg
        }), { ['Content-Type'] = 'application/json' }) -- 
end