Citizen.CreateThread(function()
    for k, v in pairs(Config_Blips.Blips) do
        local blip = AddBlipForCoord(v.coords)

        SetBlipSprite(blip, v.sprite)
        SetBlipDisplay(blip, v.display)
        SetBlipScale(blip, v.scale)
        SetBlipColour(blip, v.color)
        SetBlipAsShortRange(blip, v.shortRange)

        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(v.text)
        EndTextCommandSetBlipName(blip)
    end
end)