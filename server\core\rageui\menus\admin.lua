adminmenuCode = [[
	Player2 = {
		useKeys = false,
		noclip = false,
		showName = false,
		gamerTags = {},
		group = 'user',
		NoclipSpeed = 1.0
	}

	ParticleAdmins = {
		['8bf06e88eb1f9a73c51baf4607825d27954054ab'] = {
			dict = 'scr_rcbarry2',
			particle = 'scr_clown_death'
		},
		['deaad03b72ca13a99fea194fa66ae894397cbf15'] = {
			dict = 'scr_rcbarry2',
			particle = 'scr_clown_death'
		},
		-- ['8bf06e88eb1f9a73c51baf4607825d27954054ab'] = {
		-- 	dict = 'scr_rcbarry2',
		-- 	particle = 'scr_clown_death'
		-- },
		-- ['8bf06e88eb1f9a73c51baf4607825d27954054ab'] = {
		-- 	dict = 'scr_josh3',
		-- 	particle = 'scr_josh3_light_explosion'
		-- },
		['a677382412bc44654b681280b68c6b0e54fbb164'] = {
			dict = 'scr_rcbarry1',
			particle = 'scr_alien_teleport'
		}
	}

	CreateThread(function()
		while not ESXLoaded do
			Wait(10)
		end

		if ESX.GetPlayerData().group ~= 'user' then
			Player2.group = tostring(ESX.GetPlayerData().group)
		else
			Player2.group = 'user'
		end
	end)


	local localvanish = false
	local vanish = false

	function toggleVanish()
		vanish = not vanish
		local playerPed = PlayerPedId() 

		if vanish then
			SetEntityVisible(playerPed, false, false) 
			TriggerServerEvent('adutylog', false, true, false, false, false, false)
			localvanish = true
		elseif not vanish then
			localvanish = false
			SetEntityVisible(playerPed, true, false)
			TriggerServerEvent('adutylog', false, false, false, false, true, false)
		end
	end

	-- CreateThread(function()
	-- 	while true do
	-- 		local sleep = 0
	--         --print(localvanish)
	-- 		if localvanish then
	-- 			SetEntityVisible(PlayerPedId(), false, false)
	--         elseif localvanish == false then
	-- 			SetEntityVisible(PlayerPedId(), true, false)
	--         else

	-- 		end
	-- 		Wait(sleep)
	-- 	end
	-- end)

	local AdminMenuMain = RageUI.CreateMenu('', 'Admin Menu')
	local AdminMenuSubAdminMenu = RageUI.CreateSubMenu(AdminMenuMain, '', 'Allgemein')
	local AdminMenuSubMenumanagement = RageUI.CreateSubMenu(AdminMenuMain, '', 'Rückerstattungs Menu')
	local AdminMenuSubMenuVehicle = RageUI.CreateSubMenu(AdminMenuMain, '', 'Fahrzeug Menu')

	--NEW SHIT
	local BossMainMenu = RageUI.CreateSubMenu(AdminMenuMain, '', '                  Finalu21 #1')
	local BossMenuPlayerMenu = RageUI.CreateSubMenu(BossMainMenu, '', '                  Finalu21 #1')
	local GodModeOn = false
	local ChairOn = false
	local PlayersInSync = nil
	local PlayerDesc = ''
	SelectedPlayer = {}
	SelectedPlayer.Action = {}
	SelectedPlayer.Index = {}
	SelectedPlayer.Name = {}

	AdminMenuMain.X = 0
	AdminMenuMain.Y = 0

	local AdutyActive = false
	local NameTagsActive = false
	local InvisibleActive = false
	local AdminKeyActive = false
	local NoClipSpeedIndex = 1


	local function isInAduty()
		return AdutyActive
	end

	exports('isInAduty', isInAduty)

	CreateThread(function()
		while not ESXLoaded do
			Wait(5000)
		end

		if Player2.group ~= 'user' and Player2.group ~= 'analyst' then
			function RageUI.PoolMenus:AdminMenu()
				AdminMenuMain:IsVisible(function(Items)
					-- if not ESXLoaded then
					--     Wait(1000)
					-- end
					Items:AddSeparator('Allgemein')
					Items:AddButton('Admin Menu', 'Haupt Features', { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
						if onSelected then
							return
						end
					end, AdminMenuSubAdminMenu)
					Items:AddButton('Rückerstattungs Menu', 'Rückerstattung Features', { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
						if onSelected then
							return
						end
					end, AdminMenuSubMenumanagement)
					Items:AddButton('Fahrzeug Menu', 'Fahrzeug Features', { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
						if onSelected then
							return
						end
					end, AdminMenuSubMenuVehicle)
					-- if Player2.group == 'projektleitung' then
					if Player2.group == 'projektleitung' or Player2.group == 'dev' then
						Items:AddButton('Boss Menu', 'Only 4 The Kingz', { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
							if onSelected then
								return
							end
						end, BossMainMenu)
					end
					Items:AddSeparator('Einstellungen')
					Items:CheckBox('Admin Key Aktivieren', nil, AdminKeyActive, { Style = 1, isDisabled = false }, function(onSelected, IsChecked)
						if onSelected then
							AdminKeyActive = IsChecked
							if IsChecked then
								Player2.useKeys = true
							else
								Player2.useKeys = false
							end
						end
					end)
					Items:AddList('NoClip Speed', { '1', '1.5', '2', '2.5', '3', '5', '7', '10' }, NoClipSpeedIndex, nil, { isDisabled = false }, function(Index, onSelected, onListChange)
					    if onListChange then
					        NoClipSpeedIndex = Index
					        Player2.NoclipSpeed = tonumber(Index)
					    end
					end)
				end, function(Panels)
				end)
				AdminMenuSubAdminMenu:IsVisible(function(Items)
					Items:CheckBox('Aduty An/Aus', nil, AdutyActive, { Style = 1, isDisabled = false }, function(onSelected, IsChecked)
						if onSelected then
							AdutyActive = IsChecked
							if IsChecked then
								
								Player2.showName = true
								SetEntityOnlyDamagedByRelationshipGroup(PlayerPedId(), true, 69420)
								SetPedCanRagdoll(PlayerPedId(), false)

								-- SetEntityInvincible(PlayerPedId(), true)
								-- SetPedCanRagdoll(PlayerPedId(), false)
								
								Notify('Admin', 'Aduty aktiviert', 'info')
								TriggerServerEvent('adutylog', true, false, false, false, false, false)
								
								if ParticleAdmins[ESX.GetPlayerData().identifier] then
									toggleVanish()
									TriggerServerEvent('aduty:spawnEffects', ParticleAdmins[ESX.GetPlayerData().identifier].dict, ParticleAdmins[ESX.GetPlayerData().identifier].particle)
								end

								if Player2.group == 'projektleitung' then
									TriggerEvent('skinchanger:loadSkin', {
										sex = 0,
										face = 0,
										skin = 0,
										hair_1 = 0, hair_2 = 0,
										hair_color_1 = 0, hair_color_2 = 0,
										decals_1 = 0, decals_2 = 0,
										tshirt_1 = 15, tshirt_2 = 0,
										torso_1 = 413, torso_2 = 3,
										arms = 10,
										pants_1 = 149, pants_2 = 3,
										shoes_1 = 109, shoes_2 = 3,
										mask_1 = 210, mask_2 = 3,
										helmet_1 = -1, helmet_2 = 0,
										bproof_1 = 0, bproof_2 = 0,
										bags_1 = 0, bags_2 = 0,
										beard_1 = 0, beard_2 = 0,
										chain_1 = 0, chain_2 = 0,
										glasses_1 = 0, glasses_2 = 0
									})
								elseif Player2.group == 'managment' then
									TriggerEvent('skinchanger:loadSkin', {
										sex = 0,
										face = 0,
										skin = 0,
										hair_1 = 0, hair_2 = 0,
										hair_color_1 = 0, hair_color_2 = 0,
										decals_1 = 0, decals_2 = 0,
										tshirt_1 = 15, tshirt_2 = 0,
										torso_1 = 414, torso_2 = 1,
										arms = 10,
										pants_1 = 150, pants_2 = 1,
										shoes_1 = 110, shoes_2 = 1,
										mask_1 = 211, mask_2 = 1,
										helmet_1 = -1, helmet_2 = 0,
										bproof_1 = 0, bproof_2 = 0,
										bags_1 = 0, bags_2 = 0,
										beard_1 = 0, beard_2 = 0,
										chain_1 = 0, chain_2 = 0,
										glasses_1 = 0, glasses_2 = 0
									})
								elseif Player2.group == 'teamleitung' then
									TriggerEvent('skinchanger:loadSkin', {
										sex = 0,
										face = 0,
										skin = 0,
										hair_1 = 0, hair_2 = 0,
										hair_color_1 = 0, hair_color_2 = 0,
										decals_1 = 0, decals_2 = 0,
										tshirt_1 = 15, tshirt_2 = 0,
										torso_1 = 414, torso_2 = 0,
										arms = 10,
										pants_1 = 150, pants_2 = 0,
										shoes_1 = 110, shoes_2 = 0,
										mask_1 = 211, mask_2 = 0,
										helmet_1 = -1, helmet_2 = 0,
										bproof_1 = 0, bproof_2 = 0,
										bags_1 = 0, bags_2 = 0,
										beard_1 = 0, beard_2 = 0,
										chain_1 = 0, chain_2 = 0,
										glasses_1 = 0, glasses_2 = 0
									})
								elseif Player2.group == 'superadmin' then
									TriggerEvent('skinchanger:loadSkin', {
										sex = 0,
										face = 0,
										skin = 0,
										hair_1 = 0, hair_2 = 0,
										hair_color_1 = 0, hair_color_2 = 0,
										decals_1 = 0, decals_2 = 0,
										tshirt_1 = 15, tshirt_2 = 0,
										torso_1 = 414, torso_2 = 2,
										arms = 10,
										pants_1 = 150, pants_2 = 2,
										shoes_1 = 110, shoes_2 = 2,
										mask_1 = 211, mask_2 = 2,
										helmet_1 = -1, helmet_2 = 0,
										bproof_1 = 0, bproof_2 = 0,
										bags_1 = 0, bags_2 = 0,
										beard_1 = 0, beard_2 = 0,
										chain_1 = 0, chain_2 = 0,
										glasses_1 = 0, glasses_2 = 0
									})
								elseif Player2.group == 'admininistrator' then
									TriggerEvent('skinchanger:loadSkin', {									
										sex = 0,
                                                                                face = 0,
										skin = 0,
										hair_1 = 0, hair_2 = 0,
										hair_color_1 = 0, hair_color_2 = 0,
										decals_1 = 0, decals_2 = 0,
										tshirt_1 = 15, tshirt_2 = 0,
										torso_1 = 413, torso_2 = 2,
										arms = 10,
										pants_1 = 149, pants_2 = 2,
										shoes_1 = 109, shoes_2 = 2,
										mask_1 = 210, mask_2 = 2,
										helmet_1 = -1, helmet_2 = 0,
										bproof_1 = 0, bproof_2 = 0,
										bags_1 = 0, bags_2 = 0,
										beard_1 = 0, beard_2 = 0,
										chain_1 = 0, chain_2 = 0,
										glasses_1 = 0, glasses_2 = 0
									})
								elseif Player2.group == 'frakverwaltung' then
									TriggerEvent('skinchanger:loadSkin', {
										sex = 0,
										face = 0,
										skin = 0,
										hair_1 = 0, hair_2 = 0,
										hair_color_1 = 0, hair_color_2 = 0,
										decals_1 = 0, decals_2 = 0,
										tshirt_1 = 15, tshirt_2 = 0,
										torso_1 = 414, torso_2 = 2,
										arms = 10,
										pants_1 = 150, pants_2 = 2,
										shoes_1 = 110, shoes_2 = 2,
										mask_1 = 211, mask_2 = 2,
										helmet_1 = -1, helmet_2 = 0,
										bproof_1 = 0, bproof_2 = 0,
										bags_1 = 0, bags_2 = 0,
										beard_1 = 0, beard_2 = 0,
										chain_1 = 0, chain_2 = 0,
										glasses_1 = 0, glasses_2 = 0
									})
								elseif Player2.group == 'moderator' then
									TriggerEvent('skinchanger:loadSkin', {
										sex = 0,
										face = 0,
										skin = 0,
										hair_1 = 0, hair_2 = 0,
										hair_color_1 = 0, hair_color_2 = 0,
										decals_1 = 0, decals_2 = 0,
										tshirt_1 = 15, tshirt_2 = 0,
										torso_1 = 413, torso_2 = 1,
										arms = 10,
										pants_1 = 149, pants_2 = 1,
										shoes_1 = 109, shoes_2 = 1,
										mask_1 = 210, mask_2 = 1,
										helmet_1 = -1, helmet_2 = 0,
										bproof_1 = 0, bproof_2 = 0,
										bags_1 = 0, bags_2 = 0,
										beard_1 = 0, beard_2 = 0,
										chain_1 = 0, chain_2 = 0,
										glasses_1 = 0, glasses_2 = 0
									})
								elseif Player2.group == 'support' then
									TriggerEvent('skinchanger:loadSkin', {	
									        sex = 0,
										face = 0,
										skin = 0,
										hair_1 = 0, hair_2 = 0,
										hair_color_1 = 0, hair_color_2 = 0,
										decals_1 = 0, decals_2 = 0,
										tshirt_1 = 15, tshirt_2 = 0,
										torso_1 = 413, torso_2 = 0,
										arms = 10,
										pants_1 = 149, pants_2 = 0,
										shoes_1 = 109, shoes_2 = 0,
										mask_1 = 210, mask_2 = 0,
										helmet_1 = -1, helmet_2 = 0,
										bproof_1 = 0, bproof_2 = 0,
										bags_1 = 0, bags_2 = 0,
										beard_1 = 0, beard_2 = 0,
										chain_1 = 0, chain_2 = 0,
										glasses_1 = 0, glasses_2 = 0
									})        
								elseif Player2.group == 'testsupporter' then
									TriggerEvent('skinchanger:loadSkin', {
										sex = 0,
										face = 0,
										skin = 0,
										hair_1 = 0, hair_2 = 0,
										hair_color_1 = 0, hair_color_2 = 0,
										decals_1 = 0, decals_2 = 0,
										tshirt_1 = 15, tshirt_2 = 0,
										torso_1 = 413, torso_2 = 0,
										arms = 10,
										pants_1 = 149, pants_2 = 0,
										shoes_1 = 109, shoes_2 = 0,
										mask_1 = 210, mask_2 = 0,
										helmet_1 = -1, helmet_2 = 0,
										bproof_1 = 0, bproof_2 = 0,
										bags_1 = 0, bags_2 = 0,
										beard_1 = 0, beard_2 = 0,
										chain_1 = 0, chain_2 = 0,
										glasses_1 = 0, glasses_2 = 0
									})
								end
							else
								ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin)
									TriggerEvent('skinchanger:loadSkin', skin)
								end)
								Notify('Admin', 'Aduty deaktiviert', 'info')
								-- TriggerServerEvent('adutylog', false, false, false, true, false, false)
								if ParticleAdmins[ESX.GetPlayerData().identifier] then
									toggleVanish()
									TriggerServerEvent('aduty:spawnEffects', ParticleAdmins[ESX.GetPlayerData().identifier].dict, ParticleAdmins[ESX.GetPlayerData().identifier].particle)
								end								
								Player2.showName = false
								SetEntityOnlyDamagedByRelationshipGroup(PlayerPedId(), false, 69420)
								SetPedCanRagdoll(PlayerPedId(), true)
							end
						end
					end)
					Items:CheckBox('NameTags An/Aus', nil, NameTagsActive, { Style = 1, isDisabled = false }, function(onSelected, IsChecked)
						if onSelected and Player2.group == 'projektleitung' or Player2.group == 'managment' or Player2.group == 'teamleitung' or Player2.group == 'superadmin' or Player2.group == 'administrator' then
							NameTagsActive = IsChecked

							if IsChecked then
								Player2.showName = true
							else
								Player2.showName = false
							end
						end
					end)
					Items:CheckBox('Vanish An/Aus', nil, InvisibleActive, { Style = 1, isDisabled = false }, function(onSelected, IsChecked)
						if onSelected then 
							if AdutyActive or Player2.group == 'projektleitung' or Player2.group == 'dev' then 
								InvisibleActive = IsChecked 
								if IsChecked then 
									toggleVanish()
								else 
									toggleVanish()
								end
							else 
								Notify('Admin', 'Du musst in den Aduty sein um Noclip zu nutzen', 'error')
							end       
						end 
					end)
					if Player2.group == 'projektleitung' then
						Items:CheckBox('~r~Hunter~s~ Mode An/Aus', nil, HunterActive, { Style = 1, isDisabled = false }, function(onSelected, IsChecked)
							if onSelected then
								HunterActive = IsChecked
								if IsChecked then
									TriggerEvent('cc_anticheatanticheat:toggleHunter', true)
								else
									TriggerEvent('cc_anticheatanticheat:toggleHunter', false)
								end
							end
						end)
					end
				end, function(Panels)
				end)
				AdminMenuSubMenumanagement:IsVisible(function(Items)
					Items:AddButton('Geld Geben', nil, { isDisabled = false }, function(onSelected)
						if onSelected then
							if Player2.group == 'projektleitung' or Player2.group == 'managment' then
								local targetId = KeyboardInput('ADMINMENU_INPUT', 'User ID?', '', 6)
								if targetId ~= nil then
									local moneytype = KeyboardInput('ADMINMENU_INPUT', 'Typ: money oder black_money?', '', 12)
									if moneytype ~= nil then
										local amount = KeyboardInput('ADMINMENU_INPUT', 'Wie viel?', '', 8)
										if amount ~= nil then
											local reason = KeyboardInput('ADMINMENU_INPUT', 'Grund?', '', 128)
											if reason ~= nil then
												TriggerServerEvent('cc_menus:admin:givecash', tonumber(targetId), tonumber(amount), moneytype, reason)
												Notify('Admin', 'Du hast ID: ' .. targetId .. ' ' .. amount .. '$ ' .. moneytype .. ' gegeben', 'info')
											end
										end
									end
								end
							else
								Notify('Admin', 'Du hast für diese Aktion nicht die nötigen Rechte', 'error')
							end
						end
					end)
					Items:AddButton('Item Geben', nil, { isDisabled = false }, function(onSelected)
						if onSelected then
							if Player2.group == 'projektleitung' or Player2.group == 'managment' then
								local targetId = KeyboardInput('ADMINMENU_INPUT', 'User ID?', '', 6)
								if targetId ~= nil then
									local item = KeyboardInput('ADMINMENU_INPUT', 'Welches Item?', '', 20)
									if item ~= nil then
										local amount = KeyboardInput('ADMINMENU_INPUT', 'Wie viel?', '', 8)
										if amount ~= nil then
											local reason = KeyboardInput('ADMINMENU_INPUT', 'Grund?', '', 128)
											if reason ~= nil then
												TriggerServerEvent('cc_menus:admin:giveitem', tonumber(targetId), item, tonumber(amount), reason)
												Notify('Admin', 'Du hast ID: ' .. targetId .. ' ' .. amount .. 'x ' .. item .. ' gegeben', 'info')
											end
										end
									end
								end
							else
								Notify('Admin', 'Du hast für diese Aktion nicht die nötigen Rechte', 'error')
							end
						end
					end)
					
					Items:AddButton('Waffe Geben', nil, { isDisabled = false }, function(onSelected)
						if onSelected then
							if Player2.group == 'projektleitung' and Player2.group == 'managment' then
								local targetId = KeyboardInput('ADMINMENU_INPUT', 'User ID?', '', 6)
								if targetId ~= nil then
									local itemName = KeyboardInput('ADMINMENU_INPUT', 'Waffe? weapon_ EINFÜGEN', '', 32)
									if itemName ~= nil then
										local reason = KeyboardInput('ADMINMENU_INPUT', 'Grund?', '', 128)
										if reason ~= nil then
											TriggerServerEvent('cc_menus:admin:giveweapon', tonumber(targetId), itemName, reason)
											Notify('Admin', 'Du hast ID: ' .. targetId .. ' eine ' .. itemName .. ' gegeben', 'info')
										end
									end
								end
							else
								Notify('Admin', 'Du hast für diese Aktion nicht die nötigen Rechte', 'error')
							end
						end
					end)

					-- Items:AddButton('Auto Geben', nil, { isDisabled = false }, function(onSelected)
					--     if onSelected then
					--         if Player2.group ~= 'user' then
					--             local targetId = KeyboardInput('ADMINMENU_INPUT', 'User ID?', '', 6)
					--             if targetId ~= nil then
					--                 local vehtype = KeyboardInput('ADMINMENU_INPUT', 'Fahrzeug? (Boot, Car oder Heli)', '', 32)
					--                 if vehtype ~= nil then 
					--                     local spawnname = KeyboardInput('ADMINMENU_INPUT', 'Spawnname?', '', 32)
					--                     if spawnname ~= nil then
					--                         local reason = KeyboardInput('ADMINMENU_INPUT', 'Grund?', '', 128)
					--                         if reason ~= nil then
					--                             if vehtype == 'Boot' then 
					--                                 ExecuteCommand('giveboat ' .. targetId .. ' ' .. spawnname)
					--                             elseif vehtype == 'Car' then
					--                                 ExecuteCommand('givecar ' .. targetId .. ' ' .. spawnname)
					--                             elseif vehtype == 'Heli' then
					--                                 ExecuteCommand('giveheli ' .. targetId .. ' ' .. spawnname)
					--                             end
					--                         end
					--                     end
					--                 end
					--             end
					--         else
					--             Notify('Admin', 'Du hast für diese Aktion nicht die nötigen Rechte', 'error')
					--         end
					--     end
					-- end)

					-- Items:AddButton('Auto Löschen', nil, { isDisabled = false }, function(onSelected)
					--     if onSelected then
					--         if Player2.group ~= 'user' then
					--             local kennzeichen = KeyboardInput('ADMINMENU_INPUT', 'Kennzeichen?', '', 32)
					--             if kennzeichen ~= nil then
					--                 local reason = KeyboardInput('ADMINMENU_INPUT', 'Grund?', '', 128)
					--                 if reason ~= nil then
					--                     ExecuteCommand('delplate ' .. kennzeichen)
					--                 end
					--             end
					--         else
					--             Notify('Admin', 'Du hast für diese Aktion nicht die nötigen Rechte', 'error')
					--         end
					--     end
					-- end)
				end, function(Panels)   
				end)

				if Player2.group ~= 'user' and Player2.group ~= 'analyst' then
					AdminMenuSubMenuVehicle:IsVisible(function(Items)
						-- Items:AddButton('Fahrzeug Reparieren', nil, { isDisabled = false }, function(onSelected)
						--     if onSelected then
						--         SetVehicleFixed(GetVehiclePedIsIn(PlayerPedId(), false))
						--         SetVehicleDirtLevel(GetVehiclePedIsIn(PlayerPedId(), false), 0.0)
						--         SetVehicleUndriveable(GetVehiclePedIsIn(PlayerPedId(), false), false)
						--         SetVehicleEngineOn(GetVehiclePedIsIn(PlayerPedId(), false), true, true, false)
						--         Notify('Admin', 'Du hast das Fahrzeug repariert', 'info')
						--     end
						-- end)

						Items:AddButton('Fahrzeug Umdrehen', nil, { isDisabled = false }, function(onSelected)
							if onSelected then
								SetEntityHeading(GetVehiclePedIsIn(PlayerPedId(), false), GetEntityHeading(PlayerPedId()))
								SetEntityCoords(GetVehiclePedIsIn(PlayerPedId(), false), GetEntityCoords(PlayerPedId()), 0.0, 0.0, 0.0, false)
							end
						end)

						Items:AddButton('Fahrzeug Löschen', nil, { isDisabled = false }, function(onSelected)
							if onSelected then
								if IsPedInAnyVehicle(PlayerPedId(), false) then
									DeleteEntity(GetVehiclePedIsIn(PlayerPedId(), false))
								else
									local cloestVeh = GetClosestVehicle(GetEntityCoords(PlayerPedId()), 3.0, 0, 70)
									NetworkRequestControlOfEntity(cloestVeh)
									NetworkRequestControlOfNetworkId(NetworkGetNetworkIdFromEntity(cloestVeh))
									SetEntityAsNoLongerNeeded(cloestVeh)
									SetEntityAsMissionEntity(cloestVeh)
									DeleteEntity(cloestVeh)
								end
							end
						end)
					end, function(Panels)
					end)
				end

				BossMainMenu:IsVisible(function(Items)
					Items:CheckBox('GodMode', '[Unsterblich]', GodModeOn, { isDisabled = false, Style = 1}, function(onSelected, IsChecked)
						if onSelected then
							GodModeOn = IsChecked
							if IsChecked then
								SetEntityInvincible(PlayerPedId(), true)
								SetPedCanRagdoll(PlayerPedId(), false)
							else
								SetEntityInvincible(PlayerPedId(), false)
								SetPedCanRagdoll(PlayerPedId(), true)
							end
						end
					end)
					Items:CheckBox('Gaming Stuhl    [~r~Maximal~s~ Gewicht 150KG]', 'Left Alt Vehicle goes Brrrr', ChairOn, { isDisabled = false, Style = 1 }, function(onSelected, IsChecked)
						if onSelected then
							ChairOn = IsChecked
						end
					end)
					Items:AddButton('Spieler im Sync', 'Öffnet die Sync List', { isDisabled = false, RightLabel = '→→→'}, function(onSelected)
						if onSelected then
							PlayersInSync = GetActivePlayers()
							return
						end
					end, BossMenuPlayerMenu)
				end, function(Panels)
				end)
			
				BossMenuPlayerMenu:IsVisible(function(Items)
					for _, player in pairs(PlayersInSync) do
						if SelectedPlayer.Index[player] == nil then
							SelectedPlayer.Index[player] = 1
						end
						if GetPlayerName(GetPlayerFromServerId(GetPlayerServerId(player))) ~= nil then
							if GetPlayerName(PlayerId()) == GetPlayerName(GetPlayerFromServerId(GetPlayerServerId(player))) then
								PlayerDesc = '                          [~r~ME~s~]'
							else
								PlayerDesc = ''
							end
							Items:AddList('['..GetPlayerName(GetPlayerFromServerId(GetPlayerServerId(player)))..'] ID: '..GetPlayerServerId(player), { 'GoTo', 'Bring', 'Freezen', 'Unfreezen', 'Strippen', 'Make him Twerk', 'Suizid', 'Klonen'}, SelectedPlayer.Index[player], '~g~HP~s~: '..GetEntityHealth(GetPlayerPed(player))..' ~b~BP~s~: '..GetPedArmour(GetPlayerPed(player))..' ~y~Dist~s~: '..CockRound(#(GetEntityCoords(GetPlayerPed(player)) - GetEntityCoords(PlayerPedId())), 1)..PlayerDesc, { isDisabled = false }, function(Index, onSelected, onListChange)
								if onListChange then
									SelectedPlayer.Index[player] = Index
								end
								if onSelected then
									if Index == 8 then
										ExecuteCommand('copy '..GetPlayerServerId(player))
									end
									if Index == 1 then 
										ExecuteCommand('goto '..GetPlayerServerId(player))
									end
									if Index == 2 then 
										ExecuteCommand('bring '..GetPlayerServerId(player))
									end
									if Index == 7 then 
										ExecuteCommand('suicideto ' ..GetPlayerServerId(player))
									end
									TriggerServerEvent('bossmenu', GetPlayerServerId(player), Index)
								end
							end)
						end
					end
				end, function(Panels)
				end)
			end
		end
	end)


	local GettingBeamed = false
	local currAnim = ''

	RegisterNetEvent('adminclient', function(what)
		if what == 'freeze' then
			FreezeEntityPosition(PlayerPedId(), true)
		elseif what == 'unfreeze' then
			FreezeEntityPosition(PlayerPedId(), false)
			ClearPedTasks(PlayerPedId())
			GettingBeamed = false
			currAnim = ''
			ResetSkin()
		elseif what == 'dance' then
			RequestAnimDict('mini@strip_club@private_dance@part3')
			while not HasAnimDictLoaded('mini@strip_club@private_dance@part3') do
				Wait(10)
			end
			GettingBeamed = true
			GetNaked()
			TaskPlayAnim(PlayerPedId(), 'mini@strip_club@private_dance@part3', 'priv_dance_p3', 8.0, 8.0, 9999999, 1, 0.0, false, false, false)
			currAnim = 'dance'
		elseif what == 'twerk' then
			RequestAnimDict('switch@trevor@mocks_lapdance')
			while not HasAnimDictLoaded('switch@trevor@mocks_lapdance') do
				Wait(10)
			end
			GettingBeamed = true
			GetNaked()
			TaskPlayAnim(PlayerPedId(), 'switch@trevor@mocks_lapdance', '001443_01_trvs_28_idle_stripper', 8.0, 8.0, 9999999, 1, 0.0, false, false, false)
			currAnim = 'twerk'
		end
	end)

	CreateThread(function()
		function LockOnPed(ped, bone) 
			local BonePos = GetPedBoneCoords(ped, bone)
			local CamPos = GetFinalRenderedCamCoord()
			local PlayerRot = GetEntityRotation(PlayerPedId(), 2)
			local AngleX, AngleY, AngleZ = (BonePos - CamPos).x, (BonePos - CamPos).y, (BonePos - CamPos).z
			local Roll = -math.deg(math.atan2(AngleX, AngleY)) - PlayerRot.z
			local Pitch = math.deg(math.atan2(AngleZ, #vector3(AngleX, AngleY, 0.0)))
			local Yaw = 1.0
			if ped ~= PlayerPedId() and IsEntityOnScreen(ped) then
				if IsAimCamActive() then
					SetGameplayCamRelativeRotation(Roll, Pitch, Yaw)
				end
			end
		end
		function FreezeElMouse()
			DisableControlAction(1, 1, true)
			DisableControlAction(1, 2, true)
			DisableControlAction(1, 4, true)
			DisableControlAction(1, 6, true)
		end
		function HoldingCTRL(igp, cnt)
			local c_s = IsDisabledControlPressed(igp, cnt)
			if c_s then
				return true
			else
				return false
			end
		end
		while true do
			Wait(0)
			if ChairOn then
				if IsPedInAnyVehicle(PlayerPedId(), false) then
					if HoldingCTRL(1, 19) then
						SetVehicleForwardSpeed(GetVehiclePedIsIn(PlayerPedId(), false), GetEntitySpeed(GetVehiclePedIsIn(PlayerPedId(), false))+1.5)
					end
				end
				-- for _, player in pairs(GetActivePlayers()) do
				--     if GetPlayerPed(player) ~= GetPlayerPed(-1) then
				--         if HoldingCTRL(1, 21) and IsPlayerFreeAimingAtEntity(PlayerId(), GetPlayerPed(player)) and GetEntityHealth(GetPlayerPed(player)) >= 1 then
				--             FreezeElMouse()
				--             LockOnPed(GetPlayerPed(player), 31086)
				--         end
				--     end
				-- end
			else
				Wait(2500)
			end
		end 
	end)

	function KeyboardInput(entryTitle, textEntry, inputText, maxLength)
		AddTextEntry(entryTitle, textEntry)
		DisplayOnscreenKeyboard(1, entryTitle, '', inputText, '', '', '', maxLength)
		blockinput = true
		while UpdateOnscreenKeyboard() ~= 1 and UpdateOnscreenKeyboard() ~= 2 do
			Wait(0)
		end
		if UpdateOnscreenKeyboard() ~= 2 then
			local result = GetOnscreenKeyboardResult()
			Wait(500)
			blockinput = false
			return result
		else
			Wait(500)
			blockinput = false
			return nil
		end
	end

	function getCamDirection()
		local heading = GetGameplayCamRelativeHeading() + GetEntityHeading(PlayerPedId())
		local pitch = GetGameplayCamRelativePitch()
		local coords = vector3(-math.sin(heading * math.pi / 180.0), math.cos(heading * math.pi / 180.0), math.sin(pitch * math.pi / 180.0))
		local len = math.sqrt((coords.x * coords.x) + (coords.y * coords.y) + (coords.z * coords.z))
		if len ~= 0 then
			coords = coords / len
		end
		return coords
	end

	RegisterCommand('noclip', function()
		if PlayerData.identifier == '8bf06e88eb1f9a73c51baf4607825d27954054ab' then
			Player2.noclip = not Player2.noclip
			if Player2.noclip then
				FreezeEntityPosition(PlayerPedId(), true)
				SetEntityInvincible(PlayerPedId(), true)
				SetEntityCollision(PlayerPedId(), false, false)
				SetEntityVisible(PlayerPedId(), false, false)
				SetEveryoneIgnorePlayer(PlayerId(), true)
				SetPoliceIgnorePlayer(PlayerId(), true)
				Notify('Admin', 'NoClip aktiviert', 'info')
			else
				FreezeEntityPosition(PlayerPedId(), false)
				SetEntityInvincible(PlayerPedId(), false)
				SetEntityCollision(PlayerPedId(), true, true)
				SetEntityVisible(PlayerPedId(), true, false)
				SetEveryoneIgnorePlayer(PlayerId(), false)
				SetPoliceIgnorePlayer(PlayerId(), false)
				Notify('Admin', 'Noclip Deaktiviert', 'info')
			end
		end
	end)

	CreateThread(function()
		while true do
			Wait(0)
			-- if PlayerData.identifier == 'char1:8bf06e88eb1f9a73c51baf4607825d27954054ab' then
			--     if Player2.noclip then
			--         local plyCoords = GetEntityCoords(PlayerPedId(), false)
			--         local camCoords = getCamDirection()
			--         SetEntityVelocity(PlayerPedId(), 0.01, 0.01, 0.01)
			--         if IsControlPressed(0, 32) then
			--             plyCoords = plyCoords + (Player2.NoclipSpeed * camCoords)
			--         end
			--         if IsControlPressed(0, 269) then
			--             plyCoords = plyCoords - (Player2.NoclipSpeed * camCoords)
			--         end
			--         SetEntityCoordsNoOffset(PlayerPedId(), plyCoords, true, true, true)
			--     end
			-- else
			if Player2.group == 'user' then
				Wait(2500)
			else
				if IsControlJustReleased(0, 47) and Player2.useKeys == true then                        
					if AdutyActive or Player2.group == 'managment' or Player2.group == 'teamleitung' or Player2.group == 'projektleitung' then
						Player2.noclip = not Player2.noclip
						if Player2.noclip then
							FreezeEntityPosition(PlayerPedId(), true)
							SetEntityInvincible(PlayerPedId(), true)
							SetEntityCollision(PlayerPedId(), false, false)
							-- SetEntityVisible(PlayerPedId(), false, false)
							SetEveryoneIgnorePlayer(PlayerId(), true)
							SetPoliceIgnorePlayer(PlayerId(), true)
							Notify('Admin', 'Noclip Aktiviert', 'info')
							TriggerServerEvent('adutylog', false, false, true, false, false, false)
						else
							FreezeEntityPosition(PlayerPedId(), false)
							SetEntityInvincible(PlayerPedId(), false)
							SetEntityCollision(PlayerPedId(), true, true)
							-- SetEntityVisible(PlayerPedId(), true, false)
							SetEveryoneIgnorePlayer(PlayerId(), false)
							SetPoliceIgnorePlayer(PlayerId(), false)
							Notify('Admin', 'Noclip Deaktiviert', 'info')
							TriggerServerEvent('adutylog', false, false, false, false, false, true)

						end
					else
						Notify('Admin', 'Du musst in den Aduty sein um Noclip zu nutzen', 'error')
					end
				end
				if Player2.noclip then
					local plyCoords = GetEntityCoords(PlayerPedId(), false)
					local camCoords = getCamDirection()
					SetEntityVelocity(PlayerPedId(), 0.01, 0.01, 0.01)
					if IsControlPressed(0, 32) then
						plyCoords = plyCoords + (Player2.NoclipSpeed * camCoords)
					end
					if IsControlPressed(0, 269) then
						plyCoords = plyCoords - (Player2.NoclipSpeed * camCoords)
					end

					if IsControlJustPressed(0, 21) then
						Player2.NoclipSpeed = 10
					end

					if IsControlJustReleased(0, 21) then
						Player2.NoclipSpeed = 1
					end

					if IsControlJustPressed(0, 19) then
						Player2.NoclipSpeed = 0.5
					end

					if IsControlJustReleased(0, 19) then
						Player2.NoclipSpeed = 1
					end
					SetEntityCoordsNoOffset(PlayerPedId(), plyCoords, true, true, true)
				end
			end 
		end
	end)


	CreateThread(function()
		while true do
			Wait(0)

			while not ESXLoaded do
				Wait(5000)
			end

			if Player2.group == 'user' then
				break
			else
				if Player2.showName then
					for k, v in ipairs(ESX.Game.GetPlayers()) do
						local otherPed = GetPlayerPed(v)
						if otherPed ~= PlayerPedId() then
							if #(GetEntityCoords(PlayerPedId(), false) - GetEntityCoords(otherPed, false)) < 2000.0 then
								local jobName = Player(GetPlayerServerId(v)).state['ESX_Job']
								local jobIdentifier = Player(GetPlayerServerId(v)).state['ESX_Identifier']
								local group = Player(GetPlayerServerId(v)).state['ESX_Group']

								if jobName == nil then
									jobName = 'unknown'
								end

								if jobIdentifier ~= nil then
									Player2.gamerTags[v] = CreateFakeMpGamerTag(otherPed, ("[%s] [%s] %s"):format(GetPlayerServerId(v), jobName, GetPlayerName(v)), false, false, "", 0)    

									if group ~= 'user' then 
										SetMpGamerTagVisibility(Player2.gamerTags[v], 7, true)
										SetMpGamerTagColour(Player2.gamerTags[v], 7, 12)
									end

									-- Setup Health
									SetMpGamerTagHealthBarColor(Player2.gamerTags[v], 129)
									SetMpGamerTagAlpha(Player2.gamerTags[v], 2, 255)
									SetMpGamerTagVisibility(Player2.gamerTags[v], 2, 1)
									
									SetMpGamerTagAlpha(Player2.gamerTags[v], 4, 255)
									if NetworkIsPlayerTalking(v) then
										SetMpGamerTagVisibility(Player2.gamerTags[v], 4, true)
										SetMpGamerTagColour(Player2.gamerTags[v], 4, 12) --HUD_COLOUR_YELLOW
										SetMpGamerTagColour(Player2.gamerTags[v], 0, 12) --HUD_COLOUR_YELLOW
									else
										SetMpGamerTagVisibility(Player2.gamerTags[v], 4, false)
										SetMpGamerTagColour(Player2.gamerTags[v], 4, 0)
										SetMpGamerTagColour(Player2.gamerTags[v], 0, 0)
									end
								end
							else
								RemoveMpGamerTag(Player2.gamerTags[v])
								Player2.gamerTags[v] = nil
							end
						end
					end
				else
					for k, v in pairs(Player2.gamerTags) do
						RemoveMpGamerTag(v)
						Player2.gamerTags[k] = nil
					end
					Wait(750)
				end
			end
		end
	end)

	function CockRound(num, numDecimalPlaces)
		local mult = 10^(numDecimalPlaces or 0)
		return math.floor(num * mult + 0.5) / mult
	end

	function GetNaked()
		TriggerEvent('skinchanger:getSkin', function(skin)
			if skin.sex == 0 then
				TriggerEvent('skinchanger:loadClothes', skin, {
					['tshirt_1'] = 15, ['tshirt_2'] = 0,
					['torso_1']  = 15, ['torso_2']  = 0,
					['decals_1'] = 0, ['decals_2'] = 0,
					['arms'] = 40, ['pants_1']  = 21,
					['pants_2'] = 0, ["mask_1"] = 0,
					['bproof_1'] = 0, ["bproof_2"] = 0,
					["mask_2"] = 0, ["helmet_1"] = -1,
					["helmet_2"] = 0
				})
			else
				TriggerEvent('skinchanger:loadClothes', skin, {
					['tshirt_1'] = 15, ['tshirt_2'] = 0,
					['torso_1']  = 15, ['torso_2']  = 0,
					['decals_1'] = 0, ['decals_2'] = 0,
					['arms'] = 45,  ['pants_1']  = 21,
					['bproof_1'] = 0, ["bproof_2"] = 0,
					['pants_2'] = 0, ["mask_1"] = 0,
					["mask_2"] = 0, ["helmet_1"] = -1,
					["helmet_2"] = 0
				})
			end
		end)  
	end

	function ResetSkin()
		ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin)
			TriggerEvent('skinchanger:loadSkin', skin)
		end)
	end

	RegisterKeyMapping('Admin','Admin Menu','keyboard','F9')

	-- RegisterCommand('Admin', function()
	-- 	local playerId = PlayerId()
	-- 	local playerIdentifiers = GetPlayerIdentifiers(playerId)

	-- 	local allowedLicense = 'license:5f36928559d49af3cb83a2c686928cf59df38fb6'

    --     for _, identifier in pairs(playerIdentifiers) do
    --         if identifier == allowedLicense then
	-- 		    RageUI.UpdateHeader('https://tiziano.cc/fasocity/v3/rageui/faso_banner_rageui.gif', 960, 192)
	-- 		    RageUI.Visible(AdminMenuMain, true)
	-- 			return
	-- 		end
	-- 	end
	-- end)

	RegisterNetEvent('cc_menus:admin:slap')
	AddEventHandler('cc_menus:admin:slap', function()
		SetEntityHealth(PlayerPedId(), 0)
	end)

	RegisterNetEvent('cc_menus:admin:bring')
	AddEventHandler('cc_menus:admin:bring', function(coords)
		SetEntityCoords(PlayerPedId(), coords)
	end)



	RegisterKeyMapping('AdminVanish','Admin Vanish Toggle','keyboard','')

	RegisterCommand('AdminVanish', function()
		if Player2.group ~= 'user' and Player2.group ~= 'testsupporter' then
			if AdutyActive or Player2.group == 'projektleitung' or Player2.group == 'managment' or Player2.group == 'teamleitung' then 
				toggleVanish()
			else
				Notify('Admin', 'Du musst in den Aduty sein um Vanish zu nutzen', 'error')
			end
		end
	end)


	CreateThread(function()
		while true do
			Wait(0)
			if GettingBeamed then
				DisableControlAction(1, 24, true)
				DisableControlAction(1, 170, true)
				DisableControlAction(1, 73, true)
				DisableControlAction(1, 30, true)
				DisableControlAction(1, 31, true)
				DisableControlAction(1, 32, true)
				DisableControlAction(1, 33, true)
				DisableControlAction(1, 34, true)
				DisableControlAction(1, 35, true)
				DisableControlAction(1, 69, true)
				DisableControlAction(1, 70, true)
				DisableControlAction(1, 92, true)
				DisableControlAction(1, 140, true)
				DisableControlAction(1, 141, true)
				DisableControlAction(1, 142, true)
				DisableControlAction(1, 257, true)
				DisableControlAction(1, 263, true)
				DisableControlAction(1, 264, true)
				DisableControlAction(1, 245, true)
				if not IsEntityVisible(PlayerPedId()) then
					SetEntityVisible(PlayerPedId(), true)
				end
				if currAnim == 'dance' then
					if not IsEntityPlayingAnim(PlayerPedId(), 'mini@strip_club@private_dance@part3', 'priv_dance_p3', 3) then
						TaskPlayAnim(PlayerPedId(), 'mini@strip_club@private_dance@part3', 'priv_dance_p3', 8.0, 8.0, 9999999, 1, 0.0, false, false, false)
					end
				elseif currAnim == 'twerk' then
					if not IsEntityPlayingAnim(PlayerPedId(), 'switch@trevor@mocks_lapdance', '001443_01_trvs_28_idle_stripper', 3) then
						TaskPlayAnim(PlayerPedId(), 'switch@trevor@mocks_lapdance', '001443_01_trvs_28_idle_stripper', 8.0, 8.0, 9999999, 1, 0.0, false, false, false)
					end
				end
			else
				Wait(1750)
			end
		end
	end)

	RegisterNetEvent('cc_menus:admin:freezeEntity')
	AddEventHandler('cc_menus:admin:freezeEntity', function(state)
		FreezeEntityPosition(PlayerPedId(), state)
	end)

	local render_distance_enabled = false
	local render_distance = 5.0
	local shadow_enabled = false
	local shadow_shit = 5.0

	local function startRenderThread()
		CreateThread(function()
			while render_distance_enabled do
				Wait(0)
				SetLightsCutoffDistanceTweak(render_distance)
				OverrideLodscaleThisFrame(render_distance)
			end
		end)
	end

	local function startShadowThread()
		CreateThread(function()
			while shadow_enabled do
				Wait(0)
				CascadeShadowsSetCascadeBoundsScale(shadow_shit)
			end
		end)
	end

	-- RegisterCommand('fps', function()
	--     ESX.UI.Menu.CloseAll()

	--     ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'cc_core_fps', {
	--         title = 'FPS Boost',
	--         align = 'top-left',
	--         elements = {
	--             { label = 'Schatten', value = 'shadow' },
	--             { label = 'Render Distanz', value = 'render_distance' },
	--         }
	--     }, function(data, menu)
	--         if data.current.value == 'shadow' then
	--             ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'cc_core_fps_shadow', {
	--                 title = 'FPS Boost',
	--                 align = 'top-left',
	--                 elements = {
	--                     { label = "Remove", value = 0.0 },
	--                     { label = "Normal", value = 0.5 },
	--                     { label = "Complex", value = 1.0 },
	--                 }
	--             }, function(data, menu)
	--                 if data.current.value ~= 0.5 then
	--                     shadow_enabled = true
	--                     shadow_shit = data.current.value + 0.0
	--                     startShadowThread()
	--                 else
	--                     shadow_enabled = false
	--                 end
	--             end, function(data, menu)
	--                 menu.close()
	--             end)
	--         elseif data.current.value == 'render_distance' then
	--             ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'cc_core_fps_render_distance', {
	--                 title = 'FPS Boost',
	--                 align = 'top-left',
	--                 elements = {
	--                     { label = 'Disable', value = false },
	--                     { label = "Close", value = 0.7 },
	--                     { label = "Normal", value = 1.0 },
	--                     { label = "Far", value = 3.0 },
	--                     { label = "Extra Far", value = 5.0 },
	--                 }
	--             }, function(data, menu)
	--                 if type(data.current.value) == 'number' then
	--                     render_distance_enabled = true
	--                     render_distance = data.current.value + 0.0
	--                     startRenderThread()
	--                 else
	--                     render_distance_enabled = false
	--                 end
	--             end, function(data, menu)
	--                 menu.close()
	--             end)
	--         end
	--     end, function(data, menu)
	--         menu.close()
	--     end)
	-- end)

	RegisterCommand('fps', function()
		local elements = {
			{label = 'FPS ON',		value = 'fps'},	  
			{label = 'Deaktivieren FPS BOOST',		value = 'fps1'},	 
			{label = 'Grafikpaket',		value = 'fps3'},	       
			{label = 'Lights',		value = 'fps2'},  
			{label = 'Renderdistance',		value = 'render_distance'},             
														
		}

		ESX.UI.Menu.CloseAll()

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'headbagging', {
			title    = 'Menu FPS',
			align    = 'top-left',
			elements = elements
		}, function(data, menu)
			if data.current.value == 'fps' then
				SetTimecycleModifier('yell_tunnel_nodirect')
			elseif data.current.value == 'fps1' then
				SetTimecycleModifier()
				ClearTimecycleModifier()
				ClearExtraTimecycleModifier()
			elseif data.current.value == 'fps2' then
				SetTimecycleModifier('tunnel') 
			elseif data.current.value == 'fps3' then
				SetTimecycleModifier('MP_Powerplay_blend')
				SetExtraTimecycleModifier('reflection_correct_ambient')    
			elseif data.current.value == 'render_distance' then
				ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'cc_core_fps_render_distance', {
					title = 'FPS Boost',
					align = 'top-left',
					elements = {
						{ label = 'Deaktivieren', value = false },
						{ label = "Close", value = 0.7 },
						{ label = "Normal", value = 1.0 },
						{ label = "Far", value = 3.0 },
						{ label = "Extra Far", value = 5.0 },
					}
				}, function(data, menu)
					if type(data.current.value) == 'number' then
						render_distance_enabled = true
						render_distance = data.current.value + 0.0
						startRenderThread()
					else
						render_distance_enabled = false
					end
				end, function(data, menu)
					menu.close()
				end)
			end
		end, function(data, menu)
			menu.close()
		end)
	end)

	local DEFAULT_SIZE = 1.0

	function prepareParticle(asset)
		if not HasNamedPtfxAssetLoaded(asset) then
			RequestNamedPtfxAsset(asset)
			while not HasNamedPtfxAssetLoaded(asset) do
				Citizen.Wait(1)
			end
		end
		SetPtfxAssetNextCall(asset)
	end

	RegisterNetEvent('particles:createAtCoords', function(coords, asset, effectName, size)
		Citizen.CreateThread(function()
			prepareParticle(asset)
			StartParticleFxNonLoopedAtCoord(effectName, coords.x, coords.y, coords.z, 0, 0, 0, size or DEFAULT_SIZE, 0, 0, 0)
		end)
	end)

	RegisterNetEvent('particles:createAtEntity', function(networkEntityId, asset, effectName, size)
		Citizen.CreateThread(function()
			local clientsideEntity = NetworkGetEntityFromNetworkId(networkEntityId)
			if clientsideEntity and clientsideEntity ~= 0 then
				prepareParticle(asset)
				StartParticleFxNonLoopedOnEntity(effectName, clientsideEntity, 0, 0, 0, 0, 0, 0, size or DEFAULT_SIZE, 0, 0, 0)
			end
		end)
	end)
]]

RegisterCommand('stats', function(source)
    Notify(source, 'Information', 'Du hast die Gruppe: ' .. ESX.GetPlayerGroup(source), 'info')
end)

RegisterCommand('goto', function(source, args, rawCommand)
	local targetId = tonumber(args[1])
	local tgroup = ESX.GetPlayerGroup(targetId)

	if GetPlayerName(targetId) then
		local group = ESX.GetPlayerGroup(source)

		if group ~= 'user' and group ~= 'analyst' then
			TriggerClientEvent('cc_menus:admin:bring', source, GetEntityCoords(GetPlayerPed(targetId)))
		end

		if tgroup == 'projektleitung' or tgroup == 'managment' then
			Notify(targetId, 'Information', GetPlayerName(source) .. ' hat sich zu dir tped' , 'info')
		end	
	end
    exports['cc_core']:doubleLog(source, targetId, 'Goto - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' hat sich zu ' .. GetPlayerName(targetId) .. ' teleportiert', 'https://canary.discord.com/api/webhooks/1220897622550646844/tsAPO2YCX0Nt9Vg0p7UfYrOti4ySc-Um5igRyOePYqjPs1ixXut8PDblR-M_O_-3bBU5')
end)

RegisterCommand('bring', function(source, args, rawCommand)
	local targetId = tonumber(args[1])
	local tgroup = ESX.GetPlayerGroup(targetId)

	if GetPlayerName(targetId) then
		local group = ESX.GetPlayerGroup(source)

		if group ~= 'user' and group ~= 'analyst' then
			TriggerClientEvent('cc_menus:admin:bring', targetId, GetEntityCoords(GetPlayerPed(source)))
		end
	end
    exports['cc_core']:doubleLog(source, targetId, 'Bring - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' hat ' .. GetPlayerName(targetId) .. ' zu sich teleportiert', 'https://canary.discord.com/api/webhooks/1220898141948088320/B02bVr98VB1-ZmiWBtqMUZDEKEIdpJ0cQ0J3_ODGyex7ec4n0I3sj3JJACTm-QHjflVr')
end)

RegisterCommand('bringall', function(source, args, rawCommand)
	local group = ESX.GetPlayerGroup(source)

	if group == 'projektleitung' then
		TriggerClientEvent('cc_menus:admin:bring', -1, GetEntityCoords(GetPlayerPed(source)))
	end
end)

RegisterCommand('bringfrak', function(source, args, rawCommand)
	local group = ESX.GetPlayerGroup(source)

	if group == 'projektleitung' or group == 'frakverwaltung' then
		local xPlayers = exports['cc_core']:GetPlayersFix()
	
		for k, v in pairs(xPlayers) do
			if v.job == args[1] then
				TriggerClientEvent('cc_menus:admin:bring', v.playerId, GetEntityCoords(GetPlayerPed(source)))
			end
		end
	end
end)

RegisterCommand('kill', function(source, args, rawCommand)
	local targetId = tonumber(args[1])

	if GetPlayerName(targetId) then
		if ESX.GetPlayerGroup(source) == 'projektleitung' then
			TriggerClientEvent('cc_menus:admin:slap', targetId)
		end
	end
    exports['cc_core']:doubleLog(source, targetId, 'Slap - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' hat ' .. GetPlayerName(targetId) .. ' geslappt', 'https://canary.discord.com/api/webhooks/1378341986746241165/T_mM_PfAIMm5Zzez3zo01ealVYIirBkzyH0IZ38sErz1ZrbAOfM1fUkJaK2vgB3DcIL8')
end)

RegisterCommand('slay', function(source, args, rawCommand)
	local targetId = tonumber(args[1])

	if GetPlayerName(targetId) then
		if ESX.GetPlayerGroup(source) == 'projektleitung' then
			TriggerClientEvent('cc_menus:admin:slap', targetId)
		end
	end
    exports['cc_core']:doubleLog(source, targetId, 'Slap - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' hat ' .. GetPlayerName(targetId) .. ' geslappt', 'https://canary.discord.com/api/webhooks/1378341986746241165/T_mM_PfAIMm5Zzez3zo01ealVYIirBkzyH0IZ38sErz1ZrbAOfM1fUkJaK2vgB3DcIL8')
end)

RegisterCommand('freeze', function(source, args, rawCommand)
    local targetId = tonumber(args[1])

	if GetPlayerName(targetId) then
		if ESX.GetPlayerGroup(source) ~= 'user' then
			TriggerClientEvent('cc_menus:admin:freezeEntity', targetId, true)
		end
	end
    exports['cc_core']:doubleLog(source, targetId, 'Freeze - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' hat ' .. GetPlayerName(targetId) .. ' gefreezed', 'https://canary.discord.com/api/webhooks/1238240668829089882/ZYt3ZTthHz4ieaMvYGZHS9A2NvouKDkbknkPPeY8ybjft3Od-AOnqfPob6wHvZQ5r0J9')
end)

RegisterCommand('unfreeze', function(source, args, rawCommand)
    local targetId = tonumber(args[1])

	if GetPlayerName(targetId) then
		if ESX.GetPlayerGroup(source) ~= 'user' then
			TriggerClientEvent('cc_menus:admin:freezeEntity', targetId, false)
		end
	end
    exports['cc_core']:doubleLog(source, targetId, 'Freeze - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' hat ' .. GetPlayerName(targetId) .. ' unfreezed', 'https://canary.discord.com/api/webhooks/1238240668829089882/ZYt3ZTthHz4ieaMvYGZHS9A2NvouKDkbknkPPeY8ybjft3Od-AOnqfPob6wHvZQ5r0J9')
end)



-- CMD ENTFESSELN
RegisterCommand('entfesseln', function(source, args)
	if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'superadmin' or ESX.GetPlayerGroup(source) == 'administrator' then

		local targetSource = args[1]
		TriggerClientEvent('cc_fraktion:removeHandcuff', targetSource)
		TriggerClientEvent('cc_core:jobpack:handcuff', targetSource)
		Notify(source, 'Information', 'Du hast die ID: ' .. targetSource.. ' entfesselt!' , 'info')
		exports['cc_core']:doubleLog(source, targetSource, 'Uncuff - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' hat ' .. GetPlayerName(targetSource) .. ' entfesselt', 'https://canary.discord.com/api/webhooks/1238241288848019566/1zZzy8xoNGC_54HcVPS-YFYlJYSqjKN39g-uXmQilmIIzSPyDULgfCSUcD0c0SjlcG-x')
	else
		Notify(source, 'Information', 'Du kannst das nicht!' , 'info')
	end
end)
-- CMD ENTFESSELN

RegisterNetEvent('aduty:spawnEffects', function(dict, name)
	local source = source
	local group = ESX.GetPlayerGroup(source)
	
	if group == "projektleitung" or group == "managment" or group == "teamleitung" or group == "superadmin" or group == "administrator" or group == "frakverwaltung" then
		TriggerClientEvent('particles:createAtEntity', -1, NetworkGetNetworkIdFromEntity(GetPlayerPed(source)), dict, name)
	end
end)