local ESX = exports['es_extended']:getSharedObject()

local data = {}
local time = 5

local function addData(name, text, phone)
    table.insert(data, {
        name = name,
        text = text,
        phone = phone or 'Unbekannt'
    })
end

local function Notify(sendTo, title, message, type)
    TriggerClientEvent('cc_core:hud:notify', sendTo, type, title, message)
end

local function LifeInvaderWebhook(name, msg, phone_number)
    local embed = {
        {
            ["color"] = "16648714",
            ["author"] = {
            ["name"] = "@" .. name,
            ["icon_url"] = "https://cdn.leon1337.host/attachments/8h7sg.png",
        },
            ["description"] = msg .. '\n\nTelefonnummer: ' .. phone_number,
            ["footer"] = {
                ["text"] =  "Werbung geschalten am ".. os.date("%d.%m.%y") .. " um " .. os.date("%X") .. " Uhr",
                ["icon_url"] = "https://cdn.leon1337.host/attachments/945w0.png",
            },
        }
    }
    PerformHttpRequest("https://canary.discord.com/api/webhooks/1378341470691528785/R1YrAfvPfd-qLOLLy8ALIxYOFVrFjfw-pz2MocquID4bHTDYhI-XmHomcfK8BQaEooO-", function(err, text, headers) end, "POST", json.encode({embeds = embed}), {["Content-Type"] = "application/json"})
end

function detectXSS(input)
    local patterns = {
        "<script", 
        "<",
        ">",
        "http",
        "https",
        "://",
        "javascript:", 
        "onerror=", 
        "onload=",   
        "<iframe",   
        "<img",     
        "<svg",     
        "document%.cookie",
        "document%.write",
        "eval%(",
        "alert%(",
        "confirm%(",
        "<object",
        "<embed",
        "setTimeout%(",
        "setInterval%("
    }

    local lower_input = string.lower(input)

    for _, pattern in ipairs(patterns) do
        if lower_input:match(pattern) then
            return true, "XSS pattern detected: " .. pattern
        end
    end

    return false
end

RegisterServerEvent('cc_core:lifeinvader:addAd')
AddEventHandler('cc_core:lifeinvader:addAd', function(message, private)
    local playerId = source
    local xPlayer = ESX.GetPlayerFromId(playerId)

    if not xPlayer then return end

    local isXSS, reason = detectXSS(message)
    if isXSS then
        print("Spieler: " .. playerId .. " mit: " .. reason)
        DropPlayer(playerId, "Hauen sie rein!")
        return
    end
    local messageLength = #message
    local phone_number = ESX.GetPlayerFromId(playerId).get('phone_number')

    if time >= 5 then
        if messageLength > 15 then 
            if messageLength < 200 then
                local totalPrice = messageLength * 2
    
                if ESX.GetPlayerMoney(playerId) >= totalPrice then
                    ESX.RemovePlayerMoney(playerId, totalPrice, GetCurrentResourceName())
                    Notify(playerId, 'Lifeinvader', 'Du bezahlst ' .. totalPrice .. '$', 'info')
                    time = 0
    
                    if private then
                        addData('Anonym', message, phone_number)
                        TriggerClientEvent('cc_core:hud:lifeinvader', -1, message, phone_number, 'Anonym')
                        TriggerClientEvent('cc_core:lifeinvader:getData', -1, data)
                        -- LifeInvaderWebhook('Anonym', message, phone_number)
                    else
                        local rpName = ESX.GetPlayerRPName(playerId)
                        addData(rpName, message, phone_number)
                        TriggerClientEvent('cc_core:hud:lifeinvader', -1, message, phone_number, rpName)
                        TriggerClientEvent('cc_core:lifeinvader:getData', -1, data)
                        -- LifeInvaderWebhook(rpName, message, phone_number)
                    end
                else
                    Notify(playerId, 'Lifeinvader', 'Du hast nicht genug Geld', 'info') 
                end
            else
                Notify(playerId, 'Lifeinvader', 'Deine Nachricht ist zu lang (max. 200 Buchstaben)', 'info')
            end
        else
            Notify(playerId, 'Lifeinvader', 'Deine Nachricht ist zu kurz (mind. 15 Buchstaben)', 'info')
        end
    else
        Notify(playerId, 'Lifeinvader', 'Derzeit ist ein Cooldown aktiv', 'info')
    end
end)

RegisterNetEvent('cc_core:lifeinvader:getData')
AddEventHandler('cc_core:lifeinvader:getData', function()
    TriggerClientEvent('cc_core:lifeinvader:getData', -1, data)
end)

Citizen.CreateThread(function()
    while true do
        time = time + 1
        Citizen.Wait(60000)
    end
end)

--clientcode
-- local lifeinvaderCode = [[
-- local ESX = nil
-- local show = false

-- Citizen.CreateThread(function()
--     while ESX == nil do
--         ESX = exports['es_extended']:getSharedObject()
--         Citizen.Wait(0)
--     end

--     while ESX.GetPlayerData().job == nil do
--         Citizen.Wait(0)
--     end

--     ESX.PlayerData = ESX.GetPlayerData()
-- end)

-- Citizen.CreateThread(function()
--     local blip = AddBlipForCoord(Config_Lifeinvader.position)
--     SetBlipSprite(blip, 77)
--     SetBlipScale(blip, 0.8)
--     SetBlipColour(blip, 1)
--     SetBlipDisplay(blip, 4)
--     SetBlipAsShortRange(blip, true)
--     BeginTextCommandSetBlipName('STRING')
--     AddTextComponentString('Lifeinvader')
--     EndTextCommandSetBlipName(blip)
-- end)

-- Citizen.CreateThread(function()
--     while true do
--         Citizen.Wait(0)
--         local ped = PlayerPedId()
--         local coords = GetEntityCoords(ped)
--         local distance = #(coords - Config_Lifeinvader.position)
--         local letSleep, inRange = true, false

--         if distance <= 30.0 then
--             letSleep = false

--             DrawMarker(nil, Config_Lifeinvader.position, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, true, false, true, false, false, false)
--         end

--         if distance < 1 then
--             inRange = true

--             if IsControlJustReleased(1, 38) then
--                 SetNuiFocus(true, true)
--                 SendNUIMessage({
--                     script = 'lifeinvader',
--                     action = 'show'
--                 })

--                 TriggerServerEvent('cc_core:lifeinvader:getData')
--             end
--         end

--         if not show and inRange then
-- 			exports['cc_core']:showHelpNotification('Drücke E um denn Lifeinvader zu öffnen')
-- 			show = true
-- 		elseif show and not inRange then
-- 			exports['cc_core']:closeHelpNotification()
-- 			show = false
-- 		end

--         if letSleep then
--             Citizen.Wait(1000)
--         end
--     end
-- end)

-- RegisterNetEvent('cc_core:lifeinvader:getData')
-- AddEventHandler('cc_core:lifeinvader:getData', function(data)
--     SendNUIMessage({
--         script = 'lifeinvader',
--         action = 'addAds',
--         data = data
--     })
-- end)

-- RegisterNUICallback('escape', function(data, cb)
--     SetNuiFocus(false, false)
-- end)

-- RegisterNUICallback('sendAd', function(data, cb)
--     if data.message ~= nil and data.message ~= '' then
--         TriggerServerEvent('cc_core:lifeinvader:addAd', data.message, data.private)
--     else
--         TriggerClientEvent('cc_core:hud:notify', 'Lifeinvader', 'Du hast keine Nachricht angegeben', 'info')
--     end
-- end)
-- ]]