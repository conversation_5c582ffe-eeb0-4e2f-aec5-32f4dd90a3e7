local jobs = {}

MySQL.ready(function()
    local result = MySQL.query.await('SELECT * FROM jobs', {})

    for k, v in pairs(result) do
        jobs[v.name] = v
        jobs[v.name].grades = {}
    end

    Wait(1000)

    local result2 = MySQL.query.await('SELECT * FROM job_grades', {})

    for k, v in pairs(result2) do
        jobs[v.job_name].grades[tostring(v.grade)] = v
    end
end)

RegisterServerEvent('cc_core:society:withdrawMoney')
AddEventHandler('cc_core:society:withdrawMoney', function(amount, job3)
    local playerId = source
    local job
    
    if job3 then
        job = ESX.GetPlayerJob3(playerId).name
    else
        job = ESX.GetPlayerJob(playerId).name
    end

    if job == 'mechanic' then
        CancelEvent()
    end

    amount = ESX.Math.Round(tonumber(amount))
    
	if exports['cc_core']:isRestart() then
        Notify(playerId, 'Banking', 'Du kannst 5 Minuten vor der Sonnenwende nix auszahlen!', 'info')
		return
    end

    if job == 'unemployed' then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [FRAC-1]", false, "Afrika")
    end

    TriggerEvent('esx_addonaccount:getSharedAccount', 'society_' .. job, function(account)
        if amount > 0 and account.money >= amount then
            local jobLabel = ESX.GetPlayerJob(playerId).label
            exports['cc_core']:log(playerId, 'Fraktionskasse Logs', 'Der Spieler **' .. GetPlayerName(playerId) .. '** hebt **' ..  amount .. '$** ' .. ' von der Fraktionskasse **' .. jobLabel .. '** ab!', 'https://canary.discord.com/api/webhooks/1352803830827450434/OS6Doz4m2DQui9GwIbiIOPh4xyGEUFjOs6lQTvia3wD__BY_-bEB-A6NVlVpwfnEBvVc')
            account.removeMoney(amount)
            ESX.AddPlayerMoney(playerId, amount, GetCurrentResourceName())
            Notify(playerId, 'Information', 'Du hast ' .. amount .. '$ abgehoben', 'info')
        else
            Notify(playerId, 'Information', 'Ungültiger Betrag', 'error')
        end
    end)
end)

RegisterServerEvent('cc_core:society:depositMoney')
AddEventHandler('cc_core:society:depositMoney', function(amount, job3)
    local playerId = source
    local job
    
    if job3 then
        job = ESX.GetPlayerJob3(playerId).name
    else
        job = ESX.GetPlayerJob(playerId).name
    end

    if job == 'mechanic' then
        CancelEvent()
    end

    amount = ESX.Math.Round(tonumber(amount))

	if exports['cc_core']:isRestart() then
        Notify(playerId, 'Banking', 'Du kannst 5 Minuten vor der Sonnenwende nix einzahlen!', 'info')
		return
    end

    if job == 'unemployed' then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [FRAC-2]", false, "Afrika")
    end

    if amount > 0 and ESX.GetPlayerMoney(playerId) >= amount then
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_' .. job, function(account)
            local jobLabel = ESX.GetPlayerJob(playerId).label
            exports['cc_core']:log(playerId, 'Fraktionskasse Logs', 'Der Spieler **' .. GetPlayerName(playerId) .. '** zahlt **' ..  amount .. '$** ' .. ' in die Fraktionskasse von **' .. jobLabel .. '**!', 'https://canary.discord.com/api/webhooks/1352803830827450434/OS6Doz4m2DQui9GwIbiIOPh4xyGEUFjOs6lQTvia3wD__BY_-bEB-A6NVlVpwfnEBvVc')
            ESX.RemovePlayerMoney(playerId, amount, GetCurrentResourceName())
            account.addMoney(amount)    
            Notify(playerId, 'Information', 'Du hast ' .. amount .. '$ eingezahlt', 'info')
        end)
    else
        Notify(playerId, 'Information', 'Ungültiger Betrag', 'error')
    end
end)

RegisterServerEvent('cc_core:society:withdrawBlackMoney')
AddEventHandler('cc_core:society:withdrawBlackMoney', function(amount, job3)
    local playerId = source
    local job

    if job3 then
        job = ESX.GetPlayerJob3(playerId).name
    else
        job = ESX.GetPlayerJob(playerId).name
    end

    amount = ESX.Math.Round(tonumber(amount))
    
	if exports['cc_core']:isRestart() then
        Notify(playerId, 'Banking', 'Du kannst 5 Minuten vor der Sonnenwende nix auszahlen!', 'info')
		return
    end

    if job == 'unemployed' then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [FRAC-3]", false, "Afrika")
    end

    TriggerEvent('esx_addonaccount:getSharedAccount', 'society_' .. job, function(account)
        if amount > 0 and account.money >= amount then
            account.removeMoney(amount)
            ESX.AddPlayerAccountMoney(playerId, 'black_money', amount, GetCurrentResourceName())

            local jobLabel = ESX.GetPlayerJob(playerId).label

			exports['cc_core']:log(playerId, 'Fraktionskasse Logs', 'Der Spieler **' .. GetPlayerName(playerId) .. '** hebt **' ..  amount .. '$** ' .. ' von der (Schwarz) Fraktionskasse **' .. jobLabel .. '** ab!', 'https://canary.discord.com/api/webhooks/1352803830827450434/OS6Doz4m2DQui9GwIbiIOPh4xyGEUFjOs6lQTvia3wD__BY_-bEB-A6NVlVpwfnEBvVc')

            Notify(playerId, 'Information', 'Du hast ' .. amount .. '$ Schwarzgeld abgehoben', 'info')
        else
            Notify(playerId, 'Information', 'Ungültiger Betrag', 'error')
        end
    end)
end)

RegisterServerEvent('cc_core:society:depositBlackMoney')
AddEventHandler('cc_core:society:depositBlackMoney', function(amount, job3)
    local playerId = source
    local job

    if job3 then
        job = ESX.GetPlayerJob3(playerId).name
    else
        job = ESX.GetPlayerJob(playerId).name
    end

    amount = ESX.Math.Round(tonumber(amount))

	if exports['cc_core']:isRestart() then
        Notify(playerId, 'Banking', 'Du kannst 5 Minuten vor der Sonnenwende nix einzahlen!', 'info')
		return
    end

    if job == 'unemployed' then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [FRAC-4]", false, "Afrika")
    end

    if amount > 0 and ESX.GetPlayerAccount(playerId, 'black_money').money >= amount then
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_' .. job, function(account)
            ESX.RemovePlayerAccountMoney(playerId, 'black_money', amount, GetCurrentResourceName())
            account.addMoney(amount)

            local jobLabel = ESX.GetPlayerJob(playerId).label

            exports['cc_core']:log(playerId, 'Fraktionskasse Logs', 'Der Spieler **' .. GetPlayerName(playerId) .. '** zahlt **' ..  amount .. '$** ' .. ' in die (Schwarz) Fraktionskasse von **' .. jobLabel .. '**!', 'https://canary.discord.com/api/webhooks/1352803830827450434/OS6Doz4m2DQui9GwIbiIOPh4xyGEUFjOs6lQTvia3wD__BY_-bEB-A6NVlVpwfnEBvVc')
    
            Notify(playerId, 'Information', 'Du hast ' .. amount .. '$ Schwarzgeld eingezahlt', 'info')
        end)
    else
        Notify(playerId, 'Information', 'Ungültiger Betrag', 'error')
    end
end)

ESX.RegisterServerCallback('cc_core:society:getSocietyMoney', function(source, cb, job3)
    local job
    
    if job3 then
        job = ESX.GetPlayerJob3(source).name
    else
        job = ESX.GetPlayerJob(source).name
    end

    if job == 'unemployed' then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [FRAC-5]", false, "Afrika")
    end

	TriggerEvent('esx_addonaccount:getSharedAccount', 'society_' .. job, function(account)
		cb(account.money)
	end)
end)

ESX.RegisterServerCallback('cc_core:society:getSocietyBlackMoney', function(source, cb, job3)
    local job
    
    if job3 then
        job = ESX.GetPlayerJob3(source).name
    else
        job = ESX.GetPlayerJob(source).name
    end

    if job == 'unemployed' then
        TriggerEvent("EasyAdmin:banPlayer", playerId, "C-D [FRAC-6]", false, "Afrika")
    end

	TriggerEvent('esx_addonaccount:getSharedAccount', 'society_' .. job, function(account)
		cb(account.money)
	end)
end)

ESX.RegisterServerCallback('cc_core:society:isBoss', function(source, cb, job3)
    local grade_name
    
    if job3 then
        grade_name = ESX.GetPlayerJob3(source).grade_name
    else
        grade_name = ESX.GetPlayerJob(source).grade_name
    end

    cb(grade_name == 'boss')
end)

ESX.RegisterServerCallback('cc_core:society:getEmployees', function(source, cb, job3)
    local job
    
    if job3 then
        job = ESX.GetPlayerJob3(source).name
    else
        job = ESX.GetPlayerJob(source).name
    end

    if job == 'unemployed' then
        return
    end

    if job3 then
        MySQL.Async.fetchAll('SELECT firstname, lastname, identifier, job3, job3_grade FROM users WHERE job3 = @job ORDER by job_grade DESC', {
            ['@job'] = job
        }, function(result)
            if #result ~= 0 then
                local employees = {}
    
                for k, v in pairs(result) do
                    local xPlayer = ESX.GetPlayerFromIdentifier(v.identifier)
                    local status = 'Offline'
    
                    if xPlayer then
                        status = 'Online'
                    end
    
                    table.insert(employees, {
                        name = v.firstname .. ' ' .. v.lastname,
                        status = status,
                        identifier = v.identifier,
                        job = {
                            name = v.job3,
                            label = jobs[v.job3].label,
                            grade = v.job3_grade,
                            grade_name = jobs[v.job3].grades[tostring(v.job3_grade)].name,
                            grade_label = jobs[v.job3].grades[tostring(v.job3_grade)].label
                        }
                    })
                end
    
                cb(employees)
            end
        end)
    else
        MySQL.Async.fetchAll('SELECT firstname, lastname, identifier, job, job_grade FROM users WHERE job = @job ORDER by job_grade DESC', {
            ['@job'] = job
        }, function(result)
            if #result ~= 0 then
                local employees = {}
    
                for k, v in pairs(result) do
                    local xPlayer = ESX.GetPlayerFromIdentifier(v.identifier)
                    local status = 'Offline'
    
                    if xPlayer then
                        if exports['cc_core']:isJobLegal(xPlayer.job.name) then
                            if xPlayer.job.dienst then
                                status = 'Im Dienst'
                            else
                                status = 'Nicht im Dienst'
                            end
                        else
                            status = 'Online'
                        end
                    end
    
                    table.insert(employees, {
                        name = v.firstname .. ' ' .. v.lastname,
                        status = status,
                        identifier = v.identifier,
                        job = {
                            name = v.job,
                            label = jobs[v.job].label,
                            grade = v.job_grade,
                            grade_name = jobs[v.job].grades[tostring(v.job_grade)].name or 'NaN',
                            grade_label = jobs[v.job].grades[tostring(v.job_grade)].label
                        }
                    })
                end
    
                cb(employees)
            end
        end)
    end
end)

ESX.RegisterServerCallback('cc_core:society:getEmployees_2', function(source, cb, id, job2Type)
    MySQL.Async.fetchAll('SELECT firstname, lastname, identifier, job2, job2_grade, job2_type FROM users WHERE job2 = @job2 AND job2_type = @job2_type ORDER by job2_grade DESC', {
        ['@job2'] = id,
        ['@job2_type'] = job2Type
    }, function(result)
        if #result ~= 0 then
            local employees = {}

            for k, v in pairs(result) do
                local xPlayer = ESX.GetPlayerFromIdentifier(v.identifier)
                local status = 'Offline'

                if xPlayer then
                    status = 'Online'
                end

                table.insert(employees, {
                    name = v.firstname .. ' ' .. v.lastname,
                    status = status,
                    identifier = v.identifier,
                    job = {
                        id = v.job2,
                        grade = v.job2_grade,
                        type = v.job2_type,
                    }
                })
            end

            cb(employees)
        else
            cb({})
        end
    end)
end)

RegisterServerEvent('cc_core:society:setJob2')
AddEventHandler('cc_core:society:setJob2', function(identifier, sType, shopId)
    local playerId = source
    
    if sType == 'supermarket' then
        local isOwner = exports['cc_core']:isShopOwner(playerId, shopId)

        if isOwner then
            if ESX.GetPlayerIdentifier(playerId) ~= identifier then
                local xTarget = ESX.GetPlayerFromIdentifier(identifier)

                if xTarget then
                    xTarget.setJob2('unemployed', '0', 'unemployed')
                    Notify(xTarget.source, 'Information', 'Du wurdest aus einem Business gefeuert', 'info')
                else
                    MySQL.Async.execute('UPDATE users SET job2 = @job2, job2_grade = @job2_grade, job2_type = @job2_type WHERE identifier = @identifier', {
                        ['@job2'] = 'unemployed',
                        ['@job2_grade'] = 0,
                        ['@job2_type'] = 'unemployed',
                        ['@identifier'] = identifier
                    })
                end
            end
        end
    elseif sType == 'fuel' then
        local isOwner = exports['cc_core']:isFuelOwner(playerId, shopId)

        if isOwner then
            if ESX.GetPlayerIdentifier(playerId) ~= identifier then
                local xTarget = ESX.GetPlayerFromIdentifier(identifier)

                if xTarget then
                    xTarget.setJob2('unemployed', '0', 'unemployed')
                    Notify(xTarget.source, 'Information', 'Du wurdest aus einem Business gefeuert', 'info')
                else
                    MySQL.Async.execute('UPDATE users SET job2 = @job2, job2_grade = @job2_grade, job2_type = @job2_type WHERE identifier = @identifier', {
                        ['@job2'] = 'unemployed',
                        ['@job2_grade'] = 0,
                        ['@job2_type'] = 'unemployed',
                        ['@identifier'] = identifier
                    })
                end
            end
        end
    end
end)

ESX.RegisterServerCallback('cc_core:society:getJob', function(source, cb, job3)
    local job
    
    if job3 then
        job = json.decode(json.encode(jobs[ESX.GetPlayerJob3(source).name]))
    else
        job = json.decode(json.encode(jobs[ESX.GetPlayerJob(source).name]))
    end

    local grades = {}

    for k, v in pairs(job.grades) do
        table.insert(grades, v)
    end

    table.sort(grades, function(a, b)
        return a.grade < b.grade
    end)

    job.grades = grades

    cb(job)
end)

RegisterServerEvent('cc_core:society:setJob')
AddEventHandler('cc_core:society:setJob', function(identifier, job, grade, type, job3)
    local playerId = source
    local name, grade_name

    if job3 then
        name, grade_name = ESX.GetPlayerJob3(playerId).name, ESX.GetPlayerJob3(playerId).grade_name
    else
        name, grade_name = ESX.GetPlayerJob(playerId).name, ESX.GetPlayerJob(playerId).grade_name
    end

    if grade_name then
        local xTarget = ESX.GetPlayerFromIdentifier(identifier)

        if xTarget then
            if type == 'promote' then
                if name == job then
                    Notify(xTarget.source, 'Information', 'Du wurdest befördert', 'info')

                    if job3 then
                        xTarget.setJob3(job, grade)
                    else
                        xTarget.setJob(job, grade)
                    end
                end
            elseif type == 'fire' then
                Notify(xTarget.source, 'Information', 'Du wurdest gefeuert', 'info')

                if job3 then
                    xTarget.setJob3(job, grade)
                else
                    xTarget.setJob(job, grade)

                    if exports['cc_core']:isJobLegal(name) then
                        --xTarget.setLevel(5)
                    else
                        xTarget.setLevel(0)
                    end

                    exports['cc_core']:doubleLog(playerId, target, 'Kick Job - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat ' .. GetPlayerName(target) .. ' aus der fraktion ' .. name .. ' geworfen', 'https://canary.discord.com/api/webhooks/1337038347469590528/dirKLJ1YDDRN9g5O5a0HmWj3_LGNyt9TAVT81m3D2zCbtBo8UlnQ-Eq0xIVqnrbPar2H')
                end
            end
        else
            if job3 then
                MySQL.Async.execute('UPDATE users SET job3 = @job, job3_grade = @job_grade WHERE identifier = @identifier', {
                    ['@job'] = job,
                    ['@job_grade'] = grade,
                    ['@identifier'] = identifier
                })
            else
                local setLevel
                if exports['cc_core']:isJobLegal(name) then
                    setLevel = 5
                else
                    setLevel = 0
                end

                MySQL.Async.execute('UPDATE users SET job = @job, job_grade = @job_grade, level = @level WHERE identifier = @identifier', {
                    ['@job'] = job,
                    ['@job_grade'] = grade,
                    ['@level'] = setLevel,
                    ['@identifier'] = identifier
                })

                exports['cc_core']:log(playerId, 'Kick Job - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat ' .. identifier .. ' aus der fraktion ' .. name .. ' geworfen', 'https://canary.discord.com/api/webhooks/1337038347469590528/dirKLJ1YDDRN9g5O5a0HmWj3_LGNyt9TAVT81m3D2zCbtBo8UlnQ-Eq0xIVqnrbPar2H')
            end
        end
    end
end)

--clientcode
societyCode = [[
local function openFractionTresure(job3)
    ESX.TriggerServerCallback('cc_core:society:getSocietyMoney', function(money)
		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'fractionTresure_' .. ESX.GetPlayerData().job.name, {
			title = 'Fraktionskasse',
			align = 'top-left',
			elements = {
				{ label = 'Kasse: - <span style="color: green;">' .. money .. '$</span>' },
				{ label = 'Geld Einzahlen', value = 'deposit_money' },
				{ label = 'Geld Auszahlen', value = 'withdraw_money' }
			}
		}, function(data, menu)
			if data.current.value == 'withdraw_money' then
				ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'withdraw_money_amount_' .. ESX.GetPlayerData().job.name, {
					title = 'Auszahlungsbetrag'
				}, function(data, menu)
                    local amount = tonumber(data.value)

                    if amount ~= nil then
                        menu.close()
                        TriggerServerEvent('cc_core:society:withdrawMoney', amount, job3)
                        openFractionTresure(job3)
                    end
                end, function(data, menu)
                    menu.close()
                end)
			elseif data.current.value == 'deposit_money' then
				ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'deposit_money_amount_' .. ESX.GetPlayerData().job.name, {
					title = 'Einzahlungsbetrag'
				}, function(data, menu)
					local amount = tonumber(data.value)

					if amount ~= nil then
						menu.close()
						TriggerServerEvent('cc_core:society:depositMoney', amount, job3)
						openFractionTresure(job3)
					end
				end, function(data, menu)
					menu.close()
				end)
			end
		end, function(data, menu)
			menu.close()
		end)
    end, job3)
end

local function openFractionBlackTresure(job3)
    ESX.TriggerServerCallback('cc_core:society:getSocietyBlackMoney', function(money)
		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'fractionBlackTresure_' .. ESX.GetPlayerData().job.name, {
			title = 'Fraktionskasse Schwarzgeld',
			align = 'top-left',
			elements = {
				{ label = 'Kasse: - <span style="color: red;">' .. money .. '$</span>' },
				{ label = 'Geld Einzahlen', value = 'deposit_money' },
				{ label = 'Geld Auszahlen', value = 'withdraw_money' }
			}
		}, function(data, menu)
			if data.current.value == 'withdraw_money' then
				ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'withdraw_black_money_amount_' .. ESX.GetPlayerData().job.name, {
					title = 'Auszahlungsbetrag'
				}, function(data, menu)
                    local amount = tonumber(data.value)

                    if amount ~= nil then
                        menu.close()
                        TriggerServerEvent('cc_core:society:withdrawBlackMoney', amount, job3)
                        openFractionBlackTresure(job3)
                    end
                end, function(data, menu)
                    menu.close()
                end)
			elseif data.current.value == 'deposit_money' then
				ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'deposit_black_money_amount_' .. ESX.GetPlayerData().job.name, {
					title = 'Einzahlungsbetrag'
				}, function(data, menu)
					local amount = tonumber(data.value)

					if amount ~= nil then
						menu.close()
						TriggerServerEvent('cc_core:society:depositBlackMoney', amount, job3)
						openFractionBlackTresure(job3)
					end
				end, function(data, menu)
					menu.close()
				end)
			end
		end, function(data, menu)
			menu.close()
		end)
    end, job3)
end

local function openEmployeeList(job3)
    ESX.TriggerServerCallback('cc_core:society:getEmployees', function(employees) 
        local elements = {
            head = { 'Mitarbeiter', 'Status', 'Rang', 'Aktionen' },
            rows = {}
        }

        for k, v in pairs(employees) do
            local gradeLabel = (v.job.grade_label == '' and v.job.label or v.job.grade_label)

			table.insert(elements.rows, {
				data = v,
				cols = {
					v.name,
					v.status,
					gradeLabel,
					'{{' .. 'fördern' .. '|promote}} {{' .. 'feuern' .. '|fire}}'
				}
			})
        end

        ESX.UI.Menu.Open('list', GetCurrentResourceName(), 'employee_list_' .. ESX.GetPlayerData().job.name, elements, function(data, menu)
			local employee = data.data

			if data.value == 'promote' then
				menu.close()

                ESX.TriggerServerCallback('cc_core:society:getJob', function(job)
                    local elements = {}
            
                    for k, v in pairs(job.grades) do
                        local gradeLabel = (v.label == '' and job.label or v.label)
            
                        table.insert(elements, {
                            label = gradeLabel,
                            value = v.grade,
                            selected = (employee.job.grade == v.grade)
                        })
                    end
            
                    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'promote_employee_' .. ESX.GetPlayerData().job.name, {
                        title = 'fördern ' .. employee.name,
                        align = 'top-left',
                        elements = elements
                    }, function(data, menu)
                        menu.close()
                        Notify('Information', 'Du hast ' .. employee.name .. ' Befördert', 'info')

                        TriggerServerEvent('cc_core:society:setJob', employee.identifier, ESX.GetPlayerData().job.name, data.current.value, 'promote', job3)

                        Citizen.Wait(500)
                        openEmployeeList(job3)
                    end, function(data, menu)
                        menu.close()
                        openEmployeeList(job3)
                    end)
                end, job3)
			elseif data.value == 'fire' then
                Notify('Information', 'Du hast ' .. employee.name .. ' gefeuert', 'info')
				
                TriggerServerEvent('cc_core:society:setJob', employee.identifier, 'unemployed', 0, 'fire', job3)
                
                Citizen.Wait(500)

                openEmployeeList(job3)
			end
		end, function(data, menu)
			menu.close()
		end)
    end, job3)
end

local function openBossMenu(society, job3)
    local isBoss = nil

    ESX.TriggerServerCallback('cc_core:society:isBoss', function(boss) 
        isBoss = boss
    end, job3)

    while isBoss == nil do
        Citizen.Wait(100)
    end

    if not isBoss then
        return
    end

    local elements = {
        { label = 'Fraktionskasse', value = 'faction_tresure' },
        { label = 'Fraktionskasse Schwarzgeld', value = 'fraction_tresure_black' },
        { label = 'Mitarbeiter Liste', value = 'manage_employees' }
    }

	ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'boss_actions_' .. ESX.GetPlayerData().job.name, {
		title = 'Boss Menu',
		align = 'top-left',
		elements = elements
	}, function(data, menu)
		if data.current.value == 'faction_tresure' then
			openFractionTresure(job3)
		elseif data.current.value == 'fraction_tresure_black' then
			openFractionBlackTresure(job3)
		elseif data.current.value == 'manage_employees' then
            openEmployeeList(job3)
		end
	end, function(data, menu)
        menu.close()
	end)
end

AddEventHandler('cc_core:society:openBossMenu', function(society, job3)
	if job3 == nil then
		job3 = false
	end

	openBossMenu(society, job3)
end)
]]