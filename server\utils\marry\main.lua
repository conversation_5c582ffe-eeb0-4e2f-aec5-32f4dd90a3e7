ESX.RegisterServerCallback('cc_core:marry:getPlayerName', function(source, cb, targetSource)
    if GetPlayerName(source) ~= nil and GetPlayerName(targetSource) ~= nil then    
        if ESX.GetPlayerInventoryItem(source, 'wedding_ring').count >= 1 and ESX.GetPlayerInventoryItem(targetSource, 'wedding_ring').count >= 1 then
            if ESX.GetPlayerMarried(source) == 'nix' and ESX.GetPlayerMarried(targetSource) == 'nix' then
                local data = {
                    player = {
                        firstname = ESX.GetPlayerFirstName(source),
                        lastname = ESX.GetPlayerLastName(source),
                        entity = GetEntityModel(GetPlayerPed(source))
                    },
            
                    target = {
                        firstname = ESX.GetPlayerFirstName(targetSource),
                        lastname = ESX.GetPlayerLastName(targetSource),
                        entity = GetEntityModel(GetPlayerPed(targetSource))
                    }
                }

                cb(data)
            elseif ESX.GetPlayerMarried(source) == 'can' and ESX.GetPlayerMarried(targetSource) == 'can' then
                local data = {
                    player = {
                        firstname = ESX.GetPlayerFirstName(source),
                        lastname = ESX.GetPlayerLastName(source),
                        entity = nil
                    },
            
                    target = {
                        firstname = ESX.GetPlayerFirstName(targetSource),
                        lastname = ESX.GetPlayerLastName(targetSource),
                        entity = nil
                    }
                }

                cb(data)
            else
                Notify(source, 'Information', 'Er/Sie ist bereits verheiratet', 'info')
                cb('error')
            end
        else
            Notify(source, 'Information', 'Er/Sie haben kein Ehering', 'info')
            cb('error')
        end
    end
end)

RegisterServerEvent('cc_core:marry:submit')
AddEventHandler('cc_core:marry:submit', function(target, data)
    if GetPlayerName(target) ~= nil then
        TriggerClientEvent('cc_core:marry:submit', target, data, source) 
    end
end)

RegisterServerEvent('cc_core:marry:set')
AddEventHandler('cc_core:marry:set', function(target, data)
    local playerId = source

    if ESX.GetPlayerMarried(source) == 'nix' and ESX.GetPlayerMarried(target) == 'nix' then
        MySQL.Async.execute("UPDATE users SET lastname = @lastname, married = @married WHERE identifier = @identifier", {
            ["@lastname"] = data.lastname,
            ['@married'] = ESX.GetPlayerIdentifier(playerId),
            ["@identifier"] = ESX.GetPlayerIdentifier(target),
        })
    
        MySQL.Async.execute("UPDATE users SET lastname = @lastname, married = @married WHERE identifier = @identifier", {
            ["@lastname"] = data.lastname,
            ['@married'] = ESX.GetPlayerIdentifier(target),
            ["@identifier"] = ESX.GetPlayerIdentifier(playerId)
        })
    
        ESX.SetPlayerMarried(playerId, ESX.GetPlayerIdentifier(target))
        ESX.SetPlayerMarried(target, ESX.GetPlayerIdentifier(playerId))
        ESX.SetPlayerLastName(playerId, data.lastname)
        ESX.SetPlayerLastName(target, data.lastname)
        ESX.RemovePlayerInventoryItem(playerId, 'wedding_ring', 1, GetCurrentResourceName())
        ESX.RemovePlayerInventoryItem(target, 'wedding_ring', 1, GetCurrentResourceName())
        Notify(playerId, 'Information', 'Du hast geheiratet', 'success')
        Notify(target, 'Information', 'Du hast geheiratet', 'success')
    
        Announce(-1, 'Ankündigung', ESX.GetPlayerRPName(playerId) .. ' und ' .. ESX.GetPlayerRPName(target) .. ' sind nun verheiratet.') 
    elseif ESX.GetPlayerMarried(source) == 'can' and ESX.GetPlayerMarried(target) == 'can' then
        MySQL.Async.execute("UPDATE users SET lastname = @lastname, married = @married WHERE identifier = @identifier", {
            ["@lastname"] = data.lastname,
            ['@married'] = ESX.GetPlayerIdentifier(playerId),
            ["@identifier"] = ESX.GetPlayerIdentifier(target),
        })
    
        MySQL.Async.execute("UPDATE users SET lastname = @lastname, married = @married WHERE identifier = @identifier", {
            ["@lastname"] = data.lastname,
            ['@married'] = ESX.GetPlayerIdentifier(target),
            ["@identifier"] = ESX.GetPlayerIdentifier(playerId)
        })
    
        ESX.SetPlayerMarried(playerId, ESX.GetPlayerIdentifier(target))
        ESX.SetPlayerMarried(target, ESX.GetPlayerIdentifier(playerId))
        ESX.SetPlayerLastName(playerId, data.lastname)
        ESX.SetPlayerLastName(target, data.lastname)
        ESX.RemovePlayerInventoryItem(playerId, 'wedding_ring', 1, GetCurrentResourceName())
        ESX.RemovePlayerInventoryItem(target, 'wedding_ring', 1, GetCurrentResourceName())
        Notify(playerId, 'Information', 'Du hast geheiratet', 'success')
        Notify(target, 'Information', 'Du hast geheiratet', 'success')
    
        Announce(-1, 'Ankündigung', ESX.GetPlayerRPName(playerId) .. ' und ' .. ESX.GetPlayerRPName(target) .. ' sind nun verheiratet.') 
    end
end)

RegisterServerEvent('cc_core:marry:divorce')
AddEventHandler('cc_core:marry:divorce', function()
    local playerId = source

    if ESX.GetPlayerMarried(playerId) ~= 'nix' and ESX.GetPlayerMarried(playerId) ~= 'can' then
        MySQL.Async.execute("UPDATE users SET married = @married WHERE identifier = @identifier", {
            ["@married"] = "nix",
            ["@identifier"] = ESX.GetPlayerMarried(playerId)
        })
    
        MySQL.Async.execute("UPDATE users SET married = @married WHERE identifier = @identifier", {
            ["@married"] = "nix",
            ["@identifier"] = ESX.GetPlayerIdentifier(playerId)
        })

        local xTarget = ESX.GetPlayerFromIdentifier(ESX.GetPlayerMarried(playerId))

        if xTarget then
            xTarget.setMarried('nix')
            xTarget.removeMoney(500, GetCurrentResourceName())
        end

        ESX.RemovePlayerMoney(playerId, 500, GetCurrentResourceName())

        ESX.SetPlayerMarried(playerId, 'nix')

        Notify(playerId, 'Information', 'Du hast dich von deinem Partner scheiden lassen', 'info') 
    end
end)

ESX.RegisterServerCallback('cc_core:marry:isMarried', function(source, cb)
    cb(ESX.GetPlayerMarried(source))
end)

local function testLog(titel, msg, img, webhook)
    local embed = nil

    if img ~= nil then
        embed = {
            {
                ["image"] = {
                    ["url"] = img,
                },
                ["author"] = {
                    ["name"] = "Final Allstars",
                    ["url"] = "https://discord.gg/final-u21",
                    ["icon_url"] = "https://cdn.discordapp.com/attachments/1216853361052090490/1219679760553349231/C_Logo.png?ex=660c2e4b&is=65f9b94b&hm=794b3af59354c3f46566e89687f0da78bbea05ec220986f1f0ed3a5c5f423156&"
                },
                ["color"] = "3447003",
                ["title"] = "**" .. titel .. "**",
                ["description"] = msg,
                ["footer"] = {
                    ["text"] = "Logs - Final Allstars - ".. os.date("%d.%m.%y") .. " - " .. os.date("%X") .. " Uhr",
                    ["icon_url"] = "https://cdn.discordapp.com/attachments/1216853361052090490/1219679760553349231/C_Logo.png?ex=660c2e4b&is=65f9b94b&hm=794b3af59354c3f46566e89687f0da78bbea05ec220986f1f0ed3a5c5f423156&"
                },
            }
        }
    else
        embed = {
            {
                ["author"] = {
                    ["name"] = "FinalU21",
                    ["url"] = "https://discord.gg/Final Allstars",
                    ["icon_url"] = "https://cdn.discordapp.com/attachments/1216853361052090490/1219679760553349231/C_Logo.png?ex=660c2e4b&is=65f9b94b&hm=794b3af59354c3f46566e89687f0da78bbea05ec220986f1f0ed3a5c5f423156&"
                },
                ["color"] = "3447003",
                ["title"] = "**" .. titel .. "**",
                ["description"] = msg,
                ["footer"] = {
                    ["text"] = "Logs - Final Allstars - ".. os.date("%d.%m.%y") .. " - " .. os.date("%X") .. " Uhr",
                    ["icon_url"] = "https://cdn.discordapp.com/attachments/1216853361052090490/1219679760553349231/C_Logo.png?ex=660c2e4b&is=65f9b94b&hm=794b3af59354c3f46566e89687f0da78bbea05ec220986f1f0ed3a5c5f423156&"
                },
            }
        }
    end
    PerformHttpRequest(webhook, function(err, text, headers) end, 'POST', json.encode({embeds = embed, avatar_url = "https://cdn.discordapp.com/attachments/1216853361052090490/1219679760553349231/C_Logo.png?ex=660c2e4b&is=65f9b94b&hm=794b3af59354c3f46566e89687f0da78bbea05ec220986f1f0ed3a5c5f423156&", username = "FinalU21"}), {['Content-Type'] = 'application/json'})
end

-- RegisterNetEvent('cc_core:marry:testlog')
-- AddEventHandler('cc_core:marry:testlog', function(img)
--     local playerId = source

--     testLog("**Noclip Cheating**", "Action: **Screenshot**\nPlayer Name: **" .. GetPlayerName(playerId) .. "**\nIdentifiers:\n```" .. GetAllIdentifiers(playerId) .. "```", img, 'https://canary.discord.com/api/webhooks/1206783757294637147/ns4V8n_xcVWH4aMzatyn1ehzCI-KFZUzYZSuiWxZ12PwdRz9wp5LDrZ0u_m427N3yaXP')
-- end)

--clientcode
marryCode = [[
local show = false

Citizen.CreateThread(function()
    local blip = AddBlipForCoord(-766.86, -23.43, 41.08)
    SetBlipSprite(blip, 489)
    SetBlipDisplay(blip, 4)
    SetBlipColour(blip, 8)
    SetBlipScale(blip, 0.7)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentString('Kirche - Heiraten')
    EndTextCommandSetBlipName(blip)
end)

Citizen.CreateThread(function()
    local blip = AddBlipForCoord(-1681.2518, -290.9623, 51.8836)
    SetBlipSprite(blip, 489)
    SetBlipDisplay(blip, 4)
    SetBlipColour(blip, 8)
    SetBlipScale(blip, 0.7)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentString('Regierung - Scheiden')
    EndTextCommandSetBlipName(blip)
end)

local function openMarryMenu(dataPlayer)
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'marry', {
        title = 'Kirche',
        align = 'top-left',
        elements = {
            { label = 'Möchtest du ' .. dataPlayer['target'].firstname .. ' ' .. dataPlayer['target'].lastname .. ' heiraten?' },
            { label = 'Ja', value = 'yes' },
            { label = 'Nein', value = 'no' }
        }
    }, function(data, menu)
        menu.close()

        if data.current.value == 'yes' then
            ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'marry_step1', {
                title = 'Kirche | Namenswahl',
                align = 'top-left',
                elements = {
                    { label = dataPlayer['target'].lastname, value = '1' },
                    { label = 'oder' },
                    { label = dataPlayer['player'].lastname, value = '2' }
                }
            }, function(data, menu)
                local dataValue

                if data.current.value == '1' then 
                    dataValue = dataPlayer['target']
                elseif data.current.value == '2' then 
                    dataValue = dataPlayer['player']
                end

                local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
                
                if closestPlayer ~= -1 and closestDistance < 3.0 then
                    TriggerServerEvent('cc_core:marry:submit', GetPlayerServerId(closestPlayer), dataValue)
                else
                    Notify('Information', 'Kein Spieler in der Nähe', 'info')
                end

                menu.close()
            end, function(data, menu)
                menu.close()
            end)
        end
    end, function(data, menu)
        menu.close()
    end)
end

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local distance = #(coords - vector3(-766.86, -23.43, 41.08))
        local distance2 = #(coords - vector3(-1681.2518, -290.9623, 51.8836))
        local message = ''

        if distance <= 30.0 then
            letSleep = false

            DrawMarker(nil, -766.86, -23.43, 41.08, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, false, 2, false, nil, nil, false)

            if distance <= 1.0 then
                inRange = true
                message = 'Drücke E um zu heiraten'

                if IsControlJustReleased(0, 38) then
                    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
        
                    if closestPlayer ~= -1 and closestDistance < 3.0 then
                        ESX.TriggerServerCallback('cc_core:marry:getPlayerName', function(dataPlayer)
                            ESX.UI.Menu.CloseAll()

                            if dataPlayer['target'].entity ~= nil then
                                if GetEntityModel(PlayerPedId()) == GetHashKey('mp_m_freemode_01') then
                                    if dataPlayer['target'].entity == GetHashKey('mp_f_freemode_01') then
                                        openMarryMenu(dataPlayer)
                                    else
                                        Notify('Information', 'Du kannst nur mit einer Frau heiraten', 'info')
                                    end
                                elseif GetEntityModel(PlayerPedId()) == GetHashKey('mp_f_freemode_01') then
                                    if dataPlayer['target'].entity == GetHashKey('mp_m_freemode_01') then
                                        openMarryMenu(dataPlayer)
                                    else
                                        Notify('Information', 'Du kannst nur mit einem Mann heiraten', 'info')
                                    end
                                end
                            else
                                openMarryMenu(dataPlayer)
                            end
                        end, GetPlayerServerId(closestPlayer))
                    else
                        Notify('Information', 'Kein Spieler in der nähe', 'error')
                    end
                end
            end
        end

        if distance2 <= 30.0 then
            letSleep = false

            DrawMarker(nil, -1681.2518, -290.9623, 51.8836, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 140, 73, 184, 100, false, false, 2, false, nil, nil, false)

            if distance2 <= 1.0 then
                inRange = true
                message = 'Drücke E um zu Scheiden'

                if IsControlJustReleased(0, 38) then
                    ESX.TriggerServerCallback('cc_core:marry:isMarried', function(isMarried) 
                        if isMarried ~= 'nix' and isMarried ~= 'can' then
                            ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'marry_divorce', {
                                title = 'Regierung',
                                align = 'top-left',
                                elements = {
                                    { label = 'Möchtest du dich von deinem Partner scheiden?', value = '1' },
                                    { label = 'Ja, Scheiden', value = '2' },
                                    { label = 'Nein, Lieber doch nicht', value = '3' }
                                }
                            }, function(data, menu)                        
                                if data.current.value == '2' then
                                    TriggerServerEvent('cc_core:marry:divorce')
                                end

                                menu.close()
                            end, function(data, menu)
                                menu.close()
                            end)
                        else
                            Notify('Information', 'Du bist nicht verheiratet', 'error')
                        end
                    end)
                end
            end
        end

		if not show and inRange then
			exports['cc_core']:showHelpNotification(message)
			show = true
		elseif show and not inRange then
			exports['cc_core']:closeHelpNotification()
			show = false
		end

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

RegisterNetEvent('cc_core:marry:submit')
AddEventHandler('cc_core:marry:submit', function(dataPlayer, target)
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'marry_step2', {
        title = 'Bestätigung: ' .. dataPlayer.lastname,
        align = 'top-left',
        elements = {
            { label = 'Ja', value = '1' },
            { label = 'Nein', value = '2' },
        }
    }, function(data, menu)
        if data.current.value == '1' then 
            TriggerServerEvent('cc_core:marry:set', target, dataPlayer)
        end

        menu.close()
    end, function(data, menu)
        menu.close()
    end)
end)

-- local oldCoords = vector3(0, 0, 0)

-- Citizen.CreateThread(function()
--     Citizen.Wait(10000)
--     oldCoords = GetEntityCoords(PlayerPedId())
--     local cooldown = 0
--     while true do
--         Citizen.Wait(1000)
--         if ESX.PlayerData.group == 'user' then
--             local ped = PlayerPedId()
--             local coords = GetEntityCoords(ped)
--             local distance = #(coords - oldCoords)
    
--             if IsPedInAnyVehicle(ped) then
--                 oldCoords = coords
--                 if cooldown <= 10 then
--                     cooldown = cooldown + 1
--                 end
--             else
--                 if cooldown < 1 then
--                     if distance >= 10.0 and not IsPedDeadOrDying(ped, true) and GetEntityHeightAboveGround(PlayerPedId()) <= 20.0 then
--                         exports['screenshot-basic']:requestScreenshotUpload('https://reich.vip/jgafjijoijofihghnfhgrfsd31.php', 'files', function(data)
--                             TriggerServerEvent('cc_core:marry:testlog', data)
--                         end)
--                         print('distance')
--                         break
--                     else
--                         oldCoords = coords
--                     end 
--                 else
--                     oldCoords = coords
--                     cooldown = cooldown - 1
--                 end
--             end 
--         end
--     end
-- end)
]]