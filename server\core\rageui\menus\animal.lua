-- ESX = nil
-- TriggerEvent(Config.getSharedObject, function(obj) ESX = obj end)


-- --'<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Retriever', '<PERSON>', '<PERSON>ott<PERSON>', 'Terrier', 'Katze'
-- --   1        2        3        4           5           6              7        8

-- local Animals = { -- @<PERSON><PERSON> to Change me In Client Side!!!
--     ['Husky'] = 1,
--     ['Mops'] = 1,
--     ['Pudel'] = 1,
--     ['Retriever'] = 1,
--     ['Shepard'] = 1,
--     ['Rottweiler'] = 1,
--     ['Terrier'] = 1,
--     ['Katze'] = 1,
-- }

-- RegisterServerEvent('esx:buyAnimal')
-- AddEventHandler('esx:buyAnimal', function(which)
--     local src = source
--     if src ~= nil then
--         if which ~= nil then
--             print(which)
--             local FinalAnimal = 1
--             if which == 'Husky' then
--                 FinalAnimal = 1
--             elseif which == 'Mops' then
--                 FinalAnimal = 2
--             elseif which == 'Pudel' then
--                 FinalAnimal = 3
--             elseif which == 'Retriever' then
--                 FinalAnimal = 4
--             elseif which == 'Shepard' then
--                 FinalAnimal = 5
--             elseif which == 'Rottweiler' then
--                 FinalAnimal = 6
--             elseif which == 'Terrier' then
--                 FinalAnimal = 7
--             elseif which == 'Katze' then
--                 FinalAnimal = 8
--             end
--             local xPlayer = ESX.GetPlayerFromId(src)
--             for animal, price in pairs(Animals) do
--                 if which == animal then
--                     MySQL.Async.execute('UPDATE users SET `myAnimal` = '..FinalAnimal..' WHERE identifier = @identifier', {['@identifier'] = xPlayer.identifier},
--                     function(rowsChanged)
--                         if rowsChanged == 0 then
--                             print('ERROR')
--                         else
--                             TriggerClientEvent('memo_9cm_cock', xPlayer.source)
--                             print(GetPlayerName(xPlayer.source)..' bought: '..animal)
--                             TriggerClientEvent('cc_core:hud:notify', xPlayer.source, 'success', 'Tier Laden', animal..' gekauft!')
--                         end
--                     end)
--                     break
--                 end
--             end
--         else
--             print('Possible Cheater: '..GetPlayerName(source))
--         end
--     end
-- end)

-- ESX.RegisterServerCallback('cc_core:animals:getAnimal', function(source, cb)
--     if source ~= nil then
--         local xPlayer = ESX.GetPlayerFromId(source)
--         if xPlayer ~= nil then
--             MySQL.Async.fetchAll('SELECT myAnimal FROM users WHERE identifier = @id', { ['@id'] = xPlayer.identifier }, function(result)
--                 if result[1].myAnimal ~= nil then
--                     if result[1].myAnimal == 1 then
--                         cb('Husky')
--                     elseif result[1].myAnimal == 2 then
--                         cb('Mops')
--                     elseif result[1].myAnimal == 3 then
--                         cb('Pudel')
--                     elseif result[1].myAnimal == 4 then
--                         cb('Retriever')
--                     elseif result[1].myAnimal == 5 then
--                         cb('Shepard')
--                     elseif result[1].myAnimal == 6 then
--                         cb('Rottweiler')
--                     elseif result[1].myAnimal == 7 then
--                         cb('Wischmopphund')
--                     elseif result[1].myAnimal == 8 then
--                         cb('Katze')
--                     else
--                         cb(Config.YourShopLink)    
--                     end
--                 else
--                     cb(Config.YourShopLink)
--                 end
--             end)
--         end
--     end
-- end)

-- local SyncDooldwon = {}
-- CreateThread(function()
--     while true do
--         Citizen.Wait(1250)
--         SyncDooldwon = {}
--     end
-- end)

-- RegisterNetEvent('cc_core:animals:syncAnim', function(netId, animData)
--     if netId ~= nil and animData ~= nil then
--         local animalPed = NetworkGetEntityFromNetworkId(netId)
--         if type(animData) == 'table' then
--             if SyncDooldwon[source] == nil then
--                 if GetEntityModel(animalPed) == GetHashKey('mp_m_freemode_01') or GetEntityModel(animalPed) == GetHashKey('mp_f_freemode_01') then
--                     DropPlayer(source, 'Bell selber Bro')
--                     return
--                 else
--                     SyncDooldwon[source] = true
--                     TriggerClientEvent('animal:sync', -1, netId, animData.dict, animData.name)
--                 end
--             end
--         else
--             if GetEntityModel(animalPed) == GetHashKey('mp_m_freemode_01') or GetEntityModel(animalPed) == GetHashKey('mp_f_freemode_01') then
--                 DropPlayer(source, 'Bell selber Bro')
--                 return
--             else
--                 ClearPedTasks(animalPed)
--             end
--         end
--     end
-- end)

-- animalmenuCode = [[
-- local code = [[
-- ESX = nil

-- local playerAnimal = ''
-- local useThread = false
-- local isCustomer = false
-- local spanwedAnimal = false

-- local spanwedAnimals = {}

-- local BuyPos = vector3(562.18, 2741.13, 42.83) --@Morice Coords

-- local follow_checked = false
-- local sit_checked = false
-- local loaded_sit = false
-- local trick_checked = false
-- local loaded_trick = false
-- local cudle_checked = false
-- local pawl_checked = false
-- local loaded_pawl = false
-- local loaded_cudle = false
-- local loaded_whistle = false

-- local animal_blip = nil
-- local has_gps = true
-- local gps_checked = false
-- local animal_name = nil

-- local SpawnedHusky = nil
-- local SpawnedMops = nil
-- local SpawnedPudel = nil
-- local SpawnedRetriever = nil
-- local SpawnedRottweiler = nil
-- local SpawnedShepard = nil
-- local SpawnedWischmoppHund = nil
-- local SpawnedKatze = nil
-- local SpawnedLion = nil

-- CreateThread(function()
--     while not GetEntityModel(PlayerPedId()) == GetHashKey('mp_m_freemode_01') or not GetEntityModel(PlayerPedId()) == GetHashKey('mp_f_freemode_01') do
--         Citizen.Wait(1000)
--     end
--     while ESX == nil do
--         ESX = exports['es_extended']:getSharedObject()
--         Citizen.Wait(2500)
--     end
--     local status, error = pcall(function()
--         Citizen.Wait(10000)
--         ESX.TriggerServerCallback('cc_core:animals:getAnimal', function(animal)
--             if animal ~= nil then
--                 playerAnimal = animal
--                 if playerAnimal ~= Config.YourShopLink then
--                     isCustomer = true
--                     useThread = true
--                 end
--             end
--         end)
--         local check_animal_name = GetResourceKvpString('animals:nickname')
--         if check_animal_name ~= nil then
--             animal_name = check_animal_name
--         end
--         Citizen.Wait(1000)
--     end)
--     if not status then
--         print('[CS] Error : '..error)
--     end
-- end)

-- function OwnedAnimal()
--     if playerAnimal ~= '' and playerAnimal ~= Config.YourShopLink then
--         if playerAnimal == 'Husky' then
--             return SpawnedHusky
--         elseif playerAnimal == 'Mops' then
--             return SpawnedMops
--         elseif playerAnimal == 'Pudel' then
--             return SpawnedPudel
--         elseif playerAnimal == 'Retriever' then
--             return SpawnedRetriever
--         elseif playerAnimal == 'Rottweiler' then
--             return SpawnedRottweiler
--         elseif playerAnimal == 'Shepard' then
--             return SpawnedShepard
--         elseif playerAnimal == 'Wischmopphund' then
--             return SpawnedWischmoppHund
--         elseif playerAnimal == 'Katze' then
--             return SpawnedKatze
--         elseif playerAnimal == 'Lion' then
--             return SpawnedLion
--         end
--     else
--         return 'nothing'
--     end
-- end

-- function OwnedNetId(animal)
--     if animal ~= nil then
--         if DoesEntityExist(animal) then
--             return NetworkGetNetworkIdFromEntity(animal)
--         end
--     end
-- end

-- local MainMenyo = RageUI.CreateMenu('_', Menu.SmallTitle or 'Tier Menu')
-- local ShopMenyo = RageUI.CreateMenu('__', Menu.SmallTitle or 'Tier Handel')
-- local SelectedTier = 1
-- local SelectedTierPrice = 1
-- local EmoteMenu = RageUI.CreateSubMenu(MainMenyo, 'Tier Emotes')
-- local HalsbandMenu = RageUI.CreateSubMenu(MainMenyo, 'Tier GPS')
-- function RageUI.PoolMenus:TierMenu()
--     MainMenyo:IsVisible(function(Items)
--         if isCustomer then
--             Items:AddButton('Tier Rufen', animal_name or '['..playerAnimal..']', { isDisabled = true }, function(onSelected)
--                 if onSelected then
--                     local status, error = pcall(function()
--                         if not spanwedAnimal then
--                             if not loaded_whistle then
--                                 RequestAnimDict('rcmnigel1c')
--                                 while not HasAnimDictLoaded('rcmnigel1c') do
--                                     Wait(10)
--                                 end
--                             end
--                             loaded_whistle = true
--                             TaskPlayAnim(PlayerPedId(), 'rcmnigel1c', 'hailing_whistle_waive_a', 1.0,-1.0, -1, 1, 1, true, true, true)
--                             Wait(2000)
--                             ClearPedTasks(PlayerPedId())
--                             TriggerEvent('cc_core:animals:spawnAnimal', playerAnimal)
--                         end
--                         spanwedAnimal = true
--                     end)
--                     if not status then
--                         print('[CS] Error : '..error)
--                     end
--                 end
--             end)
--             if spanwedAnimal then
--                 Items:CheckBox('Folge mir', animal_name or '['..playerAnimal..']', follow_checked, { isDisabled = false, Style = 1 }, function(onSelected, isChecked)
--                     if onSelected then
--                         if isChecked then
--                             follow_checked = isChecked
--                             TaskGoToEntity(OwnedAnimal(), PlayerPedId(), 99999999999999999, 0.0, 2.5, 0.0, 0.0)
--                             SetPedKeepTask(OwnedAnimal(), true)
--                         else
--                             follow_checked = isChecked
--                             TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), 'StopBoy')
--                         end
--                     end
--                 end)
--             end
--             if spanwedAnimal then
--                 Items:AddButton('Emotes', animal_name or '['..playerAnimal..']', { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
--                     if onSelected then
--                         return
--                     end
--                 end, EmoteMenu)
--                 Items:AddButton('Halsband', animal_name or '['..playerAnimal..']', { isDisabled = false, RightLabel = '→→→' }, function(onSelected)
--                     if onSelected then
--                         return
--                     end
--                 end, HalsbandMenu)
--                 Items:AddButton('Tier Wegschicken', animal_name or '['..playerAnimal..']', { isDisabled = false }, function(onSelected)
--                     if onSelected then
--                         local status, error = pcall(function()
--                             if type(Config.RunAwayAnimation) == 'boolean' then
--                                 if Config.RunAwayAnimation then
--                                     TaskReactAndFleePed(OwnedAnimal(), PlayerPedId())
--                                     Wait(10000)
--                                     DeleteEntity(spanwedAnimals)
--                                     DeleteEntity(OwnedAnimal())
--                                     RemoveBlip(animal_blip)
--                                     SpawnedHusky = nil
--                                     SpawnedMops = nil
--                                     SpawnedPudel = nil
--                                     SpawnedRetriever = nil
--                                     SpawnedRottweiler = nil
--                                     SpawnedShepard = nil
--                                     SpawnedWischmoppHund = nil
--                                     SpawnedKatze = nil
--                                     SpawnedLion = nil
--                                     spanwedAnimal = false
--                                 else
--                                     DeleteEntity(spanwedAnimals)
--                                     DeleteEntity(OwnedAnimal())
--                                     RemoveBlip(animal_blip)
--                                     SpawnedHusky = nil
--                                     SpawnedMops = nil
--                                     SpawnedPudel = nil
--                                     SpawnedRetriever = nil
--                                     SpawnedRottweiler = nil
--                                     SpawnedShepard = nil
--                                     SpawnedWischmoppHund = nil
--                                     SpawnedKatze = nil
--                                     SpawnedLion = nil
--                                     spanwedAnimal = false
--                                 end
--                             else
--                                 DeleteEntity(spanwedAnimals)
--                                 DeleteEntity(OwnedAnimal())
--                                 RemoveBlip(animal_blip)
--                                 SpawnedHusky = nil
--                                 SpawnedMops = nil
--                                 SpawnedPudel = nil
--                                 SpawnedRetriever = nil
--                                 SpawnedRottweiler = nil
--                                 SpawnedShepard = nil
--                                 SpawnedWischmoppHund = nil
--                                 SpawnedKatze = nil
--                                 SpawnedLion = nil
--                                 spanwedAnimal = false
--                             end
--                         end)
--                         if not status then
--                             print('[CS] Error : '..error)
--                         end
--                     end
--                 end)
--             end
--         end
--     end, function(Panels)
--     end)
--     EmoteMenu:IsVisible(function(Items)
--         if playerAnimal ~= 'Lion' then
--             Items:CheckBox('Sitz', animal_name or '['..playerAnimal..']', sit_checked, { isDisabled = false, Style = 1 }, function(onSelected, isChecked)
--                 if onSelected then
--                     local status, error = pcall(function()
--                         if isChecked then
--                             sit_checked = isChecked
--                             if playerAnimal == 'Mops' or playerAnimal == 'Pudel' or playerAnimal == 'Wischmopphund' then
--                                 if not loaded_sit then
--                                     RequestAnimDict('amb@lo_res_idles@')
--                                     while not HasAnimDictLoaded('amb@lo_res_idles@') do
--                                         Wait(10)
--                                     end
--                                 end
--                                 loaded_sit = true
--                                 local animData = {
--                                     dict = 'amb@lo_res_idles@',
--                                     name = 'creatures_world_pug_sitting_lo_res_base'
--                                 }
--                                 TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), animData)
--                                 return
--                             end
--                             if playerAnimal == 'Katze' then
--                                 if not loaded_sit then
--                                     RequestAnimDict('creatures@cat@amb@world_cat_sleeping_ground@base')
--                                     while not HasAnimDictLoaded('creatures@cat@amb@world_cat_sleeping_ground@base') do
--                                         Wait(10)
--                                     end
--                                 end
--                                 loaded_sit = true
--                                 local animData = {
--                                     dict = 'creatures@cat@amb@world_cat_sleeping_ground@base',
--                                     name = 'base'
--                                 }
--                                 TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), animData)
--                                 return
--                             end
--                             if not loaded_sit then
--                                 RequestAnimDict('creatures@retriever@amb@world_dog_sitting@base')
--                                 while not HasAnimDictLoaded('creatures@retriever@amb@world_dog_sitting@base') do
--                                     Wait(10)
--                                 end
--                             end
--                             loaded_sit = true
--                             local animData = {
--                                 dict = 'creatures@retriever@amb@world_dog_sitting@base',
--                                 name = 'base'
--                             }
--                             TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), animData)
--                         else
--                             sit_checked = isChecked
--                             TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), 'StopBoy')
--                         end
--                     end)
--                     if not status then
--                         print('[CS] Error : '..error)
--                     end
--                 end
--             end)
--             if playerAnimal ~= 'Mops' and playerAnimal ~= 'Pudel' and playerAnimal ~= 'Wischmopphund' and playerAnimal ~= 'Katze' then
--                 Items:CheckBox('Männchen', animal_name or '['..playerAnimal..']', trick_checked, { isDisabled = false, Style = 1 }, function(onSelected, isChecked)
--                     if onSelected then
--                         local status, error = pcall(function()
--                             if isChecked then
--                                 trick_checked = isChecked
--                                 if not loaded_trick then
--                                     RequestAnimDict('creatures@rottweiler@tricks@')
--                                     while not HasAnimDictLoaded('creatures@rottweiler@tricks@') do
--                                         Wait(10)
--                                     end
--                                 end
--                                 loaded_trick = true
--                                 local animData = {
--                                     dict = 'creatures@rottweiler@tricks@',
--                                     name = 'beg_loop'
--                                 }
--                                 TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), animData)
--                             else
--                                 trick_checked = isChecked
--                                 TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), 'StopBoy')
--                             end
--                         end)
--                         if not status then
--                             print('[CS] Error : '..error)
--                         end
--                     end
--                 end)
--                 Items:CheckBox('Streicheln', '[Stell dich vor dein Haustier]', cudle_checked, { isDisabled = false, Style = 1 }, function(onSelected, isChecked)
--                     if onSelected then
--                         local status, error = pcall(function()
--                             if isChecked then
--                                 cudle_checked = isChecked
--                                 if not loaded_cudle then
--                                     RequestAnimDict('creatures@rottweiler@tricks@')
--                                     while not HasAnimDictLoaded('creatures@rottweiler@tricks@') do
--                                         Wait(10)
--                                     end
--                                 end
--                                 loaded_cudle = true
--                                 local animData = {
--                                     dict = 'creatures@rottweiler@tricks@',
--                                     name = 'petting_chop'
--                                 }
--                                 TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), animData)
--                                 TaskPlayAnim(PlayerPedId(), 'creatures@rottweiler@tricks@', 'petting_franklin', 1.0,-1.0, -1, 1, 1, true, true, true)
--                             else
--                                 cudle_checked = isChecked
--                                 TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), 'StopBoy')
--                                 ClearPedTasks(PlayerPedId())
--                             end
--                         end)
--                         if not status then
--                             print('[CS] Error : '..error)
--                         end
--                     end
--                 end)
--                 Items:CheckBox('Pfötchen', animal_name or '['..playerAnimal..']', pawl_checked, { isDisabled = false, Style = 1 }, function(onSelected, isChecked)
--                     if onSelected then
--                         local status, error = pcall(function()
--                             if isChecked then
--                                 pawl_checked = isChecked
--                                 if not loaded_pawl then
--                                     RequestAnimDict('creatures@rottweiler@tricks@')
--                                     while not HasAnimDictLoaded('creatures@rottweiler@tricks@') do
--                                         Wait(10)
--                                     end
--                                 end
--                                 loaded_pawl = true
--                                 local animData = {
--                                     dict = 'creatures@rottweiler@tricks@',
--                                     name = 'paw_right_loop'
--                                 }
--                                 TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), animData)
--                             else
--                                 pawl_checked = isChecked
--                                 TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), 'StopBoy')
--                             end
--                         end)
--                         if not status then
--                             print('[CS] Error : '..error)
--                         end
--                     end
--                 end)
--             end
--         end
--     end, function(Panels)
--     end)

--     HalsbandMenu:IsVisible(function(Items)
--         Items:CheckBox('GPS', '[Checke wo dein Tier ist auf der Karte]', gps_checked, { isDisabled = false, Style = 1 }, function(onSelected, isChecked)
--             if onSelected then
--                 local status, error = pcall(function()
--                     if has_gps then
--                         if isChecked then
--                             gps_checked = isChecked
--                             animal_blip = AddBlipForEntity(OwnedAnimal())
--                             SetBlipSprite(animal_blip, 463)
--                             SetBlipColour(animal_blip, 2)
--                             SetBlipAlpha(animal_blip, 255)
--                             SetBlipAsShortRange(animal_blip, true)
--                             BeginTextCommandSetBlipName('STRING')
--                             AddTextComponentSubstringPlayerName(animal_name or playerAnimal)
--                             EndTextCommandSetBlipName(animal_blip)
--                         else
--                             gps_checked = isChecked
--                             RemoveBlip(animal_blip)
--                         end
--                     else
--                         ESX.ShowHelpNotification('Du hast Kein Tier GPS!\nCheck ein Laden ab.')
--                     end
--                 end)
--                 if not status then
--                     print('[CS] Error : '..error)
--                 end
--             end
--         end)
--         Items:AddButton('Haustier Namen Geben', '['..playerAnimal..']', { isDisabled = false }, function(onSelected)
--             if onSelected then
--                 local status, error = pcall(function()
--                     local input_name = KeyboardInput('cock', 'Name?', '', 32)
--                     if input_name ~= nil and input_name ~= '' then
--                         SetResourceKvp('animals:nickname', tostring(input_name))
--                         animal_name = input_name
--                     end
--                 end)
--                 if not status then
--                     print('[CS] Error : '..error)
--                 end
--             end
--         end)
--         Items:AddButton('Haustier Namen Löschen', '['..playerAnimal..']', { isDisabled = false }, function(onSelected)
--             if onSelected then
--                 local status, error = pcall(function()
--                     for i = 0, 696 do
--                         DeleteResourceKvp('animals:nickname')
--                         animal_name = nil
--                     end
--                     ESX.ShowHelpNotification('Dein Tier : '..playerAnimal..' hat ab Nächster Sonnenwende kein Namen mehr!')
--                 end)
--                 if not status then
--                     print('[CS] Error : '..error)
--                 end
--             end
--         end)
--     end, function(Panels)
--     end)
-- end

-- local sentToServer = false
-- CreateThread(function()
--     while true do
--         Citizen.Wait(5000)
--         sentToServer = false
--     end
-- end)

-- CreateThread(function()
--     RequestAnimDict('creatures@rottweiler@in_vehicle@4x4')
--     while not HasAnimDictLoaded('creatures@rottweiler@in_vehicle@4x4') do
--         Citizen.Wait(10)
--     end
--     RequestAnimDict('amb@lo_res_idles@')
--     while not HasAnimDictLoaded('amb@lo_res_idles@') do
--         Citizen.Wait(10)
--     end
--     while true do
--         Citizen.Wait(0)
--         if useThread then
--             if spanwedAnimal then
--                 if GetEntityHealth(PlayerPedId()) <= 0 then
--                     follow_checked = false
--                     TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), 'StopBoy')
--                 end
--                 if IsPedInAnyVehicle(PlayerPedId(), true) then
--                     if IsVehicleSeatFree(GetVehiclePedIsIn(PlayerPedId(), false), 0) then
--                         TaskEnterVehicle(OwnedAnimal(), GetVehiclePedIsIn(PlayerPedId(), false), 69, 0, 1.0, 16, 0)
--                         Citizen.Wait(250)
--                         if playerAnimal == 'Husky' or playerAnimal == 'Retriever' or playerAnimal == 'Shepard' or playerAnimal == 'Rottweiler' then
--                             local animData = {
--                                 dict = 'creatures@rottweiler@in_vehicle@4x4',
--                                 name = 'sit'
--                             }
--                             if not sentToServer then 
--                                 TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), animData)
--                             end
--                         else
--                             local animData = {
--                                 dict = 'amb@lo_res_idles@',
--                                 name = 'creatures_world_pug_sitting_lo_res_base'
--                             }
--                             if not sentToServer then 
--                                 TriggerServerEvent('cc_core:animals:syncAnim', OwnedNetId(OwnedAnimal()), animData)
--                             end
--                         end
--                     end
--                     if IsControlJustReleased(1, 75) then
--                         Citizen.Wait(1750)
--                         local OwnerX, OwnerY, OwnerZ = table.unpack(GetEntityCoords(PlayerPedId()))
--                         SetEntityCoords(OwnedAnimal(), OwnerX-0.5, OwnerY-0.5, OwnerZ-0.8, 0.0, 0.0, 0.0, false)
--                     end
--                 end
--             else
--                 Citizen.Wait(1750)
--             end
--         else
--             Citizen.Wait(1750)
--         end
--     end
-- end)

-- RegisterNetEvent('cc_core:animals:spawnAnimal', function(which)
--     local SpawnX, SpawnY, SpawnZ = table.unpack(GetEntityCoords(PlayerPedId()))
--     if which == 'Husky' then
--         RequestModel(GetHashKey('a_c_husky'))
--         while not HasModelLoaded(GetHashKey('a_c_husky')) do
--             Wait(10)
--         end
--         SpawnedHusky = CreatePed(1, GetHashKey('a_c_husky'), SpawnX-0.3, SpawnY-0.3, SpawnZ-0.7, GetEntityHeading(PlayerPedId()), true, false)
--         SetEntityInvincible(SpawnedHusky, true)
--         SetBlockingOfNonTemporaryEvents(SpawnedHusky, true)
--         SetPedFleeAttributes(SpawnedHusky, 0, 0)
--         SetPedCombatAttributes(SpawnedHusky, 17, 1)
--         spanwedAnimals = SpawnedHusky
--     elseif which == 'Mops' then
--         RequestModel(GetHashKey('a_c_pug'))
--         while not HasModelLoaded(GetHashKey('a_c_pug')) do
--             Wait(10)
--         end
--         SpawnedMops = CreatePed(1, GetHashKey('a_c_pug'), SpawnX-0.3, SpawnY-0.3, SpawnZ-0.7, GetEntityHeading(PlayerPedId()), true, false)
--         SetEntityInvincible(SpawnedMops, true)
--         SetBlockingOfNonTemporaryEvents(SpawnedMops, true)
--         SetPedFleeAttributes(SpawnedMops, 0, 0)
--         SetPedCombatAttributes(SpawnedMops, 17, 1)
--         spanwedAnimals = SpawnedMops
--     elseif which == 'Pudel' then
--         RequestModel(GetHashKey('a_c_poodle'))
--         while not HasModelLoaded(GetHashKey('a_c_poodle')) do
--             Wait(10)
--         end
--         SpawnedPudel = CreatePed(1, GetHashKey('a_c_poodle'), SpawnX-0.3, SpawnY-0.3, SpawnZ-0.7, GetEntityHeading(PlayerPedId()), true, false)
--         SetEntityInvincible(SpawnedPudel, true)
--         SetBlockingOfNonTemporaryEvents(SpawnedPudel, true)
--         SetPedFleeAttributes(SpawnedPudel, 0, 0)
--         SetPedCombatAttributes(SpawnedPudel, 17, 1)
--         spanwedAnimals = SpawnedPudel
--     elseif which == 'Rottweiler' then
--         RequestModel(GetHashKey('a_c_rottweiler'))
--         while not HasModelLoaded(GetHashKey('a_c_rottweiler')) do
--             Wait(10)
--         end
--         SpawnedRottweiler = CreatePed(1, GetHashKey('a_c_rottweiler'), SpawnX-0.3, SpawnY-0.3, SpawnZ-0.7, GetEntityHeading(PlayerPedId()), true, false)
--         SetEntityInvincible(SpawnedRottweiler, true)
--         SetBlockingOfNonTemporaryEvents(SpawnedRottweiler, true)
--         SetPedFleeAttributes(SpawnedRottweiler, 0, 0)
--         SetPedCombatAttributes(SpawnedRottweiler, 17, 1)
--         spanwedAnimals = SpawnedRottweiler
--     elseif which == 'Shepard' then
--         RequestModel(GetHashKey('a_c_shepherd'))
--         while not HasModelLoaded(GetHashKey('a_c_shepherd')) do
--             Wait(10)
--         end
--         SpawnedShepard = CreatePed(1, GetHashKey('a_c_shepherd'), SpawnX-0.3, SpawnY-0.3, SpawnZ-0.7, GetEntityHeading(PlayerPedId()), true, false)
--         SetEntityInvincible(SpawnedShepard, true)
--         SetBlockingOfNonTemporaryEvents(SpawnedShepard, true)
--         SetPedFleeAttributes(SpawnedShepard, 0, 0)
--         SetPedCombatAttributes(SpawnedShepard, 17, 1)
--         spanwedAnimals = SpawnedShepard
--     elseif which == 'Retriever' then
--         RequestModel(GetHashKey('a_c_retriever'))
--         while not HasModelLoaded(GetHashKey('a_c_retriever')) do
--             Wait(10)
--         end
--         SpawnedRetriever = CreatePed(1, GetHashKey('a_c_retriever'), SpawnX-0.1, SpawnY-0.1, SpawnZ-0.7, GetEntityHeading(PlayerPedId()), true, false)
--         SetEntityInvincible(SpawnedRetriever, true)
--         SetBlockingOfNonTemporaryEvents(SpawnedRetriever, true)
--         SetPedFleeAttributes(SpawnedRetriever, 0, 0)
--         SetPedCombatAttributes(SpawnedRetriever, 17, 1)
--         spanwedAnimals = SpawnedRetriever
--     elseif which == 'Wischmopphund' then
--         RequestModel(GetHashKey('a_c_westy'))
--         while not HasModelLoaded(GetHashKey('a_c_westy')) do
--             Wait(10)
--         end
--         SpawnedWischmoppHund = CreatePed(1, GetHashKey('a_c_westy'), SpawnX-0.3, SpawnY-0.3, SpawnZ-0.7, GetEntityHeading(PlayerPedId()), true, false)
--         SetEntityInvincible(SpawnedWischmoppHund, true)
--         SetBlockingOfNonTemporaryEvents(SpawnedWischmoppHund, true)
--         SetPedFleeAttributes(SpawnedWischmoppHund, 0, 0)
--         SetPedCombatAttributes(SpawnedWischmoppHund, 17, 1)
--         spanwedAnimals = SpawnedWischmoppHund
--     elseif which == 'Katze' then
--         RequestModel(GetHashKey('a_c_cat_01'))
--         while not HasModelLoaded(GetHashKey('a_c_cat_01')) do
--             Wait(10)
--         end
--         SpawnedKatze = CreatePed(1, GetHashKey('a_c_cat_01'), SpawnX-0.3, SpawnY-0.3, SpawnZ-0.7, GetEntityHeading(PlayerPedId()), true, false)
--         SetEntityInvincible(SpawnedKatze, true)
--         SetBlockingOfNonTemporaryEvents(SpawnedKatze, true)
--         SetPedFleeAttributes(SpawnedKatze, 0, 0)
--         SetPedCombatAttributes(SpawnedKatze, 17, 1)
--         spanwedAnimals = SpawnedKatze
--     elseif which == 'Lion' then
--         RequestModel(GetHashKey('a_c_mtlion'))
--         while not HasModelLoaded(GetHashKey('a_c_mtlion')) do
--             Wait(10)
--         end
--         SpawnedLion = CreatePed(1, GetHashKey('a_c_mtlion'), SpawnX-0.3, SpawnY-0.3, SpawnZ-0.7, GetEntityHeading(PlayerPedId()), true, false)
--         SetEntityInvincible(SpawnedLion, true)
--         SetBlockingOfNonTemporaryEvents(SpawnedLion, true)
--         SetPedFleeAttributes(SpawnedLion, 0, 0)
--         SetPedCombatAttributes(SpawnedLion, 17, 1)
--         spanwedAnimals = SpawnedLion
--     end
-- end)

-- function KeyboardInput(entryTitle, textEntry, inputText, maxLength)
--     AddTextEntry(entryTitle, textEntry)
--     DisplayOnscreenKeyboard(1, entryTitle, '', inputText, '', '', '', maxLength)
--     while UpdateOnscreenKeyboard() ~= 1 and UpdateOnscreenKeyboard() ~= 2 do
--         Citizen.Wait(0)
--     end
--     if UpdateOnscreenKeyboard() ~= 2 then
--         local result = GetOnscreenKeyboardResult()
--         Citizen.Wait(500)
--         return result
--     else
--         Citizen.Wait(500)
--         return nil
--     end
-- end

-- AddEventHandler('onResourceStop', function(resourceName)
--     if resourceName == GetCurrentResourceName() then
--         if OwnedAnimal() ~= 'nothing' then
--             for i = 0, 69 do
--                 DeleteEntity(OwnedAnimal())
--             end
--         end
--     end
-- end)

-- -- CreateThread(function()
-- --     local blip = AddBlipForCoord(BuyPos)
-- --     SetBlipSprite(blip, 463)
-- --     SetBlipScale(blip, 0.9)
-- --     SetBlipColour(blip, 69)
-- --     SetBlipAsShortRange(blip, true)
-- --     BeginTextCommandSetBlipName('STRING')
-- --     AddTextComponentSubstringPlayerName('Tiere kaufen')
-- --     EndTextCommandSetBlipName(blip)
-- --     while true do
-- --         Citizen.Wait(0)
-- --         if ESX == nil then
-- --             Citizen.Wait(1000)
-- --         end
-- --         if #(GetEntityCoords(PlayerPedId()) - BuyPos) <= 3.5 then
-- --             ESX.ShowHelpNotification('Drücke E')
-- --             if IsControlJustReleased(1, 38) then
-- --                 if not isCustomer then
-- --                     RageUI.Visible(ShopMenyo, true)
-- --                 else
-- --                     TriggerEvent("notifications", "info", "Information", 'Du hast Bereits ein Tier!')
-- --                     Citizen.Wait(5000)
-- --                 end
-- --             end
-- --         else
-- --             Citizen.Wait(1500)
-- --         end
-- --     end
-- -- end)

-- RegisterNetEvent('memo_9cm_cock', function()
--     ESX.TriggerServerCallback('cc_core:animals:getAnimal', function(animal)
--         if animal ~= nil then
--             playerAnimal = animal
--             if playerAnimal ~= Config.YourShopLink then
--                 isCustomer = true
--                 useThread = true
--             end
--         end
--     end)
--     isCustomer = true
-- end)

-- Keys.Register(Config.DefaultOpenKey, Config.DefaultOpenKey, 'Tier Menu', function()
--     if isCustomer then
--         RageUI.Visible(MainMenyo, true)
--     end
-- end)

-- RegisterNetEvent('animal:sync', function(animId, dict, name)
--     if animId ~= nil and dict ~= nil and name ~= nil then
--         local tryTime = 0
--         local animalExists = NetworkDoesNetworkIdExist(animId)
--         while not animalExists do ----So the Client Ends the Threadto Auto if Ped is not in Sync
--             Citizen.Wait(100)
--             tryTime = tryTime +1
--             if tryTime >= 255 then
--                 break
--             end
--         end
--         local animPed = NetworkGetEntityFromNetworkId(animId)
--         TaskPlayAnim(animPed, dict, name, 1.0, -1.0, -1, 1, 1, true, true, true)
--     end
-- end)

-- print('Code Loaded')
-- ]]