RegisterServerEvent('cc_core:carwash:removeMoney')
AddEventHandler('cc_core:carwash:removeMoney', function(price)
    local playerId = source
    price = tonumber(price)

    if price >= 10000 then
        TriggerEvent("EasyAdmin:addBan", playerId, "cc_core:carwash:removeMoney Trigger Detected")
        return
    end

    if ESX.GetPlayerMoney(playerId) >= price then
        ESX.RemovePlayerMoney(playerId, price, GetCurrentResourceName())
    elseif ESX.GetPlayerAccount(playerId, 'bank').money >= price then
        ESX.RemovePlayerAccountMoney(playerId, 'bank', price, GetCurrentResourceName())
    end
end)

--clientcode
carwashCode = [[
local show = false

Citizen.CreateThread(function()
    for k, v in pairs(Config_Carwash.Positions) do
        local blip = AddBlipForCoord(v)
        SetBlipSprite(blip, 100)
        SetBlipScale(blip, 0.7)
        SetBlipColour(blip, 4)
        SetBlipDisplay(blip, 2)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString('Waschanlage')
        EndTextCommandSetBlipName(blip)
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(4)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)

        if IsPedInAnyVehicle(ped, false) then
            local vehicle = GetVehiclePedIsIn(ped, false)

            if GetPedInVehicleSeat(vehicle, -1) then
                for k, v in pairs(Config_Carwash.Positions) do
                    local distance = #(coords - v)

                    if distance <= 15.0 then
                        DrawMarker(1, v, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.0, 5.0, 2.0, 140, 73, 184, 100, false, false, false, false, false, false, false)
                        letSleep = false
                    end

                    if distance <= 5.0 then
                        inRange = true

                        if IsControlJustReleased(0, 38) then
                            if GetVehicleDirtLevel(vehicle) then
                                local dirtLevel = GetVehicleDirtLevel(vehicle, false)
                                local price = math.floor(dirtLevel * 3)
                                for k, v in pairs(ESX.GetPlayerData().accounts) do
                                    if v.name == 'money' then
                                        if v.money >= price then
                                            TriggerServerEvent('cc_core:carwash:removeMoney', price)
                                            SetVehicleDirtLevel(vehicle, 0.0)
                                            Notify('Waschanlage', 'Dein Auto wurde für ' .. price .. '$ gewaschen.', 'info')
                                        else    
                                            Notify('Waschanlage', 'Du hast nicht genug Geld dabei!', 'info')
                                        end
                                    end
                                end
                            else
                                Notify('Waschanlage', 'Dein Auto ist noch sauber', 'info')
                            end
                        end
                    end
                end
            end
        end

        helpNotify(inRange, show, 'Drücke E um dein Auto zu waschen', function(bool)
            show = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)
]]