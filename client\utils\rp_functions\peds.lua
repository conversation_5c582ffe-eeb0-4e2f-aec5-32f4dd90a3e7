local Koordinaten = {
	{-100.95, -854.07, 39.55,"Smecherica",298.79,0xD47303AC,"s_m_m_doctor_01", true},-- Surgery 
	{733.0, 2523.37, 72.22,"Smecherica",287.49,0x23B88069,"g_m_y_ballasout_01", true}, -- ffa
}

local spawnedPeds = {}

CreateThread(function()
	while true do
		local sleep = 2000
		local coords = GetEntityCoords(PlayerPedId())
		for k, v in pairs(Koordinaten) do
			if #(vector3(v[1], v[2], v[3]) - coords) < 15 then
				if not spawnedPeds[k] then
					spawnedPeds[k] = 1
					RequestModel(GetHashKey(v[7]))
					while not HasModelLoaded(GetHashKey(v[7])) do
						Wait(1)
					end
			  
					RequestAnimDict("mini@strip_club@idles@bouncer@base")
					while not HasAnimDictLoaded("mini@strip_club@idles@bouncer@base") do
						Wait(1)
					end
					ped = CreatePed(4, v[6],v[1],v[2],v[3], 3374176, false, true)
					spawnedPeds[k] = ped
					SetEntityHeading(ped, v[5])
					FreezeEntityPosition(ped, true)
					SetEntityInvincible(ped, true)
					SetBlockingOfNonTemporaryEvents(ped, true)

					if v[8] then 
						TaskPlayAnim(ped,"mini@strip_club@idles@bouncer@base","base", 8.0, 0.0, -1, 1, 0, 0, 0, 0)
					end
				end
			elseif spawnedPeds[k] then
				DeleteEntity(spawnedPeds[k])
				spawnedPeds[k] = nil
			end
		end
		Wait(sleep)
	end
end)
