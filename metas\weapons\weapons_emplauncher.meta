<?xml version="1.0" encoding="UTF-8"?>

<CWeaponInfoBlob>
  <SlotNavigateOrder>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="357" />
          <Entry>SLOT_EMPLAUNCHER</Entry>
        </Item>
      </WeaponSlots>
    </Item>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="357" />
          <Entry>SLOT_EMPLAUNCHER</Entry>
        </Item>
      </WeaponSlots>
    </Item>
  </SlotNavigateOrder>
  <SlotBestOrder>
    <WeaponSlots>
      <Item>
        <OrderNumber value="282" />
        <Entry>SLOT_EMPLAUNCHER</Entry>
      </Item>
    </WeaponSlots>
  </SlotBestOrder>
  <TintSpecValues>
    <Item>
      <Name>TINT_EMPLAUNCHER</Name>
      <Tints>
        <Item>
          <SpecFresnel value="0.950000" />
          <SpecFalloffMult value="10.000000" />
          <SpecIntMult value="1.000000" />
          <Spec2Factor value="30.000000" />
          <Spec2ColorInt value="0.250000" />
          <Spec2Color value="0x00FFFFFF" />
        </Item>
        <Item>
          <SpecFresnel value="0.950000" />
          <SpecFalloffMult value="10.000000" />
          <SpecIntMult value="1.000000" />
          <Spec2Factor value="30.000000" />
          <Spec2ColorInt value="0.250000" />
          <Spec2Color value="0x00FFFFFF" />
        </Item>
        <Item>
          <SpecFresnel value="0.950000" />
          <SpecFalloffMult value="45.000000" />
          <SpecIntMult value="1.000000" />
          <Spec2Factor value="20.000000" />
          <Spec2ColorInt value="1.500000" />
          <Spec2Color value="0x00FFAE00" />
        </Item>
        <Item>
          <SpecFresnel value="0.950000" />
          <SpecFalloffMult value="10.000000" />
          <SpecIntMult value="1.000000" />
          <Spec2Factor value="30.000000" />
          <Spec2ColorInt value="0.250000" />
          <Spec2Color value="0x00FFFFFF" />
        </Item>
        <Item>
          <SpecFresnel value="0.950000" />
          <SpecFalloffMult value="10.000000" />
          <SpecIntMult value="1.000000" />
          <Spec2Factor value="30.000000" />
          <Spec2ColorInt value="0.250000" />
          <Spec2Color value="0x00FFFFFF" />
        </Item>
        <Item>
          <SpecFresnel value="0.950000" />
          <SpecFalloffMult value="10.000000" />
          <SpecIntMult value="1.000000" />
          <Spec2Factor value="30.000000" />
          <Spec2ColorInt value="0.250000" />
          <Spec2Color value="0x00FFFFFF" />
        </Item>
        <Item>
          <SpecFresnel value="0.950000" />
          <SpecFalloffMult value="10.000000" />
          <SpecIntMult value="1.000000" />
          <Spec2Factor value="30.000000" />
          <Spec2ColorInt value="0.250000" />
          <Spec2Color value="0x00FFFFFF" />
        </Item>
        <Item>
          <SpecFresnel value="0.4" />
          <SpecFalloffMult value="200.000000" />
          <SpecIntMult value="1.000000" />
          <Spec2Factor value="22.000000" />
          <Spec2ColorInt value="1.000000" />
          <Spec2Color value="0x00E8FFFF" />
        </Item>
      </Tints>
    </Item>
  </TintSpecValues>
  <FiringPatternAliases />
  <UpperBodyFixupExpressionData />
  <AimingInfos />
  <Infos>
    <Item>
      <Infos>
        <Item type="CAmmoProjectileInfo">
          <Name>AMMO_EMPLAUNCHER</Name>
          <Model>w_lr_ml_40mm</Model>
          <Audio />
          <Slot />
          <AmmoMax value="20" />
          <AmmoMax50 value="20" />
          <AmmoMax100 value="20" />
          <AmmoMaxMP value="20" />
          <AmmoMax50MP value="20" />
          <AmmoMax100MP value="20" />
          <AmmoFlags>AddSmokeOnExplosion</AmmoFlags>
          <AmmoSpecialType>None</AmmoSpecialType>
          <Damage value="0.000000" />
          <LifeTime value="30.000000" />
          <FromVehicleLifeTime value="30.000000" />
          <LifeTimeAfterImpact value="1.000000" />
          <LifeTimeAfterExplosion value="0.000000" />
          <ExplosionTime value="0.000000" />
          <LaunchSpeed value="25.000000" />
          <SeparationTime value="0.000000" />
          <TimeToReachTarget value="2.000000" />
          <Damping value="0.000000" />
          <GravityFactor value="1.000000" />
          <RicochetTolerance value="1.000000" />
          <PedRicochetTolerance value="0.000000" />
          <VehicleRicochetTolerance value="0.000000" />
          <FrictionMultiplier value="0.600000" />
          <Explosion>
            <Default>EXP_TAG_EMPLAUNCHER_EMP</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FuseFx />
          <ProximityFx />
          <TrailFx>proj_grenade_trail</TrailFx>
          <TrailFxUnderWater />
          <FuseFxFP />
          <PrimedFxFP />
          <TrailFxFadeInTime value="0.000000" />
          <TrailFxFadeOutTime value="0.000000" />
          <PrimedFx />
          <DisturbFxDefault />
          <DisturbFxSand />
          <DisturbFxWater />
          <DisturbFxDirt />
          <DisturbFxFoliage />
          <DisturbFxProbeDist value="0.000000" />
          <DisturbFxScale value="0.000000" />
          <GroundFxProbeDistance value="2.500000" />
          <FxAltTintColour value="false" />
          <LightOnlyActiveWhenStuck value="false" />
          <LightFlickers value="false" />
          <LightSpeedsUp value="false" />
          <LightBone />
          <LightColour x="0.000000" y="0.000000" z="0.000000" />
          <LightIntensity value="0.000000" />
          <LightRange value="0.000000" />
          <LightFalloffExp value="0.000000" />
          <LightFrequency value="0.000000" />
          <LightPower value="0.000000" />
          <CoronaSize value="0.000000" />
          <CoronaIntensity value="0.000000" />
          <CoronaZBias value="0.000000" />
          <ProjectileFlags>ProcessImpacts AlignWithTrajectory</ProjectileFlags>
        </Item>
      </Infos>
    </Item>
    <Item>
      <Infos>
        <Item type="CWeaponInfo">
          <Name>WEAPON_EMPLAUNCHER</Name>
          <Model>w_lr_compactml</Model>
          <Audio>audio_item_emp_launcher</Audio>
          <Slot>SLOT_EMPLAUNCHER</Slot>
          <DamageType>EXPLOSIVE</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>PROJECTILE</FireType>
          <WheelSlot>WHEEL_HEAVY</WheelSlot>
          <Group>GROUP_HEAVY</Group>
          <AmmoInfo ref="AMMO_EMPLAUNCHER" />
          <AimingInfo ref="GRENADELAUNCHER" />
          <ClipSize value="1" />
          <AccuracySpread value="1.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMinOverride value="-1.000000" />
          <RecoilAccuracyMax value="0.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="230000.000000" />
          <HeadShotDamageModifierPlayer value="1288860.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <EnduranceDamage value="0.000000" />
          <PlayerDamageOverTimeWeapon />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <VehicleDamageModifier value="1.000000" />
          <Force value="0.000000" />
          <ForceHitPed value="0.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces />
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="1000.000000" />
          <Penetration value="0.000000" />
          <VerticalLaunchAdjustment value="0.075000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="1.500000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="1.220000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.000000" />
          <BulletBendingZoomedRadius value="0.000000" />
          <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.000000" />
          <FirstPersonBulletBendingZoomedRadius value="0.000000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_GRENADE</EffectGroup>
            <FlashFx />
            <FlashFxAlt />
            <FlashFxFP />
            <FlashFxFPAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxFP />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <MuzzleOverrideOffset x="0.000000" y="0.000000" z="0.000000" />
            <MuzzleUseProjTintColour value="false" />
            <ShellFx />
            <ShellFxFP />
            <TracerFx />
            <PedDamageHash />
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <TracerFxIgnoreCameraIntersection value="false" />
            <FlashFxChanceSP value="1.000000" />
            <FlashFxChanceMP value="1.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.300000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="true" />
            <FlashFxLightOffsetDist value="0.000000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.800000" />
          <RumbleDuration value="125" />
          <RumbleIntensity value="0.800000" />
          <RumbleIntensityTrigger value="0.800000" />
          <RumbleDamageIntensity value="1.000000" />
          <InitialRumbleDurationFps value="150" />
          <InitialRumbleIntensityFps value="1.000000" />
          <RumbleDurationFps value="150" />
          <RumbleIntensityFps value="0.900000" />
          <NetworkPlayerDamageModifier value="1.000000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.000000" />
          <LockOnRange value="25.000000" />
          <WeaponRange value="500.000000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <BulletDirectionPitchOffset value="0.000000" />
          <BulletDirectionPitchHomingOffset value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="25.000000" />
          <DamageFallOffRangeMax value="25.000000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>HIP_AIM_CAMERA</DefaultCameraHash>
          <AimCameraHash />
          <FireCameraHash />
          <CoverCameraHash>HIP_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash>HIP_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <PovTurretCameraHash />
          <CameraFov value="45.000000" />
          <FirstPersonAimFovMin value="42.000000" />
          <FirstPersonAimFovMax value="47.000000" />
          <FirstPersonScopeFov value="30.000000" />
          <FirstPersonScopeAttachmentFov value="30.000000" />
          <FirstPersonDrivebyIKOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonRNGOffset x="0.000000" y="0.000000" z="-0.050000" />
          <FirstPersonRNGRotationOffset x="1.500000" y="0.000000" z="0.000000" />
          <FirstPersonLTOffset x="0.020000" y="0.000000" z="-0.060000" />
          <FirstPersonLTRotationOffset x="2.500000" y="0.000000" z="-0.200000" />
          <FirstPersonScopeOffset x="0.006000" y="0.050000" z="-0.040000" />
          <FirstPersonScopeAttachmentOffset x="0.000000" y="0.080000" z="-0.032000" />
          <FirstPersonScopeRotationOffset x="1.300000" y="0.000000" z="-0.175000" />
          <FirstPersonScopeAttachmentRotationOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="-0.075000" y="0.000000" z="-0.075000" />
          <FirstPersonAsThirdPersonRNGOffset x="-0.025000" y="0.000000" z="-0.100000" />
          <FirstPersonAsThirdPersonLTOffset x="0.040000" y="-0.025000" z="-0.075000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.050000" y="-0.050000" z="-0.010000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="-0.050000" y="0.100000" z="-0.050000" />
          <FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000" />
          <FirstPersonDofMaxNearInFocusDistance value="0.000000" />
          <FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000" />
          <FirstPersonScopeAttachmentData />
          <ZoomFactorForAccurateMode value="1.300000" />
          <RecoilShakeHash>GRENADE_LAUNCHER_RECOIL_SHAKE</RecoilShakeHash>
          <RecoilShakeHashFirstPerson>FPS_GRENADE_LAUNCHER_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="0.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <IkRecoilDisplacement value="0.000000" />
          <IkRecoilDisplacementScope value="0.000000" />
          <IkRecoilDisplacementScaleBackward value="1.000000" />
          <IkRecoilDisplacementScaleVertical value="0.400000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <ReticuleHudPositionOffsetForPOVTurret x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.200000" y="0.115000" z="0.400000" />
          <AimProbeLengthMin value="0.350000" />
          <AimOffsetMax x="0.125000" y="-0.200000" z="0.550000" />
          <AimProbeLengthMax value="0.300000" />
          <AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000" />
          <AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000" />
          <AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000" />
          <AimOffsetMinFPSLT x="0.180000" y="0.331000" z="0.469000" />
          <AimOffsetMaxFPSLT x="0.048000" y="-0.275000" z="0.609000" />
          <AimOffsetMinFPSRNG x="0.220000" y="0.375000" z="0.509000" />
          <AimOffsetMaxFPSRNG x="0.068000" y="-0.212000" z="0.518000" />
          <AimOffsetMinFPSScope x="0.090000" y="0.278000" z="0.531000" />
          <AimOffsetMaxFPSScope x="0.066000" y="-0.059000" z="0.594000" />
          <AimOffsetEndPosMinFPSIdle x="-0.216000" y="0.507000" z="-0.098000" />
          <AimOffsetEndPosMedFPSIdle x="-0.140000" y="0.486000" z="0.562000" />
          <AimOffsetEndPosMaxFPSIdle x="-0.090000" y="0.066000" z="0.889000" />
          <AimOffsetEndPosMinFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMedFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimOffsetEndPosMaxFPSLT x="0.000000" y="0.000000" z="0.000000" />
          <AimProbeRadiusOverrideFPSIdle value="0.000000" />
          <AimProbeRadiusOverrideFPSIdleStealth value="0.000000" />
          <AimProbeRadiusOverrideFPSLT value="0.000000" />
          <AimProbeRadiusOverrideFPSRNG value="0.000000" />
          <AimProbeRadiusOverrideFPSScope value="0.000000" />
          <TorsoAimOffset x="-1.000000" y="0.530000" />
          <TorsoCrouchedAimOffset x="0.120000" y="0.050000" />
          <LeftHandIkOffset x="0.100000" y="0.075000" z="-0.025000" />
          <ReticuleMinSizeStanding value="1.000000" />
          <ReticuleMinSizeCrouched value="1.000000" />
          <ReticuleScale value="0.000000" />
          <ReticuleStyleHash>WEAPON_HEAVY_GRENADE_LAUNCHER</ReticuleStyleHash>
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_EMPLAUNCHER</PickupHash>
          <MPPickupHash>PICKUP_AMMO_GRENADELAUNCHER_MP</MPPickupHash>
          <HumanNameHash>WT_EMPL</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>EMPGL</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>normal</NmShotTuningSet>
          <AttachPoints>
            <Item>
              <AttachBone>WAPClip</AttachBone>
              <Components>
                <Item>
                  <Name>COMPONENT_EMPLAUNCHER_CLIP_01</Name>
                  <Default value="true" />
                </Item>
              </Components>
            </Item>
          </AttachPoints>
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>CarriedInHand Gun CanFreeAim Launched AnimReload AnimCrouchFire UsableOnFoot UsableInCover AllowCloseQuarterKills HasLowCoverReloads HasLowCoverSwaps DelayedFiringAfterAutoSwap UseFPSAimIK UseFPSSecondaryMotion DontPlayDryFireAnim</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="DEFAULT" />
          <AmmoDiminishingRate value="3" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="1.000000" />
          <StealthFiringBreathingAdditiveWeight value="1.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="1.000000" />
          <StealthFiringLeanAdditiveWeight value="1.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <AudioCollisionHash />
          <HudDamage value="95" />
          <HudSpeed value="10" />
          <HudCapacity value="20" />
          <HudAccuracy value="15" />
          <HudRange value="55" />
          <VehicleAttackAngle value="25.000000" />
          <TorsoIKAngleLimit value="-1.000000" />
          <MeleeDamageMultiplier value="-1.000000" />
          <MeleeRightFistTargetHealthDamageScaler value="-1.000000" />
          <AirborneAircraftLockOnMultiplier value="1.000000" />
          <ArmouredVehicleGlassDamageOverride value="-1.000000" />
          <CamoDiffuseTexIdxs />
          <RotateBarrelBone />
          <RotateBarrelBone2 />
          <FrontClearTestParams>
            <ShouldPerformFrontClearTest value="false" />
            <ForwardOffset value="0.000000" />
            <VerticalOffset value="0.000000" />
            <HorizontalOffset value="0.000000" />
            <CapsuleRadius value="0.000000" />
            <CapsuleLength value="0.000000" />
          </FrontClearTestParams>
        </Item>
      </Infos>
    </Item>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos />
    </Item>
  </Infos>
  <VehicleWeaponInfos />
  <WeaponGroupDamageForArmouredVehicleGlass />
  <Name>DLC - EMP Launcher</Name>
</CWeaponInfoBlob>