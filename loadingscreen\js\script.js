var play = true;
var video = document.getElementById("background-video");

$(function() {
    video = document.getElementById("background-video");
    
    video.muted = false;
    
    document.addEventListener("keydown", function(event) {
        if (event.code === "Space") {
            event.preventDefault();
            toggleMute();
        }
    });
    
    document.addEventListener("click", function(event) {
        if (event.target.id === "muteButton") {
            toggleMute();
        }
    });
});

function toggleMute() {
    play = !play;
    video.muted = !play;
    console.log("Video muted:", video.muted);
}

function updateMuteIndicator() {
    const indicator = document.getElementById("muteIndicator");
    if (indicator) {
        indicator.textContent = play ? "🔊 Musik an" : "🔇 Musik aus";
    }
}

function copyToClipboard(text) {
    const body = document.querySelector('body');
    const area = document.createElement('textarea');
    body.appendChild(area);
    area.value = text;
    area.select();
    document.execCommand('copy');
    body.removeChild(area);
}

setup();