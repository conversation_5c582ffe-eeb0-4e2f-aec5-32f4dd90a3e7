AddEventHandler('playerConnecting', function()
    local playerId = source
    log(playerId, 'Join <PERSON> <PERSON><PERSON>', '<PERSON> ' .. GetPlayerName(playerId) .. ' verbindet sich zum Server!', 'https://discord.com/api/webhooks/1337036249420267621/GtQ6qYBXn1D1AJ0ErmG_ELu7DlfRN_LmhRTaa14w_o882qJB4uPY5JlyDFOJEt3YgQVK')
    logExploit(playerId, 'Join - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' verbindet sich zum Server!', 'ttps://canary.discord.com/api/webhooks/1191193782025191524/uQTrWZVHQfUhfDt29HHboeU7SJBs9yY8esB8w_7GyI8HS97ppITGTfopP2ZnSXmJSwC0')
end)

AddEventHandler('playerDropped', function(reason)
    local playerId = source
    log(playerId, 'Join - Log', '<PERSON> ' .. GetPlayerName(playerId) .. ' mit der Id: ' .. playerId .. ' trennt die Verbindung zum Server (Grund: ' .. reason .. ')!', 'https://discord.com/api/webhooks/1337036431801057331/syqFevZuj8AgavfhC7rilXX_p5--fFYu-TNVqoZ-r3RR9R04Zk4H6eEddSbiBHZFpnqZ')
    logExploit(playerId, 'Join - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' mit der Id: ' .. playerId .. ' trennt die Verbindung zum Server (Grund: ' .. reason .. ')!', 'ttps://canary.discord.com/api/webhooks/1191193851478691850/LUaMLxwXf9bAHYDbH5zWo7KYFmCYMZd5UsOLqYw3xvayb4ktrQydGykbi78K__3V513d')
end)

RegisterServerEvent('cc_core:logs:playerDied')
AddEventHandler('cc_core:logs:playerDied', function(id, player, killer, deathReason, weapon)
    if GetPlayerRoutingBucket(source) ~= 0 then
		CancelEvent()
		return
	end
    
    if not IsPedAPlayer(GetPlayerPed(killer)) then
        if GetEntityType(GetPlayerPed(killer)) == 1 then
            local killerPedOwner = NetworkGetEntityOwner(GetPlayerPed(killer))
            log(source, 'Death - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' ist gestorben durch ein Killer Ped: **' .. killerPedOwner .. '**', 'https://canary.discord.com/api/webhooks/1191193933036929025/nKFdZOXocFqFMPbKVRcExc5Z07MShYkMRLOEmPckr7zdZniwGdu2-KR-0LiHICjzh4jm') 
        end
    end

    if id == 1 then
        log(source, 'Death - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' ist gestorben Grund: **' .. deathReason .. '**', 'https://discord.com/api/webhooks/1337036540970537010/Df1C_fFR54ejAAUJJzJIaQpGRReqBbFaS0z5VgeNJgfpD6AFkYwl1gneCQE5U2v9gl1k')
    elseif id == 2 then
        togLog(source, killer, 'Death - Log', 'Der Spieler **' .. GetPlayerName(source) .. '** (' .. source .. ') wurde von **' .. GetPlayerName(killer) .. '** (' .. killer .. ') mit der Waffe **' .. weapon .. '** getötet', 'https://canary.discord.com/api/webhooks/1191193933036929025/nKFdZOXocFqFMPbKVRcExc5Z07MShYkMRLOEmPckr7zdZniwGdu2-KR-0LiHICjzh4jm', false)
    else
        log(source, 'Death - Log', 'Der Spieler ' .. GetPlayerName(source) .. ' ist gestorben Grund: **' .. deathReason .. '**', 'https://canary.discord.com/api/webhooks/1211061923756642344/xUBuRSbfGI1KSUycPr0BSuxT1_jDQFcd2jUPAeXI5TY2IWnqoUTUYQLlZ6Qtmh29JfK8')
    end
end)