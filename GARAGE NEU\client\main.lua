-- ====================================
-- GARAGE SYSTEM - CLIENT SIDE
-- ====================================

ESX = exports['es_extended']:getSharedObject()

local playerVehicles = {}
local currentGarage = nil
local isInGarageMenu = false
local garageBlips = {}
local garagePeds = {}

-- ====================================
-- UTILITY FUNCTIONS
-- ====================================
local function ShowNotification(message)
    ESX.ShowNotification(message)
end

local function GetVehicleDisplayName(model)
    return GetDisplayNameFromVehicleModel(model)
end

local function IsVehicleOwned(plate)
    for k, v in pairs(playerVehicles) do
        if string.upper(v.plate) == string.upper(plate) then
            return true, v.nickname, v.fav, v.type
        end
    end
    return false
end

local function GetAvailableSpawnPoint(spawnPoints)
    for k, v in pairs(spawnPoints) do
        if ESX.Game.IsSpawnPointClear(vector3(v.x, v.y, v.z), 3.0) then
            return vector4(v.x, v.y, v.z, v.w)
        end
    end
    return nil
end

-- ====================================
-- VEHICLE MANAGEMENT
-- ====================================
local function SpawnVehicle(vehicleData, spawnPoint)
    local model = vehicleData.vehicle.model
    
    ESX.Game.SpawnVehicle(model, spawnPoint, spawnPoint.w, function(vehicle)
        if DoesEntityExist(vehicle) then
            ESX.Game.SetVehicleProperties(vehicle, vehicleData.vehicle)
            
            -- Tuning anwenden falls vorhanden
            if vehicleData.tuningData and next(vehicleData.tuningData) then
                -- Hier würde Tuning-Code stehen
            end
            
            -- Fahrzeug als ausgeparkt markieren
            TriggerServerEvent('garage:changeVehicleState', vehicleData.plate, false, currentGarage.id)
            
            ShowNotification('Fahrzeug ~g~' .. vehicleData.nickname .. '~s~ wurde ausgeparkt!')
        else
            ShowNotification('~r~Fehler beim Spawnen des Fahrzeugs!')
        end
    end)
end

local function ParkVehicle(vehicle, plate)
    if DoesEntityExist(vehicle) then
        local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)
        
        -- Fahrzeug-Eigenschaften speichern
        TriggerServerEvent('garage:saveVehicleProps', plate, vehicleProps)
        
        -- Fahrzeug als eingeparkt markieren
        TriggerServerEvent('garage:changeVehicleState', plate, true, currentGarage.id)
        
        -- Fahrzeug löschen
        ESX.Game.DeleteVehicle(vehicle)
        
        ShowNotification('Fahrzeug ~g~eingeparkt~s~!')
    end
end

-- ====================================
-- GARAGE MENU FUNCTIONS
-- ====================================
local function OpenGarageMenu(garage)
    currentGarage = garage
    isInGarageMenu = true
    
    local elements = {}
    local storedVehicles = {}
    
    -- Gespeicherte Fahrzeuge anzeigen
    for k, v in pairs(playerVehicles) do
        if v.stored and v.type == garage.type then
            table.insert(storedVehicles, v)
            table.insert(elements, {
                label = v.nickname .. ' (' .. v.plate .. ')',
                value = 'spawn_vehicle',
                vehicleData = v
            })
        end
    end
    
    -- Fahrzeuge in der Nähe zum Einparken
    local nearbyVehicles = ESX.Game.GetVehiclesInArea(GetEntityCoords(PlayerPedId()), 50.0)
    local parkableVehicles = {}
    
    for k, v in pairs(nearbyVehicles) do
        if GetVehicleNumberOfPassengers(v) == 0 and IsVehicleSeatFree(v, -1) then
            local vehicleProps = ESX.Game.GetVehicleProperties(v)
            local owned, nickname = IsVehicleOwned(vehicleProps.plate)
            
            if owned then
                table.insert(parkableVehicles, {vehicle = v, plate = vehicleProps.plate, nickname = nickname})
                table.insert(elements, {
                    label = '~b~Einparken: ~s~' .. nickname .. ' (' .. vehicleProps.plate .. ')',
                    value = 'park_vehicle',
                    vehicle = v,
                    plate = vehicleProps.plate
                })
            end
        end
    end
    
    if #elements == 0 then
        table.insert(elements, {
            label = '~r~Keine Fahrzeuge verfügbar',
            value = 'nothing'
        })
    end
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'garage_menu', {
        title = garage.label,
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        if data.current.value == 'spawn_vehicle' then
            local spawnPoint = GetAvailableSpawnPoint(garage.spawnPoints)
            
            if spawnPoint then
                SpawnVehicle(data.current.vehicleData, spawnPoint)
                menu.close()
                isInGarageMenu = false
            else
                ShowNotification('~r~Alle Spawnpunkte sind belegt!')
            end
            
        elseif data.current.value == 'park_vehicle' then
            ParkVehicle(data.current.vehicle, data.current.plate)
            menu.close()
            isInGarageMenu = false
        end
    end, function(data, menu)
        menu.close()
        isInGarageMenu = false
    end)
end

-- ====================================
-- GARAGE INTERACTION
-- ====================================
local function CreateGarageBlip(garage)
    local blip = AddBlipForCoord(garage.npc.x, garage.npc.y, garage.npc.z)
    
    SetBlipSprite(blip, garage.blipSprite or 357)
    SetBlipColour(blip, garage.blipColor or 29)
    SetBlipScale(blip, 0.7)
    SetBlipDisplay(blip, 4)
    SetBlipAsShortRange(blip, true)
    
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(garage.blipLabel or 'Garage')
    EndTextCommandSetBlipName(blip)
    
    return blip
end

local function CreateGaragePed(garage)
    local pedModel = garage.pedModel or `s_m_y_dealer_01`
    
    RequestModel(pedModel)
    while not HasModelLoaded(pedModel) do
        Wait(1)
    end
    
    local ped = CreatePed(4, pedModel, garage.npc.x, garage.npc.y, garage.npc.z - 1.0, garage.npc.w, false, true)
    
    SetEntityCanBeDamaged(ped, false)
    SetPedCanRagdollFromPlayerImpact(ped, false)
    SetBlockingOfNonTemporaryEvents(ped, true)
    SetPedFleeAttributes(ped, 0, 0)
    SetPedCombatAttributes(ped, 17, 1)
    FreezeEntityPosition(ped, true)
    
    return ped
end

-- ====================================
-- MAIN THREAD
-- ====================================
Citizen.CreateThread(function()
    while true do
        local sleep = 1000
        local playerCoords = GetEntityCoords(PlayerPedId())
        
        for k, garage in pairs(Config.Garages) do
            local distance = #(playerCoords - vector3(garage.npc.x, garage.npc.y, garage.npc.z))
            
            if distance < 10.0 then
                sleep = 0
                
                if distance < 2.0 then
                    ESX.ShowHelpNotification(garage.helpText or 'Drücke ~INPUT_CONTEXT~ um die Garage zu öffnen')
                    
                    if IsControlJustReleased(0, 38) and not isInGarageMenu then -- E Key
                        OpenGarageMenu(garage)
                    end
                end
            end
        end
        
        Wait(sleep)
    end
end)

-- ====================================
-- EVENTS
-- ====================================
RegisterNetEvent('garage:loadVehicles')
AddEventHandler('garage:loadVehicles', function(vehicles)
    playerVehicles = vehicles
end)

RegisterNetEvent('garage:addVehicleToClient')
AddEventHandler('garage:addVehicleToClient', function(vehicle, stored, plate, nickname, vehicleType, job)
    table.insert(playerVehicles, {
        vehicle = vehicle,
        stored = stored,
        plate = plate,
        nickname = nickname,
        type = vehicleType,
        job = job,
        fav = false,
        tuningData = {},
        status = nil,
        garage = nil
    })
end)

RegisterNetEvent('garage:removeVehicleFromClient')
AddEventHandler('garage:removeVehicleFromClient', function(plate)
    for k, v in pairs(playerVehicles) do
        if v.plate == plate then
            table.remove(playerVehicles, k)
            break
        end
    end
end)

RegisterNetEvent('garage:updateVehicleData')
AddEventHandler('garage:updateVehicleData', function(plate, stored, garage)
    for k, v in pairs(playerVehicles) do
        if v.plate == plate then
            v.stored = stored
            v.garage = garage
            break
        end
    end
end)

-- ====================================
-- INITIALIZATION
-- ====================================
Citizen.CreateThread(function()
    -- Warten bis ESX geladen ist
    while not ESX.IsPlayerLoaded() do
        Wait(100)
    end
    
    -- Garagen-Blips und NPCs erstellen
    for k, garage in pairs(Config.Garages) do
        if garage.showBlip then
            garageBlips[k] = CreateGarageBlip(garage)
        end
        
        if garage.showPed then
            garagePeds[k] = CreateGaragePed(garage)
        end
    end
    
    print('[GARAGE] Client-seitige Garage-Logik geladen!')
end)

-- ====================================
-- CLEANUP
-- ====================================
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- Blips entfernen
        for k, blip in pairs(garageBlips) do
            if DoesBlipExist(blip) then
                RemoveBlip(blip)
            end
        end
        
        -- NPCs entfernen
        for k, ped in pairs(garagePeds) do
            if DoesEntityExist(ped) then
                DeleteEntity(ped)
            end
        end
    end
end)
