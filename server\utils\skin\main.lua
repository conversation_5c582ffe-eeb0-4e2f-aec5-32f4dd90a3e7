RegisterServerEvent('cc_core:skin:save')
AddEventHandler('cc_core:skin:save', function(skin)
    local playerId = source

    ESX.SetPlayerSkin(playerId, skin)
    
    MySQL.Async.execute('UPDATE users SET skin = @skin WHERE identifier = @identifier', {
        ['@skin'] = json.encode(ESX.GetPlayerSkin(playerId)),
        ['@identifier'] = ESX.GetPlayerIdentifier(playerId)
    })
end)

ESX.RegisterServerCallback('cc_core:skin:getPlayerSkin', function(source, cb)
    local playerId = source
    local skin = ESX.GetPlayerSkin(playerId)

    cb(skin)
end)

RegisterCommand('skin', function(source, args, rawCommand)
    if ESX.GetPlayerGroup(source) == 'projektleitung' then
        local id = args[1]
        local xPlayer = ESX.GetPlayerFromId(id)

        if not id then
            TriggerClientEvent('cc_core:skin:openSaveableMenu', source)
        else
            TriggerClientEvent('cc_core:skin:openSaveableMenu', xPlayer.source)
        end
    end
end)

RegisterCommand('skinsave', function(source, args, rawCommand)
    if ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'superadmin' or ESX.GetPlayerGroup(source) == 'administrator' or ESX.GetPlayerGroup(source) == 'frakverwaltung' then
        TriggerClientEvent('cc_core:skin:requestSaveSkin', source)
    end
end)