Config_FFA = {}

Config_FFA.position = vector3(286.5313, -324.0699, 45.0491)

Config_FFA.ffaZones = {
    {
        name = "Markt Platz",
        label = "Only Pistol",
        maxPlayers = 10,
        position = vector3(383.8309, -339.3008, 46.8099),
        size = 100.0,
        dimension = 1000,
        imgUrl = "img/markt.png",
        
        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    <PERSON><PERSON><PERSON><PERSON><PERSON>('COMPONENT_PISTOL_CLIP_02')
                }
            },
            {
                name = 'WEAPON_PISTOL_MK2',
                components = {
                    Get<PERSON><PERSON><PERSON><PERSON>('COMPONENT_PISTOL_MK2_CLIP_02')
                }
            },
            {
                name = 'WEAPON_PISTOL50',
                components = {
                    GetHash<PERSON><PERSON>('COMPONENT_PISTOL50_CLIP_02')
                }
            }
        },
        
        spawns = {
            vector3(361.8524, -291.3102, 53.8677), 
            vector3(400.4957, -313.1645, 49.8660), 
            vector3(442.2439, -325.8990, 48.1187), 
            vector3(420.3872, -357.1692, 47.2169), 
            vector3(394.1068, -378.9253, 46.9550),
            vector3(370.5730, -380.3284, 46.5562),
            vector3(376.1372, -322.5521, 50.2008),
            vector3(373.9258, -340.6835, 47.4043),
            vector3(412.4160, -338.5406, 46.9777),
            vector3(358.3083, -339.3065, 46.7395)
        }
    },

    {
        name = "Flugzeug Friedhof",
        label = "Flugzeugfriedhof",
        maxPlayers = 20,
        position = vector3(2386.367, 3082.089, 48.19027),
        size = 100.0,
        dimension = 1001,
        imgUrl = "img/flughafen.png",
        
        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    GetHashKey('COMPONENT_PISTOL_CLIP_02')
                }
            },
            {
                name = 'WEAPON_PISTOL_MK2',
                components = {
                    GetHashKey('COMPONENT_PISTOL_MK2_CLIP_02')
                }
            },
            {
                name = 'WEAPON_PISTOL50',
                components = {
                    GetHashKey('COMPONENT_PISTOL50_CLIP_02')
                }
            }
            --{
               -- name = 'WEAPON_MACHINEPISTOL',
               -- components = {
                 --   GetHashKey('COMPONENT_AT_PI_SUPP'),
                 --   GetHashKey('COMPONENT_MACHINEPISTOL_CLIP_03')
             --   }
           -- }
        },

        spawns = {
            vector3(2360.073, 3129.041, 48.20869),
            vector3(2428.039, 3124.009, 48.14234),
            vector3(2435.5923, 3087.9827, 48.1533),
            vector3(2409.702, 3033.869, 48.16072),
            vector3(2355.944, 3038.585, 48.16628),
            vector3(2382.8660, 3027.2449, 48.1576),
            vector3(2400.7046, 3028.7883, 48.1526),
            vector3(2431.8601, 3046.2708, 48.1524),
            vector3(2378.8662, 3160.5381, 48.2112),
            vector3(2338.9009, 3110.5952, 48.2089)
        }
        
    },

    

    {
        name = "Baustelle",
        label = "Baustelle",
        maxPlayers = 20,
        position = vector3(-160.0956, -983.2939, 256.2072),
        size = 100.0,
        dimension = 1002,
        imgUrl = "img/baustelle.png",
        
        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    GetHashKey('COMPONENT_AT_PI_SUPP_02')
                }
            },
            {
                name = 'WEAPON_PISTOL_MK2',
                components = {
                    GetHashKey('COMPONENT_PISTOL_MK2_CLIP_02')
                }
            },
            {
                name = 'WEAPON_PISTOL50',
                components = {
                    GetHashKey('COMPONENT_PISTOL50_CLIP_02')
                }
            }
        },

        spawns = {
            vector3(-140.1919, -953.3641, 254.1314),
            vector3(-156.8964, -949.6334, 254.1314),
            vector3(-156.7630, -1021.6153, 254.1315),
            vector3(-145.6263, -948.2170, 269.1350),
            vector3(-189.7213, -1010.8111, 254.3521),
            vector3(-166.5982, -974.7396, 259.2296),
            vector3(-133.9850, -958.3747, 264.1339),
            vector3(-136.1249, -946.9573, 259.1328),
            vector3(-164.1968, -974.4492, 259.2292),
            vector3(-162.1042, -953.7418, 269.2273),
            vector3(-146.5233, -950.3701, 259.1329)
        }
    },

    -- {
    --     name = "Prison",
    --     label = "Prison",
    --     maxPlayers = 20,
    --     position = vector3(1691.8304, 2533.8486, 61.3355),
    --     size = 150.0,
    --     dimension = 1003,
    --     imgUrl = "https://cdn.discordapp.com/attachments/951826753129697301/964181328054616114/prisson.png",
        
    --     weapons = {
    --         {
    --             name = 'WEAPON_PISTOL',
    --             components = {
    --                 GetHashKey('COMPONENT_AT_PI_SUPP_02')
    --             }
    --         },
    --         {
    --             name = 'WEAPON_PISTOL_MK2',
    --             components = {
    --                 GetHashKey('COMPONENT_PISTOL_MK2_CLIP_02')
    --             }
    --         },
    --         {
    --             name = 'WEAPON_PISTOL50',
    --             components = {
    --                 GetHashKey('COMPONENT_PISTOL50_CLIP_02')
    --             }
    --         }
    --         --{
    --           --  name = 'WEAPON_MACHINEPISTOL',
    --           --  components = {
    --           --      GetHashKey('COMPONENT_AT_PI_SUPP'),
    --           --      GetHashKey('COMPONENT_MACHINEPISTOL_CLIP_03')
    --           --  }
    --       --  }
    --     },
        
    --     spawns = {
    --         vector3(1714.3136, 2495.2021, 45.5648),
    --         vector3(1659.6914, 2495.8630, 45.5648),
    --         vector3(1617.4119, 2553.6433, 45.5648),
    --         vector3(1698.7802, 2560.3872, 45.5581),
    --         vector3(1763.9585, 2558.1360, 45.5501),
    --         vector3(1709.2572, 2531.8989, 45.5636),
    --         vector3(1692.7716, 2469.9475, 45.6234),
    --         vector3(1664.1854, 2487.2678, 45.5649),
    --         vector3(1666.0358, 2569.0559, 45.5648),
    --         vector3(1752.4668, 2518.2371, 45.5772)
    --     }
    -- }
    {
        name = "Berg",
        label = "Berg",
        maxPlayers = 30,
        position = vector3(969.08, 3391.84, 60.13),
        size = 130.0,
        dimension = 435,
        imgUrl = "img/berg1.png",

        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    GetHashKey('COMPONENT_PISTOL_CLIP_02')
                }
            },
            {
                name = 'WEAPON_PISTOL_MK2',
                components = {
                    GetHashKey('COMPONENT_PISTOL_MK2_CLIP_02')
                }
            },
            {
                name = 'WEAPON_PISTOL50',
                components = {
                    GetHashKey('COMPONENT_PISTOL50_CLIP_02')
                }
            }
        },

        spawns = {
            vector3(1070.59, 3391.73, 46.74),
            vector3(896.49, 3293.08, 41.4),
            vector3(862.08, 3374.81, 65.06),
            vector3(842.32, 3411.16, 72.36),
            vector3(873.42, 3467.46, 46.15),
            vector3(935.55, 3498.72, 45.32),
            vector3(1009.11, 3482.08, 33.64),
            vector3(1057.23, 3457.15, 36.62),
            vector3(1081.79, 3383.45, 42.23),
            vector3(1079.73, 3333.77, 38.22),
            vector3(983.81, 3382.26, 60.32),
            vector3(964.7, 3425.13, 48.03),
            vector3(1028.66, 3401.74, 48.31)
        }

    }
}

Config_FFA.trainingZones = {
    {
        name = "Ammunation",
        label = "Easy",
        position = vector3(13.4252, -1097.5806, 29.8347),
        size = 50.0,
        imgUrl = "img/traning2.png",

        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    GetHashKey('COMPONENT_AT_PI_SUPP_02')
                }
            }
        },
        
        spawns = {
            vector3(13.4252, -1097.5806, 29.8347)
        },

        npcspawns = {
            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(14.7723, -1081.5602, 29.7970, 155.2887),
                weapon = 'WEAPON_PISTOL',
                accuracy = 50,
                shootRate = 500,
                freeze = true,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(17.3969, -1086.1943, 29.7970, 158.0436),
                weapon = 'WEAPON_PISTOL',
                accuracy = 50,
                shootRate = 500,
                freeze = true,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(20.4316, -1089.4561, 29.7960, 159.3194),
                weapon = 'WEAPON_PISTOL',
                accuracy = 50,
                shootRate = 500,
                freeze = true,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(22.0973, -1080.9722, 29.7970, 158.5128),
                weapon = 'WEAPON_PISTOL',
                accuracy = 50,
                shootRate = 500,
                freeze = true,
                oneshot = true
            }
        }
    },

    {
        name = "Bunker",
        label = "Medium",
        position = vector3(896.0181, -3172.6887, -97.1237),
        size = 60.0,
        imgUrl = "img/traning23.png",
        
        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    GetHashKey('COMPONENT_AT_PI_SUPP_02')
                }
            }
        },
        
        spawns = {
            vector3(896.0181, -3172.6887, -97.1237)
        },

        npcspawns = {
            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(899.5356, -3141.6619, -97.1237, 170.3534),
                weapon = 'WEAPON_PISTOL',
                accuracy = 75,
                shootRate = 850,
                freeze = true,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(896.3987, -3151.7078, -97.1235, 174.0234),
                weapon = 'WEAPON_PISTOL',
                accuracy = 75,
                shootRate = 850,
                freeze = true,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(900.0422, -3129.5100, -97.1237, 168.7836),
                weapon = 'WEAPON_PISTOL',
                accuracy = 75,
                shootRate = 850,
                freeze = true,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(905.7252, -3134.5728, -97.1237, 177.0921),
                weapon = 'WEAPON_PISTOL',
                accuracy = 75,
                shootRate = 850,
                freeze = true,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(902.6501, -3147.8743, -97.1237, 169.9407),
                weapon = 'WEAPON_PISTOL',
                accuracy = 75,
                shootRate = 850,
                freeze = true,
                oneshot = true
            }
        }
    },

    {
        name = "BAUSTELLE",
        label = "Hard",
        position = vector3(1138.2472, 2336.1360, 54.1706),
        size = 40.0,
        imgUrl = "img/2.png",

        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    GetHashKey('COMPONENT_AT_PI_SUPP_02')
                }
            }
        },
        
        spawns = {
            vector3(1138.2472, 2336.1360, 54.1706)
        },

        npcspawns = {
            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(1133.5673, 2366.5986, 50.7147, 14.4956),
                weapon = 'WEAPON_PISTOL50',
                accuracy = 50,
                shootRate = 500,
                freeze = false,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(1154.6033, 2372.4263, 53.8666, 116.0719),
                weapon = 'WEAPON_PISTOL50',
                accuracy = 50,
                shootRate = 500,
                freeze = false,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(1121.3159, 2316.6614, 47.4319, 312.5486),
                weapon = 'WEAPON_PISTOL50',
                accuracy = 50,
                shootRate = 500,
                freeze = false,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(1146.2010, 2306.9714, 49.8999, 306.1507),
                weapon = 'WEAPON_PISTOL50',
                accuracy = 50,
                shootRate = 500,
                freeze = false,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(1154.6475, 2326.4695, 51.5038, 49.5404),
                weapon = 'WEAPON_PISTOL50',
                accuracy = 50,
                shootRate = 500,
                freeze = false,
                oneshot = true
            }
        }
    },

    {
        name = "HIPPI DORF",
        label = "Extrem",
        position = vector3(2485.4187, 3757.2227, 42.1607),
        size = 60.0,
        imgUrl = "img/1.png",
        
        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    GetHashKey('COMPONENT_AT_PI_SUPP_02')
                }
            }
        },

        spawns = {
            vector3(2485.4187, 3757.2227, 42.1607)
        },

        npcspawns = {
            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(2493.5474, 3727.5754, 43.4472, 43.3899),
              --  weapon = 'WEAPON_MACHINEPISTOL',
                accuracy = 75,
                shootRate = 750,
                freeze = false,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(2507.9299, 3746.0474, 43.1719, 49.2077),
             --   weapon = 'WEAPON_MACHINEPISTOL',
                accuracy = 75,
                shootRate = 750,
                freeze = false,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(2493.4697, 3788.0808, 48.8035, 141.2421),
              --  weapon = 'WEAPON_MACHINEPISTOL',
                accuracy = 75,
                shootRate = 750,
                freeze = false,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(2471.2566, 3783.8738, 40.9349, 172.3683),
              --  weapon = 'WEAPON_MACHINEPISTOL',
                accuracy = 75,
                shootRate = 750,
                freeze = false,
                oneshot = true
            },

            {
                ped = 'a_m_m_beach_01',
                spawnedPed = nil,
                spawned = false,
                death = false,
                coords = vector4(2447.3413, 3767.4321, 41.1196, 255.6340),
              --  weapon = 'WEAPON_MACHINEPISTOL',
                accuracy = 75,
                shootRate = 750,
                freeze = false,
                oneshot = true
            }
        }
    }
}

Config_FFA.privateZones = {
    {
        name = "Casino Dach",
        label = "Private Lobby",
        position = vector3(477.2156, 165.6349, 110.6342),
        size = 40.0,
        imgUrl = "img/dach2.png",
        
        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    GetHashKey('COMPONENT_AT_PI_SUPP_02')
                }
            }
        },
        
        spawns = {
            vector3(459.9673, 190.6121, 110.5958), 
            vector3(495.8133, 173.7899, 110.6555), 
            vector3(481.6616, 142.1372, 110.6527), 
            vector3(444.0501, 140.7654, 110.5998), 
            vector3(456.5244, 163.0720, 110.6062),
            vector3(459.9013, 179.3582, 110.6026)
        }
    },

    {
        name = "Hotel dach",
        label = "Private Lobby",
        position = vector3(-1391.2218, 292.1165, 95.0961),
        size = 40.0,
        imgUrl = "img/dach23.png",
        
        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    GetHashKey('COMPONENT_AT_PI_SUPP_02')
                }
            }
        },
        
        spawns = {
            vector3(-1385.4042, 302.2655, 89.8643),
            vector3(-1400.2317, 307.9985, 89.8644),
            vector3(-1402.4832, 293.5674, 89.8644),
            vector3(-1393.0367, 267.0098, 89.8644),
            vector3(-1379.0585, 265.5733, 89.8643),
            vector3(-1372.2655, 271.3719, 89.8643)
        }
    },

    {
        name = "Observatorium",
        label = "Private Lobby",
        position = vector3(-419.6210, 1153.2267, 326.8732),
        size = 100.0,
        imgUrl = "img/obis.png",
        
        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    GetHashKey('COMPONENT_AT_PI_SUPP_02')
                }
            }
        },

        spawns = {
            vector3(-395.1815, 1104.2329, 325.8436),
            vector3(-386.5791, 1186.0540, 325.6815),
            vector3(-415.6097, 1202.6626, 325.6418),
            vector3(-431.6885, 1207.7985, 325.7583),
            vector3(-473.1340, 1128.6034, 325.8687),
            vector3(-413.7414, 1097.9293, 332.5338),
            vector3(-450.2201, 1108.0962, 332.5313)
        }
    },
    {
        name = "Hafen",
        label = "Private Lobby",
        position = vector3(1140.9429, -3008.4753, 5.8998),
        size = 65.0,
        imgUrl = "img/hafen.png",
        
        weapons = {
            {
                name = 'WEAPON_PISTOL',
                components = {
                    GetHashKey('COMPONENT_AT_PI_SUPP_02')
                }
            }
        },
        
        spawns = {
            vector3(1149.2280, -3026.5112, 5.9010),
            vector3(1164.8007, -3007.2209, 5.9012),
            vector3(1178.2251, -2981.6621, 5.9007),
            vector3(1135.2928, -2983.0776, 5.8982),
            vector3(1097.0931, -2999.2546, 5.7576),
            vector3(1103.3077, -3034.2813, 5.8842)
        }
    }
}

function sendNotify(title, message, type)
    TriggerEvent('cc_core:hud:notify', type, title, message)
end