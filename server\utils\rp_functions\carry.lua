carryCode = [[
ESX = exports["es_extended"]:getSharedObject()

local carry = {
	InProgress = false,
	targetSrc = 4000,
	type = '',
	personCarrying = {
		animDict = 'missfinale_c2mcs_1',
		anim = 'fin_c2_mcs_1_camman',
		flag = 49,
	},
	personCarried = {
		animDict = 'nm',
		anim = 'firemans_carry',
		attachX = 0.27,
		attachY = 0.15,
		attachZ = 0.63,
		flag = 33,
	}
}

local CarryCooldown = false

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		if CarryCooldown then
			Citizen.Wait(1000)
            CarryCooldown = false
		else
			Citizen.Wait(255)
		end
	end
end)

RegisterCommand('carry', function()
    if not CarryCooldown then
		if not IsEntityPlayingAnim(PlayerPedId(), 'mp_arresting', 'idle', 3) then
			if not carry.InProgress then
				local closestPlayer, closestPlayerDistance = ESX.Game.GetClosestPlayer()
				if closestPlayer ~= nil and closestPlayer ~= -1 and closestPlayerDistance <= 2.5 then
					if GetPlayerServerId(closestPlayer) ~= nil then
						if GetPlayerServerId(closestPlayer) ~= -1 then
							if ESX.PlayerData.group == 'projektleitung' or ESX.PlayerData.group == 'managment' or ESX.PlayerData.group == 'teamleitung' or ESX.PlayerData.group == 'superadmin' or ESX.PlayerData.group == 'administrator' or ESX.PlayerData.group == 'moderator' or ESX.PlayerData.group == 'frakverwaltung' or ESX.PlayerData.group == 'support' or ESX.PlayerData.group == 'testupporter' or ESX.PlayerData.group == 'user' then
								TriggerServerEvent('Carry:ForceCarry', GetPlayerServerId(closestPlayer))
								CarryCooldown = true
							else
								if GetEntityHealth(GetPlayerPed(closestPlayer)) <= 0 then
									TriggerServerEvent('Carry:ForceCarry', GetPlayerServerId(closestPlayer))
									CarryCooldown = true
								else
									TriggerServerEvent('Carry:SendRequest', GetPlayerServerId(closestPlayer))
									CarryCooldown = true
								end
							end
						end
					end
				end
			else
				--if carry.type ~= 'beingcarried' then
					carry.InProgress = false
					ClearPedSecondaryTask(PlayerPedId())
					DetachEntity(PlayerPedId(), true, false)
					TriggerServerEvent('Carry:StopCarrying', carry.targetSrc)
					carry.targetSrc = 0
				--end
			end
		end
    end
end)

RegisterKeyMapping('carry', 'Spieler Tragen', 'keyboard', 'NULL')

RegisterNetEvent('Carry:RequestedAccepted', function(target)
    if target ~= nil and target ~= -1 then
        carry.InProgress = true
        carry.targetSrc = target
        carry.type = 'carrying'
    end
end)

RegisterNetEvent('Carry:ClientGotRequest', function(requestId)
    if requestId ~= nil then
        if requestId ~= -1 then
            local RequestAccepted = false
            local RequestTime = 0
            while true do
                Citizen.Wait(0)
                RequestTime = RequestTime + 1
                if RequestTime >= 2500 or RequestAccepted then
                    break
                end
                if IsDisabledControlJustReleased(1, 47) then
                    if not RequestAccepted then
                        RequestAccepted = true
                        TriggerServerEvent('Carry:AcceptedRequest', requestId)
                        break
                    end
                end
            end
        end
    end
end)

local function ensureAnimDict(animDict)
    if not HasAnimDictLoaded(animDict) then
        RequestAnimDict(animDict)
        while not HasAnimDictLoaded(animDict) do
            Wait(0)
        end        
    end
    return animDict
end

RegisterNetEvent('CarryPeople:SyncTarget', function(targetSrc)
	local targetPed = GetPlayerPed(GetPlayerFromServerId(targetSrc))
	carry.InProgress = true
	ensureAnimDict(carry.personCarried.animDict)
	AttachEntityToEntity(PlayerPedId(), targetPed, 0, carry.personCarried.attachX, carry.personCarried.attachY, carry.personCarried.attachZ, 0.5, 0.5, 180, false, false, false, false, 2, false)
	carry.type = 'beingcarried'
end)

RegisterNetEvent('CarryPeople:Stop', function()
	carry.InProgress = false
	ClearPedSecondaryTask(PlayerPedId())
	DetachEntity(PlayerPedId(), true, false)
    CarryCooldown = false
end)

Citizen.CreateThread(function()
	while true do
        Citizen.Wait(0)
		if carry.InProgress then
			if carry.type == 'beingcarried' then
                DisableControlAction(1, 24, true)
                DisableControlAction(1, 25, true)
                DisableControlAction(1, 36, true)
                DisableControlAction(1, 140, true)
				if not IsEntityPlayingAnim(PlayerPedId(), carry.personCarried.animDict, carry.personCarried.anim, 3) then
					TaskPlayAnim(PlayerPedId(), carry.personCarried.animDict, carry.personCarried.anim, 8.0, -8.0, 100000, carry.personCarried.flag, 0, false, false, false)
				end
			elseif carry.type == 'carrying' then
                DisableControlAction(1, 24, true)
                DisableControlAction(1, 25, true)
                DisableControlAction(1, 36, true)
                DisableControlAction(1, 140, true)
				if not IsEntityPlayingAnim(PlayerPedId(), carry.personCarrying.animDict, carry.personCarrying.anim, 3) then
					TaskPlayAnim(PlayerPedId(), carry.personCarrying.animDict, carry.personCarrying.anim, 8.0, -8.0, 100000, carry.personCarrying.flag, 0, false, false, false)
				end
			end
        else
            Citizen.Wait(1000)
		end
	end
end)
]]

ESX = exports['es_extended']:getSharedObject()

local CarryRequests = {}
local GettingCarryd = {}
local Carrying = {}

function BanMonkey(monkeId)
    if monkeId ~= nil then
        if GetPlayerName(monkeId) ~= nil then
            TriggerEvent('EasyAdmin:banPlayer', monkeId, 'C-R [420]', false, 'Afrika') 
        end
    end
end

RegisterNetEvent('Carry:SendRequest', function(target)
    if target ~= nil then
        if target == -1 then
            return
        else
            if GetPlayerName(target) ~= nil then
                TriggerClientEvent('Carry:ClientGotRequest', target, source)
                TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Carry', 'Anfrage an: '..target..' Gesendet!')
                TriggerClientEvent('cc_core:hud:notify', target, 'info', 'Carry', 'Anfrage von: '..target..' Erhalten! [G] zum Akzeptieren.')
                CarryRequests[source] = target
                CarryRequests[target] = source
            end
        end
    end
end)

RegisterNetEvent('Carry:AcceptedRequest', function(requestOwner)
    if requestOwner == nil then
        if requestOwner == -1 then
            BanMonkey(source)
            return
        end
    else
        if GetPlayerName(requestOwner) ~= nil then
            if #(ESX.GetPlayerCoords(source, true) - ESX.GetPlayerCoords(requestOwner, true)) <= 2.5 then
                if CarryRequests[source] == requestOwner then
                    TriggerClientEvent('Carry:RequestedAccepted', requestOwner, source)
                    TriggerClientEvent('CarryPeople:SyncTarget', source, requestOwner)
                    GettingCarryd[source] = true
                    Carrying[requestOwner] = true
                end
            end
        end
    end
end)

RegisterNetEvent('Carry:StopCarrying', function(target)
    local src = source
    if target ~= nil then
        if target == -1 then
            BanMonkey(source)
            return
        else
            if GetPlayerName(src) ~= nil and GetPlayerName(target) ~= nil then
                local coords1 = ESX.GetPlayerCoords(source, true)
                local coords2 = ESX.GetPlayerCoords(target, true)

                if coords1 ~= nil and coords2 ~= nil then
                    local distance = #(vector3(coords1.x, coords1.y, coords1.z) - vector3(coords2.x, coords2.y, coords2.z))
                    if distance <= 2.5 then
                        if GettingCarryd[target] then
                            Carrying[source] = false
                            GettingCarryd[target] = false
                            TriggerClientEvent('CarryPeople:Stop', source)
                            TriggerClientEvent('CarryPeople:Stop', target)
                        end
                    end
                end
            end
        end
    end
end)


RegisterNetEvent('Carry:ForceCarry', function(target)
    if target ~= nil then
        if target == -1 then
            BanMonkey(source)
            return
        else
            if GetPlayerName(target) ~= nil then
                if #(ESX.GetPlayerCoords(source, true) - ESX.GetPlayerCoords(target, true)) <= 75.0 then --Big Dist cause Server Side Desync with Client Side Desync
                    if GetEntityHealth(GetPlayerPed(target)) <= 0 or ESX.GetPlayerGroup(source) == 'projektleitung' or ESX.GetPlayerGroup(source) == 'managment' or ESX.GetPlayerGroup(source) == 'teamleitung' or ESX.GetPlayerGroup(source) == 'superadmin' or ESX.GetPlayerGroup(source) == 'administrator' or ESX.GetPlayerGroup(source) == 'moderator' or ESX.GetPlayerGroup(source) == 'support' or ESX.GetPlayerGroup(source) == 'frakverwaltung' or ESX.GetPlayerGroup(source) == 'user' then
                        TriggerClientEvent('Carry:RequestedAccepted', source, target)
                        TriggerClientEvent('CarryPeople:SyncTarget', target, source)
                        GettingCarryd[target] = true
                        Carrying[source] = true
                    end
                end
            end
        end
    end
end)

AddEventHandler('playerDropped', function()
    if CarryRequests[source] then
        CarryRequests[source] = nil
    end
    if GettingCarryd[source] then
        GettingCarryd[source] = nil
    end
    if Carrying[source] then
        Carrying[source] = nil
    end
end)