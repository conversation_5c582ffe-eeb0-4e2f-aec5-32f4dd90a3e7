vehicle_functionsCode = [[
local ESX = nil
local isPL = false
local isInZone = false
local disableShuffle = true
local inTrunk = false
local isDead = false

function InTrunko()
	return inTrunk
end
exports('InTrunk', InTrunko)

local ped = nil

local function isJobLegal(jobName)
    if jobName == 'police' or jobName == 'fib' or jobName == 'army' or jobName == 'ambulance' then
        return true
    end

    return false
end

local function loadAnimDict(dict, cb)
    while not HasAnimDictLoaded(dict) do
        RequestAnimDict(dict)
        Citizen.Wait(50)
    end

    cb()
end

local function DrawText3D(coords, text)
    local onScreen, _x, _y = World3dToScreen2d(coords.x, coords.y, coords.z)
    local pX, pY, pZ = table.unpack(GetGameplayCamCoords())

    SetTextScale(0.4, 0.4)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextEntry("STRING")
    SetTextCentre(1)
    SetTextColour(255, 255, 255, 255)
    SetTextOutline()

    AddTextComponentString(text)
    DrawText(_x, _y)
end

Citizen.CreateThread(function()
	while ESX == nil do
		ESX = exports['es_extended']:getSharedObject()
		Citizen.Wait(0)
	end

    while ESX.GetPlayerData().group == nil do
        Citizen.Wait(0)
    end

    if ESX.GetPlayerData().group == 'penisinmeinarsch' then
		isPL = true
	end

	ESX.PlayerData = ESX.GetPlayerData()

    startThreadoo()
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESX.PlayerData.job = job
end)

RegisterNetEvent('esx:setJobDienst')
AddEventHandler('esx:setJobDienst', function(job, state)
	ESX.PlayerData.job.dienst = state
end)

AddEventHandler('esx:eath', function(data)
	isDead = true
end)

local speedBypass = false

RegisterNetEvent('toggleSpeedBypass', function(state)
	speedBypass = state
	print('^2Updated^0 ^2Speed^0-^2Bypass^0 To:', state)
end)

function startThreadoo()

    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(5000)
            ResetPlayerStamina(PlayerId())
        end
    end)

	-- Citizen.CreateThread(function()
    --     isInZone = false
	-- 	while true do
	-- 		Citizen.Wait(0)
	-- 		local playerPed = PlayerPedId()
	-- 		local coords = GetEntityCoords(playerPed)
	-- 		local foundJob = false
	-- 		local inAnySafezone = false

	-- 		for k, v in pairs(SaveZones) do
	-- 			local distance = #(coords - v.coords)

	-- 			if v.debug then
	-- 				DrawSphere(v.coords.x, v.coords.y, v.coords.z, v.radius, 39, 114, 145, 0.7)
	-- 			end

	-- 			if distance < v.radius then
	-- 				inAnySafezone = true
	-- 				for k1, v1 in pairs(v.jobs) do
	-- 					if ESX.PlayerData.job ~= nil then
	-- 						if ESX.PlayerData.job then
	-- 							if ESX.PlayerData.job.name == v1 then
	-- 								if isJobLegal(ESX.PlayerData.job.name) then
	-- 									if ESX.PlayerData.job.dienst then
	-- 										foundJob = true
	-- 									end
	-- 								else
	-- 									foundJob = true
	-- 								end
	-- 							end
	-- 						end
	-- 					end
	-- 				end

	-- 				if not foundJob and not isPL then
	-- 					if not v.weaponUse then
	-- 						SetCurrentPedWeapon(playerPed, GetHashKey('WEAPON_UNARMED'), true)
	-- 						DisableControlAction(0, 6, true)
	-- 						DisableControlAction(0, 25, true)
	-- 						DisableControlAction(0, 24, true)
	-- 						DisableControlAction(0, 45, true)
	-- 						DisableControlAction(0, 80, true)
	-- 						DisableControlAction(0, 140, true)
	-- 						DisableControlAction(0, 250, true)
	-- 						DisableControlAction(0, 303, true)
	-- 						DisableControlAction(0, 263, true)
	-- 						DisableControlAction(0, 310, true)
	-- 						DisableControlAction(0, 85, false)
	-- 						DisableControlAction(0, 264, false)
	-- 					end

	-- 					if v.vehicleMax then
	-- 						if IsPedDoingDriveby(playerPed) then
	-- 							DisableControlAction(0, 69, true)
	-- 							DisableControlAction(0, 92, true)
	-- 							DisableControlAction(0, 140, true)
	-- 						else
	-- 							DisableControlAction(0, 69, false)
	-- 							DisableControlAction(0, 92, false)
	-- 							DisableControlAction(0, 140, false)
	-- 							DisableControlAction(0, 85, false)
	-- 							DisableControlAction(0, 264, false)
	-- 						end


	-- 						if not isPL then
	-- 							if IsPedInAnyVehicle(playerPed, false) then
	-- 								local vehicle = GetVehiclePedIsUsing(playerPed)
	-- 								if GetPedInVehicleSeat(vehicle, -1) == PlayerPedId() then
	-- 									SetEntityMaxSpeed(vehicle, v.vehicleMax)
	-- 								end
	-- 							end
	-- 						end
	-- 					end

	-- 					SetPedCanRagdoll(playerPed, true)
	-- 					ClearPedBloodDamage(playerPed)
	-- 				end
	-- 			end
	-- 		end

	-- 		if not isInZone and inAnySafezone then
	-- 			isInZone = true
	-- 		elseif isInZone and not inAnySafezone then
	-- 			isInZone = false

	-- 			if not isPL then
	-- 				if IsPedInAnyVehicle(playerPed, false) then
	-- 					local vehicle = GetVehiclePedIsUsing(playerPed)
	-- 					if GetPedInVehicleSeat(vehicle, -1) == PlayerPedId() then
	-- 						SetEntityMaxSpeed(vehicle, GetVehicleMaxSpeed(vehicle))
	-- 					end
	-- 				end
	-- 			end
	-- 		end

    --         if not isInZone then
    --             Citizen.Wait(500)
    --         end
	-- 	end
	-- end)

    if ESX.GetPlayerData().group == 'projektleitung' or ESX.GetPlayerData().group == 'managment' or ESX.GetPlayerData().group == 'teamleitung' or ESX.GetPlayerData().group == 'superadmin' or ESX.GetPlayerData().group == 'administrator' then
		Citizen.CreateThread(function()
            while true do
                if IsControlPressed(1, 19) and IsControlJustReleased(1, 38) and IsInputDisabled(2) then
					local ped = PlayerPedId()
                    local waypointHandle = GetFirstBlipInfoId(8)

                    if DoesBlipExist(waypointHandle) then
                        Citizen.CreateThread(function()
                            local waypointCoords = GetBlipInfoIdCoord(waypointHandle)
                            local foundGround, zCoords, zPos = false, -500.0, 0.0

                            while not foundGround do
                                zCoords = zCoords + 10.0
                                RequestCollisionAtCoord(waypointCoords.x, waypointCoords.y, zCoords)
                                Citizen.Wait(0)
                                foundGround, zPos = GetGroundZFor_3dCoord(waypointCoords.x, waypointCoords.y, zCoords)

                                if not foundGround and zCoords >= 2000.0 then
                                    foundGround = true
                                end
                            end

                            SetPedCoordsKeepVehicle(ped, waypointCoords.x, waypointCoords.y, zPos)
                        end)
                    end
                end

                Citizen.Wait(0)
            end
        end)
    end

    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(0)
            local ped = PlayerPedId()
            local weapon = GetSelectedPedWeapon(ped)
            if IsPedArmed(ped, 6) then
                DisableControlAction(1, 140, true)
                DisableControlAction(1, 141, true)
                DisableControlAction(1, 142, true)
            else
                Citizen.Wait(500)
            end
        end
    end)

    Citizen.CreateThread(function()
        local shittystuff = false

        while true do
            if not shittystuff then
                if GetPedParachuteState(PlayerPedId()) ~= -1 and GetPedParachuteState(PlayerPedId()) ~= 0 then
                    shittystuff = true
                    TriggerServerEvent("cc_emotes:yourMonkey")
                end
            else
                if GetPedParachuteState(PlayerPedId()) == -1 then
                    shittystuff = false
                end
            end

            Citizen.Wait(500)
        end
    end)


RegisterCommand("shuff", function(source, args, raw)
    if IsPedInAnyVehicle(PlayerPedId(), false) then
        disableShuffle = false
		Citizen.Wait(5000)
        disableShuffle = true
	end
end, false)

function Notify(title, message, type)
    TriggerEvent('cc_core:hud:notify', type, title, message)
end

Citizen.CreateThread(function()
	while true do
		InvalidateIdleCam()
		InvalidateVehicleIdleCam()
		Citizen.Wait(10000)
	end
end)
]]