Config_CarPanel = {}

Config_CarPanel.General = {
  autopilot_speed = 120.0, 
  require_installation = false,
  only_driver_controls_audio = false
}

Config_CarPanel.URLBlacklist = {
  "https://tiziano.cc"
}

Config_CarPanel.CruiseControl = {
  key = "",
  description = "Tempomat umschalten",
}
  
Config_CarPanel.AutoInstalledVehicles = {
  "futo",
  "car2",
}

Config_CarPanel.BlacklistedVehicles = {
  "car3",
  "car4",
}

function CCNotify(message, type)
  TriggerEvent('cc_core:hud:notify', type, "Car Panel", message)
end