local function getWeaponIndex(index, name)
    for k, v in pairs(Config_WeaponCrafter.weaponCrafter[index].weapons) do
        if v.name == name then
            if v.heavy ~= nil then
                return v.weaponParts, v.platin, v.purple, v.gold, v.heavy
            else
                return v.weaponParts, v.platin, v.purple, v.gold, false
            end
        end
    end
end

local MeleeWeapons = {  
    'weapon_dagger',
    'weapon_bat',
    'weapon_bottle',
    'weapon_crowbar',
    'weapon_unarmed',
    'weapon_flashlight',
    'weapon_golfclub',
    'weapon_hammer',
    'weapon_hatchet',
    'weapon_knuckle',
    'weapon_knife',
    'weapon_machete',
    'weapon_switchblade',
    'weapon_nightstick',
    'weapon_wrench',
    'weapon_battleaxe',
    'weapon_poolcue',
    'weapon_stone_hatchet',
}

local function IsMeleeWeapon(name)
    for _, w in pairs(MeleeWeapons) do
        if string.upper(w) == string.upper(name) then
            return true
        end
    end
    return false
end

RegisterServerEvent('cc_core:weaponcrafter:craftWeapon')
AddEventHandler('cc_core:weaponcrafter:craftWeapon', function(data, index)
    local playerId = source

    if GetPlayerRoutingBucket(playerId) ~= 0 then
        SetPlayerRoutingBucket(playerId, 0)
    end

    local price, platin, purple, gold, heavy = getWeaponIndex(index, data.name)
    local itemName = 'waffenteile'
    if heavy then
        itemName = 'schwere_waffenteil'
    end
    
    if IsMeleeWeapon(data.name) then
        itemName = 'nahkampfteil'
    end

    if data.name == 'WEAPON_SNSPISTOL' then
        itemName = 'leichte_waffenteile'
    end

    local item = ESX.GetPlayerInventoryItem(playerId, itemName).count
    local tint = 0

    local hour = tonumber(os.date('%H'))

    if hour == 24 or hour < 9 then
        price = price * 4
    end

    if data.color then
        if data.color == 'platin' then
            price = price + platin
            tint = 7
        elseif data.color == 'purple' then
            price = price + purple
            tint = 3
        elseif data.color == 'gold' then
            price = price + gold
            tint = 2
        end

        if item >= price then
            if not ESX.HasPlayerWeapon(playerId, data.name) then
                ESX.RemovePlayerInventoryItem(playerId, itemName, price, GetCurrentResourceName())
                ESX.AddPlayerWeapon(playerId, data.name, 0, 100.0)
                ESX.SetPlayerWeaponTint(playerId, data.name, tint)

                exports['cc_core']:log(playerId, 'Weaponcrafter - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat eine ' .. ESX.GetWeaponLabel(data.name) .. ' mit ' .. price .. 'x '..itemName..' hergestellt', 'https://canary.discord.com/api/webhooks/1221946590730653838/-ddgwN47gcl6l624Y-Swyg0WK37sFYsFxGXmjpw1cgx29m6KX2sh0rXNmUHlGVTeP16E')
            else
                Notify(playerId, 'Waffencrafter', 'Du hast diese Waffe bereits', 'info')
            end
        else
            Notify(playerId, 'Waffencrafter', 'Du hast nicht genügend Waffenteile', 'info')
            if hour < 9 or hour == 24 then
                TriggerClientEvent('esx:showNotification', playerId, 'Von 00:00 bis 03:00 Alles mal 2x\nVon03:00 bis 09:00 Alles mal 3x!')
            end
        end
    else
        if item >= price then
            if not ESX.HasPlayerWeapon(playerId, data.name) then
                ESX.RemovePlayerInventoryItem(playerId, itemName, price)
                ESX.AddPlayerWeapon(playerId, data.name, 0, 100.0)

                exports['cc_core']:log(playerId, 'Weaponcrafter - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat eine ' .. ESX.GetWeaponLabel(data.name) .. ' mit ' .. price .. 'x '..itemName..' hergestellt', 'https://canary.discord.com/api/webhooks/1221946590730653838/-ddgwN47gcl6l624Y-Swyg0WK37sFYsFxGXmjpw1cgx29m6KX2sh0rXNmUHlGVTeP16E')
            else
                Notify(playerId, 'Waffencrafter', 'Du hast diese Waffe bereits', 'info')
            end
        else
            Notify(playerId, 'Waffencrafter', 'Du hast nicht genügend Waffenteile', 'info')
        end
    end
end)

RegisterServerEvent('cc_weaponcrafter:craftItem')
AddEventHandler('cc_weaponcrafter:craftItem', function(value, index)
    local playerId = source

    if GetPlayerRoutingBucket(playerId) ~= 0 then
        SetPlayerRoutingBucket(playerId, 0)
    end

    local base = Config_WeaponCrafter.weaponAttachments[index].items[value]
    local item = ESX.GetPlayerInventoryItem(playerId, base.need).count

    if item >= base.count then
        if ESX.PlayerCanCarryItem(playerId, base.name, 1) then
            ESX.AddPlayerInventoryItem(playerId, base.name, 1, GetCurrentResourceName())
            ESX.RemovePlayerInventoryItem(playerId, base.need, base.count, GetCurrentResourceName())

            exports['cc_core']:log(playerId, 'Aufsätze - Log', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat ein ' .. ESX.GetItemLabel(base.name) .. ' mit ' .. base.count .. ' Stahlteil hergestellt', 'https://canary.discord.com/api/webhooks/1221946378092019753/oioea7NRuTFcvtjYklnaJalEyL6Ud0wBcBAUOwp8r-poHmT6yhXR_N6RAS0kkXPxYd64')
        else
            Notify(playerId, 'Waffencrafter', 'Dein Inventar ist voll', 'info')
        end
    else
        Notify(playerId, 'Waffencrafter', 'Du hast nicht genügend Kompressoren! Du brauchst ' .. base.count .. 'x Kompressoren', 'info')
    end
end)

--clientcode
weaponCrafterCode = [[
local show = false
local index = 0

RegisterNUICallback('weaponcrafter/craft', function(data, cb)
    SetNuiFocus(false, false)

    isInUI = false

    TriggerServerEvent('cc_core:weaponcrafter:craftWeapon', data, index)
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)

        for k, v in pairs(Config_WeaponCrafter.weaponCrafter) do
            local distance = #(coords - vector3(v.coords.x, v.coords.y, v.coords.z))

            if distance < 2.0 then
                letSleep = false
                inRange = true

                if IsControlJustPressed(0, 38) then
                    index = k
                    SetNuiFocus(true, true)
                    
                    isInUI = true

                    SendNUIMessage({
                        script = 'weaponcrafter',
                        action = 'show',
                        weapons = v.weapons
                    })
                end
            end
        end

        helpNotify(inRange, show, 'Drücke E um den Waffencrafter zu öffnen', function(bool)
            show = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

Citizen.CreateThread(function()
    while not HasModelLoaded(0x5746CD96) do
        RequestModel(0x5746CD96)
        Citizen.Wait(1)
    end

    for k, v in pairs(Config_WeaponCrafter.weaponCrafter) do
        exports['cc_core']:createPed(vector4(v.coords.x, v.coords.y, v.coords.z - 1, v.coords.w), 0x5746CD96)
    end
end)

local show2 = false

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)

        for k, v in pairs(Config_WeaponCrafter.weaponAttachments) do
            local distance = #(coords - vector3(v.coords.x, v.coords.y, v.coords.z))

            if distance < 1.0 then
                letSleep = false
                inRange = true

                if IsControlJustPressed(0, 38) then
                    local elements = {}

                    for k1, v1 in pairs(v.items) do
                        table.insert(elements, {
                            label = v1.label,
                            value = k1
                        })
                    end
                    
                    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'weapon_attachments', {
                        title = 'Suche',
                        align = 'top-left',
                        elements = elements
                    }, function(data, menu)
                        TriggerServerEvent('cc_weaponcrafter:craftItem', data.current.value, k)
                    end, function(data, menu)
                        menu.close()
                    end)
                end
            end
        end

        helpNotify(inRange, show2, 'Drücke E um Aufsätze herzustellen', function(bool)
            show2 = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

Citizen.CreateThread(function()
    while not HasModelLoaded(0x5746CD96) do
        RequestModel(0x5746CD96)
        Citizen.Wait(1)
    end

    for k, v in pairs(Config_WeaponCrafter.weaponAttachments) do
        exports['cc_core']:createPed(vector4(v.coords.x, v.coords.y, v.coords.z - 1, v.coords.w), 0x5746CD96)
    end
end)

local entitys = {}

local function createPed(coords, model)
    entitys[#entitys + 1] = {
        streamed = false,
        coords = coords,
        model = model
    }
end

local function removePed(index)
    entitys[index] = nil
end

exports('createPed', createPed)
exports('removePed', removePed)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)

        for k, v in ipairs(entitys) do
            local distance = #(coords - vector3(v.coords.x, v.coords.y, v.coords.z))
            
            if distance < 10.0 then
                if not v.streamed then
                    local ped = CreatePed(4, v.model, v.coords.x, v.coords.y, v.coords.z, v.coords.w, false, true)
                    FreezeEntityPosition(ped, true)
                    SetEntityInvincible(ped, true)
                    SetBlockingOfNonTemporaryEvents(ped, true)
                    v.ped = ped
                    v.streamed = true
                end
            else
                if v.streamed then
                    v.streamed = false
                    
                    if DoesEntityExist(v.ped) then
                        DeleteEntity(v.ped)
                    end
                end
            end
        end
    end
end)
]]