-- local restaurants = {}

-- function GetRestaurants()
--     return restaurants
-- end

-- function LoadDefaultRestaurants()
--     for _, restaurant in ipairs(Config_RESTAURANTS.DefaultRestaurants) do
--         AddRestaurant(restaurant.name, restaurant.coords, restaurant.items)
--     end
-- end

-- function AddRestaurant(name, coords, items)
--     local restaurant = {
--         id = #restaurants + 1,
--         name = name,
--         coords = coords,
--         items = items
--     }
--     table.insert(restaurants, restaurant)
--     return restaurant.id
-- end

-- -- Event für Client-Anfragen
-- RegisterNetEvent("cc_core:server:requestRestaurants", function()
--     local src = source
--     TriggerClientEvent("cc_core:client:receiveRestaurants", src, GetRestaurants())
-- end)

-- -- Beim Start laden
-- AddEventHandler('onResourceStart', function(resourceName)
--     if GetCurrentResourceName() == resourceName then
--         LoadDefaultRestaurants()
--     end
-- end)