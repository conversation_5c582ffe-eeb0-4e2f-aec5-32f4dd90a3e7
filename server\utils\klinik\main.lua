klinikCode = [[
 local show = false

 Citizen.CreateThread(function()
     while true do
         Citizen.Wait(4)
         local letSleep, inRange = true, false
         local ped = PlayerPedId()
         local coords = GetEntityCoords(ped)
         local distance = #(coords - vector3(-100.5135, -854.0144, 40.5545))

         if distance < 50.0 then
             letSleep = false
             DrawMarker(1, -100.5135, -854.0144, 39.5545, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 2.0, 2.0, 1.0, 102, 102, 204, 100, false, true, 2, false, false, false, false)
         end

         if distance < 8.0 then
             inRange = true

             if IsControlJustPressed(0, 38) then
                 TriggerEvent('cc_core:skin:openRestrictedMenu', function(data, menu)
                     menu.close()

                     TriggerEvent('skinchanger:getSkin', function(skin)
                         TriggerServerEvent('cc_core:skin:save', skin)
                     end)
                 end)
             end
         end

         helpNotify(inRange, show, '<PERSON>ücke E um die Schönheitsklinik zu besuchen', function(bool)
             show = bool
         end)

         if letSleep then
             Citizen.Wait(1000)
         end
     end
 end)
]]