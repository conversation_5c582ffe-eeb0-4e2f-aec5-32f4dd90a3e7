tunermenuCode = [[
ESX = nil

isPL = false

Citizen.CreateThread(function()
    while ESX == nil do
        ESX = exports['es_extended']:getSharedObject()
        Citizen.Wait(10)
    end
    while ESX.GetPlayerData().job == nil do
        Citizen.Wait(50)
    end
    ESX.PlayerData = ESX.GetPlayerData()

    if ESX.GetPlayerData().group == 'projektleitung' then
		isPL = true
    end
end)

local TunerMainMenu = RageUI.CreateMenu('', '                    Tuner Menu')
local Plate = ''
local CurrPlate = ''
local PlayerName = GetPlayerName(PlayerId())

local FrontSliderStart = 0
local FrontSliderMax = 50
local FrontSliderCurr = 35
local FrontSliderSteps = 1
local FrontSliderDescr = '['..FrontSliderCurr..'% Ausbau'..']'

local BackSliderStart = 0
local BackSliderMax = 50
local BackSliderCurr = 35
local BackSliderSteps = 1
local BackSliderDescr = '['..BackSliderCurr..'% Ausbau'..']'

local FrontCurveSliderStart = 0
local FrontCurveSliderMax = 100
local FrontCurveSliderCurr = 0
local FrontCurveSliderSteps = 1
local FrontCurveSliderDescr = FrontCurveSliderCurr..'% Neigung'

local BackCurveSliderStart = 0
local BackCurveSliderMax = 100
local BackCurveSliderCurr = 0
local BackCurveSliderSteps = 1
local BackCurveSliderDescr = BackCurveSliderCurr..'% Neigung'

local NOSChecked = false
local RGBChecked = false
local AntiLagChecked = false

local SendToServerTablo = {
    ['front_out'] = 0.8,
    ['back_out'] = 0.8,
    
    ['front_curve'] = 0.0,
    ['back_curve'] = 0.0,

    ['nos'] = false,
    ['rgb_glow'] = false,

    ['backfire'] = false
}

function RageUI.PoolMenus:CorleoneTuner()
    TunerMainMenu:IsVisible(function(Items)

        Items:AddSeparator(CurrPlate)

        if FrontSliderCurr < 50 then
            FrontSliderDescr = '[' ..FrontSliderCurr.. '% Ausbau]'
        else
            FrontSliderDescr = '[~r~MAXIMUM~s~ ERREICHT]'
        end

        if BackSliderCurr < 50 then
            BackSliderDescr = '[' ..BackSliderCurr.. '% Ausbau]'
        else
            BackSliderDescr = '[~r~MAXIMUM~s~ ERREICHT]'
        end

        if FrontCurveSliderCurr < 45 then
            FrontCurveSliderDescr = '[' ..FrontCurveSliderCurr.. '% Ausbau]'
        else
            FrontCurveSliderDescr = '[~r~MAXIMUM~s~ ERREICHT]'
        end

        if BackCurveSliderCurr < 45 then
            BackCurveSliderDescr = '[' ..BackCurveSliderCurr.. '% Ausbau]'
        else
            BackCurveSliderDescr = '[~r~MAXIMUM~s~ ERREICHT]'
        end

        if PlayerName == 'JamalToss' then
            Items:AddSeparator('~r~Senioren~s~ ist es Untersagt hier zu Tunen🐒')
            Items:AddSeparator('👴🏻 LG Gehaltskürzung und Leon 👴🏻')
        else
            if CurrPlate ~= 'Du bist in ~r~Keinem~s~ Fahrzeug!' then
    
                Items:AddSeparator('Spezial Tuning')
    
                Items:CheckBox('NOS', '[Von Außen Tunebar]', NOSChecked, { Style = 1, isDisabled = false }, function(onSelected, IsChecked)
                    if onSelected then
                        NOSChecked = IsChecked
                        if NOSChecked then
                            SendToServerTablo['nos'] = true
                        else
                            SendToServerTablo['nos'] = false
                        end
                    end
                end)
    
                Items:AddSeparator('[Warenkorb]')
    
                Items:AddButton('Tuning Bestätigen', CurrPlate, { isDisabled = false }, function(onSelected)
                    if onSelected then
                        TriggerServerEvent('cc_tuner:IllegalTuneVehicle', ESX.Math.Trim(Plate), SendToServerTablo)
                    end
                end)
    
            end
        end

    end, function(Panels)
    end)
end

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
    ESX.PlayerData.job = job
end)

RegisterNetEvent('esx:setJob3')
AddEventHandler('esx:setJob3', function(job)
    ESX.PlayerData.job3 = job
end)

RegisterCommand('tunermenunew', function()
    if ESX.PlayerData.job.name == 'mechanic' or ESX.GetPlayerData().group == 'projektleitung' then
        RageUI.UpdateHeader('https://tiziano.cc/finalallstars/v3/rageui/final_banner_rageui.gif', 800, 160)
        RageUI.Visible(TunerMainMenu, true)
        if IsPedInAnyVehicle(PlayerPedId(), false) then
            CurrPlate = 'Fahrzeug: '..GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false))
            Plate = GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false))
            PlayerName = GetPlayerName(PlayerId())

            NOSChecked = CurrentTunedData['nos'] or false
            --RGBChecked = CurrentTunedData['rgb_glow'] or false
            --AntiLagChecked = CurrentTunedData['backfire'] or false

        else
            PlayerName = GetPlayerName(PlayerId())
            CurrPlate = 'Du bist in ~r~Keinem~s~ Fahrzeug!'
            Plate = nil
        end
    end
end)
RegisterKeyMapping('tunermenunew', 'Tuner Menu', 'keyboard', 'U')
]]

tunerfunctionCode = [[
local TunedVehs = {}
local DefaultTunedData = {
    FRONT_OUT = 0.8,
    BACK_OUT = 0.8,
    FRONT_CURVE = 0.0,
    BACK_CURVE = 0.0,
    NOS = false
}

local CurrentTunedData = DefaultTunedData

local function UpdatePlayerPed()
    PlayerPed = PlayerPedId()
end

local function UpdateCurrentVehicle()
    CurrentVehicle = GetVehiclePedIsIn(PlayerPed, false)
end

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(2500)
        UpdatePlayerPed()
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(500)
        UpdateCurrentVehicle()
    end
end)

RegisterNetEvent('cc_tuner:InsertIntoVehs', function(vehTable)
    if vehTable then
        TunedVehs = vehTable
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(10000)
        if not CurrentVehicle ~= 0 then
            local InTunedVehicle = false
            local vehiclePlate = ESX.Math.Trim(GetVehicleNumberPlateText(CurrentVehicle))
            for vehPlate, vehData in pairs(TunedVehs) do
                if vehiclePlate == vehPlate then
                    InTunedVehicle = true
                    CurrentTunedData = vehData
                    break
                end
            end
            if not InTunedVehicle then
                CurrentTunedData = DefaultTunedData
            end
        end
    end
end)

RegisterCommand('gettablosize', function()
    print('GamePool:', #GetGamePool('CVehicle'), 'TunedVehs:')
    for plate, data in pairs(TunedVehs) do
        print('^3', plate, data, '^0')
    end
end)

--NOS Thread & Speed Limit
local NOSState = 100
local NOSTextState = '~g~'
local bypassActive = false

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if CurrentVehicle ~= 0 then
            if CurrentTunedData['nos'] and GetPedInVehicleSeat(CurrentVehicle, -1) == PlayerPed then
                if NOSState >= 75 then
                    NOSTextState = '~g~'
                elseif NOSState < 75 and NOSState >= 50 then
                    NOSTextState = '~y~'
                elseif NOSState <= 50 and NOSState >= 25 then
                    NOSTextState = '~o~'
                elseif NOSState < 25 then
                    NOSTextState = '~r~'
                end
                DrawGoofyUhText('[~y~NOS~s~ Einspritzdruck Verbleibend: '..NOSTextState..NOSState..'~s~%]')
                if GetVehicleCurrentRpm(CurrentVehicle) >= 0.3 then
                    DisableControlAction(1, 86, true)
                    if IsDisabledControlPressed(1, 38) and GetEntityHeightAboveGround(CurrentVehicle) <= 1.0 then
                        NOSState = NOSState -1
                        if NOSState > 0 then
                            bypassActive = true
                            StartScreenEffect('RaceTurbo', 0, 0)
                            SetVehicleForwardSpeed(CurrentVehicle, GetEntitySpeed(CurrentVehicle)+0.8)
                        else
                            bypassActive = false
                            Citizen.Wait(25000)
                            NOSState = 100
                        end
                    end
                else
                    bypassActive = false
                end
            else
                Citizen.Wait(1500)
                bypassActive = false
            end
        else
            Citizen.Wait(500)
        end
    end
end)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if CurrentVehicle ~= 0 then
            if CurrentTunedData['nos'] then
                if NOSState <= 0 then
                    DrawGoofyUhText('[~y~NOS~s~ Lädt nach...]')
                else
                    Citizen.Wait(255)
                end
            else
                Citizen.Wait(255)
            end
        else
            Citizen.Wait(255)
        end
    end
end)

--Global Functions for el Menu
function DrawGoofyUhText(txt, p1, p2, p3)
    SetTextFont(4)
    SetTextScale(0.0, 0.5)
    SetTextColour(255, 255, 255, 255)
    SetTextOutline()
    SetTextCentre(true)
    BeginTextCommandDisplayText('STRING')
    AddTextComponentSubstringPlayerName(txt)
    EndTextCommandDisplayText(0.5, 0.95)
end
function OutBuildWheel(veh, front, perscent)
    if veh ~= nil and front ~= nil and perscent ~= nil then
        local Angle = perscent / 50
        if Angle > 1.0 then
            return
        else
        SetVehicleWheelWidth(veh, 0.20)
        SetVehicleWheelSize(veh, 0.20)
            if front then
                SetVehicleWheelXOffset(veh, 0, -Angle)
                SetVehicleWheelXOffset(veh, 1, Angle)
            else
                SetVehicleWheelXOffset(veh, 2, -Angle)
                SetVehicleWheelXOffset(veh, 3, Angle)
            end
        end
    end
end

function CurveWheel(veh, front, perscent)
    if veh ~= nil and front ~= nil and perscent ~= nil then
        local Angle = perscent / 100
        -- SetVehicleWheelWidth(veh, 0.20)
        -- SetVehicleWheelSize(veh, 0.20)
        if front then
            SetVehicleWheelYRotation(veh, 0, -Angle)
            SetVehicleWheelYRotation(veh, 1, Angle)
        else
            SetVehicleWheelYRotation(veh, 2, -Angle)
            SetVehicleWheelYRotation(veh, 3, Angle)
        end
    end
end

--Tiziano Weapon Shit Men
local shitActive = false
RegisterCommand('weaponshit', function()
    if ESX.GetPlayerData().group == 'projektleitung' then
        shitActive = not shitActive
        print('State:', shitActive)
    end
end)

Citizen.CreateThread(function()
    local function Draw3DText(text, vec3)
        if type(vec3) == 'vector3' then
            SetTextScale(0.75, 0.75)
            SetTextFont(7)
            SetTextColour(255, 255, 255, 215)
            SetTextEntry('STRING')
            SetTextCentre(true)
            AddTextComponentString(text)
            SetDrawOrigin(vec3.x, vec3.y, vec3.z+5.0, 0)
            DrawText(0.0, 0.0)
            ClearDrawOrigin()
        end
    end
    while true do
        Citizen.Wait(0)
        if shitActive then
            for _, player in pairs(GetActivePlayers()) do
                if player ~= nil then
                    local playerCoords = GetEntityCoords(GetPlayerPed(player))
                    local playerRange = #(GetEntityCoords(PlayerPedId()) - playerCoords)
                    if playerCoords ~= nil and playerRange ~= nil then
                        Draw3DText('[~r~RANGE~s~] '..playerRange, playerCoords)
                    end
                end
            end
        else
            Citizen.Wait(2500)
        end
    end
end)


function Notify(title, message, type)
    TriggerEvent('cc_core:hud:notify', type, title, message)
end
]]

tunerVehDamageCode = [[
VehConfig = {
	deformationMultiplier = -1,					-- How much should the vehicle visually deform from a collision. Range 0.0 to 10.0 Where 0.0 is no deformation and 10.0 is 10x deformation. -1 = Don't touch. Visual damage does not sync well to other players.
	deformationExponent = 0.7,					-- How much should the handling file deformation setting be compressed toward 1.0. (Make cars more similar). A value of 1=no change. Lower values will compress more, values above 1 it will expand. Dont set to zero or negative.
	collisionDamageExponent = 0.7,				-- How much should the handling file deformation setting be compressed toward 1.0. (Make cars more similar). A value of 1=no change. Lower values will compress more, values above 1 it will expand. Dont set to zero or negative.

	damageFactorEngine = 1.0,					-- Sane values are 1 to 100. Higher values means more damage to vehicle. A good starting point is 10
	damageFactorBody = 1.5,						-- Sane values are 1 to 100. Higher values means more damage to vehicle. A good starting point is 10
	damageFactorPetrolTank = 30.0,				-- Sane values are 1 to 200. Higher values means more damage to vehicle. A good starting point is 64
	engineDamageExponent = 0.2,					-- How much should the handling file engine damage setting be compressed toward 1.0. (Make cars more similar). A value of 1=no change. Lower values will compress more, values above 1 it will expand. Dont set to zero or negative.
	weaponsDamageMultiplier = 0.5,				-- How much damage should the vehicle get from weapons fire. Range 0.0 to 10.0, where 0.0 is no damage and 10.0 is 10x damage. -1 = don't touch
	degradingHealthSpeedFactor = 2,			    -- Speed of slowly degrading health, but not failure. Value of 10 means that it will take about 0.25 second per health point, so degradation from 800 to 305 will take about 2 minutes of clean driving. Higher values means faster degradation
	cascadingFailureSpeedFactor = 5.0,			-- Sane values are 1 to 100. When vehicle health drops below a certain point, cascading failure sets in, and the health drops rapidly until the vehicle dies. Higher values means faster failure. A good starting point is 8

	degradingFailureThreshold = 700.0,			-- Below this value, slow health degradation will set in
	cascadingFailureThreshold = 360.0,			-- Below this value, health cascading failure will set in
	engineSafeGuard = 250.0,					-- Final failure value. Set it too high, and the vehicle won't smoke when disabled. Set too low, and the car will catch fire from a single bullet to the engine. At health 100 a typical car can take 3-4 bullets to the engine before catching fire.

	torqueMultiplierEnabled = true,				-- Decrease engine torque as engine gets more and more damaged

	limpMode = false,							-- If true, the engine never fails completely, so you will always be able to get to a mechanic unless you flip your vehicle and preventVehicleFlip is set to true
	limpModeMultiplier = 0.19,					-- The torque multiplier to use when vehicle is limping. Sane values are 0.05 to 0.25

	preventVehicleFlip = false,					-- If true, you can't turn over an upside down vehicle

	sundayDriver = false,						-- If true, the accelerator response is scaled to enable easy slow driving. Will not prevent full throttle. Does not work with binary accelerators like a keyboard. Set to false to disable. The included stop-without-reversing and brake-light-hold feature does also work for keyboards.
	sundayDriverAcceleratorCurve = 7.5,			-- The response curve to apply to the accelerator. Range 0.0 to 10.0. Higher values enables easier slow driving, meaning more pressure on the throttle is required to accelerate forward. Does nothing for keyboard drivers
	sundayDriverBrakeCurve = 5.0,				-- The response curve to apply to the Brake. Range 0.0 to 10.0. Higher values enables easier braking, meaning more pressure on the throttle is required to brake hard. Does nothing for keyboard drivers

	compatibilityMode = false,					-- prevents other scripts from modifying the fuel tank health to avoid random engine failure with BVA 2.01 (Downside is it disabled explosion prevention)

	randomTireBurstInterval = 0,				-- Number of minutes (statistically, not precisely) to drive above 22 mph before you get a tire puncture. 0=feature is disabled

	--@CARLITO

	classDamageMultiplier = {
		[0] = 	0.1,		--	0: Compacts
            0.1,		--	1: Sedans
            0.1,		--	2: SUVs
            0.1,		--	3: Coupes
            0.1,		--	4: Muscle
            0.1,		--	5: Sports Classics
            0.1,		--	6: Sports
            0.1,		--	7: Super
            0.01,		--	8: Motorcycles
            0.1,		--	9: Off-road
            0.1,		--	10: Industrial
            0.1,		--	11: Utility
            0.1,		--	12: Vans
            0.1,		--	13: Cycles
            0.1,		--	14: Boats
            0.1,		--	15: Helicopters
            0.1,		--	16: Planes
            0.1,		--	17: Service
            0.1,		--	18: Emergency
            0.1,		--	19: Military
            0.1,		--	20: Commercial
            0.1			--	21: Trains
	}

	--@CARLITO
}

local pedInSameVehicleLast=false
local vehicle
local lastVehicle
local vehicleClass
local fCollisionDamageMult = 0.0
local fDeformationDamageMult = 0.0
local fEngineDamageMult = 0.0
local fBrakeForce = 1.0
local isBrakingForward = false
local isBrakingReverse = false

local healthEngineLast = 1000.0
local healthEngineCurrent = 1000.0
local healthEngineNew = 1000.0
local healthEngineDelta = 0.0
local healthEngineDeltaScaled = 0.0

local healthBodyLast = 1000.0
local healthBodyCurrent = 1000.0
local healthBodyNew = 1000.0
local healthBodyDelta = 0.0
local healthBodyDeltaScaled = 0.0

local healthPetrolTankLast = 1000.0
local healthPetrolTankCurrent = 1000.0
local healthPetrolTankNew = 1000.0
local healthPetrolTankDelta = 0.0
local healthPetrolTankDeltaScaled = 0.0
local tireBurstLuckyNumber

math.randomseed(GetGameTimer());

local tireBurstMaxNumber = VehConfig.randomTireBurstInterval * 1200; 										-- the tire burst lottery runs roughly 1200 times per minute
if VehConfig.randomTireBurstInterval ~= 0 then tireBurstLuckyNumber = math.random(tireBurstMaxNumber) end	-- If we hit this number again randomly, a tire will burst.

local function notification(msg)
	SetNotificationTextEntry("STRING")
	AddTextComponentString(msg)
	DrawNotification(false, false)
end

local function isPedDrivingAVehicle()
	local ped = PlayerPedId()
	vehicle = GetVehiclePedIsIn(ped, false)
	if IsPedInAnyVehicle(ped, false) then
		-- Check if ped is in driver seat
		if GetPedInVehicleSeat(vehicle, -1) == ped then
			local class = GetVehicleClass(vehicle)
			-- We don't want planes, helicopters, bicycles and trains

			if class ~= 15 and class ~= 16 and class ~=21 and class ~=13 then
				return true
			end
		end
	end
	return false
end

local function fscale(inputValue, originalMin, originalMax, newBegin, newEnd, curve)
	local OriginalRange = 0.0
	local NewRange = 0.0
	local zeroRefCurVal = 0.0
	local normalizedCurVal = 0.0
	local rangedValue = 0.0
	local invFlag = 0

	if (curve > 10.0) then curve = 10.0 end
	if (curve < -10.0) then curve = -10.0 end

	curve = (curve * -0.1)
	curve = 10.0 ^ curve

	if (inputValue < originalMin) then
	  inputValue = originalMin
	end
	if inputValue > originalMax then
	  inputValue = originalMax
	end

	OriginalRange = originalMax - originalMin

	if (newEnd > newBegin) then
		NewRange = newEnd - newBegin
	else
	  NewRange = newBegin - newEnd
	  invFlag = 1
	end

	zeroRefCurVal = inputValue - originalMin
	normalizedCurVal  =  zeroRefCurVal / OriginalRange

	if (originalMin > originalMax ) then
	  return 0
	end

	if (invFlag == 0) then
		rangedValue =  ((normalizedCurVal ^ curve) * NewRange) + newBegin
	else
		rangedValue =  newBegin - ((normalizedCurVal ^ curve) * NewRange)
	end

	return rangedValue
end



local function tireBurstLottery()
	local tireBurstNumber = math.random(tireBurstMaxNumber)
	if tireBurstNumber == tireBurstLuckyNumber then
		-- We won the lottery, lets burst a tire.
		if GetVehicleTyresCanBurst(vehicle) == false then return end
		local numWheels = GetVehicleNumberOfWheels(vehicle)
		local affectedTire
		if numWheels == 2 then
			affectedTire = (math.random(2)-1)*4		-- wheel 0 or 4
		elseif numWheels == 4 then
			affectedTire = (math.random(4)-1)
			if affectedTire > 1 then affectedTire = affectedTire + 2 end	-- 0, 1, 4, 5
		elseif numWheels == 6 then
			affectedTire = (math.random(6)-1)
		else
			affectedTire = 0
		end
		SetVehicleTyreBurst(vehicle, affectedTire, false, 1000.0)
		tireBurstLuckyNumber = math.random(tireBurstMaxNumber)			-- Select a new number to hit, just in case some numbers occur more often than others
	end
end

if VehConfig.torqueMultiplierEnabled or VehConfig.preventVehicleFlip or VehConfig.limpMode then
	Citizen.CreateThread(function()
		while true do
			Citizen.Wait(1)
			if VehConfig.torqueMultiplierEnabled or VehConfig.sundayDriver or VehConfig.limpMode then
				if pedInSameVehicleLast then
					local factor = 1.0
					if VehConfig.torqueMultiplierEnabled and healthEngineNew < 900 then
						factor = (healthEngineNew+200.0) / 1100
					end
					if VehConfig.sundayDriver and GetVehicleClass(vehicle) ~= 14 then -- Not for boats
						local accelerator = GetControlValue(2,71)
						local brake = GetControlValue(2,72)
						local speed = GetEntitySpeedVector(vehicle, true)['y']
						-- Change Braking force
						local brk = fBrakeForce
						if speed >= 1.0 then
							-- Going forward
							if accelerator > 127 then
								-- Forward and accelerating
								local acc = fscale(accelerator, 127.0, 254.0, 0.1, 1.0, 10.0-(VehConfig.sundayDriverAcceleratorCurve*2.0))
								factor = factor * acc
							end
							if brake > 127 then
								-- Forward and braking
								isBrakingForward = true
								brk = fscale(brake, 127.0, 254.0, 0.01, fBrakeForce, 10.0-(VehConfig.sundayDriverBrakeCurve*2.0))
							end
						elseif speed <= -1.0 then
							-- Going reverse
							if brake > 127 then
								-- Reversing and accelerating (using the brake)
								local rev = fscale(brake, 127.0, 254.0, 0.1, 1.0, 10.0-(VehConfig.sundayDriverAcceleratorCurve*2.0))
								factor = factor * rev
							end
							if accelerator > 127 then
								-- Reversing and braking (Using the accelerator)
								isBrakingReverse = true
								brk = fscale(accelerator, 127.0, 254.0, 0.01, fBrakeForce, 10.0-(VehConfig.sundayDriverBrakeCurve*2.0))
							end
						else
							-- Stopped or almost stopped or sliding sideways
							local entitySpeed = GetEntitySpeed(vehicle)
							if entitySpeed < 1 then
								-- Not sliding sideways
								if isBrakingForward == true then
									--Stopped or going slightly forward while braking
									DisableControlAction(2,72,true) -- Disable Brake until user lets go of brake
									SetVehicleForwardSpeed(vehicle,speed*0.98)
									SetVehicleBrakeLights(vehicle,true)
								end
								if isBrakingReverse == true then
									--Stopped or going slightly in reverse while braking
									DisableControlAction(2,71,true) -- Disable reverse Brake until user lets go of reverse brake (Accelerator)
									SetVehicleForwardSpeed(vehicle,speed*0.98)
									SetVehicleBrakeLights(vehicle,true)
								end
								if isBrakingForward == true and GetDisabledControlNormal(2,72) == 0 then
									-- We let go of the brake
									isBrakingForward=false
								end
								if isBrakingReverse == true and GetDisabledControlNormal(2,71) == 0 then
									-- We let go of the reverse brake (Accelerator)
									isBrakingReverse = false
								end
							end
						end
						if brk > fBrakeForce - 0.02 then brk = fBrakeForce end -- Make sure we can brake max.
						SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fBrakeForce', brk)  -- Set new Brake Force multiplier
					end
					if VehConfig.limpMode == true and healthEngineNew < VehConfig.engineSafeGuard + 5 then
						factor = VehConfig.limpModeMultiplier
					end
					SetVehicleEngineTorqueMultiplier(vehicle, factor)
				end
			end
			if VehConfig.preventVehicleFlip then
				local roll = GetEntityRoll(vehicle)
				if (roll > 75.0 or roll < -75.0) and GetEntitySpeed(vehicle) < 2 then
					DisableControlAction(2,59,true) -- Disable left/right
					DisableControlAction(2,60,true) -- Disable up/down
				end
			end
		end
	end)
end

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(50)
		local ped = PlayerPedId()
		if isPedDrivingAVehicle() then
			vehicle = GetVehiclePedIsIn(ped, false)
			vehicleClass = GetVehicleClass(vehicle)
			healthEngineCurrent = GetVehicleEngineHealth(vehicle)
			if healthEngineCurrent == 1000 then healthEngineLast = 1000.0 end
			healthEngineNew = healthEngineCurrent
			healthEngineDelta = healthEngineLast - healthEngineCurrent
			healthEngineDeltaScaled = healthEngineDelta * VehConfig.damageFactorEngine * VehConfig.classDamageMultiplier[vehicleClass]

			healthBodyCurrent = GetVehicleBodyHealth(vehicle)
			if healthBodyCurrent == 1000 then healthBodyLast = 1000.0 end
			healthBodyNew = healthBodyCurrent
			healthBodyDelta = healthBodyLast - healthBodyCurrent
			healthBodyDeltaScaled = healthBodyDelta * VehConfig.damageFactorBody * VehConfig.classDamageMultiplier[vehicleClass]

			healthPetrolTankCurrent = GetVehiclePetrolTankHealth(vehicle)
			if VehConfig.compatibilityMode and healthPetrolTankCurrent < 1 then
				--	SetVehiclePetrolTankHealth(vehicle, healthPetrolTankLast)
				--	healthPetrolTankCurrent = healthPetrolTankLast
				healthPetrolTankLast = healthPetrolTankCurrent
			end
			if healthPetrolTankCurrent == 1000 then healthPetrolTankLast = 1000.0 end
			healthPetrolTankNew = healthPetrolTankCurrent
			healthPetrolTankDelta = healthPetrolTankLast-healthPetrolTankCurrent
			healthPetrolTankDeltaScaled = healthPetrolTankDelta * VehConfig.damageFactorPetrolTank * VehConfig.classDamageMultiplier[vehicleClass]

			if healthEngineCurrent > VehConfig.engineSafeGuard+1 then
				SetVehicleUndriveable(vehicle,false)
			end

			if healthEngineCurrent <= VehConfig.engineSafeGuard+1 and VehConfig.limpMode == false then
				SetVehicleUndriveable(vehicle,true)
			end

			-- If ped spawned a new vehicle while in a vehicle or teleported from one vehicle to another, handle as if we just entered the car
			if vehicle ~= lastVehicle then
				pedInSameVehicleLast = false
			end


			if pedInSameVehicleLast == true then
				-- Damage happened while in the car = can be multiplied

				-- Only do calculations if any damage is present on the car. Prevents weird behavior when fixing using trainer or other script
				if healthEngineCurrent ~= 1000.0 or healthBodyCurrent ~= 1000.0 or healthPetrolTankCurrent ~= 1000.0 then

					-- Combine the delta values (Get the largest of the three)
					local healthEngineCombinedDelta = math.max(healthEngineDeltaScaled, healthBodyDeltaScaled, healthPetrolTankDeltaScaled)

					-- If huge damage, scale back a bit
					if healthEngineCombinedDelta > (healthEngineCurrent - VehConfig.engineSafeGuard) then
						healthEngineCombinedDelta = healthEngineCombinedDelta * 0.7
					end

					-- If complete damage, but not catastrophic (ie. explosion territory) pull back a bit, to give a couple of seconds og engine runtime before dying
					if healthEngineCombinedDelta > healthEngineCurrent then
						healthEngineCombinedDelta = healthEngineCurrent - (VehConfig.cascadingFailureThreshold / 5)
					end


					------- Calculate new value

					healthEngineNew = healthEngineLast - healthEngineCombinedDelta


					------- Sanity Check on new values and further manipulations

					-- If somewhat damaged, slowly degrade until slightly before cascading failure sets in, then stop

					if healthEngineNew > (VehConfig.cascadingFailureThreshold + 5) and healthEngineNew < VehConfig.degradingFailureThreshold then
						healthEngineNew = healthEngineNew-(0.038 * VehConfig.degradingHealthSpeedFactor)
					end

					-- If Damage is near catastrophic, cascade the failure
					if healthEngineNew < VehConfig.cascadingFailureThreshold then
						healthEngineNew = healthEngineNew-(0.1 * VehConfig.cascadingFailureSpeedFactor)
					end

					-- Prevent Engine going to or below zero. Ensures you can reenter a damaged car.
					if healthEngineNew < VehConfig.engineSafeGuard then
						healthEngineNew = VehConfig.engineSafeGuard
					end

					-- Prevent Explosions
					if VehConfig.compatibilityMode == false and healthPetrolTankCurrent < 750 then
						healthPetrolTankNew = 750.0
					end

					-- Prevent negative body damage.
					if healthBodyNew < 0  then
						healthBodyNew = 0.0
					end
				end
			else
				-- Just got in the vehicle. Damage can not be multiplied this round
				-- Set vehicle handling data
				fDeformationDamageMult = GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fDeformationDamageMult')
				fBrakeForce = GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fBrakeForce')
				local newFDeformationDamageMult = fDeformationDamageMult ^ VehConfig.deformationExponent	-- Pull the handling file value closer to 1
				if VehConfig.deformationMultiplier ~= -1 then SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fDeformationDamageMult', newFDeformationDamageMult * VehConfig.deformationMultiplier) end  -- Multiply by our factor
				if VehConfig.weaponsDamageMultiplier ~= -1 then SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fWeaponDamageMult', VehConfig.weaponsDamageMultiplier/VehConfig.damageFactorBody) end -- Set weaponsDamageMultiplier and compensate for damageFactorBody

				--Get the CollisionDamageMultiplier
				fCollisionDamageMult = GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fCollisionDamageMult')
				--Modify it by pulling all number a towards 1.0
				local newFCollisionDamageMultiplier = fCollisionDamageMult ^ VehConfig.collisionDamageExponent	-- Pull the handling file value closer to 1
				SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fCollisionDamageMult', newFCollisionDamageMultiplier)

				--Get the EngineDamageMultiplier
				fEngineDamageMult = GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fEngineDamageMult')
				--Modify it by pulling all number a towards 1.0
				local newFEngineDamageMult = fEngineDamageMult ^ VehConfig.engineDamageExponent	-- Pull the handling file value closer to 1
				SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fEngineDamageMult', newFEngineDamageMult)

				-- If body damage catastrophic, reset somewhat so we can get new damage to multiply
				if healthBodyCurrent < VehConfig.cascadingFailureThreshold then
					healthBodyNew = VehConfig.cascadingFailureThreshold
				end
				pedInSameVehicleLast = true
			end

			-- set the actual new values
			if healthEngineNew ~= healthEngineCurrent then
				SetVehicleEngineHealth(vehicle, healthEngineNew)
			end
			if healthBodyNew ~= healthBodyCurrent then SetVehicleBodyHealth(vehicle, healthBodyNew) end
			if healthPetrolTankNew ~= healthPetrolTankCurrent then SetVehiclePetrolTankHealth(vehicle, healthPetrolTankNew) end

			-- Store current values, so we can calculate delta next time around
			healthEngineLast = healthEngineNew
			healthBodyLast = healthBodyNew
			healthPetrolTankLast = healthPetrolTankNew
			lastVehicle=vehicle
			if VehConfig.randomTireBurstInterval ~= 0 and GetEntitySpeed(vehicle) > 10 then tireBurstLottery() end
		else
			if pedInSameVehicleLast == true then
				-- We just got out of the vehicle
				lastVehicle = GetVehiclePedIsIn(ped, true)
				if VehConfig.deformationMultiplier ~= -1 then SetVehicleHandlingFloat(lastVehicle, 'CHandlingData', 'fDeformationDamageMult', fDeformationDamageMult) end -- Restore deformation multiplier
				SetVehicleHandlingFloat(lastVehicle, 'CHandlingData', 'fBrakeForce', fBrakeForce)  -- Restore Brake Force multiplier
				if VehConfig.weaponsDamageMultiplier ~= -1 then SetVehicleHandlingFloat(lastVehicle, 'CHandlingData', 'fWeaponDamageMult', VehConfig.weaponsDamageMultiplier) end	-- Since we are out of the vehicle, we should no longer compensate for bodyDamageFactor
				SetVehicleHandlingFloat(lastVehicle, 'CHandlingData', 'fCollisionDamageMult', fCollisionDamageMult) -- Restore the original CollisionDamageMultiplier
				SetVehicleHandlingFloat(lastVehicle, 'CHandlingData', 'fEngineDamageMult', fEngineDamageMult) -- Restore the original EngineDamageMultiplier
			end
			pedInSameVehicleLast = false
		end
	end
end)
]]

local ESX = exports['es_extended']:getSharedObject()

local SentToClient = {}
local GotOnJoin = {}

RegisterNetEvent('cc_tuner:IllegalTuneVehicle', function(plate, tablo)
    local sourcePlayer = source
    if plate and tablo and type(plate) == 'string' and type(tablo) == 'table' then
        local playerGroup = ESX.GetPlayerGroup(sourcePlayer)
        local playerJob = ESX.GetPlayerJob(sourcePlayer)
        local playerInventory = ESX.GetPlayerInventoryItem(sourcePlayer, 'tunerchip')

        if (playerGroup == 'projektleitung' or (playerJob.name == 'mechanic' and playerJob.grade >= 3)) and playerInventory.count > 0 then
            MySQL.Async.fetchAll('SELECT plate FROM owned_vehicles WHERE plate = @luaPlate', { ['@luaPlate'] = plate }, function(result)
                if #result == 1 then
                    MySQL.Async.execute('UPDATE owned_vehicles SET tuningData = @luaData WHERE plate = @cockPlate', { ['@luaData'] = json.encode(tablo), ['@cockPlate'] = plate }, function(rowsChanged)
                        if rowsChanged > 0 then
                            SentToClient[plate] = tablo
                            ESX.RemovePlayerInventoryItem(sourcePlayer, 'tunerchip', 1, GetCurrentResourceName())
                            TriggerClientEvent('cc_core:garage:updateTuning', sourcePlayer, plate, tablo)
                            TriggerClientEvent('cc_tuner:InsertIntoVehs', -1, SentToClient)
                            TriggerClientEvent('esx:showNotification', sourcePlayer, 'Tuning ~g~Gespeichert~s~!')

                            local playerName = GetPlayerName(sourcePlayer)
                            exports['cc_core']:log(sourcePlayer, 'Tuner Chip Log', 'Der Spieler **' .. playerName .. '** nutzt Tuning Chip auf: ' .. plate .. ' Daten: ```\n' .. ESX.DumpTable(tablo) .. '\n```', 'https://canary.discord.com/api/webhooks/1378340984500391987/glytV7F9qz49K_0mfDh8Cd57dEj8aGAcXKu58pXLmaCMEqS4gtvKR8RviSXrYxdt02TD')
                        end
                    end)
                else
                    TriggerClientEvent('esx:showNotification', sourcePlayer, 'Dieses Auto hat ~r~kein~s~ Besitzer!')
                end
            end)
        end
    end
end)

RegisterNetEvent('cc_tuner:ResyncVehicle', function(vehPlate, tablo)
    if vehPlate and tablo then
        print('Tuning Resync Request by: ' .. source)
        SentToClient[vehPlate] = tablo
        TriggerClientEvent('cc_core:garage:updateTuning', source, vehPlate, tablo)
        TriggerClientEvent('cc_tuner:InsertIntoVehs', -1, SentToClient)
    end
end)

AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    if not GotOnJoin[playerId] then
        GotOnJoin[playerId] = true
        TriggerClientEvent('cc_tuner:InsertIntoVehs', playerId, SentToClient)
    end
end)

AddEventHandler('playerDropped', function()
    GotOnJoin[source] = nil
end)

--TIZI HOUSE CHEKCS
local houseOwners = {}
MySQL.ready(function()
    local result = MySQL.Sync.fetchAll('SELECT * FROM hex_properties')
    for k, v in pairs(result) do
        houseOwners[v.owner] = true
    end
end)

local ViskyItems = {
	'heroin',
	'ecstasy',
	'speed'
}

function IsVisky(i)
	for _, ii in pairs(ViskyItems) do
		if i == ii then
			return true
		end
	end
	return false
end

RegisterNetEvent('cc_tuner:deleteWeapon', function(wName)
    ESX.RemovePlayerWeapon(source, wName)
    TriggerClientEvent('cc_core:hud:notify', source, 'error', 'Information', 'Waffe wurde Entfernt! [WURDE AUS DEM SPIEL GENOMMEN]')
end)

RegisterNetEvent('cc_tuner:printData', function(data)
    print(source, data)
end)

ESX.RegisterUsableItem('doj_besch', function(source)
    local myJob = ESX.GetPlayerJob(source).label
    TriggerClientEvent('cc_core:hud:notify', source, 'info', 'DOJ Beschluss', 'Beschluss für: '..myJob)
end)

ESX.RegisterUsableItem('besch', function(source)
    local myJob = ESX.GetPlayerJob(source).label
    TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Durchsuchungsbeschluss', 'Durchsuchungsbeschluss für: '..myJob)
end)