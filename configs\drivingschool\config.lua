Config_Drivingschool = {}

Config_Drivingschool.Points = {
	{
		type = 'car',
		coords = vector3(239.471, -1380.960, 33.741),
	
		blip = {
			enabled = true, 
			sprite = 408,
			scale = 0.5,
			text = 'Auto Fahrschule'
		},

		licenses = {
			{
				license = 'dmv',
				price = 500
			},

			{
				license = 'drive',
				price = 1000
			}
		},
	},

	{
		type = 'bike',
		coords = vector3(263.7857, -1370.0332, 32.0353),
	
		blip = {
			enabled = true,
			sprite = 408,
			scale = 0.5,
			text = 'Motorrad Fahrschule'
		},

		licenses = {
			{
				license = 'dmv_bike',
				price = 500
			},

			{
				license = 'drive_bike',
				price = 1500
			}
		},
	},

	{
		type = 'boat',
		coords = vector3(-950.2289, -1480.3723, 1.5954),
	
		blip = {
			enabled = true,
			sprite = 408,
			scale = 0.5,
			text = 'Boot Fahrschule'
		},

		licenses = {
			{
				license = 'dmv_boat',
				price = 500
			},

			{
				license = 'drive_boat',
				price = 2000
			}
		},
	},

	{
		type = 'airplane',
		coords = vector3(-1156.1265, -2717.4692, 19.8875),
	
		blip = {
			enabled = true,
			sprite = 408,
			scale = 0.5,
			text = 'Flugzeug Fahrschule'
		},

		licenses = {
			{
				license = 'dmv_airplane',
				price = 500
			},

			{
				license = 'drive_airplane',
				price = 5000
			}
		},
	},

	{
		type = 'helicopter',
		coords = vector3(-1004.2811, -2415.2097, 13.9445),
	
		blip = {
			enabled = true,
			sprite = 408,
			scale = 0.5,
			text = 'Helikopter Fahrschule'
		},

		licenses = {
			{
				license = 'dmv_helicopter',
				price = 500
			},

			{
				license = 'drive_helicopter',
				price = 3000
			}
		},
	}
}

Config_Drivingschool.SpeedLimits = {
	['car'] = {
		residence = 80,
		town = 100,
		freeway = 180
	},

	['bike'] = {
		residence = 100,
		town = 120,
		freeway = 300
	},

	['boat'] = {
		residence = 999
	},
	
	['airplane'] = {
		residence = 999
	},

	['helicopter'] = {
		residence = 999
	}
}

Config_Drivingschool.Route = {
	['car'] = {
		vehicle = 'asea',
		start = vector4(249.409, -1407.230, 30.4094, 317.0),
		marker = 1,
		dir = vector3(0.0, 0.0, 0.0),
		rotation = vector3(0.0, 0.0, 0.0),
		scale = vector3(3.0, 3.0, 0.5),

		route = {
			{
				coords = vector3(255.139, -1400.731, 29.537),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt! Maximale erlaubte Geschwindigkeit: ~y~' .. Config_Drivingschool.SpeedLimits['car']['residence'] .. '~s~ km/h', 5000)
				end
			},
		
			{
				coords = vector3(271.874, -1370.574, 30.932),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},
		
			{
				coords = vector3(234.907, -1345.385, 29.542),
				action = function(ped, vehicle, setCurrentZoneType)
					Citizen.CreateThread(function()
						DrawMissionText('~r~Stopp~s~ lasse die Fußgänger ~y~vorbeigehen', 5000)
						PlaySound(-1, 'RACE_PLACED', 'HUD_AWARDS', false, 0, true)
						FreezeEntityPosition(vehicle, true)
						Citizen.Wait(4000)
		
						FreezeEntityPosition(vehicle, false)
						DrawMissionText('~g~Gut!~s~, Weiter gehts.', 5000)
					end)
				end
			},
		
			{
				coords = vector3(217.821, -1410.520, 28.292),
				action = function(ped, vehicle, setCurrentZoneType)
					setCurrentZoneType('town')
		
					Citizen.CreateThread(function()
						DrawMissionText('~r~Stopp!~s~ Und schaue nach ~y~links~s~. Maximale erlaubte Geschwindigkeit: ~y~' ..  Config_Drivingschool.SpeedLimits['car']['town'] .. '~s~ km/h', 5000)
						PlaySound(-1, 'RACE_PLACED', 'HUD_AWARDS', false, 0, true)
						FreezeEntityPosition(vehicle, true)
						Citizen.Wait(6000)
		
						FreezeEntityPosition(vehicle, false)
						DrawMissionText('~g~Gut!~s~, Rechts abbiegen und der Linie folgen.', 5000)
					end)
				end
			},
		
			{
				coords = vector3(178.550, -1401.755, 28.205),
				action = function(ped, vehicle, setCurrentZoneType)
					DrawMissionText('Achte auf den Verkehr und schalte dein Licht ein!', 5000)
				end
			},
		
			{
				coords = vector3(113.160, -1365.276, 28.205),
				action = function(ped, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},
		
			{
				coords = vector3(-73.542, -1364.335, 28.205),
				action = function(ped, vehicle, setCurrentZoneType)
					DrawMissionText('~r~Stoppe~s~ für vorbeifahrende Fahrzeuge!', 5000)
					PlaySound(-1, 'RACE_PLACED', 'HUD_AWARDS', false, 0, true)
					FreezeEntityPosition(vehicle, true)
					Citizen.Wait(6000)
					FreezeEntityPosition(vehicle, false)
				end
			},
		
			{
				coords = vector3(-355.143, -1420.282, 28.205),
				action = function(ped, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},
		
			{
				coords = vector3(-439.148, -1417.100, 28.205),
				action = function(ped, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},
		
			{
				coords = vector3(-453.790, -1444.726, 28.205),
				action = function(ped, vehicle, setCurrentZoneType)
					setCurrentZoneType('freeway')
					DrawMissionText('Es ist Zeit, auf der Autobahn zu fahren! Maximale erlaubte Geschwindigkeit: ~y~' .. Config_Drivingschool.SpeedLimits['car']['freeway'] .. '~s~ km/h')
					PlaySound(-1, 'RACE_PLACED', 'HUD_AWARDS', false, 0, true)
				end
			},
		
			{
				coords = vector3(-463.237, -1592.178, 38.019),
				action = function(ped, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},
		
			{
				coords = vector3(-900.647, -1986.28, 26.909),
				action = function(ped, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},
		
			{
				coords = vector3(1225.759, -1948.792, 39.718),
				action = function(ped, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},
		
			{
				coords = vector3(1225.759, -1948.792, 39.718),
				action = function(ped, vehicle, setCurrentZoneType)
					setCurrentZoneType('town')
					DrawMissionText('Sie befinden sich innerorts, bitte achte auf die Geschwindigkeit! Maximale erlaubte Geschwindigkeit: ~y~' .. Config_Drivingschool.SpeedLimits['car']['town'] .. '~s~ km/h', 5000)
				end
			},
		
			{
				coords = vector3(1163.603, -1841.771, 36.679),
				action = function(ped, vehicle, setCurrentZoneType)
					DrawMissionText('Ich bin beeindruckt, aber vergessen Sie nicht, während der Fahrt ~r~alarmiert~s~ zu bleiben!', 5000)
					PlaySound(-1, 'RACE_PLACED', 'HUD_AWARDS', false, 0, true)
				end
			},
		
			{
				coords = vector3(235.283, -1398.329, 29.521),
				action = function(ped, vehicle, setCurrentZoneType)
					DeleteEntity(vehicle)
				end
			}
		}
	},

	['bike'] = {
		vehicle = 'bati',
		start = vector4(275.3546, -1374.4856, 31.9351, 231.5013),
		marker = 1,
		dir = vector3(0.0, 0.0, 0.0),
		rotation = vector3(0.0, 0.0, 0.0),
		scale = vector3(3.0, 3.0, 0.5),

		route = {
			{
				coords = vector3(275.3546, -1374.4856, 31.9351),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt! Maximale erlaubte Geschwindigkeit: ~y~' .. Config_Drivingschool.SpeedLimits['bike']['residence'] .. '~s~ km/h', 5000)
				end
			},

			{
				coords = vector3(251.9067, -1426.9675, 28.6036),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(297.8146, -1502.4403, 28.5653),
				action = function(playerPed, vehicle, setCurrentZoneType)
					setCurrentZoneType('town')
					DrawMissionText('Sie befinden sich innerorts, bitte achte auf die Geschwindigkeit! Maximale erlaubte Geschwindigkeit: ~y~' .. Config_Drivingschool.SpeedLimits['bike']['town'] .. '~s~ km/h', 5000)
				end
			},

			{
				coords = vector3(439.0251, -1621.1947, 28.6789),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(513.1299, -1683.8091, 28.7407),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(941.7808, -1765.0322, 30.5970),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1130.1442, -1738.0608, 34.9640),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1173.3535, -1825.0120, 36.6377),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1393.3755, -1766.5863, 65.2310),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1270.0267, -1446.2462, 34.4350),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1168.4950, -963.8540, 46.5669),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1182.5831, -529.4918, 64.1667),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1214.0774, -389.7130, 67.7355),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(816.9299, -54.6799, 79.9061),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1150.0985, 377.0516, 90.6744),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1443.8568, 733.1688, 77.0713),
				action = function(playerPed, vehicle, setCurrentZoneType)
					setCurrentZoneType('freeway')
					DrawMissionText('Es ist Zeit, auf der Autobahn zu fahren! Maximale erlaubte Geschwindigkeit: ~y~' .. Config_Drivingschool.SpeedLimits['bike']['freeway'] .. '~s~ km/h')
					PlaySound(-1, 'RACE_PLACED', 'HUD_AWARDS', false, 0, true)
				end
			},

			{
				coords = vector3(1619.4141, 1078.3927, 80.6351),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1809.3403, 1958.5947, 77.0895),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(2021.4089, 2570.6956, 53.9766),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(2435.3264, 2909.8801, 39.7175),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(2944.1338, 3850.8931, 51.8260),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(2770.6050, 4556.0771, 45.3415),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(2461.5410, 5672.7207, 44.4332),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(2000.6803, 6227.8271, 46.0020),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1031.1379, 6493.5225, 20.3244),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-385.1061, 5998.1021, 30.8088),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1203.6864, 5267.8716, 50.7177),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1615.8715, 4890.6958, 60.4697),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2216.5415, 4333.9238, 48.9634),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2572.7524, 3365.9075, 12.7286),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2709.0151, 2344.6765, 16.3760),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2987.1270, 1548.3206, 27.9661),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-3013.0681, 631.0264, 20.3656),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2199.8909, -357.2653, 12.4378),
				action = function(playerPed, vehicle, setCurrentZoneType)
					setCurrentZoneType('town')
					DrawMissionText('Sie befinden sich innerorts, bitte achte auf die Geschwindigkeit! Maximale erlaubte Geschwindigkeit: ~y~' .. Config_Drivingschool.SpeedLimits['bike']['town'] .. '~s~ km/h', 5000)
				end
			},

			{
				coords = vector3(-1717.1320, -557.8997, 36.6542),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1090.2310, -785.2775, 18.5297),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-835.7809, -1002.6063, 12.9372),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-558.2543, -961.9681, 22.7580),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-540.2471, -1045.4594, 22.0230),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(37.3639, -1143.0798, 28.6829),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(37.3639, -1143.0798, 28.6829),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},
			
			{
				coords = vector3(221.7921, -1440.3752, 28.6780),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Ich bin beeindruckt, aber vergessen Sie nicht, während der Fahrt ~r~alarmiert~s~ zu bleiben!', 5000)
					PlaySound(-1, 'RACE_PLACED', 'HUD_AWARDS', false, 0, true)
				end
			},

			{
				coords = vector3(282.0331, -1353.6027, 31.2739),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DeleteEntity(vehicle)
				end
			},
		}
	},

	['boat'] = {
		vehicle = 'dinghy',
		start = vector4(-941.9919, -1465.7715, 0.1202, 294.9411),
		marker = 27,
		dir = vector3(0.0, 20.0, 0.0),
		rotation = vector3(100.0, 100.0, 100.0),
		scale = vector3(7.5, 7.5, 0.5),

		route = {
			{
				coords = vector3(-904.9261, -1450.7653, 0.1379),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-827.8341, -1526.1404, 0.1252),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-978.1907, -1677.0345, 0.2749),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1222.3923, -1916.1001, 0.4573),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1324.1459, -1958.0105, 0.3743),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1442.2263, -2000.1964, 0.9734),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1554.7806, -1880.3127, 0.1054),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1651.7157, -1692.3998, 2.4022),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1729.2302, -1580.6995, 0.4677),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1800.6226, -1472.4132, 0.5372),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1918.4277, -1302.5736, 1.5634),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2108.9783, -1250.4200, -0.3418),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2256.5432, -1268.4194, 1.1132),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2351.0532, -1297.0206, 0.1316),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2478.3789, -1332.4075, 0.8785),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2790.3049, -1428.5718, -0.2233),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2962.2144, -1458.1749, 0.4765),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-3256.6252, -1513.6365, 1.2263),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-3428.9426, -1580.4082, 0.1007),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-3600.3564, -1618.9689, -0.2253),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-3809.7256, -1397.4004, -0.0030),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-3647.9753, -1162.9032, 0.0845),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-3260.4814, -1273.5315, 0.1564),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-3019.5676, -1320.7524, 0.7441),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2727.6675, -1376.1691, 1.1818),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2292.4268, -1597.3132, 2.0033),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2114.2874, -1663.5991, 1.1744),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1924.3325, -1675.6013, 0.7220),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1773.9651, -1732.2340, 1.1146),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1481.3590, -1828.2610, 0.7795),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1319.2738, -1958.7762, 0.5441),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1224.3615, -1916.7644, 1.1260),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1059.9111, -1751.1399, 1.2223),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-902.9378, -1600.7874, 0.3031),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-794.1013, -1507.2932, 0.0859),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DeleteEntity(vehicle)
				end
			},
		}
	},

	['airplane'] = {
		vehicle = 'besra',
		start = vector4(-1085.4991, -3282.0525, 13.9444, 58.9541),
		marker = 27,
		dir = vector3(0.0, 20.0, 0.0),
		rotation = vector3(100.0, 100.0, 100.0),
		scale = vector3(12.5, 12.5, 0.5),

		route = {
			{
				coords = vector3(-1121.3921, -3260.4399, 14.7157),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1378.8723, -3114.9185, 72.3943),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1868.2886, -2860.7307, 202.3642),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2213.5688, -2463.6311, 231.0016),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-2029.3121, -1838.3102, 292.4374),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1483.4775, -1426.9651, 351.7073),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-941.7017, -1054.0306, 361.9962),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-254.5226, -541.8281, 409.0110),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(492.4974, -52.3069, 327.1656),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1031.7252, 413.4395, 236.8782),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1171.4231, 698.0929, 357.8533),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1258.3105, 602.1404, 201.6278),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1500.9449, 1062.1552, 213.4291),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1303.5349, 1273.7146, 244.6226),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(1171.6050, 671.1827, 174.9708),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(779.7650, 11.4669, 76.2444),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(568.5931, -378.0149, 104.7530),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(111.5460, -1177.7181, 196.1581),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-225.1801, -1645.7911, 220.8246),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-751.4221, -2172.2590, 206.3009),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1172.0996, -2375.0195, 184.7958),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1637.1171, -2639.1003, 138.0296),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1284.5690, -3176.8359, 14.8129),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1078.8203, -3293.6819, 13.9444),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DeleteEntity(vehicle)
				end
			},
		}
	},

	['helicopter'] = {
		vehicle = 'buzzard2',
		start = vector4(-1024.3906, -2394.0667, 13.9445, 241.5076),
		marker = 27,
		dir = vector3(0.0, 20.0, 0.0),
		rotation = vector3(100.0, 100.0, 100.0),
		scale = vector3(10.0, 10.0, 0.5),

		route = {
			{
				coords = vector3(-998.7770, -2406.7969, 33.9223),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-944.3615, -2431.1924, 75.1461),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-805.4043, -2480.3047, 94.3680),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-484.6154, -2402.3425, 136.4558),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-206.4916, -2160.9924, 134.1740),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-73.0392, -1878.2261, 124.4824),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-51.1996, -1555.1156, 134.6021),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-57.2453, -1197.2456, 135.8353),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-30.8650, -867.4290, 133.3835),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-102.0494, -713.0280, 122.8444),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-288.4788, -609.6000, 126.2536),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-515.1160, -503.1221, 123.3254),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-888.5909, -405.1119, 123.0819),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1001.7684, -817.2186, 103.1815),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-934.5014, -1265.7189, 49.9106),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1030.3993, -2381.1511, 41.7901),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DrawMissionText('Gehe zum nächsten Punkt!', 5000)
				end
			},

			{
				coords = vector3(-1066.5510, -2315.7605, 13.9446),
				action = function(playerPed, vehicle, setCurrentZoneType)
					DeleteEntity(vehicle)
				end
			},
		}
	}
}

Config_Drivingschool.Questions = {
	["car"] = {
		{
			question = 'Sie fahren auf eine rote Ampel zu und sehen Tiziano & Jamal, wie beide die Straße überqueren möchten! Was tun sie?',
			a = 'Tiziano Überfahren [NE BITTE].',
			b = "Jamal Überfahren [100% ÜBERFAHREN].",
			c = "Beide Überfahren.",
			d = "Sie Halten an und lassen beide passieren.",
			answer = 'd'
		},

		{
			question = 'Die Polizei fährt hinter Ihnen her und möchte, dass sie rechts ranfahren. Wie reagieren Sie?',
			a = 'Sie steigen mit Ihrer Skibrille aus und schießen auf die Polizei',
			b = "Sie eröffnen sofort das Feuer per Drive By.",
			c = "Sie halten rechts an und kooperieren mit der Polizei.",
			d = "Sie bleiben stehen und warten bis die Polizei aussteigt, dann nutzen Sie die Chance und fahren los.",
			answer = 'c'
		},

		{
			question = 'Sie kommen ins Schleudern und ihr Auto stellt sich auf den Kopf. Wie reagieren Sie?',
			a = 'Ich dreh die Gurke um und fahr weiter.',
			b = "Ich schrei um mein Leben, so dass Hilfe zu mir eilen kann.",
			c = "Ich bewahre Ruhe und steige langsam aus. Danach versuche ich die Rettung sowie den Abschleppdienst zu kontaktieren!",
			d = "Ich rufe die Polizei und versuche durch die Versicherung so viel Geld wie möglich zu machen.",
			answer = 'c'
		},

		{
			question = 'Sie fahren durch die Innenstadt und erwischen dabei ClsFx Oma mit ihrem Fahrzeug. Was tun Sie?',
			a = 'Sie rufen ihm hinterher “Selber Schuld du Vogel.',
			b = "Du steigst aus und kontaktierst sofort die Rettung!",
			c = "Du steigst aus und rufst die Rettung, danach ziehst du ihm alles ab was er hat, weil er die Frechheit hatte dein Auto mit seinem Körper zu beschädigen.",
			d = "Du ergreifst die Flucht und schiebst es auf die Mexikaner vom Vespucci Beach!",
			answer = 'b'
		},

		{
			question = 'Gestern Abend gab es Bohnensuppe und diese schlägt dir mitten auf der Autobahn auf den Magen. Du musst schnell zum nächsten stillen Örtchen. Was tust du?',
			a = 'Sie fahren zum Polizeirevier und benutzen den Vorgarten.',
			b = "Sie steigen aus und machen Ihr Geschäft auf der Autobahn.",
			c = "Sie fahren an der nächsten Ausfahrt raus und halten sich an die Straßenverkehrsordnung bis Sie zuhause sind.",
			d = "Sie fahren in die entgegengesetzte Richtung, weil das der schnellste Weg ist.",
			answer = 'c'
		},

		{
			question = 'Müssen Sie am Abend das Licht einschalten?',
			a = 'Ja.',
			b = "Nein.",
			c = "Nein, weil die Stadt beleuchtet ist.",
			d = "Ja, weil es schön ist.",
			answer = 'a'
		},

		{
			question = 'Aus welchem Grund darf die Polizei Sie anhalten und kontrollieren?',
			a = 'Wenn Sie mit einer Maske Auto fahren.',
			b = "Wenn Sie zu laute Musik hören.",
			c = "Wenn Sie ein hässliches Auto fahren.",
			d = "Wenn Sie hässlich sind.",
			answer = 'a'
		},

		{
			question = 'Auf welcher Straßenseite muss man fahren?',
			a = 'Links.',
			b = "Rechts.",
			c = "Mittig.",
			d = "wo man will.",
			answer = 'b'
		},

		{
			question = 'Wie schnell darf man auf der Autobahn fahren?',
			a = '10km/h',
			b = "99km/h",
			c = "sehr schnell.",
			d = "unbegrenzt.",
			answer = 'd'
		},

		{
			question = 'Wie überholen Sie richtig?',
			a = 'von Links.',
			b = "von Rechts.",
			c = "wegrammen und weiterfahren.",
			d = "man darf nicht überholen.",
			answer = 'a'
		}
	},

	["bike"] = {
		{
			question = 'Sie fahren auf eine rote Ampel zu und sehen Tiziano & Jamal, wie beide die Straße überqueren möchten! Was tun sie?',
			a = 'Tiziano Überfahren.',
			b = "Jamal Überfahren.",
			c = "Beide Überfahren.",
			d = "Sie Halten an und lassen beide passieren.",
			answer = 'd'
		},

		{
			question = 'Die Polizei fährt hinter Ihnen her und möchte, dass sie rechts ranfahren. Wie reagieren Sie?',
			a = 'Sie steigen mit Ihrer Skibrille aus und schießen auf die Polizei!',
			b = "Sie eröffnen sofort das Feuer per Drive By.",
			c = "Sie halten rechts an und kooperieren mit der Polizei.",
			d = "Sie bleiben stehen und warten bis die Polizei aussteigt, dann nutzen Sie die Chance und fahren los.",
			answer = 'c'
		},

		{
			question = 'Sie kommen ins Schleudern und ihr Motorrad stellt sich auf den Kopf. Wie reagieren Sie?',
			a = 'Ich dreh die Gurke um und fahr weiter.',
			b = "Ich schrei um mein Leben, so dass Hilfe zu mir eilen kann.",
			c = "Ich bewahre Ruhe und steige langsam aus. Danach versuche ich die Rettung sowie den Abschleppdienst zu kontaktieren!",
			d = "Ich rufe die Polizei und versuche durch die Versicherung so viel Geld wie möglich zu machen.",
			answer = 'c'
		},

		{
			question = 'Sie fahren durch die Innenstadt und erwischen dabei Ricos Oma mit ihrem Fahrzeug. Was tun Sie?',
			a = 'Sie rufen ihm hinterher “Selber Schuld du Vogel.',
			b = "Du steigst aus und kontaktierst sofort die Rettung!",
			c = "Du steigst aus und rufst die Rettung, danach ziehst du ihm alles ab was er hat, weil er die Frechheit hatte dein auto mit seinem Körper zu beschädigen.",
			d = "Du ergreifst die Flucht und schiebst es auf die Mexikaner vom Vespucci Beach!",
			answer = 'b'
		},

		{
			question = 'Gestern Abend gab es Bohnensuppe und diese schlägt dir mitten auf der Autobahn auf den Magen. Du musst schnell zum nächsten stillen Örtchen. Was tust du?',
			a = 'Sie fahren zum Cravatta Rossa Anwesen und benutzen den Vorgarten.',
			b = "Sie steigen aus und machen Ihr Geschäft auf der Autobahn.",
			c = "Sie fahren an der nächsten Ausfahrt raus und halten sich an die Straßenverkehrsordnung bis Sie zuhause sind.",
			d = "Sie fahren in die entgegengesetzte Richtung, weil das der schnellste Weg ist.",
			answer = 'c'
		},

		{
			question = 'Müssen Sie am Abend das Licht einschalten?',
			a = 'Ja.',
			b = "Nein.",
			c = "Nein, weil die Stadt beleuchtet ist.",
			d = "Ja, weil es schön ist.",
			answer = 'a'
		},

		{
			question = 'Aus welchem Grund darf die Polizei Sie anhalten und kontrollieren?',
			a = 'Wenn Sie mit einer Maske Motorrad fahren.',
			b = "Wenn Sie zu laute Musik hören.",
			c = "Wenn Sie ein hässliches Motorrad fahren.",
			d = "Wenn Sie hässlich sind.",
			answer = 'a'
		},

		{
			question = 'Auf welcher Straßenseite muss man fahren?',
			a = 'Links.',
			b = "Rechts.",
			c = "Mittig.",
			d = "wo man will.",
			answer = 'b'
		},

		{
			question = 'Wie schnell darf man auf der Autobahn fahren?',
			a = '10km/h',
			b = "99km/h",
			c = "Sehr schnell.",
			d = "unbegrenzt.",
			answer = 'd'
		},

		{
			question = 'Wie überholen Sie richtig?',
			a = 'von Links.',
			b = "von Rechts.",
			c = "wegrammen und weiterfahren.",
			d = "man darf nicht überholen.",
			answer = 'b'
		},
	},

	["boat"] = {
		{
			question = 'Sie fahren mit ihrem Boot bei starken Wellengang über das Wasser. Wie reagieren Sie?',
			a = 'Sie drosseln das Tempo.',
			b = "Sie werden noch schneller.",
			c = "Sie lieben die Gefahr und reiten die Wellen.",
			d = "Sie springen vom Boot und schwimmen weiter.",
			answer = 'a'
		},

		{
			question = 'Die Polizei fährt hinter Ihnen her und möchte, dass Sie anhalten. Wie reagieren Sie?',
			a = 'Sie steigen mit Ihrer Skibrille aus und schießen auf die Polizei!',
			b = "Sie eröffnen sofort das Feuer per Drive By.",
			c = "Sie halten rechts an und kooperieren mit der Polizei.",
			d = "Sie bleiben stehen und warten bis die Polizei aussteigt, dann nutzen Sie die Chance und fahren los.",
			answer = 'c'
		},

		{
			question = 'Sie kommen ins Schleudern und ihr Boot stellt sich auf den Kopf. Wie reagieren Sie?',
			a = 'Ich dreh die Gurke um und fahr weiter.',
			b = "Ich schrei um mein Leben, so dass Hilfe zu mir eilen kann.",
			c = "Ich bewahre Ruhe und steige langsam aus. Danach versuche ich die Rettung sowie den Abschleppdienst zu kontaktieren!",
			d = "Ich rufe die Polizei und versuche durch die Versicherung so viel Geld wie möglich zu machen.",
			answer = 'c'
		},

		{
			question = 'Sie begehen auf hohem Gewässer einen Mord. Kann Ihnen der Führerschein entzogen werden?',
			a = 'ja klar.',
			b = "Wenn ich alle Geiseln beseitige nicht.",
			c = "Ja aber nur wenn ich per Boot jemanden töte.",
			d = "Nein, weil auf Gewässern keine Gesetze gelten.",
			answer = 'd'
		},

		{
			question = 'Ihr Boot geht kaputt. Was tun Sie?',
			a = 'Auf dem Wasser treiben bis Ihre Story so heftig wird, dass Vinewood einen Film daraus machen will.',
			b = "Den Notdienst verständigen und auf Rettung warten.",
			c = "Vom Boot springen und anfangen zu schwimmen.",
			d = "versuchen das Boot selber zu reparieren und alle Öle und ähnliche Flüssigkeiten im Meer ablassen.",
			answer = 'b'
		},

		{
			question = 'Müssen Sie am Abend das Licht einschalten?',
			a = 'Ja.',
			b = "Nein.",
			c = "Nein, weil das Wasser beleuchtet.",
			d = "Ja, weil es schön ist.",
			answer = 'a'
		},

		{
			question = 'Aus welchem Grund darf die Polizei Sie anhalten und kontrollieren?',
			a = 'Wenn Sie mit einer Maske Boot fahren.',
			b = "Wenn Sie zu laute Musik hören.",
			c = "Wenn Sie ein hässliches Boot fahren.",
			d = "Wenn Sie hässlich sind.",
			answer = 'a'
		},

		{
			question = 'Auf welcher Straßenseite muss man fahren?',
			a = 'Links.',
			b = "Rechts.",
			c = "Mittig.",
			d = "es gibt keine Straßen auf dem Meer.",
			answer = 'd'
		},

		{
			question = 'Wie schnell darf man auf dem Wasser fahren?',
			a = '10km/h',
			b = "99km/h",
			c = "sehr schnell.",
			d = "unbegrenzt.",
			answer = 'd'
		},

		{
			question = 'Wie überholen Sie richtig?',
			a = 'von Links.',
			b = "von Rechts.",
			c = "wegrammen und weiterfahren.",
			d = "man darf nicht überholen.",
			answer = 'b'
		}
	},

	["airplane"] = {
		{
			question = 'Dürfen Sie ohne Fallschirm ein Flugzeug betreten?',
			a = 'Ja, das Risiko liegt bei mir.',
			b = "Nein, ich muss mich absichern.",
			c = "Ja, die Sicherheitsmaßnahmen obliegt dem Flugzeug.",
			d = "Nein, weil es Spaß macht raus zu springen.",
			answer = 'c'
		},

		{
			question = 'Sie haben 10m um Ihr Flugzeug zum starten zu bringen. Reicht das aus?',
			a = 'Nein geht sich nicht aus.',
			b = "Ja easy.",
			c = "Möglicherweise.",
			d = "Wer nichts wagt - kann nicht gewinnen.",
			answer = 'a'
		},

		{
			question = 'Sie sind in der Luft als plötzlich 1 Jet der Army nah an Ihrem Flugzeug fliegt und Ihnen deutet zu landen. Wie reagieren Sie?',
			a = 'Der Spinner soll weiter fliegen als ob ich das Ding jetzt lande.',
			b = "Ich lande und leiste der Army folge.",
			c = "Ich mach ein Manöver a la Top Gun und hänge den Jet ab.",
			d = "Ich deute eine Landung an und fliege im letzten Moment weg um zu entkommen.",
			answer = 'b'
		},

		{
			question = 'Ihr Flugzeug bekommt mitten im flug einen Triebwerkschaden, was machen Sie?',
			a = 'Beten.',
			b = "Versuchen auf einer freien Fläche zu lande.",
			c = "Rausspringen und das Flugzeug in eine Menschenmenge zu fliegen lassen.",
			d = "im Internet nachschauen wie man das Problem gelöst bekommt.",
			answer = 'b'
		},

		{
			question = 'Sie haben Passagiere im Flugzeug die lästig sind. Wie reagieren Sie?',
			a = 'Ich beleidige die Gäste.',
			b = "Ich fang an laut zu schreien bis sie aufhören lästig zu sein.",
			c = "Ich lande bei der nächsten Möglichkeit und verweise die Passagiere vom Flugzeug.",
			d = "Ich geh nach hinten und verteile Backpfeifen.",
			answer = 'c'
		},

		{
			question = 'Müssen Sie die Reifen des Flugzeuges einziehen wenn sie abgehoben sind?',
			a = 'Ich bin zwar abgehoben aber ich verstehe nicht was mit Reifen gemeint ist.',
			b = "Ja, ich muss die Reifen einziehen.",
			c = "Nein, muss ich nicht.",
			d = "weiß ich nicht.",
			answer = 'b'
		},

		{
			question = 'Darf ich nah an Gebäude fliegen?',
			a = 'Nein, das wäre sehr unsensibel.',
			b = "Ja.",
			c = "Nein, weil das gefährlich werden kann.",
			d = "Ja aber ich muss drauf achten nicht in Fenster rein zu schauen um die Privatsphäre der Mitmenschen zu achten.",
			answer = 'c'
		},

		{
			question = 'Welche 4 Himmelsrichtungen gibt es?',
			a = 'Norden, Westeros, Süden, Osten.',
			b = "Nordhain, Westen, Süden, Osten.",
			c = "Norden, Westen, Süden, Ostern.",
			d = "Norden, Westen, Süden, Osten.",
			answer = 'd'
		},

		{
			question = 'Es ist ein schweres Unwetter und sie möchten wohin fliegen, was tun Sie?',
			a = 'Warten bis der Sturm sich gelegt hat.',
			b = "Losfliege, macht sicher Spaß.",
			c = "Wenn man einen Regenschirm mitnimmt ist alles gut.",
			d = "ich darf 3 Wochen nicht fliegen und einen Tanz ausführen, dass es aufhört zu regnen.",
			answer = 'd'
		},

		{
			question = 'Dürfen Sie am Würfelpark landen?',
			a = 'Ja.',
			b = "Nein.",
			c = "Wenns sichs ausgeht schon.",
			d = "Keine ahnung.",
			answer = 'b'
		}
	},

	["helicopter"] = {
		{
			question = 'Dürfen Sie ohne Fallschirm ein Helikopter betreten?',
			a = 'Ja, das Risiko liegt bei mir.',
			b = "Nein, ich muss mich absichern.",
			c = "Ja, die sicherheitsmaßnahmen obliegt dem Helikopter.",
			d = "Nein, weil es Spaß macht raus zu springen.",
			answer = 'c'
		},

		{
			question = 'Sie haben unter sich ein Dach und möchten den Heli starten. Geht sich das aus?',
			a = 'Nein, geht sich nicht aus.',
			b = "Ja easy.",
			c = "Möglicherweise.",
			d = "Wer nichts wagt - kann nicht gewinnen.",
			answer = 'a'
		},

		{
			question = 'Sie sind in der Luft als plötzlich 1 Jet der Army nah an Ihrem Heli fliegt und Ihnen deutet zu landen. Wie reagieren Sie?',
			a = 'Der Spinner soll weiter fliegen als ob ich das Ding jetzt lande.',
			b = "Ich lande und leiste der Army folge.",
			c = "Ich mach ein Manöver a la Top Gun und hänge den Jet ab.",
			d = "Ich deute eine Landung an und fliege im letzten Moment weg um zu entkommen.",
			answer = 'b'
		},

		{
			question = 'Ihr Heli bekommt mitten im flug einen Triebwerkschaden, was machen Sie?',
			a = 'Beten.',
			b = "Versuchen auf einer freien Fläche zu lande.",
			c = "Rausspringen und den Heli in eine Menschenmenge zu fliegen lassen.",
			d = "im Internet nachschauen wie man das Problem gelöst bekommt.",
			answer = 'b'
		},

		{
			question = 'Sie haben Passagiere im Heli die lästig sind. Wie reagieren Sie?',
			a = 'Ich beleidige die Gäste.',
			b = "Ich fang an laut zu schreien bis sie aufhören lästig zu sein.",
			c = "Ich lande bei der nächsten Möglichkeit und verweise die Passagiere vom Heli.",
			d = "Ich geh nach hinten und verteile Backpfeifen.",
			answer = 'c'
		},

		{
			question = 'Müssen Sie die Reifen des Helis einziehen wenn sie abgehoben sind?',
			a = 'Ich bin zwar abgehoben aber ich verstehe nicht was mit Reifen gemeint ist.',
			b = "Ja, ich muss die Reifen einziehen.",
			c = "Nein.",
			d = "weiß ich nicht.",
			answer = 'b'
		},

		{
			question = 'Darf ich nah an Gebäude fliegen?',
			a = 'Nein, das wäre sehr unsensibel.',
			b = "Ja.",
			c = "Nein, weil das gefährlich werden kann.",
			d = "Ja aber ich muss drauf achten nicht in Fenster rein zu schauen um die Privatsphäre der Mitmenschen zu achten.",
			answer = 'c'
		},

		{
			question = 'Welche 4 Himmelsrichtungen gibt es?',
			a = 'Norden, Westeros, Süden, Osten.',
			b = "Nordhain, Westen, Süden, Osten.",
			c = "Norden, Westen, Süden, Ostern.",
			d = "Norden, Westen, Süden, Osten.",
			answer = 'd'
		},

		{
			question = 'Es ist ein schweres Unwetter und sie möchten wohin fliegen, was tun Sie?',
			a = 'Warten bis der Sturm sich gelegt hat.',
			b = "Losfliege, macht sicher Spaß.",
			c = "Wenn man einen Regenschirm mitnimmt ist alles gut.",
			d = "ich darf 3 Wochen nicht fliegen und einen Tanz ausführen, dass es aufhört zu regnen.",
			answer = 'a'
		},

		{
			question = 'Dürfen Sie am Würfelpark landen?',
			a = 'Ja.',
			b = "Nein.",
			c = "Wenns sichs ausgeht schon.",
			d = "Keine ahnung.",
			answer = 'b'
		},
	}
}