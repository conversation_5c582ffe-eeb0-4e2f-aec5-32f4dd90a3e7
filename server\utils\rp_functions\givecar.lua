ESX.RegisterServerCallback('esx_givecarkeys:requestPlayerCars', function(source, cb, plate)
	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @identifier', {
		['@identifier'] = ESX.GetPlayerIdentifier(source)
	}, function(result)
	    local found = false

        for k, v in pairs(result) do
            local vehicleProps = json.decode(v.vehicle)

            if trim(vehicleProps.plate) == trim(plate) then
                found = true
                break
            end
        end

		if found then
			cb(true)
		else
			cb(false)
		end
	end)
end)

RegisterServerEvent('esx_givecarkeys:frommenu')
AddEventHandler('esx_givecarkeys:frommenu', function()
	TriggerClientEvent('esx_givecarkeys:keys', source)
end)

RegisterServerEvent('esx_givecarkeys:setVehicleOwnedPlayerId')
AddEventHandler('esx_givecarkeys:setVehicleOwnedPlayerId', function(playerId, vehicleProps, modelName)
	local src = source

    MySQL.Async.fetchAll('SELECT type, job FROM owned_vehicles WHERE plate = @plate AND owner = @owner', {
        ['@owner'] = ESX.GetPlayerIdentifier(src),
        ['@plate'] = vehicleProps.plate
    }, function(res)
        local result = res
        if #result ~= 0 then
            MySQL.Async.execute('UPDATE owned_vehicles SET owner = @owner WHERE plate = @plate', {
                ['@owner'] = ESX.GetPlayerIdentifier(playerId),
                ['@plate'] = vehicleProps.plate
            }, function (rowsChanged)
                Notify(playerId, 'Information', 'Du hast ein Auto bekommen ' .. vehicleProps.plate, 'info')
                Notify(src, 'Information', 'Du hast den Schlüssel von folgendenem Kennzeichen übergeben ' .. vehicleProps.plate, 'info')
                
                if result[1].type and result[1].job then
                    exports['cc_core']:doubleLog(src, playerId, 'Autoübergabe - Log', 'Der Spieler ' .. GetPlayerName(src) .. ' hat ein Auto mit dem Kennzeichen ' .. vehicleProps.plate .. ' an ' .. GetPlayerName(playerId) .. ' gegeben (Type: ' .. result[1].type .. ' Job: ' .. result[1].job .. ' Model: ' .. modelName .. ')', 'https://discord.com/api/webhooks/1106378619632238632/ei17RIRSC9yio5_Xtw_pqSOVwBZZKv7aR7QCVQH4BjrpC1Q9eW3WBzWGMKssDp0nJFEd', false)
                end

                TriggerClientEvent('cc_core:garage:addVehicle', playerId, vehicleProps, false, vehicleProps.plate, 'Fahrzeug', result[1].type, result[1].job)
                TriggerClientEvent('cc_core:garage:removeVehicle', src, vehicleProps.plate)
                TriggerEvent('inventory:switchPlates', vehicleProps.plate, ESX.GetPlayerIdentifier(src), ESX.GetPlayerIdentifier(playerId))
            end)
        end
    end)
end)

function trim(s)
    if s ~= nil then
		return s:match("^%s*(.-)%s*$")
	else
		return nil
    end
end