local players = {}

local function isNear(coords, index)
    if Config_Revive.Positions[index] ~= nil then
        if #(coords - Config_Revive.Positions[index].coords) < 10.0 then
            return true
        end 
    end

    return false
end

AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    if players[playerId] == nil then
        players[playerId] = {}

        players[playerId].job = xPlayer.getJob().name
        players[playerId].dienst = xPlayer.getJob().dienst
    end
end)

AddEventHandler('esx:setJob', function(playerId, job, lastJob)
    if players[playerId] then
        players[playerId].job = job.name
        players[playerId].dienst = false
    end
end)

AddEventHandler('esx:setJobDienst', function(playerId, job, state)
    if players[playerId] then
        players[playerId].dienst = state
    end
end)

AddEventHandler('playerDropped', function(reason)
    local playerId = source

    if players[playerId] then
        players[playerId] = nil
    end
end)

AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        local xPlayers = exports['cc_core']:GetPlayersFix()
        
        for k, v in pairs(xPlayers) do
            if players[v.playerId] == nil then
                players[v.playerId] = {}
        
                players[v.playerId].job = ESX.GetPlayerJob(v.playerId).name
                players[v.playerId].dienst = ESX.GetPlayerJob(v.playerId).dienst
            end
        end
    end
end)

RegisterServerEvent('cc_core:revivestation:revive')
AddEventHandler('cc_core:revivestation:revive', function(target, index)
    local playerId = source

    if GetPlayerName(target) ~= nil then
        if playerId == target then
            return
        end

        local sourcePed = GetPlayerPed(playerId)
        local sourceCoords = GetEntityCoords(sourcePed)

        if not isNear(sourceCoords, index) then
            return
        end

        if #(sourceCoords - GetEntityCoords(GetPlayerPed(target))) >= 5.0 then
            return
        end

        if ESX.GetPlayerMoney(playerId) >= 3000 then
            MySQL.Async.fetchAll('SELECT is_dead FROM users WHERE identifier = @identifier', {
                ['@identifier'] = ESX.GetPlayerIdentifier(target)
            }, function(result)
                if #result ~= 0 then
                    if result[1].is_dead then
                        TriggerClientEvent('cc_core:hud:notify', playerId, 'info', 'Revivestation', 'Spieler erfolgreich revived!')
                        ESX.RemovePlayerMoney(playerId, 3000, GetCurrentResourceName())
                        TriggerClientEvent('cc_core:jobpack:revive', target, false)
                    else
                        TriggerClientEvent('cc_core:hud:notify', playerId, 'info', 'Revivestation', 'Der Spieler ist nicht tot')
                    end
                end
            end)
        else
            TriggerClientEvent('cc_core:hud:notify', playerId, 'info', 'Revivestation', 'Du hast nicht genug Geld dabei!')
        end
    end
end)

ESX.RegisterServerCallback('cc_core:revivestation:canRevive', function(source, cb)
    local jobAmount = 0

    for k, v in pairs(players) do
        if v.job == 'ambulance' then
            if v.dienst then
                jobAmount = jobAmount + 1
            end
        end
    end

    cb(jobAmount, os.date("%H"))
end)

--clientcode
reviveCode = [[
    -- local ESX = nil
    local show = false

    -- CreateThread(function()
    --     while ESX == nil do
    --         Wait(5)
    --         ESX = exports['es_extended']:getSharedObject()
    --     end

    --     while ESX.GetPlayerData().job == nil do
    --         Wait(50)
    --     end

    --     ESX.PlayerData = ESX.GetPlayerData()
    -- end)

    CreateThread(function()
        for k, v in pairs(Config_Revive.Positions) do
            local hash = GetHashKey(v.ped)

            if not HasModelLoaded(hash) then
                RequestModel(hash)
                Wait(100)
            end
                
            while not HasModelLoaded(hash) do
                Wait(0)
            end
        
            local ped = CreatePed(6, hash, v.coords.x, v.coords.y, v.coords.z - 1, v.heading, false, false)
            SetEntityInvincible(ped, true)
            FreezeEntityPosition(ped, true)
            SetBlockingOfNonTemporaryEvents(ped, true)
        end
    end)

    CreateThread(function()
        while true do
            Wait(0)
            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)
            local letSleep, inRange = true, false

            for k, v in pairs(Config_Revive.Positions) do
                local distance = #(coords - v.coords)

                if distance < 1.0 then
                    letSleep, inRange = false, true
        
                    if IsControlJustReleased(1, 38) then
                        local closestPlayer, closestPlayerDistance = ESX.Game.GetClosestPlayer()
        
                        if closestPlayer ~= -1 and closestPlayerDistance < 3.0 then
                            ESX.TriggerServerCallback('cc_core:revivestation:canRevive', function(medics, time)
                                if medics < 4 then
                                    if tonumber(time) >= 0 and tonumber(time) <= 24 then
                                        TriggerServerEvent('cc_core:revivestation:revive', GetPlayerServerId(closestPlayer), k)
                                    else
                                        TriggerEvent('cc_core:hud:notify', 'info', 'Revivestation', 'Die Revivestation ist derzeit deaktiviert')
                                    end
                                else
                                    TriggerEvent('cc_core:hud:notify', 'info', 'Revivestation', 'Es sind noch genug Medics wach')
                                end
                            end)
                        else
                            TriggerEvent('cc_core:hud:notify', 'info', 'Revivestation', 'Kein Spieler in der nähe')
                        end
                    end
                end
            end

            if not show and inRange then
                show = true
                exports['cc_core']:showHelpNotification('Drücke E um die nächste Person bei dir zu reviven, Preis 3000$', 'E')
            elseif show and not inRange then
                show = false
                exports['cc_core']:closeHelpNotification()
            end

            if letSleep then
                Wait(500)
            end
        end
    end)
]]