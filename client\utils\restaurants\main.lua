-- local cachedRestaurants = nil 

-- exports("getRestaurants", function()
--     if cachedRestaurants then
--         return cachedRestaurants
--     end

--     TriggerServerEvent("cc_core:server:requestRestaurants")
    
--     local response = nil
--     local event = RegisterNetEvent("cc_core:client:receiveRestaurants", function(restaurants)
--         response = restaurants
--     end)
    
--     local timeout = GetGameTimer() + 3000
--     while not response and GetGameTimer() < timeout do
--         Citizen.Wait(100)
--     end
    
--     RemoveEventHandler(event)
--     cachedRestaurants = response
--     return response
-- end)

-- RegisterNetEvent("cc_core:client:invalidateCache", function()
--     cachedRestaurants = nil
-- end)