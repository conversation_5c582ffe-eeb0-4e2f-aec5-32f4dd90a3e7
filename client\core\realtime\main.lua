local halloweenWheater = false

local servertime = {
    h = 0,
    m = 0,
    s = 0
}

local WEATHER = 'extrasunny'

RegisterNetEvent('cc_core:realtime:changeWeather', function(wea)
    WEATHER = wea
end)

Citizen.CreateThread(function()
    Citizen.Wait(250)
    while true do
        Citizen.Wait(5000)
        NetworkOverrideClockTime(servertime.h, servertime.m, servertime.s)
    end
end)

RegisterNetEvent('cc_core:realtime:changetime')
AddEventHandler('cc_core:realtime:changetime', function(hours, minutes, seconds)
    servertime = {
        h = hours,
        m = minutes,
        s = seconds
    }
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(60000)
        ClearOverrideWeather()
        ClearWeatherTypePersist()
        SetWeatherTypePersist(WEATHER)
        SetWeatherTypeNow(WEATHER)
        SetWeatherTypeNowPersist(WEATHER)
    end
end)