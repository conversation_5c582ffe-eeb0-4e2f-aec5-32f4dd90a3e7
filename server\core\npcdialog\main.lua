npcdialogCode = [[
local isOpen = false

function ShowNPCDialog(pedName, pedType, pedTitle, buttons, ped)
    -- print("^2[DEBUG] Buttons:", json.encode(buttons))

    SendNUIMessage({
        action = "npcDialog/openDialog",
        show = true,
        pedName = pedName,
        pedType = pedType,
        pedTitle = pedTitle,
        buttons = buttons
    })

  if not isOpen then
    isOpen = true
    CreateThread(function()
      while isOpen do
        Wait(0)
        SetEntityLocallyInvisible(PlayerPedId())
      end
    end)
  end

  if ped then
    local coords = GetOffsetFromEntityInWorldCoords(ped, 0, 1.5, 0.3)
    local cam = CreateCam('DEFAULT_SCRIPTED_CAMERA', true)
    SetEntityLocallyInvisible(PlayerPedId())
    SetCamActive(cam, true)
    RenderScriptCams(true, true, 500, true, true)
    SetCamCoord(cam, coords.x, coords.y, coords.z + 0.2)
    SetCamRot(cam, 0.0, 0.0, GetEntityHeading(ped) + 180, 5)
    SetCamFov(cam, 40.0)
  end
  DisplayRadar(false)
  hideHud(true)
  SetNuiFocus(true, true)
end

RegisterCommand("testdialog", function(source)
  local buttons = {
    {name = "Klaus schlagen", icon = "fas fa-baby", func = "klauspeitschen"},
    {name = "Klaus würgen", icon = "fas fa-bacon", func = "klausbeacon"},
  }
  ShowNPCDialog("Klaus", "Drogenhändler", "Peter Maffai", buttons)
end, false)

RegisterNUICallback("npcDialog/closeDialog", function(data, cb)
  if not isOpen then return end
  hideHud(false)
  SetNuiFocus(false, false)
  DisplayRadar(true)
  RenderScriptCams(false, true, 500, true, true)
  isOpen = false
end, false)

getClosestDialogPed = function(coords, max)
	local all = GetGamePool('CPed')
	local closest, closestCoords
	max = max or 2.0

	for i = 1, #all do
		local ped = all[i]

        if not IsPedAPlayer(ped) then
          local pedCoords = GetEntityCoords(ped)
          local distance = #(coords - pedCoords)
  
          if distance < max then
              max = distance
              closest = ped
              closestCoords = pedCoords
          end
        end
	end

	return closest, closestCoords
end

RegisterNUICallback("npcDialog/action", function(data, cb)
    local action = data.action
      if not isOpen then return end
      hideHud(false)
      SetNuiFocus(false, false)
      DisplayRadar(true)
      RenderScriptCams(false, true, 500, true, true)
      isOpen = false

    if action == "impound_openvehicles" then
      TriggerEvent("cc_core:garage:openImpound", garageI)
    elseif action == "impound_getallvehicles" then
      TriggerEvent('cc_core:impound:getAllVehicles')
    elseif action == "openfractionlager" then
      TriggerEvent("cc_core:lager:open")
    elseif action == "supermarket_open" then
      TriggerEvent("cc_core:supermarket:openwhitnpcdialog")
    elseif action == "supermarket_management_open" then
      TriggerEvent("cc_core:supermarket:openshopwhitnpcdialog")
    elseif action == "openfractionkühl" then
      print("Kommt noch")
    end

    cb('ok')
end)

]]