tempoCode = [[
local vehicleSpeed = 0

RegisterKey('tempo', 'Tempomat', 'o')

RegisterCommand('tempo', function()
    if not IsDriver() then return end
    if isBoot() then
        Notify('Tempomat', 'Du kannst bei einem Boot kein Tempomat einstellen', 'info')
        return
    end
    TriggerCruiseControl()
end, false)

local CruisedSpeed, CruisedSpeedKm = 0, 0

function TriggerCruiseControl()
    if CruisedSpeed == 0 and IsDriving() then
        if GetVehiculeSpeed() > 0 then
            CruisedSpeed = GetVehiculeSpeed()
            CruisedSpeedKm = TransformToKm(CruisedSpeed)
            
            Notify('Tempomat', 'Tempomat: ' .. math.floor(CruisedSpeed * 3.6 + 0.5) .. ' km/h', 'info')
            
            Citizen.CreateThread(function ()
                while CruisedSpeed > 0 and IsInVehicle() == PlayerPedId() do
                    Wait(0)
                    
                    if not IsTurningOrHandBraking() and GetVehiculeSpeed() < (CruisedSpeed - 1.5) then
                        CruisedSpeed = 0
                        Notify('Tempomat', 'Tempomat deaktiviert', 'info')
                        Wait(2000)
                        break
                    end
                    
                    if not IsTurningOrHandBraking() and IsVehicleOnAllWheels(GetVehicle()) and GetVehiculeSpeed() < CruisedSpeed then
                        SetVehicleForwardSpeed(GetVehicle(), CruisedSpeed)
                    end
                    
                    if IsControlJustPressed(1, 246) then
                        CruisedSpeed = GetVehiculeSpeed()
                        CruisedSpeedKm = TransformToKm(CruisedSpeed)
                    end

                    if IsControlJustPressed(2, 72) then
                        CruisedSpeed = 0
                        Notify('Tempomat', 'Tempomat deaktiviert', 'info')
                        Wait(2000)
                        break
                    end
                end
            end)
        end
    end
end
  
function IsTurningOrHandBraking()
    return IsControlPressed(2, 76) or IsControlPressed(2, 63) or IsControlPressed(2, 64)
end
  
function IsDriving()
    return IsPedInAnyVehicle(PlayerPedId(), false)
end

function isBoot()
    local class = GetVehicleClass(GetVehicle(), false)
    
    if class == 14 then
        return true
    end

    return false
end
  
function GetVehicle()
    return GetVehiclePedIsIn(PlayerPedId(), false)
end
  
function IsInVehicle()
    return GetPedInVehicleSeat(GetVehicle(), -1)
end
  
function IsDriver()
    return GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), -1) == PlayerPedId()
end
  
function GetVehiculeSpeed()
    return GetEntitySpeed(GetVehicle())
end
  
function TransformToKm(speed)
    return math.floor(speed * 3.6 + 0.5)
end
]]