<?xml version="1.0" encoding="UTF-8"?>

<CWeaponInfoBlob>
  <SlotNavigateOrder>
    <Item />
    <Item />
  </SlotNavigateOrder>
  <SlotBestOrder />
  <AimingInfos />
  <Infos>
	<Item />
	<Item />
	<Item >
	  <Infos>	
        <Item type="CWeaponInfo">
          <Name>VEHICLE_WEAPON_TURRET_TECHNICAL</Name>
          <Model />
          <Audio>AUDIO_ITEM_TECHNICAL_TURRET</Audio>
          <Slot />
          <DamageType>BULLET</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>INSTANT_HIT</FireType>
          <WheelSlot>WHEEL_SMG</WheelSlot>
          <Group />
          <AmmoInfo ref="AMMO_MG" />
          <AimingInfo ref="MG_LOW_BASE_STRAFE" />
          <ClipSize value="750" />
          <AccuracySpread value="1.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RecoilAccuracyMax value="1.000000" />
          <RecoilErrorTime value="3.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="40.000000" />
          <HeadShotDamageModifierPlayer value="18.000000" />
          <Damage value="200.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="400.000000" />
          <ForceHitPed value="200.000000" />
          <ForceHitVehicle value="5000.000000" />
          <ForceHitFlyingHeli value="5000.000000" />
          <OverrideForces />
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="2000.000000" />
          <Penetration value="0.100000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.180000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.750000" />
          <BulletBendingZoomedRadius value="0.375000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_VEHICLE_MG</EffectGroup>
            <FlashFx>muz_mounted_turret</FlashFx>
            <FlashFxAlt />
            <ShellFx>eject_mounted_turret_technical</ShellFx>
            <TracerFx>bullet_tracer_turret</TracerFx>
            <PedDamageHash>BulletLarge</PedDamageHash>
            <TracerFxChanceSP value="0.600000" />
            <TracerFxChanceMP value="0.800000" />
            <FlashFxChanceSP value="1.000000" />
            <FlashFxChanceMP value="1.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.500000" />
          </Fx>
          <InitialRumbleDuration value="150" />
          <InitialRumbleIntensity value="0.400000" />
          <InitialRumbleIntensityTrigger value="0.950000" />
          <RumbleDuration value="120" />
          <RumbleIntensity value="0.400000" />
          <RumbleIntensityTrigger value="0.850000" />
          <RumbleDamageIntensity value="1.000000" />
          <NetworkPlayerDamageModifier value="1.000000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.500000" />
          <LockOnRange value="0.000000" />
          <WeaponRange value="500.000000" />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="200.000000" />
          <AiPotentialBlastEventRange value="-1.000000" />
          <DamageFallOffRangeMin value="500.000000" />
          <DamageFallOffRangeMax value="500.000000" />
          <DamageFallOffModifier value="1.000000" />
          <VehicleWeaponHash>VEHICLE_DATA_TURRET_TECHNICAL</VehicleWeaponHash>
          <DefaultCameraHash>TECHNICAL_TURRET_CAMERA</DefaultCameraHash>
          <AimCameraHash>TECHNICAL_TURRET_AIM_CAMERA</AimCameraHash>
          <FireCameraHash>TECHNICAL_TURRET_FIRE_CAMERA</FireCameraHash>
          <CoverCameraHash />
          <CoverReadyToFireCameraHash />
          <AssistedAimingCameraHash />
          <AlternativeOrScopedCameraHash />
          <AssistedAlternativeOrScopedCameraHash />
          <CameraFov value="35.000000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash>MG_RECOIL_SHAKE</RecoilShakeHash>
	  <RecoilShakeHashFirstPerson>FPS_MG_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
          <AccuracyOffsetShakeHash>DEFAULT_THIRD_PERSON_ACCURACY_OFFSET_SHAKE</AccuracyOffsetShakeHash>
          <MinTimeBetweenRecoilShakes value="100" />
          <RecoilShakeAmplitude value="1.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <ReticuleHudPosition x="0.500000" y="0.500000" />
          <AimOffsetMin x="0.285000" y="0.175000" z="0.275000" />
          <AimProbeLengthMin value="0.525000" />
          <AimOffsetMax x="0.225000" y="-0.050000" z="0.400000" />
          <AimProbeLengthMax value="0.415000" />
          <TorsoAimOffset x="-0.900000" y="0.550000" />
          <TorsoCrouchedAimOffset x="0.100000" y="0.120000" />
          <LeftHandIkOffset x="0.015000" y="0.095000" z="-0.008000" />
          <ReticuleMinSizeStanding value="0.600000" />
          <ReticuleMinSizeCrouched value="0.500000" />
          <ReticuleScale value="0.300000" />
          <ReticuleStyleHash>WEAPONTYPE_RIFLE</ReticuleStyleHash>
          <FirstPersonReticuleStyleHash />
          <PickupHash />
          <MPPickupHash />
          <HumanNameHash>WT_V_TURRET</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>TURR</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>Normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>Gun UsableOnFoot  ForceEjectShellAfterFiring Turret StaticReticulePosition ApplyVehicleDamageToEngine Vehicle</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="DEFAULT" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="0.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.000000" />
          <HudDamage value="30" />
          <HudSpeed value="60" />
          <HudCapacity value="50" />
          <HudAccuracy value="40" />
          <HudRange value="60" />
        </Item>
	  </Infos>
	</Item>
	<Item />
  </Infos>
  <VehicleWeaponInfos>
    <Item type="CVehicleWeaponInfo">
      <Name>VEHICLE_DATA_TURRET_TECHNICAL</Name>
      <KickbackAmplitude value="0.015000" />
      <KickbackImpulse value="0.150000" />
    </Item>
  </VehicleWeaponInfos>
  <Name>DLC - Turret (Technical)</Name>
</CWeaponInfoBlob>
