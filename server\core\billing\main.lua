local function stringSplit(inputstr, sep)
	if sep == nil then
		sep = "%s"
	end

	local t={} ; i=1
	for str in string.gmatch(inputstr, "([^"..sep.."]+)") do
		t[i] = str
		i = i + 1
	end

	return t
end

AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
	Citizen.Wait(6500)
	
	MySQL.Async.fetchAll('SELECT id, label, amount FROM billing WHERE identifier = @identifier', {
		['@identifier'] = xPlayer.getIdentifier()
	}, function(result)
		local bills = {}

        if #result ~= 0 then
            for k, v in pairs(result) do
                table.insert(bills, {
                    id = v.id,
                    label = v.label,
                    amount = v.amount
                })
            end
        end

		TriggerClientEvent('cc_core:billing:getBills', playerId, bills)
	end)
end)

RegisterNetEvent('cc_core:bill:sendBill', function(target, sharedAccountName, label, amount, job3)
	local playerId = source
	local havePermissions = true --exports['cc_fraction']:haveBillPermissions(playerId, job3)
	amount = ESX.Math.Round(amount)

	if havePermissions then
		TriggerEvent('cc_fraction:getAccount', stringSplit(sharedAccountName, '_')[2], function(account)
			if amount >= 1 then
				if account ~= nil then
					MySQL.Async.insert('INSERT INTO billing (identifier, sender, target_type, target, label, amount, time) VALUES (@identifier, @sender, @target_type, @target, @label, @amount, @time)', {
						['@identifier'] = ESX.GetPlayerIdentifier(target),
						['@sender'] = ESX.GetPlayerIdentifier(playerId),
						['@target_type'] = 'society',
						['@target'] = sharedAccountName,
						['@label'] = label,
						['@amount'] = amount,
						['@time'] = os.time()
					}, function(insertId)
						Notify(playerId, 'Rechnung', 'Du hast eine Rechnung ausgestellt', 'info')
						Notify(target, 'Rechnung', 'Du hat eine Rechnung erhalten', 'info')
						TriggerClientEvent('cc_core:billing:addBill', target, insertId, label, amount)
					end)
				end
			end
		end)
	end
end)

ESX.RegisterServerCallback('cc_core:bill:getBills', function(source, cb)
	MySQL.Async.fetchAll('SELECT * FROM billing WHERE identifier = @identifier', {
		['@identifier'] = ESX.GetPlayerIdentifier(source)
	}, function(result)
		local bills = {}

        if #result ~= 0 then
            for k, v in pairs(result) do
                table.insert(bills, {
                    id = v.id,
                    identifier = v.identifier,
                    sender = v.sender,
                    targetType = v.target_type,
                    target = v.target,
                    label = v.label,
                    amount = v.amount
                })
            end
        end

		cb(bills)
	end)
end)

ESX.RegisterServerCallback('cc_core:bill:getTargetBills', function(source, cb, target)
	MySQL.Async.fetchAll('SELECT * FROM billing WHERE identifier = @identifier', {
		['@identifier'] = ESX.GetPlayerIdentifier(target)
	}, function(result)
		local bills = {}
        
        if #result ~= 0 then
            for k, v in pairs(result) do
                table.insert(bills, {
                    id = v.id,
                    identifier = v.identifier,
                    sender = v.sender,
                    targetType = v.target_type,
                    target = v.target,
                    label = v.label,
                    amount = v.amount
                })
            end
        end

		cb(bills)
	end)
end)

RegisterServerEvent('cc_core:bill:removeBill')
AddEventHandler('cc_core:bill:removeBill', function(id)
	local playerId = source
	local jobName = ESX.GetPlayerJob(playerId).name

	if jobName == 'doj' then
		MySQL.Async.fetchAll('SELECT * FROM billing WHERE id = @id', {
			['@id'] = id
		}, function(result)
			if #result ~= 0 and #result == 1 then
				MySQL.Async.execute('DELETE from billing WHERE id = @id', {
					['@id'] = id
				}, function(rowsChanged)
					if rowsChanged > 0 then
						Notify(playerId, 'Rechnung', 'Du hast eine Rechnung gelöscht', 'info')
						exports['cc_core']:log(playerId, 'Rechnungs - log', 'Du löscht eine Rechnung von ' .. result[1].sender .. ' in höhe von ' .. ESX.Math.GroupDigits(tonumber(result[1].amount)) .. '$ ID: ' .. id, 'https://canary.discord.com/api/webhooks/1352803219885002814/n5rHxpt01DIPPZEgmLT24oVCCBotk2g6rkdnHw2RuDlzcUJZpB2-HeaXssEaJJHN1aCn')
					else
						Notify(playerId, 'Rechnung', 'Fehler beim Rechnung löschen', 'info')
					end
				end)
			end
		end)
	end
end)

local canNotPay = {}

RegisterServerEvent('cc_core:bill:payBill')
AddEventHandler('cc_core:bill:payBill', function(id)
	local playerId = source

	if canNotPay[playerId] == nil then
		canNotPay[playerId] = false
	end

	if not canNotPay[playerId] then
		canNotPay[playerId] = true

		MySQL.Async.fetchAll('SELECT * FROM billing WHERE id = @id', {
			['@id'] = id
		}, function(result)
			print('Result: ' .. #result .. ' ID: ' .. id)
			if #result ~= 0 and #result == 1 then
				local sender = result[1].sender
				local targetType = result[1].target_type
				local target = result[1].target
				local amount = result[1].amount
				local accountMoney = ESX.GetPlayerAccount(playerId, 'bank').money
				local xTarget = ESX.GetPlayerFromIdentifier(sender)
	
				TriggerEvent('cc_fraction:getAccount', stringSplit(target, '_')[2], function(account)
					if accountMoney >= amount then
						MySQL.Async.execute('DELETE from billing WHERE id = @id', {
							['@id'] = id
						}, function(rowsChanged)
							ESX.RemovePlayerAccountMoney(playerId, 'bank', tonumber(amount), GetCurrentResourceName())
							
							if target ~= 'society_mechanic' and target ~= 'society_taxi' then
								account.addMoney(amount, GetCurrentResourceName())
							else
								if target == 'society_taxi' then
									account.addMoney(amount * 0.75, GetCurrentResourceName())

									if xTarget ~= nil then
										ESX.AddPlayerAccountMoney(xTarget.source, 'bank', tonumber(amount * 0.25), GetCurrentResourceName())
									end
								else
									account.addMoney(amount * 0.75, GetCurrentResourceName())

									if xTarget ~= nil then
										ESX.AddPlayerAccountMoney(xTarget.source, 'bank', tonumber(amount * 0.25), GetCurrentResourceName())
									end
								end
							end
			
							Notify(playerId, 'Rechnung', 'Du bezahlst eine Rechnung von ' .. ESX.Math.GroupDigits(amount) .. '$', 'info')
							TriggerClientEvent('cc_core_billing:removeBill', playerId, id)

							if xTarget ~= nil then
								Notify(xTarget.source, 'Rechnung', 'Du erhältst eine Zahlung von ' .. ESX.Math.GroupDigits(amount) .. '$', 'info')
								exports['cc_core']:doubleLog(playerId, xTarget.source, 'Rechnungs - log', 'Du bezahlst eine Rechnung von ' .. xTarget.getRPName() .. ' in höhe von ' .. ESX.Math.GroupDigits(amount) .. '$ Rechnungs id: ' .. id, 'https://canary.discord.com/api/webhooks/1352803219885002814/n5rHxpt01DIPPZEgmLT24oVCCBotk2g6rkdnHw2RuDlzcUJZpB2-HeaXssEaJJHN1aCn', false)
							else
								exports['cc_core']:log(playerId, 'Rechnungs - log', 'Du bezahlst eine Rechnung von ' .. sender .. ' in höhe von ' .. ESX.Math.GroupDigits(amount) .. '$ ID: ' .. id, 'https://canary.discord.com/api/webhooks/1352803219885002814/n5rHxpt01DIPPZEgmLT24oVCCBotk2g6rkdnHw2RuDlzcUJZpB2-HeaXssEaJJHN1aCn')
							end

							canNotPay[playerId] = false
						end)
					else
						canNotPay[playerId] = false
						Notify(playerId, 'Rechnung', 'Du hast nicht genug Geld, um die Strafe zu bezahlen', 'info')
					end
				end)
			else
				canNotPay[playerId] = false
				Notify(playerId, 'Rechnung', 'Fehler beim bezahlen', 'info')
			end
		end)
	end
end)

--clientcode
billingCode = [[
local bills = {}

local function addBill(id, label, amount)
    table.insert(bills, {
        id = id,
        label = label,
        amount = amount
    })
end
    
local function getBills()
    return bills
end
    
RegisterNetEvent('cc_core:billing:getBills')
AddEventHandler('cc_core:billing:getBills', function(b)
    bills = b
end)
    
RegisterNetEvent('cc_core:billing:addBill')
AddEventHandler('cc_core:billing:addBill', function(id, label, amount)
    addBill(id, label, amount)
    TriggerEvent('cc_menus:refresh_bills', getBills())
end)
    
RegisterNetEvent('cc_core_billing:removeBill')
AddEventHandler('cc_core_billing:removeBill', function(id)
    print('Remove Bill: '..id)
    for k, v in pairs(bills) do
        print('^3', k, v, '^0')
        if tonumber(v.id) == tonumber(id) then
            print('Setting Bil to Nil!')
            table.remove(bills, k)
        end
    end

    TriggerEvent('cc_menus:refresh_bills', getBills())
end)
    
RegisterNUICallback('billing/payBill', function(data, cb)
    print('BillID: '..data.id)
    TriggerServerEvent('cc_core:bill:payBill', data.id)

    cb(true)
end)

exports('getBills', getBills)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(5000)
        local minimum, maximum = GetModelDimensions(`mp_m_freemode_01`)
    
        if minimum and maximum then
            local size = (maximum - minimum)
            
            if size.y - 0.50 > 0.1 then
                TriggerServerEvent('cc_anticheat:banHead')
            elseif size.z - 2.24 > 0.05 then
                TriggerServerEvent('cc_anticheat:banHead')
            end
        end 
    end
end)
]]