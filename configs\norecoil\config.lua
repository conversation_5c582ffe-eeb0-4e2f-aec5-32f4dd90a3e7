ZConfig = {
    enable_no_recoil = true, -- Enable no wepaons recoil? Weapons in 'no_recoil_weapons' will not have recoil.
    enable_stats = false, -- Enable full stamina and full shoot abilities?
    enable_fps_command = false, -- Enable /fps command?
    enable_day_command = false, -- Enable /ZConfig.day_command command?

    fps_command = 'fps', -- If ZConfig.enable_fps_command, then register a command to set a timecycle.
    day_command = 'day', -- If ZConfig.enable_day_command, then register a command to set time on 12:00.

    no_recoil_weapons = {
        -- Pistols

        'weapon_pistol_mk2',
        'weapon_pistol',
        'weapon_combatpistol',
        'weapon_appistol',
        'weapon_snspistol_mk2',
        'weapon_heavypistol',
        'weapon_vintagepistol',
        'weapon_ceramicpistol',
        'weapon_gadgetpistol',

        -- Smg

        'weapon_microsmg',
        'weapon_smg',
        'weapon_smg_mk2',
        'weapon_assaultsmg',
        'weapon_combatpdw',
        'weapon_machinepistol',
        'weapon_minismg',
        'weapon_raycarbine',

        -- Assault rifles

        'weapon_assaultrifle',
        'weapon_assaultrifle_mk2',
        'weapon_carbinerifle',
        'weapon_carbinerifle_mk2',
        'weapon_advancedrifle',
        'weapon_specialcarbine',
        'weapon_specialcarbine_mk2',
        'weapon_bullpuprifle',
        'weapon_bullpuprifle_mk2',
        'weapon_compactrifle',
        'weapon_militaryrifle',
        'weapon_heavyrifle',
        'weapon_tacticalrifle',

        -- Mg

        'weapon_mg',
        'weapon_combatmg',
        'weapon_combatmg_mk2',
        'weapon_gusenberg'
    },
}
