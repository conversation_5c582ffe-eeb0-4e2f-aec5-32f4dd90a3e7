local clothings = {}
local loaded = {}

MySQL.ready(function()
    local result = MySQL.query.await('SELECT * FROM user_outfits', {})
    
    for k, v in ipairs(result) do
        if clothings[v.identifier] == nil then
            clothings[v.identifier] = {}
        end

        clothings[v.identifier][#clothings[v.identifier] + 1] = {
            id = v.id,
            label = v.label,
            skin = v.skin
        }
    end
end)

RegisterNetEvent('cc_core:esx:playerLoaded', function(playerId)
    Citizen.Wait(12500)

    if not loaded[playerId] then
        loaded[playerId] = true

        local identifier = ESX.GetPlayerIdentifier(playerId)

        if identifier ~= nil then           
            if clothings[identifier] == nil then
                clothings[identifier] = {}
            end

            TriggerClientEvent('cc_core:clothing:sendClothing', playerId, clothings[identifier])
        end
    end
end)

AddEventHandler('playerDropped', function()
    local playerId = source

    if loaded[playerId] then
        loaded[playerId] = nil
    end
end)

RegisterServerEvent('cc_core:clothing:removeMoney')
AddEventHandler('cc_core:clothing:removeMoney', function(price)
    local playerId = source

    if ESX.GetPlayerMoney(playerId) >= price then
        ESX.RemovePlayerMoney(playerId, price, GetCurrentResourceName())
    elseif ESX.GetPlayerAccount(playerId, 'bank').money >= price then
        ESX.RemovePlayerAccountMoney(playerId, 'bank', price, GetCurrentResourceName())
    end
end)

RegisterNetEvent('cc_core:clothing:deleteClothing', function(id, label)
    local playerId = source
    local identifier = ESX.GetPlayerIdentifier(playerId)
    local Id = id

    for k, v in pairs(clothings[identifier]) do
        if v.id == Id then
            table.remove(clothings[identifier], k)
            break
        end
    end

    MySQL.update('DELETE FROM user_outfits WHERE label = ? AND identifier = ?', {
        label,
        identifier
    })
end)

RegisterNetEvent('cc_core:clothing:saveOutfit', function(label, skin)
    local playerId = source
    local identifier = ESX.GetPlayerIdentifier(playerId)
    local skin = json.encode(skin)

    MySQL.insert('INSERT INTO user_outfits (identifier, label, skin) VALUES (?, ?, ?)', {
        identifier,
        label,
        skin
    }, function(insertId)
        clothings[identifier][#clothings[identifier] + 1] = {
            id = insertId,
            label = label,
            skin = skin
        }

        TriggerClientEvent('cc_core:clothing:addClothing', playerId, insertId, label, skin)
    end)
end)

--clientcode
clothingCode = [[
local clothings = {}
local config = nil
local IsInUISHIT = false

RegisterNetEvent('cc_core:clothing:sendClothing', function(c)
    clothings = c
end)

RegisterNetEvent('cc_core:clothing:addClothing', function(id, label, skin)
    clothings[#clothings + 1] = {
        id = id,
        label = label,
        skin = skin,
    }
end)

RegisterNetEvent('cc_core:clothing:deleteClothing', function(id, label)
    TriggerServerEvent('cc_core:clothing:deleteClothing', id, label)

    for k, v in pairs(clothings) do
        if v.id == id then
            clothings[k] = nil
            break
        end
    end
end)

local function getClothings()
    return clothings
end

local function getSex(skin)
    for k, v in pairs(skin) do
        if v.name == "sex" then
            return v.value
        end
    end

    return false
end

local function offsetPosition(x, y, z, distance)
    local object = {
        x = x + math.sin(-z * math.pi / 180) * distance,
        y = y + math.cos(-z * math.pi / 180) * distance
    }

    return object
end

local function ZoomToClothe(zoom)
    DestroyCam(cam, true)
	
    cam = CreateCam('DEFAULT_SCRIPTED_CAMERA')
    
    local player = {
        position = GetEntityCoords(PlayerPedId())
    }
	
    local coordsCam = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 0.0)
	local coordsPly = GetEntityCoords(PlayerPedId())
    local offset = offsetPosition(player.position.x, player.position.y, 0.0, 1.0);
    local targetPositionFly = vector3(offset.x, offset.y, player.position.z + zoom)
    local targetPositionPoint = vector3(player.position.x, player.position.y, player.position.z + zoom)
    SetCamCoord(cam, targetPositionFly)
    PointCamAtCoord(cam, targetPositionPoint)
	SetCamActive(cam, true)
	RenderScriptCams(true, false, 500, true, true)
end

local function CreationCamHead(coords, heading)
    DestroyCam(cam, true)
    
    if coords ~= nil then
        SetEntityCoordsNoOffset(PlayerPedId(), coords)
    end

    if heading ~= nil then
        SetEntityHeading(PlayerPedId(), heading)
    end

	cam = CreateCam('DEFAULT_SCRIPTED_CAMERA')

    local player = {
        position = GetEntityCoords(PlayerPedId())
    }

	local coordsCam = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, 2.50, 0.0)
    local offset = offsetPosition(player.position.x, player.position.y, GetEntityHeading(PlayerPedId()), 2.10)
    local targetPositionFly = vector3(offset.x, offset.y, player.position.z + 1.0)
    local targetPositionPoint = vector3(player.position.x, player.position.y, player.position.z + 0.6)
    
    if targetPositionPoint == nil then 
        return 
    end

    SetCamCoord(cam, targetPositionFly)
    PointCamAtCoord(cam, targetPositionPoint)
	SetCamActive(cam, true)
	RenderScriptCams(true, false, 500, true, true)
end

function updatePrice(plus)
    price = 10
    local newPrice = price + plus
    SendNUIMessage({
        script = 'clothing',
        action = 'price',
        data = newPrice
    })
end

RegisterNUICallback("increasePrice", function(data, cb)
    updatePrice(10)
    cb({})
end)

AddEventHandler('esx:playerLoaded', function()
    local clothings = exports['cc_core']:getClothings()
    SendNUIMessage({
        script = 'clothing',
        action = 'update_outfits',
        outfits = clothings
    })
end)

-- local function OpenShopMenu(index)
--     local elements = {
--         { label = 'Kleidungsladen', value = 'shop' },
--         { label = 'Gespeicherte Outfits', value = 'saved_cloth' },
--         { label = 'Gespeicherte Outfits Entfernen', value = 'delete_cloth' }
--     }
--     local clothings = exports['cc_core']:getClothings()

--     ESX.UI.Menu.CloseAll()

--     ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'shop_main', {
--         title = "Kleidungsladen",
--         align = 'top-left',
--         elements = elements,
--     }, function(data, menu)
--         menu.close()

--         if data.current.value == 'shop' then
--             SetNuiFocus(true, true)
            
--             isInUI = true
--             IsInUISHIT = true

--             SendNUIMessage({
--                 script = 'clothing',
--                 action = 'show',
--                 outfits = clothings
--             })

--             CreationCamHead(Config_Clothing.Positions[index].coords, Config_Clothing.Positions[index].heading)
--         elseif data.current.value == 'saved_cloth' then
--             local elements = {}

--             for k, v in pairs(clothings) do
--                 elements[#elements + 1] = {
--                     label = v.label,
--                     value = k
--                 }
--             end

--             if #elements ~= 0 then
--                 ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'player_dressing', {
--                     title = "Kleidungsladen",
--                     align = 'top-left',
--                     elements = elements,
--                 }, function(data, menu)
--                     TriggerEvent('skinchanger:getSkin', function(skin)
--                         local clothes = clothings[tonumber(data.current.value)]

--                         TriggerEvent('skinchanger:loadClothes', skin, json.decode(clothes.skin))
--                         TriggerEvent('cc_core:skin:setLastSkin', skin)
                
--                         TriggerEvent('skinchanger:getSkin', function(skin)
--                             TriggerServerEvent('cc_core:skin:save', skin)
--                         end)

--                         Notify('Kleidungs - System', 'Outfit angezogen', 'info')
--                     end)
--                 end, function(data, menu)
--                     menu.close()
--                 end)
--             else
--                 Notify('Kleidungs - System', 'Du hast keine Kleidungen!', 'info')
--             end
--         elseif data.current.value == 'delete_cloth' then
--             local clothings = exports['cc_core']:getClothings()
--             local elements = {}

--             for k, v in pairs(clothings) do
--                 elements[#elements + 1] = {
--                     label = v.label,
--                     id = v.id
--                 }
--             end

--             if #elements ~= 0 then
--                 ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'supprime_cloth', {
--                     title = "Welches Outfit löschen?",
--                     align = 'top-left',
--                     elements = elements,
--                 }, function(data, menu)
--                     print(data.current.id)
--                     menu.close()
--                     TriggerEvent('cc_core:clothing:deleteClothing', tonumber(data.current.id), data.current.label)
--                     Notify('Clothing', 'Das Outfit wurde entfernt', 'info')
--                 end, function(data, menu)
--                     menu.close()
--                 end)
--             else
--                 Notify('Kleidungs - System', 'Du hast keine Kleidungen!', 'info')
--             end
--         end
--     end, function(data, menu)
--         menu.close()
--     end)
-- end

RegisterNUICallback('clothing/loadClothes', function(data, cb)
    config = Config_Clothing.Parts[data.type]

    TriggerEvent('skinchanger:getData', function(skin, maxValues)
        local current_value, current_var, current_label = 0, 0, ''
        for k, v in pairs(skin) do 
            if v.name == config.var_id then
                current_value = v.value
                current_label = v.label
            elseif v.name == config.color_id then
                if config.color_id == 'arms' then
                    return
                end
                
                current_var = v.value
            end
        end

        Citizen.Wait(100)

        TriggerEvent('skinchanger:getData', function(skin, maxValues)
            for k, v in pairs(skin) do
                if v.name == config.var_id then
                    ZoomToClothe(v.camOffset)
                end
            end
        end)

        cb({
            value_max = maxValues[config.var_id],
            var_max = maxValues[config.color_id],
            current_value = current_value,
            current_var = current_var,
            current_label = current_label
        })
    end)
end)

RegisterNUICallback('clothing/setClothes', function(data, cb)
    TriggerEvent('skinchanger:change', config.var_id, tonumber(data.value))

    TriggerEvent('skinchanger:getData', function(skin, maxValues)                
        if config.var_id == 'torso_1' then
            if Config_Clothing.MaleTorsoData[tonumber(data.value)] ~= nil then
                TriggerEvent('skinchanger:change', "arms", Config_Clothing.MaleTorsoData[tonumber(data.value)].arms)
            end
        end

        cb({
            value_max = maxValues[config.var_id],
            var_max = maxValues[config.color_id],
        })
    end)
end)

RegisterNUICallback('clothing/setVar', function(data, cb)
    TriggerEvent('skinchanger:change', config.color_id, tonumber(data.var))

    TriggerEvent('skinchanger:getData', function(skin, maxValues)
        if config.color_id == "arms" then 
            return 
        end
        
        cb({
            value_max = maxValues[config.var_id],
            var_max = maxValues[config.color_id],
        })
    end)
end)

RegisterNUICallback('clothing/buyCloth', function(data, cb)
    SetNuiFocus(true, true)
    SendNUIMessage({
        script = 'clothing',
        action = 'showsafe'
    })

    isInUI = true
    IsInUISHIT = true
end)

RegisterNUICallback('clothing/saveOutfit', function(data, cb)
    SetNuiFocus(false, false)
    RenderScriptCams(0)
    isInUI = false
    IsInUISHIT = false

    hideHud(false)

    local outfitName = data.name

    TriggerEvent('skinchanger:getSkin', function(skin)
        TriggerServerEvent('cc_core:skin:save', skin)
        TriggerServerEvent('cc_core:clothing:removeMoney', 100)
        TriggerServerEvent('cc_core:clothing:saveOutfit', outfitName, skin)
    end)

    Notify('clothing', 'Outfit gespeichert', 'success')
end)

local Player = {
	isDead = false,
	inAnim = false,
	crouched = false,
	handsUp = false,
	pointing = false,
}

RegisterNUICallback('clothing/handsup', function(data, cb)
	local plyPed = PlayerPedId()
	if DoesEntityExist(plyPed) and not IsEntityDead(plyPed) and IsPedOnFoot(plyPed) then
		Player.handsUp = not Player.handsUp
		if Player.handsUp then
			RequestAnimDict('random@mugging3')
			while not HasAnimDictLoaded('random@mugging3') do Citizen.Wait(0) end
			TaskPlayAnim(plyPed, 'random@mugging3', 'handsup_standing_base', 8.0, -8, -1, 49, 0, 0, 0, 0)
		else
			ClearPedSecondaryTask(plyPed)
		end
	end
end)

RegisterNUICallback('clothing/escape', function(data, cb)
    SetNuiFocus(false, false)
    RenderScriptCams(0)

    isInUI = false
    IsInUISHIT = false

    hideHud(false)

    ESX.TriggerServerCallback('cc_core:skin:getPlayerSkin', function(skin) 
        TriggerEvent('skinchanger:loadSkin', skin) 
    end)
end)

RegisterNUICallback('clothing/rotate', function(data, cb)
    SetEntityHeading(PlayerPedId(), data.heading + 0.0)
end)

Citizen.CreateThread(function()
    for k, v in pairs(Config_Clothing.Positions) do
        local blip = AddBlipForCoord(v.coords)
		SetBlipSprite(blip, 73)
		SetBlipDisplay(blip, 4)
		SetBlipScale(blip, 0.7)
		SetBlipColour(blip, 47)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString("Kleidungsladen")
		EndTextCommandSetBlipName(blip)
    end
end)

local show = false

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        local letSleep, inRange = true, false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local clothings = exports['cc_core']:getClothings()

        for k, v in pairs(Config_Clothing.Positions) do
            local distance = #(coords - v.coords)

            if distance < 3.0 then
                letSleep = false
                inRange = true
                
                if IsControlJustReleased(0, 38) then
                    SetNuiFocus(true, true)
                    
                    isInUI = true
                    IsInUISHIT = true

                    SendNUIMessage({
                        script = 'clothing',
                        action = 'show',
                        outfits = clothings
                    })

                    CreationCamHead(Config_Clothing.Positions[k].coords, Config_Clothing.Positions[k].heading)
                end
            end
        end

        helpNotify(inRange, show, 'Drücke E um dich umzuziehen', function(bool)
            show = bool
        end)

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

local showA = false

-- Citizen.CreateThread(function()
--     while true do
--         Citizen.Wait(250)

--         if IsInUISHIT then
--             for k, v in pairs(GetActivePlayers()) do
--                 local ped = GetPlayerPed(v)
--                 local coords = GetEntityCoords(ped)
--                 local distance = #(GetEntityCoords(PlayerPedId()) - coords)
                
--                 if ped ~= PlayerPedId() then
--                     if distance < 5 then
--                         SetEntityAlpha(ped, 0, false)
--                     else
--                         ResetEntityAlpha(ped)
--                     end     
--                 end
--             end

--             showA = true
--         elseif showA and not IsInUISHIT then
--             showA = false
            
--             for k, v in pairs(GetActivePlayers()) do
--                 local ped = GetPlayerPed(v)

--                 ResetEntityAlpha(ped)
--             end
--         else
--             Citizen.Wait(1000)
--         end
--     end
-- end)

RegisterNUICallback('clothing/rotate', function(data, cb)
  SetEntityHeading(PlayerPedId(), data.heading + 0.0)
end)

local function w2s(position)
  local onScreen, _x, _y = GetScreenCoordFromWorldCoord(position.x, position.y, position.z)

  if not onScreen then
    return nil
  end

  return vector3((_x - 0.5) * 2, (_y - 0.5) * 2, 0.0)
end

local function degToRad(deg)
  return (deg * math.pi) / 180.0
end

local function rotationToDirection(rotation)
  local z = degToRad(rotation.z)
  local x = degToRad(rotation.x)
  local num = math.abs(math.cos(x))

  return vector3((-math.sin(z) * num), math.cos(z) * num, math.sin(x))
end

local function s2w(camPos, relX, relY, cam)
  local cameraRotation = GetCamRot(cam, 2)
  local cameraForward = rotationToDirection(cameraRotation)
  local rotUp = (cameraRotation + vector3(10.0, 0.0, 0.0))
  local rotDown = (cameraRotation + vector3(-10.0, 0.0, 0.0))
  local rotLeft = (cameraRotation + vector3(0.0, 0.0, -10.0))
  local rotRight = (cameraRotation + vector3(0.0, 0.0, 10.0))

  local camRight = (rotationToDirection(rotRight) - rotationToDirection(rotLeft))
  local camUp = (rotationToDirection(rotUp) - rotationToDirection(rotDown))

  local rollRad = -degToRad(cameraRotation.y)
  local camRightRoll = ((camRight * math.cos(rollRad)) - (camUp * math.sin(rollRad)))
  local camUpRoll = ((camRight * math.sin(rollRad)) + (camUp * math.cos(rollRad)))

  local point3D = (((camPos + (cameraForward * 10.0)) + camRightRoll) + camUpRoll)

  local point2D = w2s(point3D)

  if point2D == nil then
    return (camPos + (cameraForward * 10.0))
  end

  local point3DZero = (camPos + (cameraForward * 10.0))
  local point2DZero = w2s(point3DZero)

  if point2DZero == nil then
    return (camPos + (cameraForward * 10.0))
  end

  local eps = 0.001

  if math.abs(point2D.x - point2DZero.x) < eps or math.abs(point2D.y - point2DZero.y) < eps then
    return (camPos + (cameraForward * 10.0))
  end

  local scaleX = (relX - point2DZero.x) / (point2D.x - point2DZero.x)
  local scaleY = (relY - point2DZero.y) / (point2D.y - point2DZero.y)
  local point3Dret = (((camPos + (cameraForward * 10.0)) + (camRightRoll * scaleX)) + (camUpRoll * scaleY))

  return point3Dret
end

local function processCoordinates(x, y)
  local screenX, screenY = GetActiveScreenResolution()
  local relativeX = 1 - (x / screenX) * 1.0 * 2
  local relativeY = 1 - (y / screenY) * 1.0 * 2

  if relativeX > 0.0 then
    relativeX = -relativeX;
  else
    relativeX = math.abs(relativeX)
  end

  if relativeY > 0.0 then
    relativeY = -relativeY
  else
    relativeY = math.abs(relativeY)
  end

  return { x = relativeX, y = relativeY }
end

local function screenToWorld(flags, cam)
  local x, y = GetNuiCursorPosition()

  local absoluteX = x
  local absoluteY = y

  local camPos = GetGameplayCamCoord()
  camPos = GetCamCoord(cam)
  local processedCoords = processCoordinates(absoluteX, absoluteY)
  local target = s2w(camPos, processedCoords.x, processedCoords.y, cam)

  local dir = (target - camPos)
  local from = (camPos + (dir * 0.05))
  local to = (camPos + (dir * 300))

  local ray = StartShapeTestRay(from.x, from.y, from.z, to.x, to.y, to.z, flags, ignore, 0)
  local a, b, c, d, e = GetShapeTestResult(ray)
  return b, c, e, to
end

local function GetEntityMouseOn(cam)
  local hit, endCoords, entityHit, _ = screenToWorld(10, cam)
  return hit, endCoords, entityHit
end

RegisterNUICallback('clothing/mouseup', function(data, cb)
    isRotatingMouseDown = false
end)

RegisterNUICallback('clothing/mousedown', function(data, cb)
    -- print('da')
    local found, coords, mouseon = GetEntityMouseOn(cam)
    local ped = PlayerPedId()

    if not found then
      return false
    end

    if mouseon == ped then
      -- print('da')
      isRotatingMouseDown = true
      local currentEntityHeading = GetEntityHeading(ped)
      lastX = GetNuiCursorPosition()
      local currentX = lastX

      CreateThread(function()
        while isRotatingMouseDown do
          currentX = GetNuiCursorPosition()
          local diff = (currentX - lastX) * 0.3
          local newHeading

          if diff < 0 then
            newHeading = currentEntityHeading + diff
          elseif diff > 0 then
            newHeading = currentEntityHeading + diff
          end

          if newHeading and currentEntityHeading ~= newHeading then
            SetEntityHeading(ped, newHeading + 0.0)
            currentEntityHeading = newHeading
          end

          lastX = currentX
          Wait(0)
        end
      end)
    end
end)

RegisterNUICallback('clothing/applyOutfit', function(data, cb)
    local clothings = exports['cc_core']:getClothings()
    local outfit = clothings[tonumber(data.index)]

    if outfit then
        TriggerEvent('skinchanger:getSkin', function(currentSkin)
            TriggerEvent('skinchanger:loadClothes', currentSkin, json.decode(outfit.skin))

            Citizen.Wait(100)

            TriggerEvent('skinchanger:getSkin', function(updatedSkin)
                TriggerEvent('cc_core:skin:setLastSkin', updatedSkin)
                TriggerServerEvent('cc_core:skin:save', updatedSkin)
            end)
        end)
    end

    cb({})
end)

RegisterNUICallback('clothing/deleteOutfit', function(data, cb)
    TriggerEvent('cc_core:clothing:deleteClothing', data.id, 'N/A')
    cb({})
end)

exports('getClothings', getClothings)
]]