RegisterNetEvent('panicbutton:sendLocation')
AddEventHandler('panicbutton:sendLocation', function(position, fib)
    local src = source
    local playerName = ESX.GetPlayerRPName(source)
    local police = {}
    local xPlayers = exports['cc_core']:GetPlayersFix()
  
    for k, v in pairs(xPlayers) do
        if v.job == 'police' or v.job == 'doj' or v.job == 'fib' or v.job == 'sheriff' then 
            if fib then 
                TriggerClientEvent('panicbutton:showLocation', v.playerId, position, playerName, true)
            else
                TriggerClientEvent('panicbutton:showLocation', v.playerId, position, playerName, false)
            end
        end
    end
end)

RegisterServerEvent('panicbutton:verify')
AddEventHandler('panicbutton:verify', function()
	local item = ESX.GetPlayerInventoryItem(source, 'panicbutton').count
	
	if item > 0 then
        check = 1
		TriggerClientEvent("panicbutton:verifycheck", source, check)	
	else
        check = 0
		TriggerClientEvent("panicbutton:verifycheck", source, check)	
	end
end)

--clientcode
panicCode = [[
ESX = nil

CreateThread(function()
    -- Warten, bis ESX geladen ist
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) 
            ESX = obj
        end)
        Wait(10)
    end

    -- Sicherstellen, dass PlayerData verfügbar ist, nachdem ESX geladen wurde
    while ESX.GetPlayerData().job == nil do
        Wait(100)
    end

    PlayerData = ESX.GetPlayerData()
end)

AddEventHandler('esx:onPlayerDeath', function(data)
    isDead = true
end)

function playSound()
    PlaySoundFrontend(-1, "TIMER_STOP", "HUD_MINI_GAME_SOUNDSET", 1)
    Wait(900)
    PlaySoundFrontend(-1, "TIMER_STOP", "HUD_MINI_GAME_SOUNDSET", 1)
    Wait(900)
    PlaySoundFrontend(-1, "TIMER_STOP", "HUD_MINI_GAME_SOUNDSET", 1)
end

RegisterNetEvent('panicbutton:showLocation')
AddEventHandler('panicbutton:showLocation', function(position, playerName, anonym)
    local targetPlayerId = GetPlayerServerId(PlayerId())

    local blip = AddBlipForCoord(position.x, position.y, position.z)
    SetBlipSprite(blip, 161)
    SetBlipScale(blip, 1.5)
    SetBlipColour(blip, 3)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Panic Button")
    EndTextCommandSetBlipName(blip)

    alerteinprogress = true

    playSound()

    if anonym then 
        Notify('Panicbutton', 'Ein Panicbutton wurde gedrückt', 'success')
    else
        Notify('Panicbutton', playerName .. ' hat einen Panicbutton gedrückt', 'success')
    end

    SetNewWaypoint(position.x, position.y)

    Wait(timeactive)
    alerteinprogress = false
    RemoveBlip(blip)
end)

canPressPanicButton = true

RegisterNetEvent('panicbutton:verifycheck')
AddEventHandler('panicbutton:verifycheck', function(check)
    if check == 1 then
        if canPressPanicButton then
            canPressPanicButton = false
            ESX.SetTimeout(30000, function()
                canPressPanicButton = true
            end)
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)
            local position = {x = coords.x, y = coords.y, z = coords.z}

            -- start anim
            RequestAnimDict("reaction@intimidation@1h")
            while not HasAnimDictLoaded("reaction@intimidation@1h") do
                Wait(100)
            end
            TaskPlayAnim(PlayerPedId(), "reaction@intimidation@1h", "intro", 8.0, -8.0, 700, 0, 0)

            -- end anim

            if ESX.PlayerData.job.name == 'fib' then
                TriggerServerEvent('panicbutton:sendLocation', position, true)
            else
                TriggerServerEvent('panicbutton:sendLocation', position, false)
            end

            Notify('Panicbutton', 'Panicbutton betätigt', 'success')
        else
            Notify('Panicbutton', 'Du kannst den Panicbutton nur alle 30 Sekunden drücken', 'error')
        end
    else
        Notify('Panicbutton', 'Du hast keinen Panicbutton bei dir', 'error')
    end
end)

RegisterKeyMapping('panicbutton', 'Panicbutton', 'keyboard', 'F9')

RegisterCommand("panicbutton", function()
    if PlayerData.job ~= nil and (PlayerData.job.name == 'police' or PlayerData.job.name == 'doj' or PlayerData.job.name == 'fib' or PlayerData.job.name == 'sheriff') then
        if not isDead then
            TriggerServerEvent("panicbutton:verify")
        else 
            Notify('Panicbutton', 'Du kannst gefesselt/bewusstlos keinen Panicbutton betätigen', 'error')
        end
    end
end)
]]