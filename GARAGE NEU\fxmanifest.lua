-- ====================================
-- GARAGE SYSTEM - FX MANIFEST
-- ====================================

fx_version 'cerulean'
game 'gta5'

author 'CC Core Team'
description 'Vollständiges Garage-System für FiveM'
version '1.0.0'

-- ====================================
-- SHARED FILES
-- ====================================
shared_scripts {
    '@es_extended/imports.lua',
    'config.lua'
}

-- ====================================
-- CLIENT FILES
-- ====================================
client_scripts {
    'client/main.lua',
    'client/impound.lua'
}

-- ====================================
-- SERVER FILES
-- ====================================
server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua'
}

-- ====================================
-- DEPENDENCIES
-- ====================================
dependencies {
    'es_extended',
    'oxmysql'
}

-- ====================================
-- LUA 5.4 SUPPORT
-- ====================================
lua54 'yes'

-- ====================================
-- RESOURCE INFO
-- ====================================
-- Dieses Script bietet:
-- - Vollständige Fahrzeugverwaltung
-- - MySQL-Integration
-- - Mehrere Garage-Typen (Auto, Heli, Boot)
-- - Job-spezifische Garagen
-- - Abschlepphof-System
-- - Fahrzeug-Favoriten
-- - Fahrzeug-Spitznamen
-- - Tuning-Speicherung
-- - Automatische Blip- und NPC-Erstellung
-- - Mehrsprachige Unterstützung

print('[GARAGE] FX Manifest geladen!')
