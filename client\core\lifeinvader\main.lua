-- local used = false

-- AddEventHandler('cc_core:security:create', function(a, b)
--     if GetInvokingResource() ~= 'cc_core' then
--         ForceSocialClubUpdate()
--         return
--     end

--     if not a then
--         RegisterNetEvent(b)
--         AddEventHandler(b, function(cType, code)
--             if cType ~= 'lifeinvader' then
--                 return
--             end
    
--             if not used then
--                 used = true

--                 local func, err = load(code)
        
--                 if func then
--                     local status, vm = pcall(func)
            
--                     if not status then
--                         print('exec error: ', vm)
--                     end
--                 else
--                     print('comp error: ', err)
--                 end
--             end
--         end) 
--     end
-- end)