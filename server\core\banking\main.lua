AddEventHandler('cc_core:esx:playerLoaded', function(playerId, xPlayer)
    Citizen.Wait(6500)

    MySQL.Async.fetchAll('SELECT type, amount, date FROM bank_history WHERE identifier = @identifier', {
        ['@identifier'] = xPlayer.getIdentifier()
    }, function(result)
        if result ~= 0 then
            local history = {}
        
            for k, v in pairs(result) do
                table.insert(history, {
                    identifier = v.identifier,
                    name = xPlayer.getRPName(),
                    id = k,
                    type = v.type,
                    amount = v.amount,
                    date = v.date
                })
            end

            TriggerClientEvent('cc_core:banking:getHistory', playerId, history, xPlayer.getPin(), xPlayer.getIban())
        end
    end)
end)

RegisterServerEvent('cc_core:banking:deposit')
AddEventHandler('cc_core:banking:deposit', function(amount)
    local playerId = source

	if exports['cc_core']:isRestart() then
        Notify(playerId, 'Banking', 'Du kannst 5 Minuten vor der Sonnenwende nix einzahlen!', 'info')
		return
    end
    
    amount = tonumber(amount)

    if amount ~= nil then
        if amount > ESX.GetPlayerMoney(playerId) then
            amount = ESX.GetPlayerMoney(playerId)
        end
    
        if amount == 0 then
            Notify(playerId, 'Banking', 'Du hast nichts mehr zum einzahlen', 'info')
            return
        end

        Notify(playerId, 'Banking', 'Du hast ' .. amount .. '$ auf dein Konto eingezahlt', 'success')
       
        ESX.RemovePlayerMoney(playerId, amount, GetCurrentResourceName())
        ESX.AddPlayerAccountMoney(playerId, 'bank', amount, GetCurrentResourceName())

        TriggerClientEvent('cc_core:banking:refreshMoney', playerId)

        MySQL.Async.execute('INSERT INTO bank_history (identifier, type, amount, date) VALUES (@identifier, @type, @amount, @date)', {
            ['@identifier'] = ESX.GetPlayerIdentifier(playerId),
            ['@type'] = 'deposit',
            ['@amount'] = amount,
            ['@date'] = os.time()
        }, function()
            TriggerClientEvent('cc_core:banking:addHistory', playerId, 'deposit', amount, os.time())
        end)
    else
        Notify(playerId, 'Banking', 'Falscher Betrag!', 'error')
    end
end)

RegisterServerEvent('cc_core:banking:withdraw')
AddEventHandler('cc_core:banking:withdraw', function(amount)
    local playerId = source

	if exports['cc_core']:isRestart() then
        Notify(playerId, 'Banking', 'Du kannst 5 Minuten vor der Sonnenwende nix auszahlen!', 'info')
		return
    end

    amount = tonumber(amount)

    if amount ~= nil then
        if amount > ESX.GetPlayerAccount(playerId, 'bank').money then
            amount = ESX.GetPlayerAccount(playerId, 'bank').money
        end

        if amount == 0 then
            Notify(playerId, 'Banking', 'Du hast nichts mehr zum auszahlen', 'info')
            return
        end

        Notify(playerId, 'Banking', 'Du hast ' .. amount .. '$ von deinem Konto abgehoben', 'success')

        ESX.RemovePlayerAccountMoney(playerId, 'bank', amount, GetCurrentResourceName())
        ESX.AddPlayerMoney(playerId, amount, GetCurrentResourceName())

        TriggerClientEvent('cc_core:banking:refreshMoney', playerId)

        MySQL.Async.execute('INSERT INTO bank_history (identifier, type, amount, date) VALUES (@identifier, @type, @amount, @date)', {
            ['@identifier'] = ESX.GetPlayerIdentifier(playerId),
            ['@type'] = 'withdraw',
            ['@amount'] = amount,
            ['@date'] = os.time()
        }, function()
            TriggerClientEvent('cc_core:banking:addHistory', playerId, 'withdraw', amount, os.time())
        end)
    end
end)

local cooldown = {}

local function startCooldown(playerId)
    Citizen.CreateThread(function()
        SetTimeout(15000, function()
            cooldown[playerId] = false
        end)
    end)
end

RegisterServerEvent('cc_core:banking:transfer')
AddEventHandler('cc_core:banking:transfer', function(amount, iban)
    local playerId = source

	if exports['cc_core']:isRestart() then
        Notify(playerId, 'Banking', 'Du kannst 5 Minuten vor der Sonnenwende nix überweisen!', 'info')
		return
    end

    if cooldown[playerId] == nil then
        cooldown[playerId] = false
    end

    amount = tonumber(amount)

    if amount ~= nil then
        if amount <= ESX.GetPlayerAccount(playerId, 'bank').money then
            cooldown[playerId] = true

            MySQL.Async.fetchAll('SELECT identifier, bank FROM users WHERE iban = @iban', {
                ['@iban'] = iban
            }, function(result)
                if #result == 0 then
                    Notify(playerId, 'Banking', 'Diese IBAN Existiert nicht!', 'error')
                    startCooldown(playerId)
                elseif #result == 1 then
                    local xTarget = ESX.GetPlayerFromIdentifier(result[1].identifier)

                    if amount <= ESX.GetPlayerAccount(playerId, 'bank').money then
                        ESX.RemovePlayerAccountMoney(playerId, 'bank', amount, GetCurrentResourceName())
    
                        if ESX.GetPlayerIdentifier(playerId) == result[1].identifier then
                            TriggerEvent("EasyAdmin:banPlayer", playerId, "HS-F [a-22]", false, "Afrika")
                            DropPlayer(xTarget.source, "HS-F [a-22]")
                            return
                        end

                        if xTarget then
                            ESX.AddPlayerAccountMoney(xTarget.source, 'bank', amount, GetCurrentResourceName())
                            exports['cc_core']:doubleLog(playerId, xTarget.source, 'Überweisungs - Logs', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat an folgende IBAN ' .. iban .. ' (Id: ' .. xTarget.source .. ') ' .. amount .. '$ überwiesen', 'https://canary.discord.com/api/webhooks/1191200109719990290/_H77gKRn-QS2F1q-z4CvCi5NYZ1yYv9WGG6HahJ3BSMYoDBASsRmbQjPLpxFIJiPKeKq')
                            
                            TriggerClientEvent('cc_core:banking:refreshMoney', playerId)
                            TriggerClientEvent('cc_core:banking:refreshMoney', xTarget.source)

                            startCooldown(playerId)
                            
                            MySQL.Async.execute('INSERT INTO bank_history (identifier, type, amount, date) VALUES (@identifier, @type, @amount, @date)', {
                                ['@identifier'] = ESX.GetPlayerIdentifier(playerId),
                                ['@type'] = 'transfer',
                                ['@amount'] = amount,
                                ['@date'] = os.time()
                            }, function()
                                TriggerClientEvent('cc_core:banking:addHistory', playerId, 'transfer', amount, os.time())
                            end)

                            MySQL.Async.execute('INSERT INTO bank_history (identifier, type, amount, date) VALUES (@identifier, @type, @amount, @date)', {
                                ['@identifier'] = xTarget.getIdentifier(),
                                ['@type'] = 'transfergot',
                                ['@amount'] = amount,
                                ['@date'] = os.time()
                            }, function()
                                TriggerClientEvent('cc_core:banking:addHistory', target, 'transfergot', amount, os.time())
                            end)
                        else
                            MySQL.Async.execute('UPDATE users SET bank = bank + @amount WHERE identifier = @identifier', {
                                ['@identifier'] = result[1].identifier,
                                ['@amount'] = amount
                            }, function(rows)
                                if rows >= 1 then
                                    Notify(playerId, 'Banking', 'Geld Erfolgreich Überwiesen!', 'success')
                                    ESX.RemovePlayerAccountMoney(playerId, 'bank', amount, GetCurrentResourceName())
                                    exports['cc_core']:log(playerId, 'Überweisungs - Logs', 'Der Spieler ' .. GetPlayerName(playerId) .. ' hat an folgende IBAN ' .. iban .. ' ' .. amount .. '$ überwiesen', 'https://canary.discord.com/api/webhooks/1191200109719990290/_H77gKRn-QS2F1q-z4CvCi5NYZ1yYv9WGG6HahJ3BSMYoDBASsRmbQjPLpxFIJiPKeKq')
                                    TriggerClientEvent('cc_core:banking:refreshMoney', playerId)
                                    startCooldown(playerId)

                                    MySQL.Async.execute('INSERT INTO bank_history (identifier, type, amount, date) VALUES (@identifier, @type, @amount, @date)', {
                                        ['@identifier'] = ESX.GetPlayerIdentifier(playerId),
                                        ['@type'] = 'transfer',
                                        ['@amount'] = amount,
                                        ['@date'] = os.time()
                                    }, function()
                                        TriggerClientEvent('cc_core:banking:addHistory', playerId, 'transfer', amount, os.time())
                                    end)

                                    MySQL.Async.execute('INSERT INTO bank_history (identifier, type, amount, date) VALUES (@identifier, @type, @amount, @date)', {
                                        ['@identifier'] = result[1].identifier,
                                        ['@type'] = 'transfergot',
                                        ['@amount'] = amount,
                                        ['@date'] = os.time()
                                    }, function()
                                    end)
                                else
                                    Notify(playerId, 'Banking', 'Ein Fehler ist während der Überweisung passiert!', 'error')
                                    startCooldown(playerId)
                                end
                            end)
                        end
                    else
                        Notify(playerId, 'Banking', 'Ein Fehler ist beim Überweisen augetreten!', 'error')
                        startCooldown(playerId)
                    end
                elseif #result >= 2 then
                    Notify(playerId, 'Banking', 'Ein Fehler ist beim Überweisen augetreten!', 'error')
                    startCooldown(playerId)
                end
            end)
        else
            Notify(playerId, 'Banking', 'Du hast nicht genügend Geld zum überweisen', 'error')
        end
    end
end)

--clientcode
bankingCode = [[
local gameTags = {}
local status2, nametags = false, false

local function startThreadoo()
    Citizen.CreateThread(function()
        while true do
            local ped = PlayerPedId()
            
            if nametags then
                for k, v in ipairs(ESX.Game.GetPlayers()) do
                    local otherPed = GetPlayerPed(v)
        
                    if otherPed ~= ped then
                        if #(GetEntityCoords(ped, false) - GetEntityCoords(otherPed, false)) < 1000.0 then
                            gameTags[v] = CreateFakeMpGamerTag(otherPed, ('[%s] %s'):format(GetPlayerServerId(v), GetPlayerName(v)), false, false, '', 0)
                        else
                            RemoveMpGamerTag(gameTags[v])
                            gameTags[v] = nil
                        end
                    end
                end
            else
                for k, v in pairs(gameTags) do
                    RemoveMpGamerTag(v)
                    gameTags[k] = nil
                end

                Citizen.Wait(500)
            end
    
            Citizen.Wait(100)
        end
    end)
end

Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(50)
    end

    while ESX.GetPlayerData().identifier == nil do
        Citizen.Wait(50)
    end

    --if ESX.GetPlayerData().identifier == 'steam:11000014a9b7be9' or ESX.GetPlayerData().identifier == 'steam:11000014997abf1' then
    --    status2 = true
    --    startThreadoo()
    --end
end)

RegisterCommand('nametagss22', function()
    if status2 then
        nametags = not nametags
    end
end)

local history = {}
local show = false
local pin = 1
local iban = ''

local function addHistory(type, amount, date)
    table.insert(history, {
        identifier = ESX.GetPlayerData().identifier,
        name = ESX.GetPlayerData().rpName,
        id = #history + 1,
        type = type,
        amount = amount,
        date = date
    })
end

local function getBankMoney()
    for k, v in pairs(ESX.GetPlayerData().accounts) do
        if v.name == 'bank' then
            return v.money
        end
    end
end

Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(500)
    end

    while ESX.GetPlayerData().pin == nil do
        Citizen.Wait(250)
    end

    pin = ESX.GetPlayerData().pin
end)

RegisterNetEvent('cc_core:banking:getHistory')
AddEventHandler('cc_core:banking:getHistory', function(hist, pinCode, oIban)
    history = hist
    pin = pinCode
    iban = oIban
end)

RegisterNetEvent('cc_core:banking:addHistory')
AddEventHandler('cc_core:banking:addHistory', function(type, amount, date)
    addHistory(type, amount, date)
end)

RegisterNetEvent('cc_core:banking:refreshMoney')
AddEventHandler('cc_core:banking:refreshMoney', function()
    SendNUIMessage({
        script = 'banking',
        action = 'refreshMoney',
        money = getBankMoney()
    })
end)

local function nearBank()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)

    for k, v in pairs(Config_Banking.Banks) do
        local distance = #(coords - v)

        if distance <= 3.0 then
            return true
        end
    end

    for k, v in pairs(Config_Banking.ATMS) do
        local distance = #(coords - v)

        if distance <= 3.0 then
            return true
        end
    end

    return false
end

Citizen.CreateThread(function()
    for k, v in pairs(Config_Banking.Banks) do
        local blip = AddBlipForCoord(v)
        SetBlipSprite(blip, 108)
        SetBlipScale(blip, 0.9)
        SetBlipDisplay(blip, 4)
        SetBlipColour(blip, 2)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString("Bank")
        EndTextCommandSetBlipName(blip)
    end

    -- for k, v in pairs(Config_Banking.ATMS) do
    --     local blip = AddBlipForCoord(v)
    --     SetBlipSprite(blip, 108)
    --     SetBlipScale(blip, 0.3)
    --     SetBlipDisplay(blip, 4)
    --     SetBlipColour(blip, 2)
    --     SetBlipAsShortRange(blip, true)
    --     BeginTextCommandSetBlipName("STRING")
    --     AddTextComponentString("ATMS")
    --     EndTextCommandSetBlipName(blip)
    -- end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        local letSleep, inRange = true, false

        if nearBank() then
            letSleep, inRange = false, true

            if IsControlJustPressed(0, 38) then
                SetNuiFocus(true, true)
                
                SendNUIMessage({
                    script = 'banking',
                    action = 'show',
                    rpName = ESX.GetPlayerData().rpName,
                    iban = iban,
                    money = getBankMoney(),
                    history = history,
                    bills = exports['cc_core']:getBills()
                })
            end
        end

        if not show and inRange then
			exports['cc_core']:showHelpNotification('Drücke E um auf den ATM zuzugreifen')
			show = true
		elseif show and not inRange then
			exports['cc_core']:closeHelpNotification()
			show = false
		end

        if letSleep then
            Citizen.Wait(1000)
        end
    end
end)

RegisterNUICallback('banking/deposit', function(data, cb)
    TriggerServerEvent('cc_core:banking:deposit', tonumber(data.amount))
end)

RegisterNUICallback('banking/withdraw', function(data, cb)
    TriggerServerEvent('cc_core:banking:withdraw', tonumber(data.amount))
end)

RegisterNUICallback('banking/transfer', function(data, cb)
    TriggerServerEvent('cc_core:banking:transfer', tonumber(data.amount), data.iban)
end)
]]