ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

local VehiclePlaylists = {}

ESX.RegisterServerCallback("cc_core:carpanel:isInstalled", function(source, cb, plate)
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.fetchScalar('SELECT installed FROM vehicle_carpanel WHERE plate = @plate', {
        ['@plate'] = plate
    }, function(installed)
        if installed then
            cb(true)
        else
            if Config_CarPanel.General.require_installation then
                local hasCarpanelItem = false
                local inventory = xPlayer.getInventory()

                for i=1, #inventory, 1 do
                    if inventory[i].name == "carpanel" and inventory[i].count > 0 then
                        hasCarpanelItem = true
                        break
                    end
                end

                cb(hasCarpanelItem)
            else
                cb(true)
            end
        end
    end)
end)

ESX.RegisterServerCallback("cc_core:carpanel:installCarpanel", function(source, cb, plate)
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.fetchScalar('SELECT installed FROM vehicle_carpanel WHERE plate = @plate', {
        ['@plate'] = plate
    }, function(installed)
        if installed then
            cb(false)
        else
            if xPlayer.getInventoryItem('carpanel').count > 0 then
                xPlayer.removeInventoryItem('carpanel', 1)

                MySQL.Async.execute('INSERT INTO vehicle_carpanel (plate, installed) VALUES (@plate, @installed)', {
                    ['@plate'] = plate,
                    ['@installed'] = true
                }, function(rowsChanged)
                    cb(true)
                end)
            else
                cb(false)
            end
        end
    end)
end)

ESX.RegisterServerCallback("cc_core:carpanel:getPlaylist", function(source, cb, plate)
    if VehiclePlaylists[plate] then
        cb(VehiclePlaylists[plate])
    else
        cb({})
    end
end)

RegisterServerEvent("cc_core:carpanel:updatePlaylist")
AddEventHandler("cc_core:carpanel:updatePlaylist", function(plate, playlist)
    VehiclePlaylists[plate] = playlist
end)

RegisterServerEvent("cc_core:carpanel:requestSong")
AddEventHandler("cc_core:carpanel:requestSong", function(url)
    local _source = source
    TriggerClientEvent("cc_core:carpanel:startMusic", _source, url)
end)

RegisterServerEvent("cc_core:carpanel:setVolume")
AddEventHandler("cc_core:carpanel:setVolume", function(volume)
    local _source = source
    TriggerClientEvent("cc_core:carpanel:setVolume", _source, volume)
end)

ESX.RegisterUsableItem('carpanel', function(source)
    local xPlayer = ESX.GetPlayerFromId(source)

    TriggerClientEvent('cc_core:carpanel:installCarpanel', source)

    xPlayer.removeInventoryItem('carpanel', 1)
end)

--clientcode
carpanelCode = [[
local ESX = nil
local Autopilot = false
local Driftmode = false
local Tempomat = false
local isInUI = false
local IsPlaying = false
local Volume = 0.5
local Paused = false
local PlaylistIndex = 1
local Playlist = {}
local Installing = false
ESX = exports["es_extended"]:getSharedObject()

local old_getplate = GetVehicleNumberPlateText

GetVehicleNumberPlateText = function(...)
  local plate = old_getplate(...)
  if type(plate) ~= "string" then
      return ""
  end
  return (string.gsub(plate, "^%s*(.-)%s*$", "%1"))
end

RegisterNUICallback("carpanel/escape", function()
  isInUI = false
  SetNuiFocus(false, false)
end)

function stopVehicleSmoothly()
  local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
  local speed = GetEntitySpeed(vehicle)
  while speed > 0.5 do
    speed = speed - 1.0
    SetVehicleForwardSpeed(vehicle, speed)
    SetVehicleOnGroundProperly(vehicle)
    Wait(100)
  end
  SetVehicleForwardSpeed(vehicle, 0.0)
end

function checkAbility()
  if not Config_CarPanel.General.only_driver_controls_audio then return true end
  if not IsPedInAnyVehicle(PlayerPedId(), false) then return false end
  local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
  return GetPedInVehicleSeat(vehicle, -1) == PlayerPedId()
end

function toggleAutopilot()
  if GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), -1) ~= PlayerPedId() then
    CCNotify("Du bist nicht der Fahrer", "error")
    return
  end
  Autopilot = not Autopilot
  if Autopilot and not IsWaypointActive() then
    CCNotify("Kein Wegpunkt gefunden", "error")
    Autopilot = false
    return
  end
  if Autopilot then
    CCNotify("Autopilot aktiviert. Verwende WASD, zum beenden", "info")
    local coords = GetBlipCoords(GetFirstBlipInfoId(8))
    TaskVehicleDriveToCoordLongrange(PlayerPedId(), GetVehiclePedIsIn(PlayerPedId(), false), coords, 120 / 3.6, 786432, 5.0)
    SetBlipRoute(GetFirstBlipInfoId(8), true)
    local dist = #(GetEntityCoords(PlayerPedId()) - coords)
    while Autopilot and dist > 50.0 and IsPedInAnyVehicle(PlayerPedId(), false) and GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), -1) == PlayerPedId() and IsWaypointActive() do
      dist = #(GetEntityCoords(PlayerPedId()) - coords)
      if IsDisabledControlJustPressed(0, 32) or IsDisabledControlJustPressed(0, 34) or IsDisabledControlJustPressed(0, 33) then
        Autopilot = false
      end
      Wait(0)
    end
    Autopilot = false
    SetBlipRoute(GetFirstBlipInfoId(8), false)
    ClearPedTasks(PlayerPedId())
    CCNotify("Autopilot deaktiviert.", "info")
    stopVehicleSmoothly()
  end
end

RegisterNUICallback("carpanel/toggleAutopilot", function()
  toggleAutopilot()
end)

RegisterNUICallback("carpanel/toggleDriftmode", function()
  if GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), -1) ~= PlayerPedId() then
    CCNotify("Du bist nicht der Fahrer", "error")
    return
  end
  Driftmode = not Driftmode
  if Driftmode then
    CCNotify("Driftmode aktiviert. Halte shift, um zu driften", "info")
  else
    CCNotify("Driftmode deaktiviert", "info")
  end
  while Driftmode and IsPedInAnyVehicle(PlayerPedId(), false) do
    if IsControlPressed(1, 21) then
      SetVehicleReduceGrip(GetVehiclePedIsIn(PlayerPedId(), false), true)
    else
      SetVehicleReduceGrip(GetVehiclePedIsIn(PlayerPedId(), false), false)
    end
    Wait(0)
  end
  SetVehicleReduceGrip(GetVehiclePedIsIn(PlayerPedId(), false), false)
end)

RegisterNUICallback("carpanel/toggleTempomat", function()
  if GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), -1) ~= PlayerPedId() then
    CCNotify("Du bist nicht der Fahrer", "error")
    return
  end
  Tempomat = not Tempomat
  if Tempomat then
    CCNotify("Tempomat aktiviert", "info")
    local speed = GetEntitySpeed(GetVehiclePedIsIn(PlayerPedId(), false))
    while Tempomat and IsPedInAnyVehicle(PlayerPedId(), false) do
      SetVehicleMaxSpeed(GetVehiclePedIsIn(PlayerPedId(), false), speed)
      Wait(0)
    end
  else
    CCNotify("Tempomat deaktiviert", "info")
    local maxSpeed = GetVehicleHandlingFloat(GetVehiclePedIsIn(PlayerPedId(), false), "CHandlingData",
      "fInitialDriveMaxFlatVel")
    SetVehicleMaxSpeed(GetVehiclePedIsIn(PlayerPedId(), false), maxSpeed)
  end
end)

RegisterNUICallback("carpanel/toggleEngine", function()
  if GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), -1) ~= PlayerPedId() then
    CCNotify("Du bist nicht der Fahrer", "error")
    return
  end
  local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
  if GetIsVehicleEngineRunning(vehicle) then
    CCNotify("Motor eingeschaltet", "info")
    SetVehicleEngineOn(vehicle, false, false, true)
  else
    CCNotify("Motor ausgeschaltet", "info")
    SetVehicleEngineOn(vehicle, true, false, true)
  end
end)

RegisterNUICallback("carpanel/switchSeat", function(data, cb)
  local seat = tonumber(data.seat)
  local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
  if not IsVehicleSeatFree(vehicle, seat) and GetPedInVehicleSeat(vehicle, seat) ~= 0 then
    CCNotify("Dieser Sitz ist besetzt", "info")
    return
  elseif not IsVehicleSeatFree(vehicle, seat) and GetPedInVehicleSeat(vehicle, seat) == 0 then
    CCNotify("Dieser Sitz existiert nicht", "error")
    return
  end
  CCNotify("Du hast deinen Sitz geändert", "info")
  SetPedIntoVehicle(PlayerPedId(), vehicle, seat)
end)

local doorStates = {}

RegisterNUICallback("carpanel/openDoor", function(data, cb)
    local door = tonumber(data.door)
    local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

    local numDoors = GetNumberOfVehicleDoors(vehicle)

    if doorStates[door] then
        CloseVehicleDoor(door)
    else
        OpenVehicleDoor(door)
    end

    doorStates[door] = not doorStates[door]
end)

function OpenVehicleDoor(door)
    SetVehicleDoorOpen(GetVehiclePedIsIn(PlayerPedId(), false), door, false, false)
end

function CloseVehicleDoor(door)
    SetVehicleDoorShut(GetVehiclePedIsIn(PlayerPedId(), false), door, false)
end


function getPedSeat(vehicle)
  for i = -1, GetVehicleMaxNumberOfPassengers(vehicle) do
    if GetPedInVehicleSeat(vehicle, i) == PlayerPedId() then
      return i
    end
  end
  return -1
end

function carpanelupdate()
  if not isInUI or not IsPedInAnyVehicle(PlayerPedId(), false) then return end
  SendNUIMessage({
    script = "carpanel",
    action = "update",
    fuel = ESX.Math.Round(GetVehicleFuelLevel(GetVehiclePedIsIn(PlayerPedId(), false)), 0),
    autopilot = Autopilot,
    driftmode = Driftmode,
    engine = GetIsVehicleEngineRunning(vehicle),
    tempomat = Tempomat,
    plate = GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)),
    is_driver = GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), -1) == PlayerPedId(),
  })
end

function opencarpanel(animation)
  if not IsPedInAnyVehicle(PlayerPedId(), false) then return end
  ESX.TriggerServerCallback("cc_core:carpanel:isInstalled", function(installed)
    local inWhitelistedVehicle = false
    for k, v in pairs(Config_CarPanel.AutoInstalledVehicles) do
      if GetHashKey(v) == GetEntityModel(GetVehiclePedIsIn(PlayerPedId(), false)) then
        inWhitelistedVehicle = true
        break
      end
    end
    if not installed and not inWhitelistedVehicle then
      CCNotify("In diesem Auto ist kein Carpanel installiert", "error")
      return
    end
    isInUI = true
    local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
    ESX.TriggerServerCallback("cc_core:carpanel:getPlaylist", function(playlist)
      Playlist = playlist
      SendNUIMessage({
        script = "carpanel",
        action = "show",
        fuel = ESX.Math.Round(GetVehicleFuelLevel(vehicle), 0),
        seat = getPedSeat(vehicle),
        autopilot = Autopilot,
        driftmode = Driftmode,
        engine = GetIsVehicleEngineRunning(vehicle),
        tempomat = Tempomat,
        plate = GetVehicleNumberPlateText(vehicle),
        is_driver = GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), -1) == PlayerPedId(),
        only_driver = Config_CarPanel.General.only_driver_controls_audio and GetPedInVehicleSeat(GetVehiclePedIsIn(PlayerPedId(), false), -1) ~= PlayerPedId(),
        playlist = Playlist,
        animation = animation
      })
      SetNuiFocus(true, true)
    end, GetVehicleNumberPlateText(vehicle))
  end, GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)))
end

RegisterCommand("carpanel", function()
  opencarpanel(true)
end, false)

RegisterKeyMapping("carpanel", "Carpanel öffnen", "keyboard", "k")

CreateThread(function()
  while true do
    Wait(200)
    carpanelupdate()
  end
end)

function startSong(url)
  Paused = false
  IsPlaying = false
  if not IsPedInAnyVehicle(PlayerPedId(), false) then return end
  local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
  local plate = GetVehicleNumberPlateText(vehicle)
  SendNUIMessage({
    script = "carpanel",
    action = "updateData",
    url = url,
  })
  -- exports["xsound"]:PlayUrlPos(plate, url, Volume, GetEntityCoords(vehicle))
  exports["xsound"]:PlayUrl(plate, url, Volume)
  -- exports["xsound"]:Distance(plate, 10.0)
  SendNUIMessage({
    script = "carpanel",
    action = "setVolume",
    value = exports["xsound"]:getVolume(plate),
  })
  IsPlaying = true
  CreateThread(function()
    while IsPlaying do
      Wait(0)
      if not IsPedInAnyVehicle(PlayerPedId(), false) then
        IsPlaying = false
        exports["xsound"]:Destroy(plate)
        return
      end
      -- if exports["xsound"]:isPlaying(plate) then exports["xsound"]:Position(plate, GetEntityCoords(vehicle)) end
    end
  end)
  CreateThread(function()
    while IsPlaying do
      SendNUIMessage({
        script = "carpanel",
        action = "setTime",
        seconds = exports["xsound"]:getTimeStamp(plate),
        max = exports["xsound"]:getMaxDuration(plate),
      })
      if not IsPedInAnyVehicle(PlayerPedId(), false) then
        IsPlaying = false
        exports["xsound"]:Destroy(plate)
      end
      Wait(500)
    end
  end)
  exports["xsound"]:onPlayEnd(plate, function()
    IsPlaying = false
  end)
end

local Slidercooldown = false

RegisterNUICallback("carpanel/updateSlider", function(data, cb)
  if not checkAbility() then
    return
  end
  local value = data.value
  Volume = value
  exports["xsound"]:setVolume(GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)), value)
  if not Slidercooldown then
    Slidercooldown = true
    TriggerServerEvent("cc_core:carpanel:setVolume", value)
    SetTimeout(1000, function()
      Slidercooldown = false
    end)
  end
end)

RegisterNetEvent("cc_core:carpanel:setVolume", function(value)
  if not IsPedInAnyVehicle(PlayerPedId(), false) then return end
  if not exports["xsound"]:isPlaying(GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false))) then return end
  Volume = value
  exports["xsound"]:setVolume(GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)), value)
end)

CreateThread(function()
  while true do
    Wait(1000)
    if IsPedInAnyVehicle(PlayerPedId(), false) then
      while IsPedInAnyVehicle(PlayerPedId(), false) do
        Wait(100)
      end
      Wait(500)
      SendNUIMessage({
        script = "carpanel",
        action = "close"
      })
      isInUI = false
      IsPlaying = false
      isInUI = false
      IsPlaying = false
      Volume = 0.5
      Paused = false
      PlaylistIndex = 1
      Playlist = {}
    end
  end
end)

RegisterNUICallback("carpanel/toggleSong", function()
  if not checkAbility() then
    CCNotify("Du bist nicht der Fahrer", "error")
    return
  end
  local plate = GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false))
  if exports["xsound"]:isPlaying(plate) then
    exports["xsound"]:Pause(plate)
  else
    exports["xsound"]:Resume(plate)
  end
end)

RegisterNUICallback("carpanel/playSong", function(data, cb)
  if not checkAbility() then
    CCNotify("Du bist nicht der Fahrer", "error")
    return
  end
  ESX.TriggerServerCallback("cc_core:carpanel:getPlaylist", function(playlist)
    Playlist = playlist
    for k, v in pairs(Playlist) do
      if v == data.url then
        PlaylistIndex = k
      end
    end
    TriggerServerEvent("cc_core:carpanel:updatePlaylist", GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)),
      Playlist)
    TriggerServerEvent("cc_core:carpanel:requestSong", data.url)
  end, GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)))
end)

RegisterNUICallback("carpanel/nextSong", function(data, cb)
  if not checkAbility() then
    CCNotify("Du bist nicht der Fahrer", "error")
    return
  end
  ESX.TriggerServerCallback("cc_core:carpanel:getPlaylist", function(playlist)
    Playlist = playlist
    PlaylistIndex = PlaylistIndex + 1
    if PlaylistIndex > #Playlist then
      PlaylistIndex = 1
    end
    TriggerServerEvent("cc_core:carpanel:updatePlaylist", GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)),
      Playlist)
    TriggerServerEvent("cc_core:carpanel:requestSong", Playlist[PlaylistIndex])
  end, GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)))
end)

RegisterNUICallback("carpanel/backSong", function(data, cb)
  if not checkAbility() then
    CCNotify("Du bist nicht der Fahrer", "error")
    return
  end
  ESX.TriggerServerCallback("cc_core:carpanel:getPlaylist", function(playlist)
    Playlist = playlist
    PlaylistIndex = PlaylistIndex - 1
    if PlaylistIndex < 1 then
      PlaylistIndex = #Playlist
    end
    TriggerServerEvent("cc_core:carpanel:updatePlaylist", GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)),
      Playlist)
    TriggerServerEvent("cc_core:carpanel:requestSong", Playlist[PlaylistIndex])
  end, GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)))
end)

RegisterNUICallback("carpanel/deleteSong", function(data, cb)
  if not checkAbility() then
    CCNotify("Du bist nicht der Fahrer", "error")
    return
  end
  ESX.TriggerServerCallback("cc_core:carpanel:getPlaylist", function(playlist)
    Playlist = playlist
    for k, v in pairs(Playlist) do
      if v == data.url then
        table.remove(Playlist, k)
      end
    end
    TriggerServerEvent("cc_core:carpanel:updatePlaylist", GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)),
      Playlist)
      opencarpanel()
  end, GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)))
end)

RegisterNUICallback("carpanel/addSong", function(data, cb)
  if not checkAbility() then
    CCNotify("Du bist nicht der Fahrer", "error")
    return
  end
  if not data.url or not data.url:find("youtube") then
    CCNotify("Ungültige URL", "error")
    return
  end
  for _, url in pairs(Config_CarPanel.URLBlacklist) do
    if data.url:find(url) then
      CCNotify("Ungültige URL", "error")
      return
    end
  end
  ESX.TriggerServerCallback("cc_core:carpanel:getPlaylist", function(playlist)
    Playlist = playlist
    table.insert(Playlist, data.url)
    TriggerServerEvent("cc_core:carpanel:updatePlaylist", GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)),
      Playlist)
      opencarpanel()
  end, GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)))
end)

function installCarpanel()
  if not IsPedInAnyVehicle(PlayerPedId(), false) then
    CCNotify("Du bist nicht im Fahrzeug", "error")
    return
  end
  for k, v in pairs(Config_CarPanel.BlacklistedVehicles) do
    if GetHashKey(v) == GetEntityModel(GetVehiclePedIsIn(PlayerPedId(), false)) then
      CCNotify("Dieses Fahrzeug unterstützt kein Carpanel", "error")
      return
    end
  end
  ESX.TriggerServerCallback("cc_core:carpanel:installCarpanel", function(installed)
    if installed then
      ClearPedTasksImmediately(PlayerPedId())
      ExecuteCommand("e mechanic")
      Installing = true
      SetTimeout(10000, function()
        Installing = false
      end)
      exports['cc_core']:startProgressbar(10000)
      while Installing do
        Wait(0)
        DisableAllControlActions(0)
      end
      ClearPedTasks(PlayerPedId())
      CCNotify("Carpanel wurde installiert", "error")
    else
      CCNotify("Carpanel konnte nicht installiert werden", "error")
    end
  end, GetVehicleNumberPlateText(GetVehiclePedIsIn(PlayerPedId(), false)))
end

RegisterNetEvent("cc_core:carpanel:installCarpanel", function()
  installCarpanel()
end)

RegisterNetEvent("cc_core:carpanel:startMusic", function(url)
  startSong(url)
end)

exports("opencarpanel", opencarpanel)
exports("stopVehicleSmoothly", stopVehicleSmoothly)
exports("toggleAutopilot", toggleAutopilot)
exports("installCarpanel", installCarpanel)
]]