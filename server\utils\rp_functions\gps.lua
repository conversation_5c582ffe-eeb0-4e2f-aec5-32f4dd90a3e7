local ESX = exports['es_extended']:getSharedObject()
local used = {}

ESX.RegisterUsableItem('gps', function(source)
    if ESX.GetPlayerJob(source).name == 'unemployed' then
        TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Information', 'Du kannst als Arbeitsloser kein GPS Benutzen')
        return
    end
    
    if used[source] == nil then
        used[source] = {}
        used[source].used = true
        TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Information', 'GPS Aktiviert')
        TriggerClientEvent('cc_gps:setInUse', source, true)
        return
    end

    if not used[source].used then
        used[source].used = true
        TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Information', 'GPS Aktiviert')
        TriggerClientEvent('cc_gps:setInUse', source, true)
    else
        used[source].used = false
        TriggerClientEvent('cc_core:hud:notify', source, 'info', 'Information', 'GPS Deaktiviert')
        TriggerClientEvent('cc_gps:clearBlips', source)
        TriggerClientEvent('cc_gps:setInUse', source, false)
    end
end)

AddEventHandler('esx:setJob', function(playerId, job, lastJob)
    if used[playerId] ~= nil then
        used[playerId].used = false
        TriggerClientEvent('cc_gps:clearBlips', playerId)
    end
end)

RegisterServerEvent('esx:onPlayerDeath')
AddEventHandler('esx:onPlayerDeath', function(data)
    local playerId = source

    if used[playerId] ~= nil then
        used[playerId].used = false
        if ESX.GetPlayerJob(playerId).name == 'ambulance' then
            return
        else
            TriggerClientEvent('cc_gps:clearBlips', playerId)
            TriggerClientEvent('cc_gps:setInUse', playerId, false)
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(30000)
        local xPlayers = exports['cc_core']:GetPlayersFix()
        local peds = {}

        for k, v in pairs(xPlayers) do
            if used[v.playerId] ~= nil then
                if used[v.playerId].used then
                    if ESX.GetPlayerJob(v.playerId).name ~= 'unemployed' then
                        local item = ESX.GetPlayerInventoryItem(v.playerId, 'gps')

                        if item ~= nil then
                            if item.count >= 1 then
                                table.insert(peds, {
                                    playerId = v.playerId,
                                    coords = ESX.GetPlayerCoords(v.playerId, true),
                                    rpName = ESX.GetPlayerRPName(v.playerId),
                                    job = ESX.GetPlayerJob(v.playerId).name
                                })
                            end 
                        end
                    end
                end
            end
        end

        TriggerClientEvent('cc_gps:sendGPS', -1, peds)  
    end
end)