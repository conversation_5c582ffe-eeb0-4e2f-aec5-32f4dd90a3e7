const ESX = globalThis.exports['es_extended'].getSharedObject()

const executeQuery = async (sql, query, parameters) => {
    return new Promise((resolve, reject) => {
        globalThis.exports['oxmysql'][sql](query, parameters, (result, error) => {
            if (error) return reject(error)
            return resolve(result)
        })
    })
}

onNet('cc_clsguide:sendNewLicense', async (oldPlate, newPlate, vehicle) => {
    const playerId = source

    if (ESX.GetPlayerInventoryItem(playerId, "licenseplate").count >= 1) {
        if (!/[^a-zA-Z0-9- \-]/.test(newPlate) && newPlate[0] != ' ' && newPlate[newPlate.length - 1] != ' ' && !newPlate.includes('-')) {
            if (newPlate.length < 9) {
                const identifier = ESX.GetPlayerIdentifier(playerId)
                const result = await executeQuery('query', 'SELECT owner FROM owned_vehicles WHERE owner = ? AND plate = ?', [identifier, oldPlate.toUpperCase()])

                if (result.length == 1) {
                    const result2 = await executeQuery('query', 'SELECT plate FROM owned_vehicles WHERE plate = ?', [newPlate.toUpperCase()])

                    console.log(result2.length)

                    if (result2.length == 0) {
                        const result3 = await executeQuery('query', 'SELECT vehicle FROM owned_vehicles WHERE plate = ?', [oldPlate.toUpperCase()])

                        if (result3.length == 1) {
                            const originalVehicleProps = JSON.parse(result3[0].vehicle)
                            originalVehicleProps.plate = newPlate.toUpperCase()
                            const oldVehicleProps = JSON.stringify(originalVehicleProps)

                            ESX.RemovePlayerInventoryItem(playerId, 'licenseplate', 1, GetCurrentResourceName())
                            emitNet('cc_core:hud:notify', playerId, 'info', 'Kennzeichen', 'Kennzeichen Erfolgreich geändert!')
                            emitNet('cc_clsguide:sendLicensePlate1337', playerId, vehicle, oldPlate.toUpperCase(), newPlate.toUpperCase())
                            emit('cc_menus:trunk:remove', oldPlate.toUpperCase(), newPlate.toUpperCase())

                            await executeQuery('update', 'UPDATE owned_vehicles SET vehicle = ?, plate = ? WHERE owner = ? AND plate = ?', [oldVehicleProps, newPlate.toUpperCase(), identifier, oldPlate.toUpperCase()])
                            await executeQuery('update', 'UPDATE inventories SET identifier = ? WHERE identifier = ?', [newPlate.toUpperCase(), oldPlate.toUpperCase()])
                            await executeQuery('update', 'UPDATE trunk_inventory SET plate = ? WHERE plate = ?', [newPlate.toUpperCase(), oldPlate.toUpperCase()])
                        
                            globalThis.exports['cc_core'].log(playerId, 'Kennzeichen log', 'Der Spieler ' + GetPlayerName(playerId) + ' hat sein Kennzeichen ' + oldPlate.toUpperCase() + ' zu ' + newPlate.toUpperCase() + ' geändert!', 'https://discord.com/api/webhooks/1028167814403604561/hKobrJZovl3LrnKfCELzU5nl_l-hIj1h2DwNbcnssKRQr21rZ_tFuciVFHJoxTuY8EKY')
                        }
                    } else {
                        emitNet('cc_core:hud:notify', playerId, 'info', 'Kennzeichen', 'Das Kennzeichen gibt es schon!')    
                    }
                } else {
                    emitNet('cc_core:hud:notify', playerId, 'info', 'Kennzeichen', 'Das Fahrzeug gehört nicht dir!')
                }
            } else {
                emitNet('cc_core:hud:notify', playerId, 'info', 'Kennzeichen', 'Das Kennzeichen darf Max. 8 Zeichen lang sein!')
            }
        } else {
            emitNet('cc_core:hud:notify', playerId, 'info', 'Kennzeichen', 'DAS KENNZEICHEN DARF KEINE SONDERZEICHEN HABEN!!!!!!!!!')
        }
    }
})