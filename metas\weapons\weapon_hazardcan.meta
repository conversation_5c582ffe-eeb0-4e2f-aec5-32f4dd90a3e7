<?xml version="1.0" encoding="UTF-8"?>

<CWeaponInfoBlob>
  <SlotNavigateOrder>
    <Item>
      <WeaponSlots>
        <Item>
          <OrderNumber value="421" />
          <Entry>SLOT_HAZARDCAN</Entry>
        </Item>
      </WeaponSlots>
    </Item>
    <Item>
      <WeaponSlots>
		<Item>
          <OrderNumber value="421" />
          <Entry>SLOT_HAZARDCAN</Entry>
        </Item>
      </WeaponSlots>
    </Item>
  </SlotNavigateOrder>
  <SlotBestOrder />
  <TintSpecValues />
  <FiringPatternAliases />
  <UpperBodyFixupExpressionData />
  <AimingInfos />
  <Infos>
    <Item>
      <Infos>
        <Item type="CAmmoInfo">
          <Name>AMMO_HAZARDCAN</Name>
          <Model />
          <Audio />
          <Slot />
          <AmmoMax value="4500" />
          <AmmoMax50 value="4500" />
          <AmmoMax100 value="4500" />
          <AmmoMaxMP value="4500" />
          <AmmoMax50MP value="4500" />
          <AmmoMax100MP value="4500" />
          <AmmoFlags />
        </Item>
	  </Infos>
    </Item>
    <Item>
      <Infos>
        <Item type="CWeaponInfo">
          <Name>WEAPON_HAZARDCAN</Name>
          <Model>w_ch_jerrycan</Model>
          <Audio>AUDIO_ITEM_PETROLCAN</Audio>
          <Slot>SLOT_HAZARDCAN</Slot>
          <DamageType>NONE</DamageType>
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FireType>VOLUMETRIC_PARTICLE</FireType>
          <WheelSlot>WHEEL_THROWABLE_SPECIAL</WheelSlot>
          <Group>GROUP_PETROLCAN</Group>
          <AmmoInfo ref="AMMO_HAZARDCAN" />
          <AimingInfo ref="PETROLCAN" />
          <ClipSize value="4500" />
          <AccuracySpread value="1.000000" />
          <AccurateModeAccuracyModifier value="0.500000" />
          <RunAndGunAccuracyModifier value="2.000000" />
          <RunAndGunAccuracyMinOverride value="-1.000000" />
          <RecoilAccuracyMax value="0.000000" />
          <RecoilErrorTime value="0.000000" />
          <RecoilRecoveryRate value="1.000000" />
          <RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
          <MinHeadShotDistanceAI value="1000.000000" />
          <MaxHeadShotDistanceAI value="1000.000000" />
          <HeadShotDamageModifierAI value="1000.000000" />
          <RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
          <MinHeadShotDistancePlayer value="5.000000" />
          <MaxHeadShotDistancePlayer value="230000.000000" />
          <HeadShotDamageModifierPlayer value="1288860.000000" />
          <Damage value="0.000000" />
          <DamageTime value="0.000000" />
          <DamageTimeInVehicle value="0.000000" />
          <DamageTimeInVehicleHeadShot value="0.000000" />
          <HitLimbsDamageModifier value="0.500000" />
          <NetworkHitLimbsDamageModifier value="0.800000" />
          <LightlyArmouredDamageModifier value="0.750000" />
          <Force value="0.000000" />
          <ForceHitPed value="0.000000" />
          <ForceHitVehicle value="0.000000" />
          <ForceHitFlyingHeli value="0.000000" />
          <OverrideForces />
          <ForceMaxStrengthMult value="1.000000" />
          <ForceFalloffRangeStart value="0.000000" />
          <ForceFalloffRangeEnd value="50.000000" />
          <ForceFalloffMin value="1.000000" />
          <ProjectileForce value="0.000000" />
          <FragImpulse value="250.000000" />
          <Penetration value="0.010000" />
          <VerticalLaunchAdjustment value="0.000000" />
          <DropForwardVelocity value="0.000000" />
          <Speed value="2000.000000" />
          <BulletsInBatch value="1" />
          <BatchSpread value="0.000000" />
          <ReloadTimeMP value="-1.000000" />
          <ReloadTimeSP value="-1.000000" />
          <VehicleReloadTime value="-1.000000" />
          <AnimReloadRate value="1.000000" />
          <BulletsPerAnimLoop value="1" />
          <TimeBetweenShots value="0.000000" />
          <TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
          <SpinUpTime value="0.000000" />
          <SpinTime value="0.000000" />
          <SpinDownTime value="0.000000" />
          <AlternateWaitTime value="-1.000000" />
          <BulletBendingNearRadius value="0.000000" />
          <BulletBendingFarRadius value="0.750000" />
          <BulletBendingZoomedRadius value="0.375000" />
		  <FirstPersonBulletBendingNearRadius value="0.000000" />
          <FirstPersonBulletBendingFarRadius value="0.750000" />
          <FirstPersonBulletBendingZoomedRadius value="0.375000" />
          <Fx>
            <EffectGroup>WEAPON_EFFECT_GROUP_PISTOL_SMALL</EffectGroup>
            <FlashFx>weap_ch_hazcan</FlashFx>
            <FlashFxAlt />
            <MuzzleSmokeFx />
            <MuzzleSmokeFxMinLevel value="0.000000" />
            <MuzzleSmokeFxIncPerShot value="0.000000" />
            <MuzzleSmokeFxDecPerSec value="0.000000" />
            <ShellFx />
            <TracerFx />
            <PedDamageHash>BulletSmall</PedDamageHash>
            <TracerFxChanceSP value="0.000000" />
            <TracerFxChanceMP value="0.000000" />
            <FlashFxChanceSP value="0.000000" />
            <FlashFxChanceMP value="0.000000" />
            <FlashFxAltChance value="0.000000" />
            <FlashFxScale value="1.000000" />
            <FlashFxLightEnabled value="false" />
            <FlashFxLightCastsShadows value="true" />
            <FlashFxLightOffsetDist value="0.000000" />
            <FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
            <FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
            <FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
            <FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
            <GroundDisturbFxEnabled value="false" />
            <GroundDisturbFxDist value="5.000000" />
            <GroundDisturbFxNameDefault />
            <GroundDisturbFxNameSand />
            <GroundDisturbFxNameDirt />
            <GroundDisturbFxNameWater />
            <GroundDisturbFxNameFoliage />
          </Fx>
          <InitialRumbleDuration value="0" />
          <InitialRumbleIntensity value="0.000000" />
          <InitialRumbleIntensityTrigger value="0.000000" />
          <RumbleDuration value="0" />
          <RumbleIntensity value="0.000000" />
          <RumbleIntensityTrigger value="0.000000" />
          <RumbleDamageIntensity value="1.000000" />
          <NetworkPlayerDamageModifier value="1.000000" />
          <NetworkPedDamageModifier value="1.000000" />
          <NetworkHeadShotPlayerDamageModifier value="1.000000" />
          <LockOnRange value="45.000000" />
          <WeaponRange value="500.000000"  />
          <BulletDirectionOffsetInDegrees value="0.000000" />
          <AiSoundRange value="-1.000000" />
          <AiPotentialBlastEventRange value="3.000000" />
          <DamageFallOffRangeMin value="50.000000" />
          <DamageFallOffRangeMax value="50.000000" />
          <DamageFallOffModifier value="0.300000" />
          <VehicleWeaponHash />
          <DefaultCameraHash>HIP_AIM_CAMERA</DefaultCameraHash>
          <CoverCameraHash>HIP_AIM_IN_COVER_CAMERA</CoverCameraHash>
          <CoverReadyToFireCameraHash />
          <RunAndGunCameraHash>JERRY_CAN_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
          <CinematicShootingCameraHash />
          <AlternativeOrScopedCameraHash />
          <RunAndGunAlternativeOrScopedCameraHash />
          <CinematicShootingAlternativeOrScopedCameraHash />
          <CameraFov value="45.000000" />
          <FirstPersonAsThirdPersonIdleOffset x="-0.025" y="-0.1" z="-0.10" />
          <FirstPersonAsThirdPersonRNGOffset x="0.050000" y="-0.100000" z="-0.200000" />
          <FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
          <FirstPersonAsThirdPersonWeaponBlockedOffset x="-0.0500000" y="0.100000" z="-0.050000" />
          <ZoomFactorForAccurateMode value="1.000000" />
          <RecoilShakeHash />
          <RecoilShakeHashFirstPerson />
          <AccuracyOffsetShakeHash />
          <MinTimeBetweenRecoilShakes value="150" />
          <RecoilShakeAmplitude value="0.000000" />
          <ExplosionShakeAmplitude value="-1.000000" />
          <ReticuleHudPosition x="0.000000" y="0.000000" />
          <AimOffsetMin x="0.000000" y="0.000000" z="0.115000" />
          <AimProbeLengthMin value="1.000000" />
          <AimOffsetMax x="0.000000" y="0.000000" z="0.115000" />
          <AimProbeLengthMax value="0.000000" />
          <TorsoAimOffset x="0.150000" y="0.550000" />
          <TorsoCrouchedAimOffset x="0.200000" y="0.050000" />
          <LeftHandIkOffset x="0.965000" y="-0.230000" z="-0.350000" />
          <ReticuleMinSizeStanding value="0.650000" />
          <ReticuleMinSizeCrouched value="0.550000" />
          <ReticuleScale value="0.300000" />
          <ReticuleStyleHash>WEAPON_UNARMED</ReticuleStyleHash>
          <FirstPersonReticuleStyleHash />
          <PickupHash>PICKUP_WEAPON_HAZARDCAN</PickupHash>
          <MPPickupHash>PICKUP_WEAPON_HAZARDCAN</MPPickupHash>
          <HumanNameHash>WT_HAZARDCAN</HumanNameHash>
          <MovementModeConditionalIdle />
          <StatName>HAZARDCAN</StatName>
          <KnockdownCount value="-1" />
          <KillshotImpulseScale value="1.000000" />
          <NmShotTuningSet>Normal</NmShotTuningSet>
          <AttachPoints />
          <GunFeedBone />
          <TargetSequenceGroup />
          <WeaponFlags>CarriedInHand Automatic Silenced AnimCrouchFire UsableOnFoot UsableClimbing AllowEarlyExitFromFireAnimAfterBulletFired CanBeFiredLikeGun OnlyAllowFiring SuppressGunshotEvent NoAutoRunWhenFiring DontBlendFireOutro DiscardWhenOutOfAmmo CreatesAPotentialExplosionEventWhenFired DelayedFiringAfterAutoSwapPreviousWeapon DisableCombatRoll NoWheelStats DisableStealth OnlyUseAimingInfoInFPS EnableFPSRNGOnly AttachFPSLeftHandIKToRight UseFPSAimIK UseFPSSecondaryMotion HideReticule</WeaponFlags>
          <TintSpecValues ref="TINT_DEFAULT" />
          <FiringPatternAliases ref="NULL" />
          <ReloadUpperBodyFixupExpressionData ref="default" />
          <AmmoDiminishingRate value="0" />
          <AimingBreathingAdditiveWeight value="1.000000" />
          <FiringBreathingAdditiveWeight value="1.000000" />
          <StealthAimingBreathingAdditiveWeight value="0.000000" />
          <StealthFiringBreathingAdditiveWeight value="0.000000" />
          <AimingLeanAdditiveWeight value="1.000000" />
          <FiringLeanAdditiveWeight value="1.000000" />
          <StealthAimingLeanAdditiveWeight value="0.000000" />
          <StealthFiringLeanAdditiveWeight value="0.000000" />
          <ExpandPedCapsuleRadius value="0.600000" />
          <AudioCollisionHash />
          <HudDamage value="0" />
          <HudSpeed value="10" />
          <HudCapacity value="80" />
          <HudAccuracy value="30" />
          <HudRange value="1" />
        </Item>
      </Infos>
    </Item>
    <Item>
      <Infos />
    </Item>
    <Item>
      <Infos />
    </Item>
  </Infos>
  <VehicleWeaponInfos />
  <WeaponGroupDamageForArmouredVehicleGlass />
  <Name>DLC - Hazardous Jerry Can</Name>
</CWeaponInfoBlob>