Config_Holdup = {}

Config_Holdup.Stores = {	
	["bank"] = {
		position = { x = 254.11, y = 225.64, z = 101.88, sprite = 455, scale = 1.0 },
		type = 'money',
		reward = math.random(60000, 80000),
		giveBlackMoney = true,
		nameOfStore = "Bank",
		secondsRemaining = 700, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 30,
		lastRobbed = 0,
		blip = false,
		timerBeforeNewRob = 3000*4
	},

	["palbank"] = {
		position = { x = -103.76, y = 6477.78, z = 31.63, sprite = 455, scale = 1.0 },
		type = 'money',
		reward = math.random(15000, 18000),
		giveBlackMoney = true,
		nameOfStore = "PalBank",
		secondsRemaining = 500, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 30,
		lastRobbed = 0,
		blip = false,
		timerBeforeNewRob = 2500*4
	},

	
	["humane_labs"] = {
		position = { x = 3560.02, y = 3675.05, z = 28.12, sprite = 118, scale = 1.0 },
		type = 'money',
		reward = math.random(110000, 125000),
		giveBlackMoney = true,
		nameOfStore = "Humane Labs",
		secondsRemaining = 500, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 20,
		lastRobbed = 0,
		blip = true,
		timerBeforeNewRob = 3000*4
	},
	

	["juwelier"] = {
		position = { x = -622.19, y = -230.87, z = 38.06, sprite = 617, scale = 1.0 },
		type = 'money',
		reward = math.random(11000, 15000),
		giveBlackMoney = true,
		nameOfStore = "Juwelier",
		secondsRemaining = 500, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 20,
		lastRobbed = 0,
		blip = true,
		timerBeforeNewRob = 2500*4
	},
	
	["yacht"] = {
		position = { x = -2079.69, y = -1019.8, z = 8.97, sprite = 455, scale = 1.0 },
		type = 'money',
		reward = math.random(27000, 31000),
		giveBlackMoney = true,
		nameOfStore = "Yacht",
		secondsRemaining = 700, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 20,
		lastRobbed = 0,
		blip = true,
		timerBeforeNewRob = 2500*4
	},

	["paleto_twentyfourseven"] = {
		position = { x = 1736.32, y = 6419.47, z = 35.03, sprite = 156, scale = 0.8 },
		type = 'money',
		reward = math.random(8000, 12000),
		giveBlackMoney = true,
		nameOfStore = "24/7. (Paleto Bay)",
		secondsRemaining = 200, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 20,
		lastRobbed = 0,
		blip = true,
		timerBeforeNewRob = 1800*4
	},

	["sandyshores_twentyfoursever"] = {
		position = { x = 1961.24, y = 3749.46, z = 32.34, sprite = 156, scale = 0.8 },
		type = 'money',
		reward = math.random(8000, 12000),
		giveBlackMoney = true,
		nameOfStore = "24/7. (Sandy Shores)",
		secondsRemaining = 200, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 20,
		lastRobbed = 0,
		blip = true,
		timerBeforeNewRob = 1800*4
	},

	["littleseoul_twentyfourseven"] = {
		position = { x = -709.17, y = -904.21, z = 19.21, sprite = 156, scale = 0.8 },
		type = 'money',
		reward = math.random(8000, 12000),
		giveBlackMoney = true,
		nameOfStore = "24/7. (Little Seoul)",
		secondsRemaining = 200, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 20,
		lastRobbed = 0,
		blip = true,
		timerBeforeNewRob = 1800*4
	},

	["ocean_liquor"] = {
		position = { x = -2959.33, y = 388.21, z = 14.00, sprite = 156, scale = 0.8 },
		type = 'money',
		reward = math.random(8000, 12000),
		giveBlackMoney = true,
		nameOfStore = "Robs Liquor. (Great Ocean Highway)",
		secondsRemaining = 200, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 20,
		lastRobbed = 0,
		blip = true,
		timerBeforeNewRob = 1800*4
	},

	["rancho_liquor"] = {
		position = { x = 1126.80, y = -980.40, z = 45.41, sprite = 156, scale = 0.8 },
		type = 'money',
		reward = math.random(8000, 12000),
		giveBlackMoney = true,
		nameOfStore = "Robs Liquor. (El Rancho Blvd)",
		secondsRemaining = 200, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 20,
		lastRobbed = 0,
		blip = true,
		timerBeforeNewRob = 1800*4
	},

	["sanandreas_liquor"] = {
		position = { x = -1219.85, y = -916.27, z = 11.32, sprite = 156, scale = 0.8 },
		type = 'money',
		reward = math.random(8000, 12000),
		giveBlackMoney = true,
		nameOfStore = "Robs Liquor. (San Andreas Avenue)",
		secondsRemaining = 200, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 20,
		lastRobbed = 0,
		blip = true,
		timerBeforeNewRob = 1800*4
	},

	["grove_ltd"] = {
		position = { x = -43.40, y = -1749.20, z = 29.42, sprite = 156, scale = 0.8 },
		type = 'money',
		reward = math.random(8000, 12000),
		giveBlackMoney = true,
		nameOfStore = "Robs Liquor. (Grove Street)",
		secondsRemaining = 200, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 20,
		lastRobbed = 0,
		blip = true,
		timerBeforeNewRob = 1800*4
	},

	["mirror_ltd"] = {
		position = { x = 1160.67, y = -314.40, z = 69.20, sprite = 156, scale = 0.8 },
		type = 'money',
		reward = math.random(8000, 12000),
		giveBlackMoney = true,
		nameOfStore = "Mirror Park Boulevard",
		secondsRemaining = 200, -- seconds
		policeNumberRequired = 3,
		blipRobbery = nil,
		MaxDistance = 20,
		lastRobbed = 0,
		blip = true,
		timerBeforeNewRob = 1800*4
	},
}