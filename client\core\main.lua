ESX, isInUI = nil, false

local NumberCharset, Charset = {}, {}

for i = 48, 57 do
    table.insert(NumberCharset, string.char(i))
end

for i = 65, 90 do
    table.insert(Charset, string.char(i))
end

for i = 97, 122 do
    table.insert(Charset, string.char(i))
end

Citizen.CreateThread(function()
    while ESX == nil do
        ESX = exports['es_extended']:getSharedObject()
        Citizen.Wait(10)
    end

    while ESX.GetPlayerData().job == nil do
        Citizen.Wait(50)
    end

    ESX.PlayerData = ESX.GetPlayerData()
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
    ESX.PlayerData.job = job
end)

RegisterNetEvent('esx:setJob2')
AddEventHandler('esx:setJob2', function(job2)
    ESX.PlayerData.job2 = job2
end)

RegisterNetEvent('cc_core:security:createE')
AddEventHandler('cc_core:security:createE', function(a, b)
    if GetInvokingResource() ~= nil then 
        ForceSocialClubUpdate()
        return        
    end

    if a then
        TriggerEvent('cc_core:security:create', false, b)
        TriggerServerEvent('cc_core:security:createE')
    end
end)

function loadAnimDict(dict, cb)
    while not HasAnimDictLoaded(dict) do
        RequestAnimDict(dict)
        Citizen.Wait(50)
    end

    cb()
end

function round(number, numDec)
    local multi = 10 ^ (numDec or 0)

    return math.floor(number * multi + 0.5) / multi
end

function startProgressbar(time)
    SendNUIMessage({
        script = 'progressbar',
        action = 'start',
        time = time
    })
end

function stopProgress()
    SendNUIMessage({
        script = 'progressbar',
        action = 'stop'
    })
end

function helpNotify(inRange, show, text, cb)
    if inRange and not show and not isInUI then
        PlaySound(-1, "SELECT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 0, 0, 1)
        
        SendNUIMessage({
            script = 'hud',
            action = 'pause',
            pause = false
        })

        SendNUIMessage({
            script = 'hud',
            action = 'showHelpNotification',
            text = text,
            key = key
        })

        cb(true)
    elseif not inRange and show then
        SendNUIMessage({
            script = 'hud',
            action = 'hideHelpNotification'
        })

        cb(false)
    elseif inRange and show and isInUI then
        SendNUIMessage({
            script = 'hud',
            action = 'pause',
            pause = true
        })

        SendNUIMessage({
            script = 'hud',
            action = 'hideHelpNotification'
        })

        cb(false)
    end
end

function showHelpNotification(text)
    SendNUIMessage({
        script = 'hud',
        action = 'showHelpNotification',
        text = text,
        key = key
    })
end

function closeHelpNotification()
    SendNUIMessage({
        script = 'hud',
        action = 'hideHelpNotification'
    })
end

function RegisterKey(command, desc, keybind)
    RegisterKeyMapping(command, desc, 'keyboard', keybind)
end

function haveMoney(money)
    for k, v in pairs(ESX.GetPlayerData().accounts) do
        if v.name == 'money' then
            if v.money >= money then
                return true
            end
        elseif v.name == 'bank' then
            if v.money >= money then
                return true
            end
        end
    end

    return false
end

function haveItem(item, count)
    for k, v in pairs(ESX.GetPlayerData().inventory) do
        if v.name == item then
            if v.count >= count then
                return true
            end
        end
    end

    return false
end

function getMoney(account)
    for k, v in pairs(ESX.GetPlayerData().accounts) do
        if v.name == account then
            return v.money
        end
    end

    return 0
end

function GetRandomNumber(length)
	Citizen.Wait(1)
	math.randomseed(GetGameTimer())
	if length > 0 then
		return GetRandomNumber(length - 1) .. NumberCharset[math.random(1, #NumberCharset)]
	else
		return ''
	end
end

function GetRandomLetter(length)
	Citizen.Wait(1)
	math.randomseed(GetGameTimer())
	if length > 0 then
		return GetRandomLetter(length - 1) .. Charset[math.random(1, #Charset)]
	else
		return ''
	end
end

function hideHud(state)
    isInUI = state
    
    SendNUIMessage({
        script = 'hud',
        action = 'pause',
        pause = state
    })
end

function DrawMissionText(mesage, time)
    ClearPrints()
    BeginTextCommandPrint('STRING')
    AddTextComponentSubstringPlayerName(mesage)
    EndTextCommandPrint(time, true)
end

RegisterNUICallback('escape', function()
    SetNuiFocus(false, false)

    isInUI = false
end)

exports('hideHud', hideHud)
exports('helpNotify', helpNotify)
exports('closeHelpNotification',closeHelpNotification)
exports('showHelpNotification', showHelpNotification)
exports('startProgressbar', startProgressbar)
exports('stopProgress', stopProgress)

local Ran = false

-- Citizen.CreateThread(function()
--     while true do
--         Citizen.Wait(0)
--         DisableControlAction(1, 44, true)


--     end
-- end)

RegisterCommand("testprogress", function()
    startProgressbar(500)
    Wait(500)
    stopProgress()
end)