-- ====================================
-- GARAGE SYSTEM - SERVER SIDE
-- ====================================

ESX = exports['es_extended']:getSharedObject()

local playerVehicles = {}

-- ====================================
-- MYSQL READY & INITIALIZATION
-- ====================================
MySQL.ready(function()
    -- Alle nicht gespeicherten Fahrzeuge beim Start einparken
    MySQL.Async.fetchAll('SELECT plate FROM owned_vehicles WHERE stored = @stored', {
        ['@stored'] = false
    }, function(result)
        if #result > 0 then
            MySQL.Async.execute('UPDATE owned_vehicles SET `stored` = 1 WHERE `stored` = @stored', {
                ['@stored'] = 0
            }, function(rowsChanged)
                if rowsChanged > 0 then
                    print(('[GARAGE] %s Fahrzeuge wurden beim Start eingeparkt!'):format(rowsChanged))
                end
            end)
        end
    end)

    -- Kennzeichen zu Großbuchstaben konvertieren
    MySQL.Async.execute('UPDATE owned_vehicles SET plate = UPPER(plate)', function(affectedRows)
        if affectedRows > 0 then
            print(('[GARAGE] %s Kennzeichen wurden korrigiert!'):format(affectedRows))
        end
    end)
end)

-- ====================================
-- PLAYER LOADED EVENT
-- ====================================
AddEventHandler('esx:playerLoaded', function(playerId, xPlayer)
    if playerVehicles[playerId] == nil then
        playerVehicles[playerId] = {}
    end

    Citizen.Wait(math.random(2000, 5000))
    
    MySQL.Async.fetchAll('SELECT `vehicle`, `stored`, `plate`, `nickname`, `fav`, `type`, `job`, `tuningData`, `status`, `garage` FROM owned_vehicles WHERE `owner` = @owner', {
        ['@owner'] = xPlayer.getIdentifier()
    }, function(result)
        if #result > 0 then
            local ownedCars = {}
            for k, v in pairs(result) do
                local vehicle = json.decode(v.vehicle or '{}')
                local tuningData = json.decode(v.tuningData or '{}')
                
                table.insert(ownedCars, {
                    vehicle = vehicle,
                    stored = v.stored,
                    plate = v.plate,
                    nickname = v.nickname or 'Fahrzeug',
                    type = v.type,
                    job = v.job,
                    fav = v.fav or false,
                    tuningData = tuningData,
                    status = v.status,
                    garage = v.garage
                })
            end
            
            playerVehicles[playerId] = ownedCars
            TriggerClientEvent('garage:loadVehicles', playerId, ownedCars)
        end
    end)
end)

-- ====================================
-- PLAYER DROPPED EVENT
-- ====================================
AddEventHandler('esx:playerDropped', function(playerId)
    if playerVehicles[playerId] then
        playerVehicles[playerId] = nil
    end
end)

-- ====================================
-- VEHICLE STATE MANAGEMENT
-- ====================================
RegisterServerEvent('garage:changeVehicleState')
AddEventHandler('garage:changeVehicleState', function(plate, state, garage)
    local playerId = source
    local xPlayer = ESX.GetPlayerFromId(playerId)
    
    if not xPlayer then return end
    
    MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored, garage = @garage WHERE plate = @plate AND owner = @owner', {
        ['@plate'] = plate,
        ['@stored'] = state,
        ['@garage'] = garage,
        ['@owner'] = xPlayer.getIdentifier()
    }, function(rowsChanged)
        if rowsChanged > 0 then
            TriggerClientEvent('garage:updateVehicleData', playerId, plate, state, garage)
        end
    end)
end)

-- ====================================
-- VEHICLE PROPERTIES SAVE
-- ====================================
RegisterServerEvent('garage:saveVehicleProps')
AddEventHandler('garage:saveVehicleProps', function(plate, vehicleProps)
    local playerId = source
    local xPlayer = ESX.GetPlayerFromId(playerId)
    
    if not xPlayer then return end
    
    MySQL.Async.execute('UPDATE owned_vehicles SET vehicle = @vehicle WHERE plate = @plate AND owner = @owner', {
        ['@vehicle'] = json.encode(vehicleProps),
        ['@plate'] = plate,
        ['@owner'] = xPlayer.getIdentifier()
    })
end)

-- ====================================
-- VEHICLE NICKNAME CHANGE
-- ====================================
RegisterServerEvent('garage:changeNickname')
AddEventHandler('garage:changeNickname', function(plate, nickname)
    local playerId = source
    local xPlayer = ESX.GetPlayerFromId(playerId)
    
    if not xPlayer then return end
    
    MySQL.Async.execute('UPDATE owned_vehicles SET nickname = @nickname WHERE plate = @plate AND owner = @owner', {
        ['@nickname'] = nickname,
        ['@plate'] = plate,
        ['@owner'] = xPlayer.getIdentifier()
    })
end)

-- ====================================
-- VEHICLE FAVORITE TOGGLE
-- ====================================
RegisterServerEvent('garage:changeFavorite')
AddEventHandler('garage:changeFavorite', function(plate, fav)
    local playerId = source
    local xPlayer = ESX.GetPlayerFromId(playerId)
    
    if not xPlayer then return end
    
    MySQL.Async.execute('UPDATE owned_vehicles SET fav = @fav WHERE plate = @plate AND owner = @owner', {
        ['@fav'] = fav,
        ['@plate'] = plate,
        ['@owner'] = xPlayer.getIdentifier()
    })
end)

-- ====================================
-- ADD NEW VEHICLE
-- ====================================
RegisterServerEvent('garage:addVehicle')
AddEventHandler('garage:addVehicle', function(vehicleProps, vehicleType, job)
    local playerId = source
    local xPlayer = ESX.GetPlayerFromId(playerId)
    
    if not xPlayer then return end
    
    MySQL.Async.execute('INSERT INTO owned_vehicles (owner, plate, vehicle, type, job, stored, nickname) VALUES (@owner, @plate, @vehicle, @type, @job, @stored, @nickname)', {
        ['@owner'] = xPlayer.getIdentifier(),
        ['@plate'] = vehicleProps.plate,
        ['@vehicle'] = json.encode(vehicleProps),
        ['@type'] = vehicleType or 'car',
        ['@job'] = job,
        ['@stored'] = 1,
        ['@nickname'] = 'Fahrzeug'
    }, function(insertId)
        if insertId then
            TriggerClientEvent('garage:addVehicleToClient', playerId, vehicleProps, true, vehicleProps.plate, 'Fahrzeug', vehicleType or 'car', job)
            TriggerClientEvent('esx:showNotification', playerId, 'Fahrzeug mit Kennzeichen ' .. vehicleProps.plate .. ' wurde hinzugefügt!')
        end
    end)
end)

-- ====================================
-- REMOVE VEHICLE
-- ====================================
RegisterServerEvent('garage:removeVehicle')
AddEventHandler('garage:removeVehicle', function(plate)
    local playerId = source
    local xPlayer = ESX.GetPlayerFromId(playerId)
    
    if not xPlayer then return end
    
    MySQL.Async.execute('DELETE FROM owned_vehicles WHERE plate = @plate AND owner = @owner', {
        ['@plate'] = plate,
        ['@owner'] = xPlayer.getIdentifier()
    }, function(rowsChanged)
        if rowsChanged > 0 then
            TriggerClientEvent('garage:removeVehicleFromClient', playerId, plate)
            TriggerClientEvent('esx:showNotification', playerId, 'Fahrzeug mit Kennzeichen ' .. plate .. ' wurde entfernt!')
        end
    end)
end)

-- ====================================
-- GET PLAYER VEHICLES CALLBACK
-- ====================================
ESX.RegisterServerCallback('garage:getPlayerVehicles', function(source, cb, vehicleType)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then 
        cb({})
        return 
    end
    
    local query = 'SELECT * FROM owned_vehicles WHERE owner = @owner'
    local params = {['@owner'] = xPlayer.getIdentifier()}
    
    if vehicleType then
        query = query .. ' AND type = @type'
        params['@type'] = vehicleType
    end
    
    MySQL.Async.fetchAll(query, params, function(result)
        local vehicles = {}
        for k, v in pairs(result) do
            table.insert(vehicles, {
                id = v.id,
                plate = v.plate,
                vehicle = json.decode(v.vehicle or '{}'),
                type = v.type,
                stored = v.stored,
                nickname = v.nickname,
                fav = v.fav,
                tuningData = json.decode(v.tuningData or '{}'),
                status = v.status,
                garage = v.garage
            })
        end
        cb(vehicles)
    end)
end)

-- ====================================
-- CHECK PLATE AVAILABILITY
-- ====================================
ESX.RegisterServerCallback('garage:isPlateAvailable', function(source, cb, plate)
    MySQL.Async.fetchAll('SELECT plate FROM owned_vehicles WHERE plate = @plate', {
        ['@plate'] = plate
    }, function(result)
        cb(#result == 0)
    end)
end)

-- ====================================
-- IMPOUND SYSTEM
-- ====================================
RegisterServerEvent('garage:impoundVehicle')
AddEventHandler('garage:impoundVehicle', function(plate)
    local playerId = source

    MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored WHERE plate = @plate', {
        ['@plate'] = plate,
        ['@stored'] = 0
    }, function(rowsChanged)
        if rowsChanged > 0 then
            TriggerClientEvent('esx:showNotification', playerId, 'Fahrzeug ~r~' .. plate .. '~s~ wurde abgeschleppt!')
        end
    end)
end)

RegisterServerEvent('garage:releaseVehicle')
AddEventHandler('garage:releaseVehicle', function(plate, price)
    local playerId = source
    local xPlayer = ESX.GetPlayerFromId(playerId)

    if not xPlayer then return end

    if xPlayer.getMoney() >= price then
        xPlayer.removeMoney(price)

        MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored WHERE plate = @plate AND owner = @owner', {
            ['@plate'] = plate,
            ['@stored'] = 1,
            ['@owner'] = xPlayer.getIdentifier()
        }, function(rowsChanged)
            if rowsChanged > 0 then
                TriggerClientEvent('garage:vehicleReleased', playerId, plate)
            end
        end)
    else
        TriggerClientEvent('esx:showNotification', playerId, '~r~Nicht genug Geld!')
    end
end)

RegisterServerEvent('garage:releaseAllVehicles')
AddEventHandler('garage:releaseAllVehicles', function(totalPrice)
    local playerId = source
    local xPlayer = ESX.GetPlayerFromId(playerId)

    if not xPlayer then return end

    if xPlayer.getMoney() >= totalPrice then
        xPlayer.removeMoney(totalPrice)

        MySQL.Async.execute('UPDATE owned_vehicles SET stored = @stored WHERE owner = @owner AND stored = @currentStored', {
            ['@stored'] = 1,
            ['@currentStored'] = 0,
            ['@owner'] = xPlayer.getIdentifier()
        }, function(rowsChanged)
            if rowsChanged > 0 then
                TriggerClientEvent('garage:allVehiclesReleased', playerId, rowsChanged)
            end
        end)
    else
        TriggerClientEvent('esx:showNotification', playerId, '~r~Nicht genug Geld!')
    end
end)

print('[GARAGE] Server-seitige Garage-Logik geladen!')
