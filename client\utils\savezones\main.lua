ESX = exports['es_extended']:getSharedObject()

local closestZone = 1

CreateThread(function()
    while (true) do
        local sleep = 1000
        local playerPed = PlayerPedId()
        local x, y, z = table.unpack(GetEntityCoords(playerPed, true))
        local minDistance = 400
        Wait(1)
        for i = 1, #MINERA_SAFEZONE.SafeZones, 1 do
            dist = Vdist(MINERA_SAFEZONE.SafeZones[i].Zone.x, MINERA_SAFEZONE.SafeZones[i].Zone.y, MINERA_SAFEZONE.SafeZones[i].Zone.z, x, y, z)
            if (dist < minDistance) then
                minDistance = dist
                closestZone = i
            end
        end
        Wait(sleep)
    end
end)

CreateThread(function()
    while (true) do
        local sleep = 300
        local player = GetPlayerPed(-1)
        local x, y, z = table.unpack(GetEntityCoords(player, true))
        local dist =Vdist(MINERA_SAFEZONE.SafeZones[closestZone].Zone.x, MINERA_SAFEZONE.SafeZones[closestZone].Zone.y, MINERA_SAFEZONE.SafeZones[closestZone].Zone.z,x,y,z)
        if (dist <=  MINERA_SAFEZONE.SafeZones[closestZone].ZoneSize) then
            sleep = 4
            Zone = true
            SetEntityInvincible(player, true)
            if (MINERA_SAFEZONE.SafeZones[closestZone].DisablePlayerFiring) then
                DisablePlayerFiring(player, true)
                DisableControlAction(0, 140)
                DisableControlAction(0, 45)
            end
        else
            if (Zone) then
                Zone = false
                SetEntityInvincible(player, false)
            end
        end
        if (dist <= MINERA_SAFEZONE.SafeZones[closestZone].Marker.RenderDistance) and (MINERA_SAFEZONE.SafeZones[closestZone].Marker.Enabled) then
            sleep = 4
            DrawMarker(MINERA_SAFEZONE.SafeZones[closestZone].Marker.Type, MINERA_SAFEZONE.SafeZones[closestZone].Zone.x, MINERA_SAFEZONE.SafeZones[closestZone].Zone.y, MINERA_SAFEZONE.SafeZones[closestZone].Zone.z + 2, 0.0, 0.0, 0.0, 0.0, 180.0, 0.0, MINERA_SAFEZONE.SafeZones[closestZone].ZoneSize+1.0, MINERA_SAFEZONE.SafeZones[closestZone].ZoneSize+1.0, MINERA_SAFEZONE.SafeZones[closestZone].ZoneSize, MINERA_SAFEZONE.SafeZones[closestZone].Marker.Color.R, MINERA_SAFEZONE.SafeZones[closestZone].Marker.Color.G, MINERA_SAFEZONE.SafeZones[closestZone].Marker.Color.B, MINERA_SAFEZONE.SafeZones[closestZone].Marker.Color.Alpha, false, true, 2, nil, nil, false)
        end
        Wait(sleep)
    end
end)

CreateThread(function()
    while (true) do
        local sleep = 300
        local weapon = GetSelectedPedWeapon(player)
        local player = GetPlayerPed(-1)
        local vehicle = GetVehiclePedIsIn(player, true)
        local x, y, z = table.unpack(GetEntityCoords(player, true))
        local dist = Vdist(MINERA_SAFEZONE.SafeZones[closestZone].Zone.x, MINERA_SAFEZONE.SafeZones[closestZone].Zone.y, MINERA_SAFEZONE.SafeZones[closestZone].Zone.z,x,y,z)

        if (dist <= MINERA_SAFEZONE.SafeZones[closestZone].ZoneSize) then
            SetEntityMaxSpeed(vehicle, MINERA_SAFEZONE.SafeZones[closestZone].SpeedLimit)
            if MINERA_SAFEZONE.SafeZones[closestZone].AllowWeaponsInHands then
            else
                SetCurrentPedWeapon(player,GetHashKey("WEAPON_UNARMED"),true);
            end
            if not (Entered) and (MINERA_SAFEZONE.SafeZones[closestZone].ShowNotify) then
                Entered = true
                MINERA_SAFEZONE.Notification(nil, 'client', nil, MINERA_SAFEZONE.NotifyTranslations.Entered)
            end
            if (MINERA_SAFEZONE.SafeZones[closestZone].ShowSafeZoneText) then
            end
        else
            if (Entered) then
                Entered = false
                MINERA_SAFEZONE.Notification(nil, 'client', nil, MINERA_SAFEZONE.NotifyTranslations.Leaved)
                SetEntityMaxSpeed(vehicle, 1000.0)
            end
        end
        Wait(sleep)
    end
end)

exports('inSafeZone', function()
    return Entered
end)